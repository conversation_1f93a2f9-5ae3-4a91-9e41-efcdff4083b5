"""
LangGraph Studio Integration for externalized prompt management.

Integrates with LangGraph Studio to:
- Sync externalized prompts with Studio configuration
- Update langgraph.json automatically
- Enable version management in Studio
- Support A/B testing integration
- Handle prompt deployment between environments
"""

import json
import re
import shutil
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .ab_testing import ABTestManager, TestStatus
from .prompt_loader import PromptLoader
from .version_manager import VersionManager


@dataclass
class StudioPromptConfig:
    """Configuration for a prompt in LangGraph Studio."""

    name: str
    description: str
    default_value: str
    langgraph_nodes: List[str]
    langgraph_type: str = "prompt"
    schema_type: str = "string"


@dataclass
class StudioGraphConfig:
    """Configuration for a graph in LangGraph Studio."""

    name: str
    path: str
    config_schema: Dict[str, Any]


class LangGraphStudioIntegrator:
    """Integrates externalized prompts with LangGraph Studio."""

    def __init__(self, prompts_dir: str, langgraph_config_path: str = "langgraph.json"):
        self.prompts_dir = Path(prompts_dir)
        self.langgraph_config_path = Path(langgraph_config_path)
        self.studio_config_dir = self.prompts_dir / ".studio"

        # Create studio directory
        self.studio_config_dir.mkdir(exist_ok=True)

        # Initialize managers
        self.prompt_loader = PromptLoader(prompts_dir)
        self.version_manager = VersionManager(prompts_dir)
        self.ab_test_manager = ABTestManager(prompts_dir)

        # Load existing langgraph.json
        self.langgraph_config = self._load_langgraph_config()

    def _load_langgraph_config(self) -> Dict[str, Any]:
        """Load existing langgraph.json configuration."""
        if self.langgraph_config_path.exists():
            with open(self.langgraph_config_path, "r") as f:
                return json.load(f)
        return {"dependencies": ["."], "graphs": {}, "env": ".env.local"}

    def _save_langgraph_config(self):
        """Save updated langgraph.json configuration."""
        with open(self.langgraph_config_path, "w") as f:
            json.dump(self.langgraph_config, f, indent=2)

    def _extract_prompt_name_from_key(self, config_key: str) -> Optional[str]:
        """Extract prompt name from configuration key."""
        # Convert keys like "strength_coach_prompt" -> "strength_coach"
        if config_key.endswith("_prompt"):
            return config_key[:-7]  # Remove '_prompt' suffix
        return None

    def _generate_config_key(self, prompt_name: str) -> str:
        """Generate configuration key for a prompt."""
        return f"{prompt_name}_prompt"

    def register_prompt_with_studio(
        self,
        prompt_name: str,
        graph_names: List[str],
        nodes: Optional[List[str]] = None,
        description: Optional[str] = None,
        version: Optional[str] = None,
    ) -> StudioPromptConfig:
        """Register a prompt with LangGraph Studio configuration."""
        # Load prompt
        config = self.prompt_loader.load_prompt(prompt_name, version=version)

        # Generate Studio configuration
        config_key = self._generate_config_key(prompt_name)

        # Use provided nodes or try to infer from prompt name
        if nodes is None:
            nodes = [prompt_name.replace("_", "-")]

        studio_config = StudioPromptConfig(
            name=config_key,
            description=description
            or config.metadata.description
            or f"System prompt for {prompt_name.replace('_', ' ').title()}",
            default_value=config.prompt.system,
            langgraph_nodes=nodes,
            langgraph_type="prompt",
        )

        # Update all specified graphs
        for graph_name in graph_names:
            self._add_prompt_to_graph(graph_name, studio_config)

        # Save configuration
        self._save_langgraph_config()

        return studio_config

    def _add_prompt_to_graph(self, graph_name: str, studio_config: StudioPromptConfig):
        """Add prompt configuration to a specific graph."""
        if graph_name not in self.langgraph_config["graphs"]:
            raise ValueError(f"Graph '{graph_name}' not found in langgraph.json")

        graph_config = self.langgraph_config["graphs"][graph_name]

        # Ensure config_schema exists
        if "config_schema" not in graph_config:
            graph_config["config_schema"] = {"type": "object", "properties": {}}

        # Add prompt to config schema
        graph_config["config_schema"]["properties"][studio_config.name] = {
            "type": studio_config.schema_type,
            "description": studio_config.description,
            "default": studio_config.default_value,
            "langgraph_nodes": studio_config.langgraph_nodes,
            "langgraph_type": studio_config.langgraph_type,
        }

    def sync_all_prompts_to_studio(
        self, overwrite: bool = False
    ) -> Dict[str, List[str]]:
        """Sync all externalized prompts to LangGraph Studio configuration."""
        all_prompts = self.prompt_loader.list_prompts()
        sync_results = {"updated": [], "skipped": [], "errors": []}

        for prompt_name in all_prompts:
            try:
                # Check if prompt is already configured
                config_key = self._generate_config_key(prompt_name)
                is_configured = self._is_prompt_configured(config_key)

                if is_configured and not overwrite:
                    sync_results["skipped"].append(prompt_name)
                    continue

                # Auto-detect which graphs should use this prompt
                target_graphs = self._detect_prompt_graphs(prompt_name)

                if target_graphs:
                    self.register_prompt_with_studio(
                        prompt_name=prompt_name,
                        graph_names=target_graphs,
                        nodes=[prompt_name.replace("_", "-")],
                    )
                    sync_results["updated"].append(prompt_name)
                else:
                    sync_results["skipped"].append(prompt_name)

            except Exception as e:
                sync_results["errors"].append(f"{prompt_name}: {str(e)}")

        return sync_results

    def _is_prompt_configured(self, config_key: str) -> bool:
        """Check if a prompt is already configured in any graph."""
        for graph_config in self.langgraph_config["graphs"].values():
            if "config_schema" in graph_config:
                properties = graph_config["config_schema"].get("properties", {})
                if config_key in properties:
                    return True
        return False

    def _detect_prompt_graphs(self, prompt_name: str) -> List[str]:
        """Auto-detect which graphs should use a specific prompt."""
        target_graphs = []

        # Pattern matching based on prompt name
        graph_mappings = {
            r".*coach.*": ["coaching_agent"],
            r"strength.*": ["strength_agent", "coaching_agent"],
            r"nutrition.*": ["nutrition_agent", "coaching_agent"],
            r"cardio.*": ["cardio_agent", "coaching_agent"],
            r"cycling.*": ["cycling_agent", "coaching_agent"],
            r"recovery.*": ["recovery_agent", "coaching_agent"],
            r"mental.*": ["mental_agent", "coaching_agent"],
            r"head.*coach.*": ["head_coach"],
            r".*system.*": list(self.langgraph_config["graphs"].keys()),
        }

        for pattern, graphs in graph_mappings.items():
            if re.match(pattern, prompt_name, re.IGNORECASE):
                target_graphs.extend(graphs)
                break

        # Filter to only existing graphs
        existing_graphs = list(self.langgraph_config["graphs"].keys())
        return [g for g in set(target_graphs) if g in existing_graphs]

    def update_prompt_in_studio(
        self,
        prompt_name: str,
        version: Optional[str] = None,
        force_update: bool = False,
    ) -> Dict[str, bool]:
        """Update a prompt in Studio configuration with new content."""
        config_key = self._generate_config_key(prompt_name)

        # Load prompt content
        config = self.prompt_loader.load_prompt(prompt_name, version=version)
        new_content = config.prompt.system

        update_results = {}

        # Update in all graphs that use this prompt
        for graph_name, graph_config in self.langgraph_config["graphs"].items():
            if "config_schema" in graph_config:
                properties = graph_config["config_schema"].get("properties", {})
                if config_key in properties:
                    # Check if update is needed
                    current_default = properties[config_key].get("default", "")
                    if current_default != new_content or force_update:
                        properties[config_key]["default"] = new_content
                        properties[config_key]["description"] = (
                            config.metadata.description
                            or properties[config_key].get("description", "")
                        )
                        update_results[graph_name] = True
                    else:
                        update_results[graph_name] = False

        if any(update_results.values()):
            self._save_langgraph_config()

        return update_results

    def deploy_ab_test_to_studio(
        self, test_id: str, target_graphs: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Deploy A/B test configuration to Studio."""
        test = self.ab_test_manager.load_test(test_id)

        if test.status != TestStatus.ACTIVE:
            raise ValueError(f"Test {test_id} is not active")

        # Use all graphs if none specified
        if target_graphs is None:
            target_graphs = list(self.langgraph_config["graphs"].keys())

        deployment_config = {
            "test_id": test_id,
            "test_name": test.name,
            "prompt_name": test.prompt_name,
            "variants": [],
            "deployed_at": datetime.now().isoformat(),
            "target_graphs": target_graphs,
        }

        # Create Studio configuration for each variant
        for variant in test.variants:
            # Load version content
            version_config = self.version_manager.load_version(
                test.prompt_name, variant.version
            )

            variant_config = {
                "name": variant.name,
                "version": variant.version,
                "traffic_percentage": variant.traffic_percentage,
                "content": version_config.prompt.system,
                "description": variant.description,
            }
            deployment_config["variants"].append(variant_config)

        # Save deployment configuration
        deployment_file = self.studio_config_dir / f"ab_test_{test_id}.json"
        with open(deployment_file, "w") as f:
            json.dump(deployment_config, f, indent=2)

        # Update Studio configuration with traffic splitting logic
        # This would typically involve updating the graph configuration
        # to support dynamic prompt selection based on user context

        return deployment_config

    def create_studio_environment_config(
        self, environment: str, prompt_labels: Dict[str, str] = None
    ) -> str:
        """Create Studio configuration for a specific environment."""
        if prompt_labels is None:
            prompt_labels = {"production": "production", "staging": "staging"}

        env_config = self.langgraph_config.copy()

        # Update all prompt defaults based on environment labels
        for graph_name, graph_config in env_config["graphs"].items():
            if "config_schema" in graph_config:
                properties = graph_config["config_schema"].get("properties", {})

                for prop_name, prop_config in properties.items():
                    if prop_config.get("langgraph_type") == "prompt":
                        prompt_name = self._extract_prompt_name_from_key(prop_name)
                        if prompt_name:
                            try:
                                # Load prompt with environment-specific label
                                label = prompt_labels.get(environment, "production")
                                config = self.prompt_loader.load_prompt(
                                    prompt_name, label=label
                                )
                                prop_config["default"] = config.prompt.system
                            except Exception:
                                # Keep existing default if prompt not found
                                pass

        # Save environment-specific configuration
        env_config_file = self.studio_config_dir / f"langgraph_{environment}.json"
        with open(env_config_file, "w") as f:
            json.dump(env_config, f, indent=2)

        return str(env_config_file)

    def backup_studio_config(self, backup_dir: Optional[str] = None) -> str:
        """Backup current Studio configuration."""
        if backup_dir is None:
            backup_dir = self.studio_config_dir / "backups"

        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_path / f"langgraph_config_backup_{timestamp}.json"

        shutil.copy2(self.langgraph_config_path, backup_file)

        return str(backup_file)

    def validate_studio_config(self) -> Dict[str, List[str]]:
        """Validate Studio configuration for consistency."""
        validation_results = {
            "valid_prompts": [],
            "missing_prompts": [],
            "invalid_defaults": [],
            "configuration_errors": [],
        }

        for graph_name, graph_config in self.langgraph_config["graphs"].items():
            if "config_schema" not in graph_config:
                continue

            properties = graph_config["config_schema"].get("properties", {})

            for prop_name, prop_config in properties.items():
                if prop_config.get("langgraph_type") == "prompt":
                    prompt_name = self._extract_prompt_name_from_key(prop_name)

                    if prompt_name:
                        try:
                            # Validate prompt exists
                            config = self.prompt_loader.load_prompt(prompt_name)

                            # Check if default matches current prompt content
                            current_default = prop_config.get("default", "")
                            actual_content = config.prompt.system

                            if current_default == actual_content:
                                validation_results["valid_prompts"].append(
                                    f"{graph_name}.{prop_name}"
                                )
                            else:
                                validation_results["invalid_defaults"].append(
                                    f"{graph_name}.{prop_name} (content mismatch)"
                                )

                        except Exception as e:
                            validation_results["missing_prompts"].append(
                                f"{graph_name}.{prop_name}: {str(e)}"
                            )
                    else:
                        validation_results["configuration_errors"].append(
                            f"{graph_name}.{prop_name}: Cannot extract prompt name"
                        )

        return validation_results

    def generate_studio_documentation(self) -> str:
        """Generate documentation for Studio integration."""
        doc_content = [
            "# LangGraph Studio Prompt Integration Documentation",
            "",
            "## Overview",
            "This document describes the integration between externalized prompts and LangGraph Studio.",
            "",
            "## Configured Prompts",
            "",
        ]

        for graph_name, graph_config in self.langgraph_config["graphs"].items():
            if "config_schema" not in graph_config:
                continue

            doc_content.append(f"### Graph: {graph_name}")
            doc_content.append("")

            properties = graph_config["config_schema"].get("properties", {})
            prompt_props = {
                k: v
                for k, v in properties.items()
                if v.get("langgraph_type") == "prompt"
            }

            if prompt_props:
                for prop_name, prop_config in prompt_props.items():
                    prompt_name = self._extract_prompt_name_from_key(prop_name)
                    nodes = prop_config.get("langgraph_nodes", [])
                    description = prop_config.get("description", "")

                    doc_content.extend(
                        [
                            f"**{prop_name}**",
                            f"- Prompt Name: `{prompt_name}`",
                            f"- Nodes: `{', '.join(nodes)}`",
                            f"- Description: {description}",
                            "",
                        ]
                    )
            else:
                doc_content.append("No prompt configurations found.")
                doc_content.append("")

        doc_content.extend(
            [
                "## Usage in Studio",
                "",
                "1. **Editing Prompts**: Prompts can be edited directly in LangGraph Studio UI",
                "2. **Version Management**: Use the CLI to manage prompt versions",
                "3. **A/B Testing**: Deploy A/B tests through the CLI integration",
                "4. **Environment Deployment**: Use environment-specific configurations",
                "",
                "## CLI Commands",
                "",
                "```bash",
                "# Sync all prompts to Studio",
                "python -m athlea_langgraph.cli studio sync",
                "",
                "# Update specific prompt in Studio",
                "python -m athlea_langgraph.cli studio update PROMPT_NAME",
                "",
                "# Deploy A/B test to Studio",
                "python -m athlea_langgraph.cli studio deploy-test TEST_ID",
                "",
                "# Validate Studio configuration",
                "python -m athlea_langgraph.cli studio validate",
                "```",
            ]
        )

        doc_file = self.studio_config_dir / "integration_docs.md"
        with open(doc_file, "w") as f:
            f.write("\n".join(doc_content))

        return str(doc_file)

    def get_integration_status(self) -> Dict[str, Any]:
        """Get current integration status."""
        status = {
            "langgraph_config_path": str(self.langgraph_config_path),
            "studio_config_dir": str(self.studio_config_dir),
            "total_graphs": len(self.langgraph_config["graphs"]),
            "graphs_with_prompts": 0,
            "total_prompt_configs": 0,
            "externalized_prompts": len(self.prompt_loader.list_prompts()),
            "active_ab_tests": 0,
            "last_updated": None,
        }

        # Count graphs with prompt configurations
        for graph_config in self.langgraph_config["graphs"].values():
            if "config_schema" in graph_config:
                properties = graph_config["config_schema"].get("properties", {})
                prompt_props = [
                    p
                    for p in properties.values()
                    if p.get("langgraph_type") == "prompt"
                ]
                if prompt_props:
                    status["graphs_with_prompts"] += 1
                    status["total_prompt_configs"] += len(prompt_props)

        # Count active A/B tests
        active_tests = self.ab_test_manager.list_tests(status=TestStatus.ACTIVE)
        status["active_ab_tests"] = len(active_tests)

        # Check last update time
        if self.langgraph_config_path.exists():
            status["last_updated"] = datetime.fromtimestamp(
                self.langgraph_config_path.stat().st_mtime
            ).isoformat()

        return status
