"""
Coach-Driven Coaching Graph

This implementation follows production LangGraph best practices where:
1. Coaches decide when they need external knowledge/tools
2. GraphRAG is a tool available to coaches, not a separate routing step
3. Simple queries bypass knowledge retrieval entirely
4. Tools are bound to coach nodes, not separate nodes

Based on production patterns from CyberArk Engineering and graph database best practices.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

from dotenv import load_dotenv

load_dotenv()

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

# Import essential components
from ..agents.intelligence_hub_node import intelligence_hub_node
from ..agents.reasoning_node import reasoning_node
from ..agents.graphrag_nodes import get_graphrag_retriever
from ..services.azure_openai_service import create_azure_chat_openai

# Import existing configuration
from .archived.comprehensive_coaching_graph import (
    ComprehensiveCoachingConfig,
    initialize_global_resources,
)

# Import the optimized state
from ..states.optimized_state import OptimizedCoachingState

logger = logging.getLogger(__name__)


class GraphRAGTool:
    """GraphRAG tool that coaches can invoke when they need external knowledge."""

    name = "graphrag_search"
    description = "Search the knowledge base for evidence-based strategies, research, and best practices"

    def __init__(self):
        self.retriever = None

    async def _get_retriever(self):
        """Lazy initialization of GraphRAG retriever."""
        if self.retriever is None:
            self.retriever = await get_graphrag_retriever()
        return self.retriever

    async def invoke(self, query: str, domain: str = "fitness") -> str:
        """Invoke GraphRAG search for the given query."""
        try:
            retriever = await self._get_retriever()

            # Perform hybrid search (vector + graph)
            search_results = await retriever.hybrid_search(query, domain)

            # Synthesize knowledge from results
            vector_results = search_results.get("vector_results", [])
            graph_results = search_results.get("graph_results", [])

            knowledge_context = await retriever.synthesize_knowledge(
                vector_results, graph_results, query, domain
            )

            return knowledge_context

        except Exception as e:
            logger.error(f"GraphRAG tool error: {e}")
            return f"Unable to retrieve knowledge for: {query}"


# Global GraphRAG tool instance
graphrag_tool = GraphRAGTool()


async def create_coach_driven_coaching_graph(
    config: Optional[Union[Dict[str, Any], ComprehensiveCoachingConfig]] = None,
) -> CompiledStateGraph:
    """
    Create the coach-driven coaching graph following production patterns.

    Key improvements:
    - Coaches decide when to use GraphRAG
    - No pre-emptive knowledge retrieval
    - Tools are bound to coach nodes
    - Simple queries bypass expensive operations
    """
    logger.info("🚀 COACH_DRIVEN_GRAPH: Starting coach-driven graph creation")

    if config is None:
        config = {}

    # Parse configuration
    if isinstance(config, ComprehensiveCoachingConfig):
        coaching_config = config
    elif isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        coaching_config = ComprehensiveCoachingConfig()

    # Initialize global resources
    await initialize_global_resources(coaching_config)

    # Build the coach-driven graph
    uncompiled_graph = await build_coach_driven_graph(coaching_config)

    # Compile with memory if enabled
    if coaching_config.enable_memory:
        logger.info("🧠 COACH_DRIVEN_GRAPH: Memory enabled")
        memory = MemorySaver()
        graph = uncompiled_graph.compile(checkpointer=memory)
    else:
        graph = uncompiled_graph.compile()

    logger.info("✅ COACH_DRIVEN_GRAPH: Graph compiled successfully")
    return graph


async def build_coach_driven_graph(config: ComprehensiveCoachingConfig) -> StateGraph:
    """
    Build the coach-driven graph structure.

    Flow:
    START → reasoning → intelligence_hub → [direct_coaches_with_tools] → [aggregation] → END
    """
    logger.info("🏗️ COACH_DRIVEN_GRAPH: Building coach-driven structure")

    # Get tools manager (reuse existing logic)
    from ..agents.specialized_coaches import get_tools_manager

    tools_manager = await get_tools_manager()

    async def enhanced_reasoning_node(state: OptimizedCoachingState) -> Dict[str, Any]:
        """Enhanced reasoning node - same as optimized version."""
        logger.info("🧠 REASONING: Starting reasoning analysis")

        # Extract state information
        if isinstance(state, dict):
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
            execution_steps = state.get("execution_steps", [])
        else:
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])
            execution_steps = getattr(state, "execution_steps", [])

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        try:
            # Call the original reasoning node
            original_result = await reasoning_node(state)

            # Extract reasoning content
            reasoning_output = ""
            if original_result.get("messages"):
                for msg in original_result["messages"]:
                    if isinstance(msg, AIMessage) and msg.name == "ReasoningAgent":
                        reasoning_output = msg.content
                        break

            if not reasoning_output:
                reasoning_output = (
                    f"User query: {user_query}. Need to proceed with analysis."
                )

            logger.info(
                f"✅ REASONING: Generated analysis ({len(reasoning_output)} chars)"
            )

            return {
                "current_node": "reasoning",
                "reasoning_output": reasoning_output,
                "messages": original_result.get("messages", messages),
                "execution_steps": execution_steps + ["reasoning"],
                "debug_info": {
                    "node": "reasoning",
                    "action": "reasoning_complete",
                },
            }

        except Exception as e:
            logger.error(f"❌ REASONING: Error: {e}")
            return {
                "current_node": "reasoning",
                "reasoning_output": f"User query: {user_query}. Need to proceed with analysis.",
                "messages": messages,
                "execution_steps": execution_steps + ["reasoning"],
                "debug_info": {"node": "reasoning", "error": str(e)},
            }

    async def create_coach_with_tools(domain: str, tools_manager):
        """Create a coach node that can invoke tools when needed."""

        async def coach_with_tools_node(
            state: OptimizedCoachingState,
        ) -> Dict[str, Any]:
            """Coach node that can decide to use GraphRAG and other tools."""
            logger.info(f"🎯 COACH_WITH_TOOLS: [{domain}] Starting execution")

            if isinstance(state, dict):
                user_query = state.get("user_query", "")
                messages = state.get("messages", [])
                user_profile = state.get("user_profile", {})
                execution_steps = state.get("execution_steps", [])
                routing_decision = state.get("routing_decision", "direct_coach")
                required_coaches = state.get("required_coaches", [])
            else:
                user_query = getattr(state, "user_query", "")
                messages = getattr(state, "messages", [])
                user_profile = getattr(state, "user_profile", {})
                execution_steps = getattr(state, "execution_steps", [])
                routing_decision = getattr(state, "routing_decision", "direct_coach")
                required_coaches = getattr(state, "required_coaches", [])

            # Get the latest user message if user_query is empty
            if not user_query and messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_query = msg.content
                        break

            try:
                # Create LLM with tools for this coach
                coach_llm = create_azure_chat_openai(
                    node_type=f"{domain}_coach_with_tools",
                    temperature=0.7,
                    max_tokens=4000,
                )

                # Define available tools for this coach
                available_tools = []

                # All coaches get access to GraphRAG
                available_tools.append(
                    {
                        "type": "function",
                        "function": {
                            "name": "graphrag_search",
                            "description": "Search the knowledge base for evidence-based strategies, research, and best practices",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "The search query for knowledge retrieval",
                                    },
                                    "domain": {
                                        "type": "string",
                                        "description": f"The domain for search (default: {domain})",
                                    },
                                },
                                "required": ["query"],
                            },
                        },
                    }
                )

                # Bind tools to the LLM
                coach_with_tools = coach_llm.bind(tools=available_tools)

                # Create coach-specific system prompt
                system_prompt = f"""You are an expert {domain} coach. You can provide coaching advice and use tools when you need additional information.

Available tools:
- graphrag_search: Use this when you need evidence-based research, scientific studies, or detailed best practices

Instructions:
1. First, try to answer based on your training
2. If you need more specific research or evidence-based strategies, use the graphrag_search tool
3. Always provide practical, actionable advice
4. Be concise but comprehensive

User profile: {json.dumps(user_profile) if user_profile else "No profile available"}
Domain: {domain}
"""

                # Prepare messages for the coach
                coach_messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_query),
                ]

                # First response - coach decides if tools are needed
                logger.info(f"🎯 COACH_WITH_TOOLS: [{domain}] Getting initial response")
                response = await coach_with_tools.ainvoke(coach_messages)

                # Check if coach wants to use tools
                if response.tool_calls:
                    logger.info(
                        f"🔧 COACH_WITH_TOOLS: [{domain}] Coach requesting tools: {[tc['name'] for tc in response.tool_calls]}"
                    )

                    # Execute tool calls
                    tool_results = []
                    for tool_call in response.tool_calls:
                        if tool_call["name"] == "graphrag_search":
                            # Execute GraphRAG search
                            search_query = tool_call["args"].get("query", user_query)
                            search_domain = tool_call["args"].get("domain", domain)

                            logger.info(
                                f"🔍 GRAPHRAG: Searching for '{search_query}' in domain '{search_domain}'"
                            )
                            knowledge = await graphrag_tool.invoke(
                                search_query, search_domain
                            )

                            tool_results.append(
                                {"tool_call_id": tool_call["id"], "content": knowledge}
                            )

                    # Add tool results to conversation and get final response
                    from langchain_core.messages import ToolMessage

                    coach_messages.append(response)
                    for tool_result in tool_results:
                        coach_messages.append(
                            ToolMessage(
                                content=tool_result["content"],
                                tool_call_id=tool_result["tool_call_id"],
                            )
                        )

                    # Get final response with tool context
                    logger.info(
                        f"🎯 COACH_WITH_TOOLS: [{domain}] Getting final response with tool context"
                    )
                    final_response = await coach_llm.ainvoke(coach_messages)
                    final_answer = final_response.content

                else:
                    # Coach provided direct answer without tools
                    logger.info(
                        f"🎯 COACH_WITH_TOOLS: [{domain}] Direct answer without tools"
                    )
                    final_answer = response.content

                logger.info(f"✅ COACH_WITH_TOOLS: [{domain}] Generated response")

                return {
                    "current_node": f"{domain}_coach",
                    "final_response": final_answer,
                    "coach_responses": {f"{domain}_coach": final_answer},
                    "execution_steps": execution_steps + [f"{domain}_coach"],
                    "routing_decision": routing_decision,
                    "required_coaches": required_coaches,
                    "debug_info": {
                        "node": f"{domain}_coach",
                        "used_tools": (
                            len(response.tool_calls) > 0
                            if hasattr(response, "tool_calls")
                            else False
                        ),
                        "action": "coach_with_tools_complete",
                    },
                }

            except Exception as e:
                logger.error(f"❌ COACH_WITH_TOOLS: [{domain}] Error: {e}")
                return {
                    "current_node": f"{domain}_coach",
                    "final_response": "I apologize, but I encountered an error. Please try again.",
                    "execution_steps": execution_steps + [f"{domain}_coach"],
                    "routing_decision": routing_decision,
                    "required_coaches": required_coaches,
                    "debug_info": {
                        "node": f"{domain}_coach",
                        "error": str(e),
                        "action": "error_fallback",
                    },
                }

        return coach_with_tools_node

    # Build the graph
    builder = StateGraph(OptimizedCoachingState)

    # Core nodes
    builder.add_node("reasoning", enhanced_reasoning_node)
    builder.add_node("intelligence_hub", intelligence_hub_node)

    # Create coach nodes with tools
    coach_domains = ["strength", "cardio", "cycling", "nutrition", "recovery", "mental"]
    for domain in coach_domains:
        coach_node = await create_coach_with_tools(domain, tools_manager)
        builder.add_node(f"{domain}_coach", coach_node)
        logger.info(f"✅ COACH_DRIVEN_GRAPH: Added {domain}_coach with tools")

    # Aggregation node (reuse from optimized graph)
    async def coach_driven_aggregation_node(
        state: OptimizedCoachingState,
    ) -> Dict[str, Any]:
        """Aggregation for multi-coach scenarios."""
        logger.info("🔄 AGGREGATION: Starting multi-coach aggregation")

        if isinstance(state, dict):
            coach_responses = state.get("coach_responses", {})
            execution_steps = state.get("execution_steps", [])
        else:
            coach_responses = getattr(state, "coach_responses", {})
            execution_steps = getattr(state, "execution_steps", [])

        # Simple aggregation for now
        aggregated_response = "Here's what our coaching team recommends:\n\n"
        for coach_name, response in coach_responses.items():
            display_name = coach_name.replace("_", " ").title()
            aggregated_response += f"**{display_name}:**\n{response}\n\n"

        return {
            "current_node": "aggregation",
            "final_response": aggregated_response,
            "aggregated_response": aggregated_response,
            "execution_steps": execution_steps + ["aggregation"],
            "debug_info": {
                "node": "aggregation",
                "coaches_aggregated": len(coach_responses),
            },
        }

    builder.add_node("aggregation", coach_driven_aggregation_node)

    # Define flow
    builder.add_edge(START, "reasoning")
    builder.add_edge("reasoning", "intelligence_hub")

    # Intelligence Hub routing
    def route_from_intelligence_hub(state: OptimizedCoachingState) -> str:
        """Route from Intelligence Hub to appropriate coaches."""
        if isinstance(state, dict):
            routing_decision = state.get("routing_decision", "direct_coach")
            required_coaches = state.get("required_coaches", [])
            primary_coach = state.get("primary_coach", "strength_coach")
        else:
            routing_decision = getattr(state, "routing_decision", "direct_coach")
            required_coaches = getattr(state, "required_coaches", [])
            primary_coach = getattr(state, "primary_coach", "strength_coach")

        logger.info(f"🔀 COACH_DRIVEN_ROUTING: Decision: {routing_decision}")
        logger.info(f"  - Required coaches: {required_coaches}")
        logger.info(f"  - Primary coach: {primary_coach}")

        # Route based on decision
        if routing_decision == "direct_coach":
            # Route to primary coach with tools
            coach_name = primary_coach.replace("_coach", "") + "_coach"
            logger.info(f"  → Routing to {coach_name} (with tools)")
            return coach_name
        elif routing_decision == "multi_coach" and len(required_coaches) > 1:
            # For now, route to primary coach (can be enhanced for parallel execution)
            coach_name = primary_coach.replace("_coach", "") + "_coach"
            logger.info(f"  → Routing to {coach_name} (multi-coach primary)")
            return coach_name
        else:
            # Default to strength coach
            logger.info("  → Routing to strength_coach (fallback)")
            return "strength_coach"

    # Add routing from intelligence hub to coaches
    routing_destinations = {}
    for domain in coach_domains:
        routing_destinations[f"{domain}_coach"] = f"{domain}_coach"

    builder.add_conditional_edges(
        "intelligence_hub",
        route_from_intelligence_hub,
        routing_destinations,
    )

    # Individual coach routing
    def route_from_coach(state: OptimizedCoachingState) -> str:
        """Route from individual coaches."""
        if isinstance(state, dict):
            routing_decision = state.get("routing_decision", "direct_coach")
            required_coaches = state.get("required_coaches", [])
        else:
            routing_decision = getattr(state, "routing_decision", "direct_coach")
            required_coaches = getattr(state, "required_coaches", [])

        # If direct coach or single coach, go to END
        if routing_decision == "direct_coach" or len(required_coaches) <= 1:
            logger.info("🔀 COACH_ROUTING: Single coach → END")
            return "END"
        # If multi-coach, go to aggregation
        else:
            logger.info("🔀 COACH_ROUTING: Multi-coach → aggregation")
            return "aggregation"

    # Add conditional routing for all coaches
    for domain in coach_domains:
        builder.add_conditional_edges(
            f"{domain}_coach",
            route_from_coach,
            {
                "aggregation": "aggregation",
                "END": END,
            },
        )

    # Aggregation to END
    builder.add_edge("aggregation", END)

    logger.info("✅ COACH_DRIVEN_GRAPH: Graph structure built successfully")
    logger.info("📊 COACH-DRIVEN SUMMARY:")
    logger.info("  - Coaches decide when to use GraphRAG tools")
    logger.info("  - No pre-emptive knowledge retrieval")
    logger.info("  - Simple queries bypass expensive operations")
    logger.info("  - Tools are bound to coach nodes, not separate nodes")
    logger.info("  - Follows production LangGraph patterns")

    return builder


# Entry points
async def create_coach_driven_test_graph(config=None):
    """Create coach-driven test graph for development."""
    config_dict = {
        "user_id": "test_user",
        "thread_id": "test_thread",
        "enable_memory": False,
        "use_react_agents": False,  # Using tool-enabled coaches instead
        "max_iterations": 3,
        "enable_human_feedback": False,
    }

    if config and hasattr(config, "configurable"):
        config_dict.update(config.configurable)
    elif isinstance(config, dict):
        config_dict.update(config)

    return await create_coach_driven_coaching_graph(config_dict)


if __name__ == "__main__":

    async def main():
        graph = await create_coach_driven_test_graph()
        print("✅ Coach-driven coaching graph created successfully!")
        print("🎯 Key improvements:")
        print("  - Coaches decide when to use GraphRAG")
        print("  - Tools bound to coach nodes (production pattern)")
        print("  - No pre-emptive knowledge retrieval")
        print("  - Simple queries are fast and cost-effective")

    asyncio.run(main())
