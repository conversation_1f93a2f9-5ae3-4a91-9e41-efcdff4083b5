# Athlea Python LangGraph Coaching System

A comprehensive Python implementation of the Athlea coaching system using LangGraph. This system provides intelligent, multi-agent coaching across various fitness domains with sophisticated reasoning, planning, execution capabilities, and advanced memory management.

## 🌟 Features

### Multi-Graph Architecture
- **Coaching Graph**: Main coaching system with reasoning, planning, and specialized agents
- **Onboarding Graph**: User onboarding and profile creation workflow
- **Individual Agent Graphs**: Standalone specialist agents for focused interactions
- **Graph Factory**: Centralized graph creation and management with caching

### Advanced Memory Management
- **MongoDB Integration**: Persistent memory storage with MongoSaver checkpointer
- **Memory Decay**: Intelligent forgetting with decay management
- **Summarization Engine**: Automatic conversation summarization
- **Domain-Specific Memory**: Specialized memory for different coaching domains
- **Advanced Retrieval**: Sophisticated memory retrieval with analytics
- **Mem0 Integration**: External memory service integration

### Core Architecture
- **Reasoning Node**: Analyzes user requests and provides structured thought process
- **Planning Node**: Determines which specialists should handle requests using intelligent routing
- **Head Coach**: Coordinates between specialists and handles general conversation
- **Specialized Coaches**: Domain experts with ReAct agent capabilities and hardened tools

### Specialized Coaching Agents
- 🏋️ **Strength Coach**: Resistance training, powerlifting, bodybuilding
- 🏃 **Running/Cardio Coach**: Running techniques, training plans, cardiovascular fitness
- 🚴 **Cycling Coach**: Cycling training, bike fit, route planning
- 🥗 **Nutrition Coach**: Sports nutrition, meal planning, dietary optimization
- 😴 **Recovery Coach**: Sleep, rest, injury prevention, mobility
- 🧠 **Mental Coach**: Sports psychology, motivation, goal setting

### Production-Ready Features
- **Hardened Tools**: Circuit breakers, timeout protection, retry logic
- **API Streaming**: Real-time streaming for coaching and onboarding sessions
- **LangGraph Studio**: Multiple graph configurations for development and testing
- **CLI Interface**: Command-line tools for development, testing, and management
- **State Management**: Multiple state types (AgentState, MemoryEnhancedAgentState, OnboardingState)
- **Schema Validation**: Pydantic-based validation throughout the system
- **Comprehensive Logging**: Structured logging with metrics and analytics

### Tool Integration
- **External Services**: Google Maps, Azure Maps, Azure Search
- **Session Generation**: Automated training session creation
- **Circuit Breakers**: Fault tolerance for external service calls
- **Tool Contracts**: Standardized tool interfaces across domains

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- Poetry for dependency management
- Azure OpenAI API access
- MongoDB (optional, for persistent memory)

### Installation

1. **Clone and setup the project:**
```bash
cd python-langgraph
poetry install
```

2. **Configure environment variables:**
Copy `.env.example` to `.env` and fill in your configuration:
```bash
cp .env.example .env
# Edit .env with your Azure OpenAI and other service configurations
```

3. **Start the Backend Server:**

**Option A: Automatic Startup (Recommended)**
```bash
# Using Python startup script
python start_backend.py

# Or using Bash script
./start-backend.sh
```

**Option B: Manual Startup**
```bash
# Direct module execution
python -m athlea_langgraph.api.main

# Or using Poetry
poetry run python -m athlea_langgraph.api.main
```

The backend server will start on `http://localhost:8000` with the following endpoints:
- **Health Check**: `http://localhost:8000/api/health`
- **Coaching**: `http://localhost:8000/api/coaching`
- **Onboarding**: `http://localhost:8000/api/onboarding`
- **Available Coaches**: `http://localhost:8000/api/coaches`

4. **Run example sessions:**
```bash
# Basic coaching session
poetry run python examples/example_coaching_session.py

# Memory-enhanced coaching
poetry run python examples/example_memory_coaching.py

# Tool integration demo
poetry run python examples/demo_tool_integration.py
```

5. **Use the CLI:**
```bash
# Run development commands
poetry run python -m athlea_langgraph.cli development --help

# Manage prompts
poetry run python -m athlea_langgraph.cli prompt-management --help
```

## 📋 Environment Configuration

Required environment variables in your `.env` file:

```env
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_DEPLOYMENT_NAME=gpt-4o-mini

# MongoDB (for persistent memory)
MONGODB_URI=mongodb://localhost:27017

# External Services (optional)
AZURE_SEARCH_API_KEY=your_azure_search_api_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
AZURE_MAPS_SUBSCRIPTION_KEY=your_azure_maps_key

# MCP Server Configuration
MCP_WORKFLOW_ENABLED=true
# ... see .env.example for complete list
```

## 💻 Usage Examples

### Basic Coaching with Graph Factory

```python
import asyncio
from athlea_langgraph import create_coaching_graph
from langchain_core.messages import HumanMessage

async def basic_example():
    # Create coaching graph with memory
    graph = create_coaching_graph(
        checkpointer_type="mongo",
        connection_string="mongodb://localhost:27017",
        database_name="athlea_coaching"
    )
    
    # Initial state
    initial_state = {
        "messages": [HumanMessage(content="Help me with deadlift form")],
        "user_profile": {
            "name": "Alex",
            "fitness_level": "intermediate",
            "goals": ["strength"],
            "limitations": ["knee injury"]
        }
    }
    
    # Run with thread ID for session continuity
    config = {"configurable": {"thread_id": "user_123_session_1"}}
    result = await graph.ainvoke(initial_state, config=config)
    
    print(f"Coach: {result['messages'][-1].content}")

asyncio.run(basic_example())
```

### Onboarding Flow

```python
from athlea_langgraph import create_onboarding_graph_factory
from athlea_langgraph.states import create_initial_onboarding_state

async def onboarding_example():
    # Create onboarding graph
    graph = create_onboarding_graph_factory(checkpointer_type="memory")
    
    # Start onboarding
    initial_state = create_initial_onboarding_state()
    config = {"configurable": {"thread_id": "onboarding_123"}}
    
    result = await graph.ainvoke(initial_state, config=config)
```

### Individual Agent Usage

```python
# Use individual strength agent
from athlea_langgraph.individual_graphs import create_strength_agent_graph

async def strength_agent_example():
    graph = create_strength_agent_graph()
    # Use the individual strength agent for focused interactions
```

### API Streaming

```python
from athlea_langgraph.api.coaching_stream import CoachingStreamAPI

async def streaming_example():
    api = CoachingStreamAPI()
    
    async for chunk in api.stream_coaching_response(
        message="Help with my workout plan",
        user_id="user_123",
        thread_id="session_456"
    ):
        print(chunk)
```

## 🏗️ Architecture

### Graph Types and Factory Pattern
```
Graph Factory
├── Coaching Graph (Main system)
│   ├── Reasoning Node
│   ├── Planning Node  
│   ├── Head Coach
│   └── Specialized Coaches (with ReAct agents)
├── Onboarding Graph
│   ├── Profile Collection
│   ├── Goal Setting
│   └── Plan Generation
└── Individual Agent Graphs
    ├── Strength Agent
    ├── Nutrition Agent
    ├── Cardio Agent
    ├── Recovery Agent
    ├── Mental Agent
    └── Cycling Agent
```

### Memory Architecture
```
Memory Management
├── MongoDB Storage (Persistent)
├── Memory Saver (In-memory)
├── Summarization Engine
├── Decay Management
├── Domain-Specific Memory
├── Analytics & Metrics
└── Mem0 Integration
```

### State Management
- **AgentState**: Core coaching conversations
- **MemoryEnhancedAgentState**: With advanced memory features
- **OnboardingState**: User onboarding and profile creation
- **Message Reducers**: Intelligent message handling and summarization

### Tool Integration
- **Hardened Tools**: Production-ready with circuit breakers
- **Domain-Specific Tools**: Specialized tools for each coaching area
- **External APIs**: Google Maps, Azure services, search capabilities
- **Session Generation**: Automated workout and plan creation

## 🧪 Testing and Development

### Run Tests
```bash
poetry run pytest tests/ -v
```

### CLI Development Tools
```bash
# Development utilities
poetry run python -m athlea_langgraph.cli development

# Prompt management
poetry run python -m athlea_langgraph.cli prompt-management

# Version management
poetry run python -m athlea_langgraph.cli version-management

# A/B testing utilities
poetry run python -m athlea_langgraph.cli ab-testing
```

### LangGraph Studio
Launch LangGraph Studio for visual development and testing:
```bash
langgraph up
```

Available graphs in Studio:
- `coaching_agent`: Main comprehensive coaching system
- `head_coach`: Coordination and general conversation
- `strength_agent`: Individual strength training agent
- `nutrition_agent`: Individual nutrition agent
- `cardio_agent`: Individual cardio training agent
- `recovery_agent`: Individual recovery agent
- `mental_agent`: Individual mental training agent
- `cycling_agent`: Individual cycling agent

## 📁 Project Structure

```
python-langgraph/
├── athlea_langgraph/           # Main package
│   ├── __init__.py            # Package exports
│   ├── states/                # State management
│   │   ├── state.py           # Core AgentState
│   │   ├── state_with_memory.py # Memory-enhanced state
│   │   └── onboarding_state.py # Onboarding workflow state
│   ├── graphs/                # Graph implementations
│   │   ├── coaching_graph.py   # Main coaching graph
│   │   └── onboarding_graph.py # Onboarding graph
│   ├── agents/                # Individual agent implementations
│   ├── tools/                 # Hardened tools with circuit breakers
│   │   ├── base_tool.py       # Tool base classes
│   │   ├── circuit_breaker.py # Fault tolerance
│   │   ├── strength/          # Strength training tools
│   │   ├── nutrition/         # Nutrition tools
│   │   ├── cardio/           # Cardio training tools
│   │   ├── recovery/         # Recovery tools
│   │   ├── mental/           # Mental training tools
│   │   └── external/         # External service integrations
│   ├── memory/               # Advanced memory management
│   │   ├── mongo_memory.py   # MongoDB integration
│   │   ├── summarization_engine.py # Conversation summarization
│   │   ├── decay_manager.py  # Memory decay logic
│   │   ├── analytics.py      # Memory analytics
│   │   └── mem0_adapter.py   # Mem0 service integration
│   ├── api/                  # API endpoints with streaming
│   │   ├── coaching_stream.py # Coaching API
│   │   └── onboarding_stream.py # Onboarding API
│   ├── cli/                  # Command-line interface
│   │   ├── commands/         # CLI command implementations
│   │   └── main.py          # CLI entry point
│   ├── services/            # External service clients
│   ├── schemas/             # Pydantic schemas
│   ├── config/             # Configuration management
│   └── graph_factory.py    # Graph creation and management
├── docs/                   # Comprehensive documentation
│   ├── 1_OVERVIEW.md      # Project introduction and navigation
│   ├── 2_Core_Architecture/ # System internals (4 files)
│   ├── 3_Integrations/    # External service connections (5 files)
│   └── 4_Developer_Guides/ # Practical how-to guides (4 files)
├── examples/               # Usage examples and demos
├── tests/                 # Comprehensive test suite
│   ├── integration/       # Integration tests
│   ├── e2e/              # End-to-end tests
│   ├── runners/          # Test runner utilities
│   └── unit/             # Unit tests
├── Step by step plan.md   # Project roadmap and architecture plan
├── langgraph.json        # LangGraph Studio configuration
├── pyproject.toml       # Poetry configuration
└── README.md           # This file
```

## 🔧 Customization

### Adding New Graph Types
1. Create new graph in `athlea_langgraph/graphs/`
2. Add to `GraphType` enum in `graph_factory.py`
3. Update factory methods

### Adding New Memory Backends
1. Implement checkpointer interface
2. Add to `GraphFactory.create_checkpointer()`
3. Update configuration options

### Creating Custom Tools
1. Inherit from `HardenedTool` base class
2. Implement schema validation
3. Add circuit breaker configuration
4. Register with appropriate agent

### Extending State Types
1. Create new state class in `states/`
2. Add appropriate reducers
3. Update graph configurations

## 🏭 Production Deployment

### Memory Configuration
```python
# MongoDB for production
graph = create_coaching_graph(
    checkpointer_type="mongo",
    connection_string="mongodb://prod-cluster:27017",
    database_name="athlea_production"
)
```

### API Deployment
```python
# FastAPI integration with streaming
from athlea_langgraph.api.coaching_stream import CoachingStreamAPI
app = FastAPI()
api = CoachingStreamAPI()

@app.post("/coaching/stream")
async def stream_coaching(request: CoachingRequest):
    return api.stream_coaching_response(
        message=request.message,
        user_id=request.user_id,
        thread_id=request.thread_id
    )
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

## 📄 License

This project is part of the Athlea fitness coaching platform.

## 🆘 Support and Troubleshooting

### Common Issues
1. **Memory Connection**: Ensure MongoDB is running for persistent memory
2. **API Keys**: Verify all service credentials in `.env`
3. **Graph Loading**: Check LangGraph Studio configuration in `langgraph.json`
4. **Tool Failures**: Circuit breakers automatically handle service outages

### Development Tools
- Use CLI for debugging: `poetry run python -m athlea_langgraph.cli development`
- Monitor memory with analytics: Check memory analytics dashboard
- Test individual agents: Use individual graph endpoints in Studio

### Performance Optimization
- Enable graph caching in production
- Use MongoDB for persistent memory
- Configure circuit breakers for external services
- Monitor memory decay and summarization

## 📚 Documentation

The project includes comprehensive documentation organized in a logical 4-tier hierarchy:

- **[`docs/1_OVERVIEW.md`](docs/1_OVERVIEW.md)** - Project introduction and navigation guide
- **[`docs/2_Core_Architecture/`](docs/2_Core_Architecture/)** - Deep dives into system design:
  - System Architecture and component integration
  - Data and State Management (MongoDB, Redis, Mem0)
  - Session Generation planning and execution
  - Configuration and Constants management
- **[`docs/3_Integrations/`](docs/3_Integrations/)** - External service connections:
  - GraphRAG for evidence-based coaching
  - LangSmith for monitoring and debugging
  - n8n workflow automation
  - Streaming API for real-time responses
- **[`docs/4_Developer_Guides/`](docs/4_Developer_Guides/)** - Practical how-to guides:
  - Agent and Tool Development
  - LangGraph Studio setup and usage
  - Testing Guide with specific commands
  - Development best practices

## 🗺️ Project Roadmap

See [`Step by step plan.md`](Step%20by%20step%20plan.md) for the comprehensive project roadmap, including:
- **Phase 1**: Unification & Seamless User Journeys
- **Phase 2**: Optimizing the Core Coaching Engine  
- **Phase 3**: Activating Advanced Capabilities
- **Phase 4**: Deployment & Continuous Improvement

The plan focuses on creating a fully speech-enabled, multimodal AI coaching platform by orchestrating existing mature components.

The system provides comprehensive coaching capabilities with production-ready features including fault tolerance, memory management, streaming APIs, and extensive tooling for development and deployment. 