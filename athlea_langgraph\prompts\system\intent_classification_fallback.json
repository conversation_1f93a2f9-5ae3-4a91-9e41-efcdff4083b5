{"metadata": {"name": "intent_classification_fallback", "version": "1.0.0", "description": "Fallback prompt for intent classification when the main prompt fails to load. Quickly categorizes user fitness queries.", "author": "Athlea System", "created_at": "2025-01-22T00:00:00.000Z", "updated_at": "2025-01-22T00:00:00.000Z", "prompt_type": "system", "tags": ["intent-classification", "fallback", "routing", "quick-classification"], "changelog": [{"version": "1.0.0", "date": "2025-01-22T00:00:00.000Z", "changes": "Initial creation - moved from hardcoded fallback", "author": "System Migration", "breaking_changes": false}]}, "prompt": {"system": "Classify user fitness queries quickly into these categories:\n\nSIMPLE_GREETING: Basic greetings like \"hello\", \"hi\", \"hey\"\nUNCLEAR_SHORT: Vague requests like \"help\", \"advice\", \"guidance\"\nCOMPLEX_QUERY: Specific fitness questions requiring detailed coaching\n\nRespond with ONLY the category name.", "context_template": null, "user_template": "Query: {user_query}", "examples": [{"input": "hi there", "output": "SIMPLE_GREETING"}, {"input": "help me", "output": "UNCLEAR_SHORT"}, {"input": "I need a strength training program for powerlifting", "output": "COMPLEX_QUERY"}], "instructions": "Respond with only one of the three category names: SIMPLE_GREETING, UNCLEAR_SHORT, or COMPLEX_QUERY."}, "variables": {"temperature": 0.1, "max_tokens": 50, "top_p": 1.0}, "validation": {"required_context": ["user_query"], "max_length": 100}}