"""\
Specialized Coaching Agents with ReAct Implementation - FINAL

Python implementation of specialized coaching agents using ReAct pattern.
Final production-ready version with comprehensive tool integration.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Sequence, Literal

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate, MessagesPlaceholder
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

from ..services.azure_openai_service import create_azure_chat_openai
from ..states import AgentState
from ..tools.external.airtable_mcp import get_airtable_langchain_tool
from ..tools.external.azure_maps import AzureMapsTool
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..tools.external.google_maps_elevation import GoogleMapsElevationTool
from ..tools.external.session_generation import SessionGenerationTool
from ..utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)

# Initialize prompt loader
_prompt_loader = get_prompt_loader()


def load_coach_prompt(prompt_id: str) -> str:
    """Load coach prompt from JSON file."""
    try:
        prompt_config = _prompt_loader.load_prompt(prompt_id)
        return prompt_config.get_rendered_prompt()
    except Exception as e:
        logger.error(f"Failed to load {prompt_id} prompt: {e}")
        raise ValueError(f"Could not load required prompt: {prompt_id}") from e


class ReActSpecializedCoachFinal:
    """
    Final corrected ReAct-based specialized coach implementation.

    This correctly handles tool call ID matching to fix the Azure OpenAI format issue.
    """

    def __init__(
        self,
        coach_name: str,
        coach_prompt: str,
        tools: List[BaseTool],
        llm_temperature: float = 0.7,
    ):
        self.coach_name = coach_name
        self.coach_prompt = coach_prompt
        self.tools = tools
        self.tools_by_name = {tool.name: tool for tool in tools}

        # Create LLM with tools bound
        self.llm = create_azure_chat_openai(
            temperature=llm_temperature, max_tokens=4000
        )
        if tools:
            self.llm = self.llm.bind_tools(tools)

        # Create the ReAct graph using the correct pattern
        self.graph = self._create_react_graph()

    def _create_react_graph(self) -> StateGraph:
        """Create the ReAct graph following the official LangGraph pattern."""

        def should_continue(state: AgentState) -> Literal["continue", "end"]:
            """Determine whether to continue with tool calls or end."""
            messages = state.get("messages", [])
            if not messages:
                return "end"

            last_message = messages[-1]
            # Check for tool calls in the correct format
            if hasattr(last_message, "tool_calls") and last_message.tool_calls:
                print(
                    f"🔧 Found {len(last_message.tool_calls)} tool calls, continuing to tools"
                )
                return "continue"
            else:
                print("✅ No tool calls found, ending")
                return "end"

        def call_model(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
            """Call the model with the current state."""
            messages = state.get("messages", [])
            user_profile = state.get("user_profile")

            # Prepare system message with coach prompt and user profile
            system_content = self.coach_prompt
            if user_profile:
                system_content += (
                    f"\n\nUser Profile:\n{json.dumps(user_profile, indent=2)}"
                )

            # Prepare conversation messages
            conversation_messages = [SystemMessage(content=system_content)]

            # Add recent conversation history (last 10 messages to avoid token limits)
            recent_messages = messages[-10:] if len(messages) > 10 else messages
            for msg in recent_messages:
                if isinstance(msg, (HumanMessage, AIMessage, ToolMessage)):
                    conversation_messages.append(msg)

            print(
                f"🤖 Calling {self.coach_name} LLM with {len(conversation_messages)} messages..."
            )

            # Call the model
            response = self.llm.invoke(conversation_messages, config)

            # Debug the response
            if hasattr(response, "tool_calls") and response.tool_calls:
                print(f"🔧 LLM returned {len(response.tool_calls)} tool calls:")
                for i, tc in enumerate(response.tool_calls):
                    print(f"  Tool {i+1}: {tc['name']} (ID: {tc['id']})")
            else:
                print(f"💬 LLM returned text response: {response.content[:100]}...")

            return {"messages": [response]}

        # Create the graph using the correct LangGraph pattern
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("agent", call_model)
        if self.tools:
            # Use the prebuilt ToolNode which handles tool execution correctly
            # This automatically handles the tool call ID matching
            workflow.add_node("tools", ToolNode(self.tools))

        # Set entry point
        workflow.add_edge(START, "agent")

        # Add conditional edges
        if self.tools:
            workflow.add_conditional_edges(
                "agent",
                should_continue,
                {
                    "continue": "tools",
                    "end": END,
                },
            )
            workflow.add_edge("tools", "agent")
        else:
            workflow.add_edge("agent", END)

        return workflow.compile()

    async def invoke(
        self, state: AgentState, config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """Invoke the ReAct coach with the given state."""
        try:
            print(f"🚀 Starting {self.coach_name} ReAct workflow...")
            result = await self.graph.ainvoke(state, config)
            print(f"✅ {self.coach_name} workflow completed successfully")
            return result
        except Exception as e:
            print(f"❌ Error in {self.coach_name}: {e}")
            import traceback

            traceback.print_exc()
            return {
                "messages": [
                    AIMessage(
                        content=f"I apologize, but I encountered an error while processing your request. Please try again.",
                        name=self.coach_name,
                    )
                ]
            }


class FinalSpecializedCoachesManager:
    """Manages all specialized coaches with their domain-specific tools."""

    def __init__(self):
        self._coaches: Dict[str, ReActSpecializedCoachFinal] = {}
        self._tools_initialized = False

    async def initialize_tools(self):
        """Initialize all tools for the coaches."""
        if self._tools_initialized:
            return

        print("🔧 Initializing Final ReAct coaches and tools...")

        # Initialize tools with error handling
        available_tools = {}

        try:
            airtable_tool = get_airtable_langchain_tool()
            available_tools["airtable"] = airtable_tool
            print("✅ Airtable tool initialized")
        except Exception as e:
            print(f"⚠ Airtable tool failed: {e}")

        try:
            azure_search_tool = self._create_azure_search_tool()
            available_tools["azure_search"] = azure_search_tool
            print("✅ Azure Search tool initialized")
        except Exception as e:
            print(f"⚠ Azure Search tool failed: {e}")

        try:
            session_generation_tool = self._create_session_generation_tool()
            available_tools["session_generation"] = session_generation_tool
            print("✅ Session Generation tool initialized")
        except Exception as e:
            print(f"⚠ Session Generation tool failed: {e}")

        # Create coaches with available tools
        coaches_config = {
            "strength_coach": {
                "prompt": load_coach_prompt("strength_coach"),
                "tools": ["airtable", "azure_search", "session_generation"],
            },
        }

        for coach_name, config in coaches_config.items():
            try:
                # Get available tools for this coach
                coach_tools = []
                for tool_name in config["tools"]:
                    if tool_name in available_tools:
                        coach_tools.append(available_tools[tool_name])

                print(f"🤖 Creating {coach_name} with {len(coach_tools)} tools...")

                self._coaches[coach_name] = ReActSpecializedCoachFinal(
                    coach_name=coach_name,
                    coach_prompt=config["prompt"],
                    tools=coach_tools,
                )
                print(f"✅ {coach_name} created successfully")

            except Exception as e:
                print(f"❌ Failed to create {coach_name}: {e}")
                import traceback

                traceback.print_exc()

        self._tools_initialized = True
        print(f"✅ Initialized {len(self._coaches)} Final ReAct specialized coaches")
        print(f"📋 Available coaches: {list(self._coaches.keys())}")

    def _create_azure_search_tool(self) -> BaseTool:
        """Create Azure Search tool as LangChain tool."""
        azure_search = AzureSearchRetrieverTool()

        class AzureSearchLangChainTool(BaseTool):
            name: str = "azure_cognitive_search"
            description: str = """Search Azure Cognitive Search for research, documentation, and evidence-based information.
            
            Use this tool for:
            - Research findings and scientific studies
            - Exercise explanations and technique details
            - Nutrition science and supplement information
            - Training methodology and programming principles
            - Evidence-based recommendations
            
            Input should be a search query string."""

            def _run(self, query: str) -> str:
                import asyncio

                try:
                    loop = asyncio.get_event_loop()
                    result = loop.run_until_complete(
                        azure_search.invoke({"query": query})
                    )
                except RuntimeError:
                    result = asyncio.run(azure_search.invoke({"query": query}))

                if result.success and result.results:
                    formatted_results = []
                    for item in result.results[:5]:  # Top 5 results
                        formatted_results.append(
                            f"Title: {item.title}\nContent: {item.content[:300]}...\nSource: {item.source_url or 'N/A'}"
                        )
                    return f"Found {result.result_count} results:\n\n" + "\n\n".join(
                        formatted_results
                    )
                else:
                    return f"Search failed: {result.message}"

            async def _arun(self, query: str) -> str:
                result = await azure_search.invoke({"query": query})
                if result.success and result.results:
                    formatted_results = []
                    for item in result.results[:5]:  # Top 5 results
                        formatted_results.append(
                            f"Title: {item.title}\nContent: {item.content[:300]}...\nSource: {item.source_url or 'N/A'}"
                        )
                    return f"Found {result.result_count} results:\n\n" + "\n\n".join(
                        formatted_results
                    )
                else:
                    return f"Search failed: {result.message}"

        return AzureSearchLangChainTool()

    def _create_session_generation_tool(self) -> BaseTool:
        """Create session generation tool as LangChain tool."""
        session_gen = SessionGenerationTool()

        class SessionGenerationLangChainTool(BaseTool):
            name: str = "session_generation"
            description: str = """Generate structured training sessions and workout plans.
            
            Use this tool to create:
            - Strength training workouts
            - Cardio sessions
            - Recovery protocols
            - Nutrition plans
            - Mental training sessions
            
            Input should be a JSON string with session parameters."""

            def _run(self, session_params: str) -> str:
                try:
                    import json

                    params = json.loads(session_params)
                    result = session_gen.generate_session(params)
                    return json.dumps(result, indent=2)
                except Exception as e:
                    return f"Session generation failed: {str(e)}"

            async def _arun(self, session_params: str) -> str:
                return self._run(session_params)

        return SessionGenerationLangChainTool()

    async def get_coach(self, coach_name: str) -> Optional[ReActSpecializedCoachFinal]:
        """Get a specific coach by name."""
        if not self._tools_initialized:
            await self.initialize_tools()

        return self._coaches.get(coach_name)

    async def list_coaches(self) -> List[str]:
        """List all available coach names."""
        if not self._tools_initialized:
            await self.initialize_tools()

        return list(self._coaches.keys())


# Global manager instance
_final_coaches_manager = FinalSpecializedCoachesManager()


async def get_final_specialized_coach(
    coach_name: str,
) -> Optional[ReActSpecializedCoachFinal]:
    """Get a final specialized coach by name."""
    return await _final_coaches_manager.get_coach(coach_name)


async def list_final_available_coaches() -> List[str]:
    """List all available final specialized coaches."""
    return await _final_coaches_manager.list_coaches()
