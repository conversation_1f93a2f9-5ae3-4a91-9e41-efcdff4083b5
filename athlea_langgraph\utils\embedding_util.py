"""
Azure OpenAI Embedding Utility for Athlea LangGraph Backend
Replicates the frontend embedding integration approach.
"""

import json
import logging
import os
from ..config import config
import re
from typing import List, Optional
import asyncio

import aiohttp
from opentelemetry import trace

logger = logging.getLogger(__name__)
tracer = trace.get_tracer("athlea-ai-coach")


def extract_deployment_id_from_url(url: str) -> Optional[str]:
    """
    Extract deployment ID from Azure OpenAI embedding endpoint URL.

    Args:
        url: The Azure OpenAI embedding endpoint URL

    Returns:
        The extracted deployment ID or None if not found
    """
    if not url:
        return None

    # Match pattern: deployments/DEPLOYMENT_ID/embeddings
    match = re.search(r"/deployments/([^/]+)/embeddings", url)

    if match and match.group(1):
        return match.group(1)

    return None


async def generate_embedding(
    text: str,
    api_key: Optional[str] = None,
    endpoint: Optional[str] = None,
    forced_model_id: Optional[str] = None,
) -> Optional[List[float]]:
    """
    Generate embeddings using Azure OpenAI.

    Args:
        text: The text to embed
        api_key: The Azure OpenAI API key, defaults to EMBEDDING_API_KEY
        endpoint: The endpoint URL, defaults to EMBEDDING_ENDPOINT
        forced_model_id: Optional override for model ID, normally extracted from endpoint URL

    Returns:
        The embedding vector or None on failure
    """
    # Create a span for tracing
    with tracer.start_as_current_span("embedding_util.generate_embedding") as span:
        span.set_attributes(
            {
                "component": "embedding_util",
                "function_name": "generate_embedding",
                "text_length": len(text) if text else 0,
                "custom_api_key": bool(api_key),
                "custom_endpoint": bool(endpoint),
                "custom_model_id": bool(forced_model_id),
            }
        )

        # Use provided values or fall back to centralized configuration
        from ..config import config

        embedding_api_key = api_key or config.embedding.api_key
        embedding_endpoint = endpoint or config.embedding.endpoint

        if not embedding_api_key or not embedding_endpoint:
            logger.error("generate_embedding: Missing API key or endpoint")
            span.set_attributes(
                {"error": True, "error_message": "Missing API key or endpoint"}
            )
            return None

        # Try to extract deployment ID from the URL
        url_deployment_id = extract_deployment_id_from_url(embedding_endpoint)

        # Priority: 1. Forced model ID, 2. URL deployment ID, 3. Environment variable, 4. Default
        model_deployment_id = (
            forced_model_id
            or url_deployment_id
            or config.embedding.deployment_id
            or "text-embedding-3-large"
        )

        span.set_attributes(
            {
                "model_id": model_deployment_id,
                "extracted_deployment_id": url_deployment_id or "none",
            }
        )

        try:
            logger.info(f'Generating embedding for: "{text[:50]}..."')
            logger.info(f"Using embedding endpoint: {embedding_endpoint}")
            logger.info(f"Using model ID: {model_deployment_id}")
            logger.info(
                f"(Deployment ID extracted from URL: {url_deployment_id or 'None'})"
            )

            request_body = {
                "input": text,
                "model": model_deployment_id,
            }

            headers = {
                "Content-Type": "application/json",
                "api-key": embedding_api_key,
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    embedding_endpoint,
                    headers=headers,
                    json=request_body,
                    timeout=aiohttp.ClientTimeout(total=30),
                ) as response:

                    if not response.ok:
                        error_text = await response.text()
                        logger.error(
                            f"generate_embedding: Failed ({response.status}): {error_text}"
                        )

                        span.set_attributes(
                            {
                                "error": True,
                                "error_status": response.status,
                                "error_message": error_text,
                            }
                        )
                        return None

                    result = await response.json()
                    if (
                        result.get("data")
                        and len(result["data"]) > 0
                        and result["data"][0].get("embedding")
                    ):
                        embedding_vector = result["data"][0]["embedding"]
                        logger.info(
                            f"Successfully generated embedding vector of length {len(embedding_vector)}"
                        )

                        span.set_attributes(
                            {"success": True, "vector_length": len(embedding_vector)}
                        )
                        return embedding_vector
                    else:
                        logger.error(
                            f"generate_embedding: Invalid response structure: {result}"
                        )

                        span.set_attributes(
                            {
                                "error": True,
                                "error_message": "Invalid response structure",
                            }
                        )
                        return None

        except Exception as error:
            logger.error(f"generate_embedding: Error: {error}")

            span.set_attributes({"error": True, "error_message": str(error)})
            return None


def get_embedding_config_for_mem0() -> dict:
    """
    Get embedding configuration dictionary for mem0 integration.

    Returns:
        Configuration dictionary compatible with mem0
    """
    from ..config import config

    embedding_endpoint = config.embedding.endpoint
    embedding_api_key = config.embedding.api_key

    if not embedding_endpoint or not embedding_api_key:
        logger.warning("Missing embedding credentials, falling back to OpenAI")
        return {
            "provider": "openai",
            "config": {
                "model": "text-embedding-ada-002",
                "api_key": config.azure_openai.api_key,
            },
        }

    # Extract deployment ID from URL
    deployment_id = extract_deployment_id_from_url(embedding_endpoint)

    if not deployment_id:
        logger.warning(
            "Could not extract deployment ID from URL, falling back to OpenAI"
        )
        return {
            "provider": "openai",
            "config": {
                "model": "text-embedding-ada-002",
                "api_key": config.azure_openai.api_key,
            },
        }

    # Extract base URL (remove the deployment path)
    base_url = embedding_endpoint.split("/openai/deployments/")[0]

    # Extract API version from URL if present
    api_version = "2023-05-15"  # default
    if "api-version=" in embedding_endpoint:
        version_match = re.search(r"api-version=([^&]+)", embedding_endpoint)
        if version_match:
            api_version = version_match.group(1)

    # Determine embedding dimensions based on model
    embedding_dims = 1536  # default for ada-002
    if "text-embedding-3-large" in deployment_id.lower():
        embedding_dims = 3072
    elif "text-embedding-3-small" in deployment_id.lower():
        embedding_dims = 1536

    logger.info(f"Extracted deployment ID: {deployment_id}")
    logger.info(f"Base URL: {base_url}")
    logger.info(f"API Version: {api_version}")
    logger.info(f"Embedding dimensions: {embedding_dims}")

    return {
        "provider": "azure_openai",
        "config": {
            "model": deployment_id,
            "embedding_dims": embedding_dims,
            "azure_kwargs": {
                "azure_deployment": deployment_id,
                "api_version": api_version,
                "azure_endpoint": base_url,
                "api_key": embedding_api_key,
            },
        },
    }


# Test function to verify the embedding utility works
async def test_embedding_util():
    """Test the embedding utility with sample text."""
    test_text = "This is a test sentence for embedding generation."

    logger.info("Testing embedding utility...")
    embedding = await generate_embedding(test_text)

    if embedding:
        logger.info(f"✅ Successfully generated embedding of length {len(embedding)}")
        return True
    else:
        logger.error("❌ Failed to generate embedding")
        return False


if __name__ == "__main__":
    # Configure logging for standalone testing
    logging.basicConfig(level=logging.INFO)

    # Test the utility
    asyncio.run(test_embedding_util())

    # Show the mem0 config
    config = get_embedding_config_for_mem0()
    print("Mem0 Embedding Config:")
    print(json.dumps(config, indent=2))
