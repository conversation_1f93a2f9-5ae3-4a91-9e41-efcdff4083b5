"""
Cardiovascular Training Tools

This module provides comprehensive tools for cardiovascular fitness assessment,
training zones calculation, and endurance coaching support.
"""

import json

# LangChain tool integrations
from langchain_core.tools import tool

from ..base_tool import BaseDomainTool
from .cardio_assessment import (
    CardioAssessmentInput,
    CardioAssessmentOutput,
    CardioAssessmentTool,
    CardioExperienceInput,
    CardioGoalsInput,
    CardiovascularMetricsInput,
    EquipmentEnvironmentInput,
    PerformanceTestingInput,
)
from .training_zones import (
    CyclingPowerZonesInput,
    HeartRateZonesInput,
    RunningPaceZonesInput,
    SwimmingPaceZonesInput,
    TrainingZonesCalculator,
    TrainingZonesInput,
    TrainingZonesOutput,
)


@tool("comprehensive_cardio_assessment")
async def comprehensive_cardio_assessment(assessment_data: str) -> str:
    """
    Perform a comprehensive cardiovascular fitness assessment including VO2 max estimation,
    performance analysis, training readiness evaluation, and personalized recommendations.

    Args:
        assessment_data: JSON string containing complete cardio assessment input data

    Returns:
        JSON string with detailed assessment results and recommendations
    """
    try:
        # Parse the input data
        data = json.loads(assessment_data)

        # Create the assessment input object
        assessment_input = CardioAssessmentInput(**data)

        # Initialize the assessment tool
        assessment_tool = CardioAssessmentTool()

        # Perform the assessment
        results = await assessment_tool.assess_cardio(assessment_input)

        # Return results as JSON string
        return json.dumps(results.model_dump(), indent=2, default=str)

    except Exception as e:
        return json.dumps(
            {"error": f"Cardio assessment failed: {str(e)}", "status": "failed"},
            indent=2,
        )


@tool("calculate_training_zones")
async def calculate_training_zones(zones_data: str) -> str:
    """
    Calculate personalized training zones for heart rate, running pace, cycling power,
    and swimming pace with sport-specific recommendations and guidelines.

    Args:
        zones_data: JSON string containing training zones calculation input data

    Returns:
        JSON string with calculated zones and training recommendations
    """
    try:
        # Parse the input data
        data = json.loads(zones_data)

        # Create the zones input object
        zones_input = TrainingZonesInput(**data)

        # Initialize the zones calculator
        zones_calculator = TrainingZonesCalculator()

        # Calculate the zones
        results = await zones_calculator.calculate_training_zones(zones_input)

        # Return results as JSON string
        return json.dumps(results.model_dump(), indent=2, default=str)

    except Exception as e:
        return json.dumps(
            {
                "error": f"Training zones calculation failed: {str(e)}",
                "status": "failed",
            },
            indent=2,
        )


@tool("calculate_heart_rate_zones")
def calculate_heart_rate_zones(
    age: int,
    resting_heart_rate: int = None,
    max_heart_rate: int = None,
    method: str = "karvonen",
) -> str:
    """
    Quick heart rate zones calculation for immediate use.

    Args:
        age: User age
        resting_heart_rate: Resting HR in BPM (optional)
        max_heart_rate: Maximum HR in BPM (optional, calculated if not provided)
        method: Calculation method (karvonen, percentage_max, lactate_threshold)

    Returns:
        JSON string with heart rate zones
    """
    try:
        # Create input object
        hr_input = HeartRateZonesInput(
            age=age,
            resting_heart_rate=resting_heart_rate,
            max_heart_rate=max_heart_rate,
            method=method,
        )

        # Initialize calculator
        calculator = TrainingZonesCalculator()

        # Calculate zones
        zones = calculator.calculate_heart_rate_zones(hr_input)

        return json.dumps(zones.model_dump(), indent=2)

    except Exception as e:
        return json.dumps(
            {
                "error": f"Heart rate zones calculation failed: {str(e)}",
                "status": "failed",
            },
            indent=2,
        )


__all__ = [
    "CardioAssessmentTool",
    "CardioAssessmentInput",
    "CardioAssessmentOutput",
    "CardiovascularMetricsInput",
    "PerformanceTestingInput",
    "CardioExperienceInput",
    "CardioGoalsInput",
    "EquipmentEnvironmentInput",
    "TrainingZonesCalculator",
    "TrainingZonesInput",
    "TrainingZonesOutput",
    "HeartRateZonesInput",
    "RunningPaceZonesInput",
    "CyclingPowerZonesInput",
    "SwimmingPaceZonesInput",
    "comprehensive_cardio_assessment",
    "calculate_training_zones",
    "calculate_heart_rate_zones",
]
