#!/usr/bin/env python3
"""
Entry point for running the API as a module.

Usage: python -m athlea_langgraph.api
"""

import os
import logging
import warnings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Main entry point for the API module."""
    import uvicorn
    from .main import app

    # Comprehensive deprecation warning suppression
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", message=".*websockets.legacy.*")
    warnings.filterwarnings("ignore", message=".*WebSocketServerProtocol.*")

    # Suppress uvicorn's websockets warnings at the logging level
    websockets_logger = logging.getLogger("websockets.legacy")
    websockets_logger.setLevel(logging.ERROR)
    uvicorn_logger = logging.getLogger("uvicorn.protocols.websockets")
    uvicorn_logger.setLevel(logging.ERROR)

    # Load environment variables
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")

    logger.info(f"Starting Athlea Coaching API on {host}:{port}")

    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=True,
        log_level="info",
        ws="auto",  # Let uvicorn choose the best WebSocket implementation
    )


if __name__ == "__main__":
    main()
