#!/usr/bin/env python3
"""
Multi-Query Tool Testing - Comprehensive Tool Usage Scenarios

This script tests various queries that should trigger different tools across
all specialized coaches to demonstrate the complete system capabilities.
"""

import asyncio
import logging
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test scenarios for different coaches and tools
TEST_SCENARIOS = [
    {
        "coach": "strength_coach",
        "query": "I want to find all deadlift variations in your exercise database. Can you search for exercises with 'deadlift' in the name?",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search Airtable for deadlift exercises",
    },
    {
        "coach": "strength_coach",
        "query": "Show me what exercise databases you have access to. I want to see all available fitness databases.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "list_bases",
        "description": "Should list all Airtable bases",
    },
    {
        "coach": "strength_coach",
        "query": "I need a structured strength training workout for today. Can you generate a session for me?",
        "expected_tools": ["session_generation", "airtable_mcp"],
        "expected_operation": "session_generation",
        "description": "Should use session generation tool",
    },
    {
        "coach": "nutrition_coach",
        "query": "Find me high-protein meal recipes in your nutrition database. Search for meals with protein content over 30g.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search nutrition database for high-protein meals",
    },
    {
        "coach": "nutrition_coach",
        "query": "What nutrition databases do you have? Show me all the tables in your main nutrition base.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "list_tables",
        "description": "Should list tables in nutrition base",
    },
    {
        "coach": "nutrition_coach",
        "query": "Create a meal plan for muscle building. I need a structured nutrition session.",
        "expected_tools": ["session_generation", "airtable_mcp"],
        "expected_operation": "session_generation",
        "description": "Should generate nutrition session",
    },
    {
        "coach": "cardio_coach",
        "query": "I want to plan a running route. Can you help me find the elevation profile for a route from Central Park to Brooklyn Bridge?",
        "expected_tools": ["google_maps_elevation", "azure_maps"],
        "expected_operation": "elevation_lookup",
        "description": "Should use Google Maps elevation tool",
    },
    {
        "coach": "cardio_coach",
        "query": "Find me HIIT cardio workouts in your database. Search for high-intensity interval training programs.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search for HIIT programs in Airtable",
    },
    {
        "coach": "cycling_coach",
        "query": "I need route planning for a 50-mile bike ride. Can you analyze the terrain and elevation for a route from San Francisco to Marin County?",
        "expected_tools": ["google_maps_elevation", "azure_maps"],
        "expected_operation": "route_analysis",
        "description": "Should use maps and elevation tools",
    },
    {
        "coach": "cycling_coach",
        "query": "Show me cycling training programs in your database. I want to see power-based training plans.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search cycling programs",
    },
    {
        "coach": "recovery_coach",
        "query": "Find me recovery protocols in your database. Search for sleep optimization and recovery techniques.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search recovery protocols",
    },
    {
        "coach": "recovery_coach",
        "query": "Generate a recovery session for me. I need a structured rest day plan with mobility work.",
        "expected_tools": ["session_generation", "airtable_mcp"],
        "expected_operation": "session_generation",
        "description": "Should generate recovery session",
    },
    {
        "coach": "mental_coach",
        "query": "Search your database for mental training techniques. I want to find mindfulness and focus exercises.",
        "expected_tools": ["airtable_mcp"],
        "expected_operation": "search_records",
        "description": "Should search mental training database",
    },
    {
        "coach": "mental_coach",
        "query": "Create a mental training session for competition preparation. I need structured psychological preparation.",
        "expected_tools": ["session_generation", "airtable_mcp"],
        "expected_operation": "session_generation",
        "description": "Should generate mental training session",
    },
]


async def test_coach_with_query(
    coach_name: str, query: str, scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Test a specific coach with a query and analyze tool usage."""
    print(f"\n🎯 Testing {coach_name}")
    print(f"Query: {query}")
    print(f"Expected: {scenario['description']}")
    print("-" * 60)

    try:
        # Import the specific coach node
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches import (
            cardio_coach_node,
            cycling_coach_node,
            mental_coach_node,
            nutrition_coach_node,
            recovery_coach_node,
            strength_coach_node,
        )
        from athlea_langgraph.state import AgentState

        # Map coach names to functions
        coach_functions = {
            "strength_coach": strength_coach_node,
            "nutrition_coach": nutrition_coach_node,
            "cardio_coach": cardio_coach_node,
            "cycling_coach": cycling_coach_node,
            "recovery_coach": recovery_coach_node,
            "mental_coach": mental_coach_node,
        }

        coach_function = coach_functions.get(coach_name)
        if not coach_function:
            return {"error": f"Unknown coach: {coach_name}"}

        # Create test state
        test_state = AgentState(
            messages=[HumanMessage(content=query)],
            user_query=query,
            user_profile={
                "name": "Test User",
                "fitness_level": "intermediate",
                "goals": ["general fitness", "strength", "endurance"],
                "restrictions": {"injuries": [], "dietary": [], "time_constraints": []},
            },
        )

        # Execute the coach
        result = await coach_function(test_state)

        # Analyze the result
        analysis = {
            "coach": coach_name,
            "query": query,
            "success": True,
            "tool_calls": [],
            "response_content": "",
            "expected_tools": scenario["expected_tools"],
            "tools_used": [],
            "tools_matched": False,
        }

        if result.get("messages"):
            last_message = result["messages"][-1]
            analysis["response_content"] = (
                last_message.content[:200] + "..."
                if len(last_message.content) > 200
                else last_message.content
            )

            if hasattr(last_message, "tool_calls") and last_message.tool_calls:
                analysis["tool_calls"] = last_message.tool_calls
                analysis["tools_used"] = [tc["name"] for tc in last_message.tool_calls]

                # Check if expected tools were used
                expected_tools = set(scenario["expected_tools"])
                used_tools = set(analysis["tools_used"])
                analysis["tools_matched"] = bool(
                    expected_tools.intersection(used_tools)
                )

                print(f"✅ Tool calls made: {len(last_message.tool_calls)}")
                for i, tool_call in enumerate(last_message.tool_calls):
                    print(f"  {i+1}. {tool_call['name']}: {tool_call['args']}")

                if analysis["tools_matched"]:
                    print(f"🎯 Expected tools used: {analysis['tools_used']}")
                else:
                    print(f"⚠️ Expected {expected_tools}, got {used_tools}")
            else:
                print("⚠️ No tool calls made")

        print(f"📝 Response: {analysis['response_content']}")

        return analysis

    except Exception as e:
        print(f"❌ Error testing {coach_name}: {e}")
        return {
            "coach": coach_name,
            "query": query,
            "success": False,
            "error": str(e),
            "tool_calls": [],
            "tools_used": [],
            "tools_matched": False,
        }


async def test_all_scenarios():
    """Test all scenarios and provide comprehensive analysis."""
    print("🚀 Multi-Query Tool Testing")
    print("=" * 80)
    print("Testing various queries across all specialized coaches to verify tool usage")
    print("=" * 80)

    results = []

    for i, scenario in enumerate(TEST_SCENARIOS, 1):
        print(f"\n📋 Scenario {i}/{len(TEST_SCENARIOS)}")

        result = await test_coach_with_query(
            scenario["coach"], scenario["query"], scenario
        )

        results.append(result)

        # Small delay between tests
        await asyncio.sleep(1)

    # Generate summary report
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)

    successful_tests = [r for r in results if r.get("success", False)]
    tool_using_tests = [r for r in results if r.get("tool_calls")]
    matched_tool_tests = [r for r in results if r.get("tools_matched", False)]

    print(f"Total scenarios tested: {len(results)}")
    print(f"Successful executions: {len(successful_tests)}/{len(results)}")
    print(f"Tests that made tool calls: {len(tool_using_tests)}/{len(results)}")
    print(f"Tests that used expected tools: {len(matched_tool_tests)}/{len(results)}")

    # Tool usage breakdown
    print(f"\n🔧 Tool Usage Breakdown:")
    tool_usage = {}
    for result in results:
        for tool in result.get("tools_used", []):
            tool_usage[tool] = tool_usage.get(tool, 0) + 1

    for tool, count in sorted(tool_usage.items()):
        print(f"  {tool}: {count} times")

    # Coach performance
    print(f"\n👥 Coach Performance:")
    coach_stats = {}
    for result in results:
        coach = result.get("coach", "unknown")
        if coach not in coach_stats:
            coach_stats[coach] = {"total": 0, "tool_calls": 0, "matched": 0}

        coach_stats[coach]["total"] += 1
        if result.get("tool_calls"):
            coach_stats[coach]["tool_calls"] += 1
        if result.get("tools_matched"):
            coach_stats[coach]["matched"] += 1

    for coach, stats in sorted(coach_stats.items()):
        print(
            f"  {coach}: {stats['tool_calls']}/{stats['total']} made tool calls, {stats['matched']}/{stats['total']} matched expectations"
        )

    # Failed scenarios
    failed_results = [r for r in results if not r.get("success", False)]
    if failed_results:
        print(f"\n❌ Failed Scenarios:")
        for result in failed_results:
            print(f"  {result['coach']}: {result.get('error', 'Unknown error')}")

    # Scenarios without tool calls
    no_tool_results = [
        r for r in results if r.get("success", False) and not r.get("tool_calls")
    ]
    if no_tool_results:
        print(f"\n⚠️ Scenarios without tool calls:")
        for result in no_tool_results:
            print(f"  {result['coach']}: {result['query'][:50]}...")

    print(f"\n✅ Multi-query tool testing completed!")
    return results


if __name__ == "__main__":
    asyncio.run(test_all_scenarios())
