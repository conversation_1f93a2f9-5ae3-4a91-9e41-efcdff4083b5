"""
Base configuration classes for Athlea LangGraph.
Provides type-safe, validated configuration using Pydantic.
"""

import os
from typing import Optional, Dict, Any, List
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from enum import Enum


class Environment(str, Enum):
    """Supported environments."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class AzureOpenAIConfig(BaseSettings):
    """Azure OpenAI service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    api_key: str = Field(validation_alias="AZURE_OPENAI_API_KEY")
    endpoint: str = Field(validation_alias="AZURE_OPENAI_ENDPOINT")
    deployment_name: str = Field(
        default="gpt-4o-mini", validation_alias="AZURE_DEPLOYMENT_NAME"
    )
    api_version: str = Field(default="2024-12-01-preview")
    chat_deployment: str = Field(
        default="gpt-4.1-nano", validation_alias="AZURE_OPENAI_CHAT_DEPLOYMENT"
    )


class AzureMapsConfig(BaseSettings):
    """Azure Maps service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    subscription_key: str = Field(validation_alias="AZURE_MAPS_SUBSCRIPTION_KEY")
    api_version: str = Field(default="1.0")
    base_url: str = Field(default="https://atlas.microsoft.com/")


class AzureSearchConfig(BaseSettings):
    """Azure Search service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    service_name: str = Field(validation_alias="AZURE_SEARCH_SERVICE_NAME")
    api_key: str = Field(validation_alias="AZURE_SEARCH_API_KEY")
    index_name: str = Field(
        default="docs-chunks-index-v2", validation_alias="AZURE_SEARCH_INDEX_NAME"
    )

    @property
    def endpoint(self) -> str:
        return f"https://{self.service_name}.search.windows.net/"


class EmbeddingConfig(BaseSettings):
    """Embedding service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    api_key: str = Field(validation_alias="EMBEDDING_API_KEY")
    endpoint: str = Field(validation_alias="EMBEDDING_ENDPOINT")
    deployment_id: str = Field(
        default="text-embedding-3-large",
        validation_alias="AZURE_EMBEDDING_DEPLOYMENT_ID",
    )


class DatabaseConfig(BaseSettings):
    """Database configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    mongodb_uri: str = Field(validation_alias="MONGODB_URI")


class AirtableConfig(BaseSettings):
    """Airtable service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    api_key: str = Field(validation_alias="AIRTABLE_API_KEY")
    strength_base_id: str = Field(validation_alias="AIRTABLE_STRENGTH_BASE_ID")
    cycling_base_id: str = Field(validation_alias="AIRTABLE_CYCLING_BASE_ID")
    running_base_id: str = Field(validation_alias="AIRTABLE_RUNNING_BASE_ID")


class Mem0Config(BaseSettings):
    """Mem0 memory service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    api_key: Optional[str] = Field(default=None, validation_alias="MEM0_API_KEY")
    pinecone_api_key: Optional[str] = Field(
        default=None, validation_alias="PINECONE_API_KEY"
    )
    pinecone_environment: Optional[str] = Field(
        default="us-east-1", validation_alias="PINECONE_ENVIRONMENT"
    )


class N8nConfig(BaseSettings):
    """n8n workflow automation service configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    api_url: str = Field(
        default="http://localhost:5678/api/v1", validation_alias="N8N_API_URL"
    )
    api_key: Optional[str] = Field(default=None, validation_alias="N8N_API_KEY")
    webhook_base_url: str = Field(
        default="http://localhost:8000/api/webhooks/n8n",
        validation_alias="N8N_WEBHOOK_BASE_URL",
    )
    environment: str = Field(default="development", validation_alias="N8N_ENVIRONMENT")
    timeout_seconds: int = Field(default=30, validation_alias="N8N_TIMEOUT_SECONDS")
    max_retries: int = Field(default=3, validation_alias="N8N_MAX_RETRIES")

    @property
    def is_enabled(self) -> bool:
        """Check if n8n integration is enabled (API key is provided)."""
        return bool(self.api_key)

    @property
    def workflows_endpoint(self) -> str:
        """Get the workflows API endpoint."""
        return f"{self.api_url.rstrip('/')}/workflows"

    @property
    def executions_endpoint(self) -> str:
        """Get the executions API endpoint."""
        return f"{self.api_url.rstrip('/')}/executions"


class BaseConfig(BaseSettings):
    """Base configuration class that combines all service configurations."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    environment: Environment = Field(
        default=Environment.DEVELOPMENT, validation_alias="ENVIRONMENT"
    )
    debug: bool = Field(default=False, validation_alias="DEBUG")

    # Declare nested config objects as fields
    azure_openai: AzureOpenAIConfig = Field(default_factory=AzureOpenAIConfig)
    azure_maps: AzureMapsConfig = Field(default_factory=AzureMapsConfig)
    azure_search: AzureSearchConfig = Field(default_factory=AzureSearchConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    airtable: AirtableConfig = Field(default_factory=AirtableConfig)
    mem0: Mem0Config = Field(default_factory=Mem0Config)
    n8n: N8nConfig = Field(default_factory=N8nConfig)

    @field_validator("environment", mode="before")
    @classmethod
    def validate_environment(cls, v):
        if isinstance(v, str):
            return Environment(v.lower())
        return v

    @field_validator("debug", mode="before")
    @classmethod
    def validate_debug(cls, v):
        """Convert string values to boolean for debug field."""
        if isinstance(v, str):
            # Handle common string representations
            if v.lower() in ("true", "1", "yes", "on"):
                return True
            elif v.lower() in ("false", "0", "no", "off", "warn", "error", "info"):
                return False
            else:
                # Default to False for any other string value
                return False
        return bool(v) if v is not None else False

    # __init__ is generally not needed for Pydantic BaseSettings
    # if fields are type-hinted, Pydantic handles their initialization.
    # def __init__(self, **kwargs):
    #     super().__init__(**kwargs)
