# Automatic Prompt Synchronization with <PERSON><PERSON><PERSON>

This feature automatically synchronizes your local JSON prompts to Lang<PERSON><PERSON>'s Prompt Engineering interface when you run `langgraph dev`, ensuring your prompt updates are immediately available for testing and iteration.

## 🚀 Features

- **Automatic Sync on Startup**: Prompts are uploaded when `langgraph dev` starts
- **Background Watching**: Continuous sync during development (optional)
- **Manual Control**: CLI commands for manual sync and status checking
- **Variable Conversion**: Automatic conversion between LangGraph `{var}` and Lang<PERSON><PERSON> `{{var}}` formats
- **Version Tracking**: Maintains prompt versions and metadata in LangSmith

## 📋 Setup Requirements

### 1. LangSmith API Key

```bash
export LANGSMITH_API_KEY="your_langsmith_api_key"
```

Get your API key from [LangSmith Settings](https://smith.langchain.com/settings).

### 2. Project Configuration (Optional)

```bash
export LANGCHAIN_PROJECT="athlea-coaching"  # Default project name
```

## 🔄 How It Works

### Automatic Sync on `langgraph dev`

When you run `langgraph dev`, the system automatically:

1. **Scans Local Prompts**: Discovers all JSON files in `athlea_langgraph/prompts/`
2. **Converts Format**: Transforms `{variable}` to `{{variable}}` for LangSmith compatibility
3. **Creates/Updates**: Creates new prompts or updates existing ones in LangSmith
4. **Logs Results**: Shows upload status for each prompt

```bash
# Start development server with automatic prompt sync
langgraph dev

# You'll see output like:
# 🚀 Starting automatic prompt sync to LangSmith...
#    ✨ Created: strength_coach
#    📝 Updated: mental_coach
#    ✨ Created: nutrition_coach
# ✅ Prompt sync complete: 25 uploaded, 0 failed
```

### Background Sync (Development Mode)

In development mode, prompts are checked for changes every 5 minutes:

```bash
export LANGGRAPH_DEV=true
# or
export ENVIRONMENT=development

langgraph dev
# 🔄 Prompt watcher started for development mode
# 👀 Watching for prompt changes every 300s...
```

## 🛠️ Manual Control

### Check Sync Status

```bash
python -m athlea_langgraph.cli langsmith status
```

Output:
```
📝 LangSmith Prompt Sync Status
========================================
API Key: ✅ Configured
Project: athlea-coaching
Sync Enabled: ✅ Yes
LangSmith URL: https://smith.langchain.com/projects/p/athlea-coaching/prompts
Local Prompts: 25 found
  - Coach prompts: 6
  - System prompts: 8
  - Other prompts: 11
```

### Manual Sync

```bash
# Sync all prompts
python -m athlea_langgraph.cli langsmith sync

# Force sync (ignore change detection)
python -m athlea_langgraph.cli langsmith sync --force
```

### Export Single Prompt

```bash
# Export to stdout (copy-paste to LangSmith)
python -m athlea_langgraph.cli langsmith export mental_coach

# Export to file
python -m athlea_langgraph.cli langsmith export mental_coach -o mental_coach.txt
```

### Import Optimized Prompt

```bash
# After optimizing in LangSmith, import back
python -m athlea_langgraph.cli langsmith import-optimized mental_coach optimized_mental_coach.txt
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `LANGSMITH_API_KEY` | - | **Required**: Your LangSmith API key |
| `LANGCHAIN_PROJECT` | `athlea-coaching` | LangSmith project name |
| `LANGGRAPH_DEV` | `false` | Enable development mode with background sync |
| `ENVIRONMENT` | - | Set to `development` for dev features |

### Disabling Auto-Sync

If you want to disable automatic sync:

```bash
# Remove or unset the API key
unset LANGSMITH_API_KEY

# Or set it to empty
export LANGSMITH_API_KEY=""
```

## 📊 Supported Prompt Formats

### JSON to LangSmith Conversion

**Your JSON Format:**
```json
{
  "prompt": {
    "system": "You are a {coach_type} coach. Help with {user_query}.",
    "user_template": "User question: {user_query}"
  }
}
```

**LangSmith Format (Auto-converted):**
```json
{
  "_type": "chat",
  "messages": [
    {"role": "system", "content": "You are a {{coach_type}} coach. Help with {{user_query}}."},
    {"role": "user", "content": "User question: {{user_query}}"}
  ]
}
```

### Simple Prompts

**Your JSON:**
```json
{
  "prompt": {
    "system": "You are a helpful coach. Answer: {question}"
  }
}
```

**LangSmith:**
```json
{
  "_type": "prompt",
  "template": "You are a helpful coach. Answer: {{question}}"
}
```

## 🔍 Workflow Example

### 1. Local Development
```bash
# Edit your prompt
vim athlea_langgraph/prompts/coaches/mental_coach.json

# Start dev server (auto-sync happens)
langgraph dev
# ✨ Created: mental_coach
```

### 2. LangSmith Testing
1. Open [LangSmith Prompts](https://smith.langchain.com/prompts)
2. Find your `mental_coach` prompt
3. Test and optimize in the playground
4. Save optimized version to file

### 3. Import Back to Code
```bash
# Import the optimized version
python -m athlea_langgraph.cli langsmith import-optimized mental_coach optimized.txt
# ✅ Imported optimized mental_coach v1.0.1
```

### 4. Continuous Development
```bash
# Check status anytime
python -m athlea_langgraph.cli langsmith status

# Manual sync when needed
python -m athlea_langgraph.cli langsmith sync
```

## 🐛 Troubleshooting

### Common Issues

**Sync Disabled**
```
📝 LangSmith prompt sync disabled - no API key found
```
**Solution**: Set `LANGSMITH_API_KEY` environment variable.

**Upload Failures**
```
❌ Update failed for mental_coach: 404 Not Found
```
**Solution**: Check prompt name format and LangSmith permissions.

**Variable Conversion Issues**
```
⚠️ Conversion may have issues - check manually
```
**Solution**: Use the validation command:
```bash
python -m athlea_langgraph.cli langsmith validate-conversion mental_coach
```

### Debug Mode

For detailed logging, enable debug mode:

```bash
export LANGCHAIN_VERBOSE=true
export LANGCHAIN_TRACING_V2=true
langgraph dev
```

## 🎯 Benefits

✅ **Immediate Availability**: Prompts are instantly available in LangSmith  
✅ **Seamless Workflow**: No manual copy-paste between systems  
✅ **Version Control**: Automatic version tracking and changelog  
✅ **Format Conversion**: Automatic variable format translation  
✅ **Development Speed**: Focus on prompt content, not sync logistics  
✅ **Team Collaboration**: Shared prompts across team members  

## 📚 Related Documentation

- [LangSmith Prompt Engineering Guide](https://docs.smith.langchain.com/prompt_engineering/how_to_guides)
- [LangGraph CLI Reference](../cli/README.md)
- [Prompt Management System](../utils/prompt_loader.md)

## 🤝 Contributing

Found an issue or want to improve the sync functionality? Please check our [contributing guidelines](../CONTRIBUTING.md) and submit a pull request! 