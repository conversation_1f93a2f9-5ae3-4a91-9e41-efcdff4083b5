"""
Streaming Helpers

Utilities for enhanced streaming experience in the coaching system.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, AsyncIterator, Callable, Dict, Optional

from langchain_core.messages import AIMessage, BaseMessage
from langgraph.graph.message import add_messages

logger = logging.getLogger(__name__)


class StreamEventType(Enum):
    """Types of streaming events."""

    NODE_START = "node_start"
    NODE_END = "node_end"
    CONTENT_CHUNK = "content_chunk"
    THINKING = "thinking"
    ERROR = "error"
    INTERRUPT = "interrupt"
    METADATA = "metadata"


@dataclass
class StreamEvent:
    """Represents a streaming event."""

    type: StreamEventType
    node_name: str
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class StreamingCoordinator:
    """Coordinates streaming output for better user experience."""

    def __init__(
        self,
        enable_thinking_indicators: bool = True,
        chunk_delay_ms: int = 0,
        node_transition_delay_ms: int = 500,
    ):
        """
        Initialize the streaming coordinator.

        Args:
            enable_thinking_indicators: Show thinking indicators for long operations
            chunk_delay_ms: Delay between content chunks for readability
            node_transition_delay_ms: Delay between node transitions
        """
        self.enable_thinking_indicators = enable_thinking_indicators
        self.chunk_delay_ms = chunk_delay_ms
        self.node_transition_delay_ms = node_transition_delay_ms
        self.active_nodes = set()
        self.node_start_times = {}

    async def emit_node_start(
        self, node_name: str, display_name: Optional[str] = None
    ) -> StreamEvent:
        """Emit a node start event."""
        self.active_nodes.add(node_name)
        self.node_start_times[node_name] = time.time()

        event = StreamEvent(
            type=StreamEventType.NODE_START,
            node_name=node_name,
            metadata={"display_name": display_name or node_name},
        )

        logger.info(f"Node started: {node_name}")
        return event

    async def emit_node_end(self, node_name: str) -> StreamEvent:
        """Emit a node end event."""
        if node_name in self.active_nodes:
            self.active_nodes.remove(node_name)

        duration = None
        if node_name in self.node_start_times:
            duration = time.time() - self.node_start_times[node_name]
            del self.node_start_times[node_name]

        event = StreamEvent(
            type=StreamEventType.NODE_END,
            node_name=node_name,
            metadata={"duration": duration},
        )

        logger.info(f"Node ended: {node_name} (duration: {duration:.2f}s)")

        # Add transition delay
        if self.node_transition_delay_ms > 0:
            await asyncio.sleep(self.node_transition_delay_ms / 1000.0)

        return event

    async def stream_content(
        self,
        content_iterator: AsyncIterator[str],
        node_name: str,
        show_thinking: bool = True,
    ) -> AsyncIterator[StreamEvent]:
        """
        Stream content with optional thinking indicators.

        Args:
            content_iterator: Async iterator of content chunks
            node_name: Name of the node producing content
            show_thinking: Whether to show thinking indicator

        Yields:
            StreamEvent objects
        """
        if show_thinking and self.enable_thinking_indicators:
            yield StreamEvent(
                type=StreamEventType.THINKING,
                node_name=node_name,
                content="🤔 Thinking...",
            )

        chunk_count = 0
        total_content = ""

        async for chunk in content_iterator:
            if chunk:
                chunk_count += 1
                total_content += chunk

                yield StreamEvent(
                    type=StreamEventType.CONTENT_CHUNK,
                    node_name=node_name,
                    content=chunk,
                    metadata={"chunk_number": chunk_count},
                )

                # Add delay between chunks if configured
                if self.chunk_delay_ms > 0:
                    await asyncio.sleep(self.chunk_delay_ms / 1000.0)

        # Log completion
        logger.info(
            f"Streamed {chunk_count} chunks, {len(total_content)} chars from {node_name}"
        )

    async def emit_error(self, node_name: str, error: Exception) -> StreamEvent:
        """Emit an error event."""
        return StreamEvent(
            type=StreamEventType.ERROR,
            node_name=node_name,
            content=str(error),
            metadata={"error_type": type(error).__name__},
        )

    async def emit_interrupt(
        self, node_name: str, reason: str, context: Dict[str, Any]
    ) -> StreamEvent:
        """Emit an interrupt event."""
        return StreamEvent(
            type=StreamEventType.INTERRUPT,
            node_name=node_name,
            content=reason,
            metadata=context,
        )


class ProgressTracker:
    """Tracks progress through the coaching workflow."""

    def __init__(self):
        self.steps_completed = []
        self.current_step = None
        self.total_steps = None
        self.start_time = time.time()

    def start_step(self, step_name: str):
        """Mark a step as started."""
        self.current_step = {
            "name": step_name,
            "start_time": time.time(),
            "status": "in_progress",
        }

    def complete_step(self, step_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Mark a step as completed."""
        if self.current_step and self.current_step["name"] == step_name:
            self.current_step["end_time"] = time.time()
            self.current_step["duration"] = (
                self.current_step["end_time"] - self.current_step["start_time"]
            )
            self.current_step["status"] = "completed"
            if metadata:
                self.current_step["metadata"] = metadata
            self.steps_completed.append(self.current_step)
            self.current_step = None

    def get_progress(self) -> Dict[str, Any]:
        """Get current progress information."""
        total_duration = time.time() - self.start_time

        return {
            "steps_completed": len(self.steps_completed),
            "current_step": self.current_step["name"] if self.current_step else None,
            "total_duration": total_duration,
            "steps": self.steps_completed,
            "completion_percentage": (
                (len(self.steps_completed) / self.total_steps * 100)
                if self.total_steps
                else None
            ),
        }


async def format_specialist_response(
    specialist_name: str, response_content: str, is_streaming: bool = False
) -> str:
    """
    Format specialist response for display.

    Args:
        specialist_name: Name of the specialist
        response_content: The response content
        is_streaming: Whether this is part of a stream

    Returns:
        Formatted response string
    """
    if is_streaming:
        # For streaming, just return the content
        return response_content
    else:
        # For complete responses, add formatting
        emoji_map = {
            "Strength Coach": "💪",
            "Running Coach": "🏃",
            "Cardio Coach": "❤️",
            "Cycling Coach": "🚴",
            "Nutrition Coach": "🥗",
            "Recovery Coach": "🧘",
            "Mental Coach": "🧠",
            "Head Coach": "👨‍🏫",
        }

        emoji = emoji_map.get(specialist_name, "👤")

        return f"\n{emoji} **{specialist_name}**\n{response_content}\n"


class StreamBuffer:
    """Buffers streaming content for smoother display."""

    def __init__(self, buffer_size: int = 10, flush_interval_ms: int = 100):
        """
        Initialize the stream buffer.

        Args:
            buffer_size: Number of chunks to buffer
            flush_interval_ms: Time interval to flush buffer
        """
        self.buffer_size = buffer_size
        self.flush_interval_ms = flush_interval_ms
        self.buffer = []
        self.last_flush = time.time()

    async def add_chunk(self, chunk: str) -> Optional[str]:
        """
        Add a chunk to the buffer.

        Returns:
            Flushed content if buffer is full or interval reached
        """
        self.buffer.append(chunk)

        current_time = time.time()
        time_since_flush = (current_time - self.last_flush) * 1000

        if (
            len(self.buffer) >= self.buffer_size
            or time_since_flush >= self.flush_interval_ms
        ):
            return await self.flush()

        return None

    async def flush(self) -> str:
        """Flush the buffer and return combined content."""
        if not self.buffer:
            return ""

        content = "".join(self.buffer)
        self.buffer = []
        self.last_flush = time.time()
        return content

    async def force_flush(self) -> str:
        """Force flush any remaining content."""
        return await self.flush()
