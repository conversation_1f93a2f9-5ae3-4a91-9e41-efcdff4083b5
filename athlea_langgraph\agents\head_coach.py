"""
Head Coach Node

The head coach is responsible for routing to appropriate specialist coaches.
"""

import logging
from typing import Any, Dict, List, Literal, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState

logger = logging.getLogger(__name__)


async def head_coach_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Head coach node - ONLY handles routing decisions.
    Does NOT generate responses or aggregate specialist outputs.
    """
    logger.info("--- Head Coach Node ---")

    # Get the current plan and step
    plan = state.get("plan", [])
    current_step = state.get("current_step", 0)

    # Log current state
    logger.info(f"User query: {state.get('user_query', 'No query')}")
    logger.info(f"Current plan: {plan}")
    logger.info(f"Current step: {current_step}")

    # Check if we have a plan to execute
    if plan and current_step < len(plan):
        next_node = plan[current_step]
        logger.info(f"Continuing with plan step {current_step}: {next_node}")
        # Increment the step counter for next iteration
        return {"current_step": current_step + 1}
    else:
        # No plan or plan completed
        logger.info("No plan to execute or plan completed")
        return {}


def head_coach_router(
    state: AgentState,
) -> Literal[
    "strength_coach",
    "running_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
    "clarification",
    "aggregate_responses",
]:
    """
    Router function for head coach - determines next node based on plan.
    """
    logger.info("--- Head Coach Router ---")

    plan = state.get("plan", [])
    current_step = state.get("current_step", 0)

    # Log current state for debugging
    logger.info(f"Plan: {plan}, Current step: {current_step}")

    # Check if we need to aggregate responses
    specialist_responses = []
    if state.get("strength_response"):
        specialist_responses.append("strength")
    if state.get("running_response"):
        specialist_responses.append("running")
    if state.get("cardio_response"):
        specialist_responses.append("cardio")
    if state.get("cycling_response"):
        specialist_responses.append("cycling")
    if state.get("nutrition_response"):
        specialist_responses.append("nutrition")
    if state.get("recovery_response"):
        specialist_responses.append("recovery")
    if state.get("mental_response"):
        specialist_responses.append("mental")

    # If we have specialist responses, go to aggregation
    if specialist_responses:
        logger.info(
            f"Have specialist responses from: {specialist_responses}, routing to aggregate_responses"
        )
        return "aggregate_responses"

    # If we have a valid plan and are within bounds, execute the current step
    if plan and current_step < len(plan):
        next_node = plan[current_step]
        logger.info(f"Executing plan step {current_step}: {next_node}")

        # Map plan names to actual node names
        node_mapping = {
            "strength_coach": "strength_coach",
            "running_coach": "cardio_coach",
            "cardio_coach": "cardio_coach",
            "cycling_coach": "cycling_coach",
            "nutrition_coach": "nutrition_coach",
            "recovery_coach": "recovery_coach",
            "mental_coach": "mental_coach",
            "clarification": "clarification",
        }

        mapped_node = node_mapping.get(next_node, next_node)
        logger.info(f"Routing to mapped node: {mapped_node}")
        return mapped_node

    # Check if this is a clarification request
    if plan and len(plan) == 1 and plan[0] == "clarification":
        logger.info("Plan is clarification, routing to clarification node")
        return "clarification"

    # If plan is completed, check for responses to aggregate
    if plan and current_step >= len(plan):
        logger.info("Plan completed, checking for specialist responses...")
        if specialist_responses:
            return "aggregate_responses"
        else:
            # No responses yet, something went wrong, default to clarification
            logger.info("Plan completed but no specialist responses found")
            return "clarification"

    # Default: no plan means we need clarification
    logger.info("No valid plan found, defaulting to clarification")
    return "clarification"


async def clarification_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Clarification node - asks for more information when the request is unclear.
    """
    logger.info("--- Clarification Node ---")

    # Create clarification prompt
    clarification_prompt = f"""
    The user's request needs clarification. Based on the reasoning analysis:
    
    User Query: {state.get('user_query', '')}
    Reasoning: {state.get('reasoning_output', 'Request is unclear')}
    
    Ask a clarifying question to better understand what the user needs.
    Be specific and helpful.
    """

    messages = [
        SystemMessage(
            content="You are a helpful fitness coach asking for clarification."
        ),
        HumanMessage(content=clarification_prompt),
    ]

    # Get LLM response
    llm = create_azure_chat_openai(temperature=0.7, streaming=False)
    response = await llm.ainvoke(messages)

    # Add the clarification message
    return {
        "messages": [AIMessage(content=response.content, name="head_coach")],
        "clarification_output": response.content,
    }
