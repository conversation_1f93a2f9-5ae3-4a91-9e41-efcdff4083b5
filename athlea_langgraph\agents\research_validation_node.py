"""
Research Validation Node

Graph node that intercepts coach responses and automatically triggers knowledge graph
queries when research claims are detected, preventing hallucination at the graph level.
"""

import logging
from typing import Dict, Any, Union, Optional, List
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from athlea_langgraph.states import AgentState  
from athlea_langgraph.services.research_validation_service import (
    get_research_validation_service,
    ValidationResult
)
from athlea_langgraph.services.graphrag_service import get_graphrag_service
from athlea_langgraph.tools.external.graphrag_tool import create_graphrag_tool

logger = logging.getLogger(__name__)

async def research_validation_node(
    state: Union[Dict[str, Any], AgentState]
) -> Dict[str, Any]:
    """
    Graph node that validates coach responses for research claims and
    automatically triggers knowledge graph queries to prevent hallucination.
    
    This node sits between coach generation and final output, acting as a
    validation layer that ensures all research claims are verified.
    """
    
    logger.info("🔬 RESEARCH_VALIDATION: Starting validation check")
    
    # Extract the latest AI message (coach response)
    messages = state.get("messages", [])
    if not messages:
        logger.warning("🔬 RESEARCH_VALIDATION: No messages found")
        return state
        
    latest_message = messages[-1]
    if not isinstance(latest_message, AIMessage):
        logger.info("🔬 RESEARCH_VALIDATION: Latest message not from AI, skipping")
        return state
    
    coach_response = latest_message.content
    user_query = ""
    
    # Find the most recent user query
    for msg in reversed(messages[:-1]):
        if isinstance(msg, HumanMessage):
            user_query = msg.content
            break
    
    if not user_query:
        logger.warning("🔬 RESEARCH_VALIDATION: No user query found")
        return state
    
    # Validate the coach response for research claims
    validation_service = get_research_validation_service()
    validation_result = validation_service.validate_response(coach_response, user_query)
    
    logger.info(f"🔬 RESEARCH_VALIDATION: Found {len(validation_result.claims)} research claims, risk level: {validation_result.risk_level}")
    
    # If no research claims, pass through unchanged
    if not validation_result.has_research_claims:
        logger.info("🔬 RESEARCH_VALIDATION: No research claims detected, passing through")
        return {
            **state,
            "research_validation": {
                "claims_detected": 0,
                "knowledge_graph_triggered": False,
                "risk_level": "low"
            }
        }
    
    # If high-risk claims (likely hallucination), block and require knowledge graph
    if validation_result.risk_level == "high":
        logger.warning(f"🔬 RESEARCH_VALIDATION: HIGH RISK claims detected, triggering mandatory knowledge graph query")
        corrected_response = await _generate_knowledge_graph_enhanced_response(
            user_query, coach_response, validation_result
        )
        
        # Replace the coach's response with the corrected version
        new_messages = messages[:-1] + [AIMessage(content=corrected_response)]
        
        return {
            **state,
            "messages": new_messages,
            "research_validation": {
                "claims_detected": len(validation_result.claims),
                "knowledge_graph_triggered": True,
                "risk_level": validation_result.risk_level,
                "action_taken": "response_corrected"
            }
        }
    
    # For medium-risk claims, enhance with knowledge graph if available
    if validation_result.risk_level == "medium":
        logger.info("🔬 RESEARCH_VALIDATION: Medium risk claims, attempting knowledge graph enhancement")
        try:
            enhanced_response = await _enhance_with_knowledge_graph(
                user_query, coach_response, validation_result
            )
            
            if enhanced_response != coach_response:
                new_messages = messages[:-1] + [AIMessage(content=enhanced_response)]
                return {
                    **state,
                    "messages": new_messages,
                    "research_validation": {
                        "claims_detected": len(validation_result.claims),
                        "knowledge_graph_triggered": True,
                        "risk_level": validation_result.risk_level,
                        "action_taken": "response_enhanced"
                    }
                }
        except Exception as e:
            logger.warning(f"🔬 RESEARCH_VALIDATION: Knowledge graph enhancement failed: {e}")
    
    # Low risk or enhancement failed - add warning but allow through
    return {
        **state,
        "research_validation": {
            "claims_detected": len(validation_result.claims),
            "knowledge_graph_triggered": False,
            "risk_level": validation_result.risk_level,
            "action_taken": "warning_logged"
        }
    }

async def _generate_knowledge_graph_enhanced_response(
    user_query: str, 
    original_response: str, 
    validation_result: ValidationResult
) -> str:
    """
    Generate a new response using knowledge graph data instead of hallucinated content.
    """
    logger.info("🔬 RESEARCH_VALIDATION: Generating knowledge graph enhanced response")
    
    try:
        # Query knowledge graph with suggested query
        graphrag_tool = create_graphrag_tool()
        knowledge_result = await graphrag_tool._arun(
            query=validation_result.suggested_query or user_query,
            include_causalities=True
        )
        
        if knowledge_result and hasattr(knowledge_result, 'acs_chunks') and knowledge_result.acs_chunks:
            # Use verified research data to reconstruct response
            verified_response = f"""I searched our research database to provide you with evidence-based information.

{knowledge_result.response if hasattr(knowledge_result, 'response') else 'Based on the research data I found, here are the key insights:'}

**Sources:** {len(knowledge_result.acs_chunks)} verified research documents were consulted for this response.

Note: I replaced my initial response to ensure all information is backed by verified research rather than general claims."""
            
            return verified_response
        else:
            # No knowledge found - provide safe fallback
            return f"""I wanted to provide you with research-backed information, but after searching our knowledge database, I couldn't find specific studies on this topic.

Instead, let me provide practical guidance based on established training principles:

{_extract_practical_advice_from_response(original_response)}

For the most current research on this topic, I'd recommend consulting recent peer-reviewed publications or speaking with a qualified professional."""
            
    except Exception as e:
        logger.error(f"🔬 RESEARCH_VALIDATION: Knowledge graph query failed: {e}")
        return _create_safe_fallback_response(user_query, original_response)

async def _enhance_with_knowledge_graph(
    user_query: str,
    original_response: str, 
    validation_result: ValidationResult
) -> str:
    """
    Enhance the original response with knowledge graph data where possible.
    """
    try:
        graphrag_tool = create_graphrag_tool()
        knowledge_result = await graphrag_tool._arun(
            query=validation_result.suggested_query or user_query,
            include_causalities=True
        )
        
        if knowledge_result and hasattr(knowledge_result, 'response'):
            # Add verified research as supporting evidence
            enhanced_response = f"""{original_response}

**Additional Research Context:**
{knowledge_result.response}

*This response has been enhanced with verified research data from our knowledge database.*"""
            
            return enhanced_response
        
    except Exception as e:
        logger.warning(f"🔬 RESEARCH_VALIDATION: Knowledge graph enhancement failed: {e}")
    
    return original_response

def _extract_practical_advice_from_response(response: str) -> str:
    """Extract practical advice from response, removing research claims."""
    
    # Remove common research claim patterns
    import re
    
    # Patterns to remove
    research_patterns = [
        r"Studies show[^.]*\.",
        r"Research indicates[^.]*\.",
        r"According to research[^.]*\.",
        r"\([^)]*et al\.?[^)]*\)",  # Citation patterns
        r"A study by [^.]*\.",
        r"Recent findings[^.]*\."
    ]
    
    cleaned_response = response
    for pattern in research_patterns:
        cleaned_response = re.sub(pattern, "", cleaned_response, flags=re.IGNORECASE)
    
    # Clean up extra whitespace
    cleaned_response = re.sub(r'\s+', ' ', cleaned_response).strip()
    
    return cleaned_response

def _create_safe_fallback_response(user_query: str, original_response: str) -> str:
    """Create a safe fallback response when knowledge graph is unavailable."""
    
    practical_advice = _extract_practical_advice_from_response(original_response)
    
    return f"""I want to ensure I provide you with accurate information. While I can offer general guidance based on established training principles, I cannot cite specific research studies without first verifying them in our knowledge database.

{practical_advice}

For evidence-based research on this topic, I recommend:
- Consulting peer-reviewed publications
- Speaking with qualified professionals in the field
- Checking reputable sources like PubMed or sports science journals

Is there a specific practical aspect of your question I can help with using established training fundamentals?"""

# Helper function to add research validation to any graph
def add_research_validation_to_graph(graph_builder, after_node: str = "aggregation"):
    """
    Add research validation node to an existing graph.
    
    Args:
        graph_builder: LangGraph StateGraphBuilder
        after_node: Node to insert validation after (default: "aggregation")
    """
    
    # Add the validation node
    graph_builder.add_node("research_validation", research_validation_node)
    
    # Redirect the original output to validation first
    graph_builder.add_edge(after_node, "research_validation")
    
    # The validation node now goes to END
    graph_builder.add_edge("research_validation", "__end__")
    
    logger.info(f"🔬 Added research validation after '{after_node}' node") 