"""
Nutrition Domain MCP Server

Exposes nutrition and dietary tools via Model Context Protocol (MCP).
Includes calorie calculation, meal planning, and nutritional analysis tools.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolResult,
    GetPromptResult,
    InitializeResult,
    Prompt,
    PromptArgument,
    ServerCapabilities,
)
from mcp.types import TextContent
from mcp.types import TextContent as TextContentType
from mcp.types import Tool

from athlea_langgraph.tools.nutrition import (
    NutritionAssessmentTool,
    NutritionCalorieCalculator,
    NutritionMealPlanTool,
    NutritionRecipeRecommendationTool,
)
from athlea_langgraph.tools.nutrition.meal_plan_tool import MealPlanInput
from athlea_langgraph.tools.nutrition.recipe_recommendation_tool import (
    NutritionRecommendationInput,
    RecipeSearchInput,
)

logger = logging.getLogger(__name__)

# Initialize domain tools
calorie_calc = NutritionCalorieCalculator()
meal_plan_tool = NutritionMealPlanTool()
recipe_tool = NutritionRecipeRecommendationTool()
nutrition_assessment = NutritionAssessmentTool()

app = Server("nutrition-mcp-server")

NUTRITION_TOOLS = [
    Tool(
        name="calculate_daily_calories",
        description="Calculate daily caloric needs based on user profile, activity level, and goals using multiple formulas",
        inputSchema={
            "type": "object",
            "properties": {
                "age": {"type": "integer", "minimum": 13, "maximum": 100},
                "gender": {"type": "string", "enum": ["male", "female", "other"]},
                "weight_kg": {"type": "number", "minimum": 30, "maximum": 300},
                "height_cm": {"type": "number", "minimum": 100, "maximum": 250},
                "activity_level": {
                    "type": "string",
                    "enum": [
                        "sedentary",
                        "light",
                        "moderate",
                        "very_active",
                        "extremely_active",
                    ],
                    "description": "Physical activity level",
                },
                "goal": {
                    "type": "string",
                    "enum": [
                        "weight_loss",
                        "weight_gain",
                        "maintenance",
                        "muscle_gain",
                        "fat_loss",
                    ],
                    "description": "Primary nutrition goal",
                },
                "body_fat_percentage": {
                    "type": "number",
                    "minimum": 5,
                    "maximum": 50,
                    "description": "Optional: for more accurate calculations",
                },
            },
            "required": [
                "age",
                "gender",
                "weight_kg",
                "height_cm",
                "activity_level",
                "goal",
            ],
        },
    ),
    Tool(
        name="generate_comprehensive_meal_plan",
        description="Generate comprehensive meal plans with detailed nutritional information, macro tracking, and shopping lists",
        inputSchema={
            "type": "object",
            "properties": {
                "daily_calories": {"type": "number", "minimum": 800, "maximum": 6000},
                "days": {"type": "integer", "minimum": 1, "maximum": 14, "default": 7},
                "macros": {
                    "type": "object",
                    "properties": {
                        "protein_percent": {
                            "type": "number",
                            "minimum": 10,
                            "maximum": 50,
                        },
                        "carb_percent": {
                            "type": "number",
                            "minimum": 20,
                            "maximum": 70,
                        },
                        "fat_percent": {"type": "number", "minimum": 15, "maximum": 50},
                    },
                    "description": "Macronutrient distribution as percentages",
                },
                "dietary_restrictions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Dietary restrictions (vegetarian, vegan, gluten-free, etc.)",
                },
                "allergies": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Food allergies and intolerances",
                },
                "meals_per_day": {
                    "type": "integer",
                    "minimum": 3,
                    "maximum": 6,
                    "default": 3,
                },
                "include_snacks": {
                    "type": "boolean",
                    "default": False,
                    "description": "Include healthy snacks in the meal plan",
                },
                "budget_consideration": {
                    "type": "string",
                    "enum": ["low", "medium", "high"],
                    "default": "medium",
                    "description": "Budget consideration level",
                },
                "cooking_time_preference": {
                    "type": "string",
                    "enum": ["quick", "moderate", "elaborate"],
                    "default": "moderate",
                    "description": "Preferred cooking time complexity",
                },
            },
            "required": ["daily_calories"],
        },
    ),
    Tool(
        name="search_recipes",
        description="Search for recipes using Edamam API with advanced filtering by nutrition, diet, and health labels",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for recipes (e.g., 'chicken breast', 'high protein breakfast')",
                },
                "diet_type": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Diet labels (balanced, high-protein, low-carb, low-fat, etc.)",
                },
                "health_labels": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Health labels (vegetarian, vegan, gluten-free, dairy-free, etc.)",
                },
                "cuisine_type": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Cuisine types (American, Asian, Mediterranean, etc.)",
                },
                "meal_type": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Meal types (breakfast, lunch, dinner, snack)",
                },
                "max_calories": {
                    "type": "integer",
                    "description": "Maximum calories per serving",
                },
                "min_protein": {
                    "type": "integer",
                    "description": "Minimum protein grams per serving",
                },
                "max_results": {
                    "type": "integer",
                    "default": 10,
                    "maximum": 50,
                    "description": "Maximum number of results to return",
                },
            },
            "required": ["query"],
        },
    ),
    Tool(
        name="get_personalized_recipe_recommendations",
        description="Get personalized recipe recommendations based on specific nutritional goals and preferences",
        inputSchema={
            "type": "object",
            "properties": {
                "calorie_target": {
                    "type": "integer",
                    "description": "Target calories per meal",
                },
                "diet_preferences": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Diet preferences (high-protein, low-carb, balanced, etc.)",
                },
                "health_restrictions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Health restrictions/allergies (gluten-free, dairy-free, etc.)",
                },
                "meal_type": {
                    "type": "string",
                    "enum": [
                        "breakfast",
                        "brunch",
                        "lunch",
                        "dinner",
                        "snack",
                        "teatime",
                    ],
                    "description": "Type of meal needed",
                },
                "cuisine_preference": {
                    "type": "string",
                    "description": "Preferred cuisine type (optional)",
                },
            },
            "required": ["calorie_target", "meal_type"],
        },
    ),
    Tool(
        name="analyze_nutrition",
        description="Analyze nutritional content of foods, meals, or daily intake with detailed breakdown",
        inputSchema={
            "type": "object",
            "properties": {
                "foods": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "quantity": {"type": "number"},
                            "unit": {
                                "type": "string",
                                "enum": [
                                    "g",
                                    "oz",
                                    "cup",
                                    "tbsp",
                                    "tsp",
                                    "piece",
                                    "slice",
                                ],
                            },
                        },
                        "required": ["name", "quantity", "unit"],
                    },
                    "description": "List of foods to analyze",
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["basic", "detailed", "micronutrients"],
                    "default": "basic",
                    "description": "Level of nutritional analysis",
                },
                "compare_to_rda": {
                    "type": "boolean",
                    "default": False,
                    "description": "Compare nutrients to recommended daily allowances",
                },
                "user_profile": {
                    "type": "object",
                    "properties": {
                        "age": {"type": "integer"},
                        "gender": {"type": "string"},
                        "weight_kg": {"type": "number"},
                        "activity_level": {"type": "string"},
                    },
                    "description": "User profile for RDA comparisons",
                },
            },
            "required": ["foods"],
        },
    ),
    Tool(
        name="recommend_supplements",
        description="Provide supplement recommendations based on goals, diet analysis, and individual needs",
        inputSchema={
            "type": "object",
            "properties": {
                "goals": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Health and fitness goals",
                },
                "dietary_analysis": {
                    "type": "object",
                    "description": "Results from nutrition analysis showing potential deficiencies",
                },
                "training_type": {
                    "type": "string",
                    "enum": ["strength", "endurance", "mixed", "none"],
                    "description": "Primary training focus",
                },
                "budget": {
                    "type": "string",
                    "enum": ["low", "medium", "high"],
                    "description": "Budget tier for supplements",
                },
                "existing_supplements": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Currently taken supplements",
                },
                "health_conditions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Relevant health conditions or concerns",
                },
            },
            "required": ["goals"],
        },
    ),
    Tool(
        name="track_hydration",
        description="Calculate hydration needs and track water intake based on activity and environment",
        inputSchema={
            "type": "object",
            "properties": {
                "weight_kg": {"type": "number", "minimum": 30, "maximum": 300},
                "activity_duration_minutes": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 480,
                },
                "activity_intensity": {
                    "type": "string",
                    "enum": ["low", "moderate", "high", "very_high"],
                    "description": "Exercise intensity level",
                },
                "environment": {
                    "type": "string",
                    "enum": ["cool", "moderate", "hot", "very_hot"],
                    "description": "Environmental conditions",
                },
                "sweat_rate": {
                    "type": "string",
                    "enum": ["low", "average", "high"],
                    "description": "Individual sweat rate",
                    "default": "average",
                },
                "current_intake_ml": {
                    "type": "integer",
                    "minimum": 0,
                    "description": "Current daily water intake in ml",
                },
            },
            "required": [
                "weight_kg",
                "activity_duration_minutes",
                "activity_intensity",
            ],
        },
    ),
]


@app.list_tools()
async def list_tools() -> List[Tool]:
    """List all available nutrition domain tools."""
    return NUTRITION_TOOLS


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """Execute nutrition domain tool calls."""
    try:
        if name == "calculate_daily_calories":
            # Handle calorie calculation - need to adapt to new tool interface
            result = await calorie_calc.calculate_daily_calories(arguments)
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "generate_comprehensive_meal_plan":
            meal_input = MealPlanInput(**arguments)
            result = await meal_plan_tool.generate_meal_plan(meal_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "search_recipes":
            search_input = RecipeSearchInput(**arguments)
            result = await recipe_tool.search_recipes(search_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "get_personalized_recipe_recommendations":
            recommendation_input = NutritionRecommendationInput(**arguments)
            result = await recipe_tool.get_personalized_recommendations(
                recommendation_input
            )
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "analyze_nutrition":
            # Handle nutrition analysis - need to adapt to new tool interface
            result = await nutrition_assessment.analyze_nutrition(arguments)
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "track_hydration":
            # Simple hydration calculation
            weight_kg = arguments["weight_kg"]
            activity_duration = arguments["activity_duration_minutes"]
            intensity = arguments["activity_intensity"]

            base_hydration = weight_kg * 35  # 35ml per kg body weight

            activity_multipliers = {
                "low": 1.2,
                "moderate": 1.4,
                "high": 1.6,
                "very_high": 1.8,
            }
            activity_bonus = (
                (activity_duration / 60)
                * 500
                * activity_multipliers.get(intensity, 1.4)
            )

            total_needed = base_hydration + activity_bonus
            current_intake = arguments.get("current_intake_ml", 0)
            remaining = max(0, total_needed - current_intake)

            result = {
                "daily_hydration_goal_ml": round(total_needed),
                "current_intake_ml": current_intake,
                "remaining_ml": round(remaining),
                "percentage_complete": round((current_intake / total_needed) * 100, 1),
                "recommendations": {
                    "pre_workout_ml": 400,
                    "during_workout_ml_per_hour": 200 if activity_duration > 60 else 0,
                    "post_workout_ml": 600,
                },
            }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        else:
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


@app.list_prompts()
async def list_prompts() -> List[Prompt]:
    """List available nutrition coaching prompts."""
    return [
        Prompt(
            name="nutrition_meal_planning",
            description="Generate meal planning advice based on caloric needs and preferences",
            arguments=[
                PromptArgument(
                    name="daily_calories",
                    description="Target daily calories",
                    required=True,
                ),
                PromptArgument(
                    name="dietary_preferences",
                    description="Dietary preferences and restrictions",
                    required=True,
                ),
                PromptArgument(
                    name="goals", description="Nutrition goals", required=True
                ),
            ],
        ),
        Prompt(
            name="nutrition_optimization",
            description="Provide nutrition optimization recommendations",
            arguments=[
                PromptArgument(
                    name="current_diet",
                    description="Current dietary habits",
                    required=True,
                ),
                PromptArgument(
                    name="training_type", description="Type of training", required=True
                ),
                PromptArgument(
                    name="goals", description="Performance goals", required=True
                ),
            ],
        ),
    ]


@app.get_prompt()
async def get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
    """Get nutrition coaching prompts."""
    if name == "nutrition_meal_planning":
        daily_calories = arguments.get("daily_calories", "")
        dietary_preferences = arguments.get("dietary_preferences", "")
        goals = arguments.get("goals", "")

        prompt_text = f"""
        As a sports nutritionist, create a meal planning strategy for:
        
        Daily Calorie Target: {daily_calories}
        Dietary Preferences: {dietary_preferences}
        Goals: {goals}
        
        Please provide:
        1. Optimal macronutrient distribution for the goals
        2. Meal timing recommendations around training
        3. 3-day sample meal plan with recipes
        4. Preparation and shopping tips
        5. Hydration strategy
        
        Focus on nutrient density, practical implementation, and performance optimization.
        """

        return GetPromptResult(
            description="Nutrition meal planning guidance",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    elif name == "nutrition_optimization":
        current_diet = arguments.get("current_diet", "")
        training_type = arguments.get("training_type", "")
        goals = arguments.get("goals", "")

        prompt_text = f"""
        As a performance nutritionist, analyze and optimize this nutrition plan:
        
        Current Diet: {current_diet}
        Training Type: {training_type}
        Goals: {goals}
        
        Please provide:
        1. Analysis of current diet strengths and weaknesses
        2. Specific improvements for training performance
        3. Timing optimization around workouts
        4. Supplement recommendations if needed
        5. Long-term sustainability strategies
        
        Provide evidence-based recommendations with practical implementation steps.
        """

        return GetPromptResult(
            description="Nutrition optimization recommendations",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    else:
        raise ValueError(f"Unknown prompt: {name}")


async def main():
    """Run the nutrition MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            protocolVersion="2024-11-05",
            serverInfo={"name": "nutrition-mcp-server", "version": "0.1.0"},
        )


if __name__ == "__main__":
    asyncio.run(main())
