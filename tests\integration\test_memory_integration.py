#!/usr/bin/env python3
"""
Comprehensive Test for Pinecone and Mem0 Integration

This script tests:
1. Pinecone connection and basic operations
2. Mem0 integration (if available)
3. Data storage and retrieval
4. Error handling and configuration issues
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_environment_variables():
    """Test that required environment variables are set."""
    logger.info("🔍 Testing environment variables...")

    required_vars = {
        "PINECONE_API_KEY": "Pinecone API key",
        "OPENAI_API_KEY": "OpenAI API key (for embeddings)",
    }

    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
            logger.error(f"❌ {var} is not set")
        else:
            logger.info(f"✅ {var} is set: {value[:10]}...")

    if missing_vars:
        logger.error("Missing environment variables:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        return False

    logger.info("✅ All required environment variables are set")
    return True


async def test_pinecone_connection():
    """Test basic Pinecone connection and operations."""
    logger.info("🔍 Testing Pinecone connection...")

    try:
        from pinecone import Pinecone, ServerlessSpec
        import openai

        # Initialize Pinecone
        pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))

        # List existing indexes
        indexes = pc.list_indexes()
        logger.info(
            f"✅ Pinecone connection successful. Found {len(indexes.indexes)} indexes:"
        )
        for idx in indexes.indexes:
            logger.info(
                f"  - {idx.name}: {idx.dimension} dimensions, {idx.metric} metric"
            )

        # Test with athlea-memory index
        index_name = "athlea-memory"
        try:
            index = pc.Index(index_name)
            stats = index.describe_index_stats()
            logger.info(f"✅ Connected to index '{index_name}':")
            logger.info(f"  - Total vectors: {stats.total_vector_count}")
            logger.info(f"  - Dimension: {stats.dimension}")
            logger.info(
                f"  - Namespaces: {list(stats.namespaces.keys()) if stats.namespaces else 'None'}"
            )

            return index, True

        except Exception as e:
            logger.error(f"❌ Cannot connect to index '{index_name}': {e}")
            return None, False

    except ImportError as e:
        logger.error(f"❌ Pinecone library not installed: {e}")
        return None, False
    except Exception as e:
        logger.error(f"❌ Pinecone connection failed: {e}")
        return None, False


async def test_openai_embeddings():
    """Test OpenAI embeddings generation."""
    logger.info("🔍 Testing OpenAI embeddings...")

    try:
        from openai import OpenAI

        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Test embedding generation
        test_text = "This is a test text for embedding generation"
        response = client.embeddings.create(
            model="text-embedding-3-large", input=test_text
        )

        embedding = response.data[0].embedding
        logger.info(f"✅ OpenAI embeddings working:")
        logger.info(f"  - Model: text-embedding-3-large")
        logger.info(f"  - Dimension: {len(embedding)}")
        logger.info(f"  - Sample values: {embedding[:5]}...")

        return embedding, True

    except ImportError as e:
        logger.error(f"❌ OpenAI library not installed: {e}")
        return None, False
    except Exception as e:
        logger.error(f"❌ OpenAI embeddings failed: {e}")
        return None, False


async def test_pinecone_storage_retrieval(index, embedding):
    """Test storing and retrieving data from Pinecone."""
    logger.info("🔍 Testing Pinecone storage and retrieval...")

    if not index or not embedding:
        logger.error("❌ Cannot test storage - index or embedding not available")
        return False

    try:
        # Test data
        test_records = [
            {
                "id": f"test-memory-{datetime.now().strftime('%Y%m%d-%H%M%S')}-1",
                "values": embedding,
                "metadata": {
                    "text": "User prefers strength training over cardio",
                    "category": "preference",
                    "user_id": "test_user_123",
                    "timestamp": datetime.now().isoformat(),
                    "type": "coaching_insight",
                },
            },
            {
                "id": f"test-memory-{datetime.now().strftime('%Y%m%d-%H%M%S')}-2",
                "values": [0.1] * len(embedding),  # Different embedding
                "metadata": {
                    "text": "User completed 5 sets of squats with 135lbs",
                    "category": "workout_log",
                    "user_id": "test_user_123",
                    "timestamp": datetime.now().isoformat(),
                    "type": "performance_data",
                },
            },
        ]

        # Store test records
        logger.info("📝 Storing test records...")
        upsert_response = index.upsert(vectors=test_records, namespace="test_coaching")
        logger.info(f"✅ Stored {upsert_response.upserted_count} records")

        # Wait a moment for indexing
        await asyncio.sleep(2)

        # Test query
        logger.info("🔍 Testing similarity search...")
        query_response = index.query(
            vector=embedding, top_k=2, include_metadata=True, namespace="test_coaching"
        )

        logger.info(
            f"✅ Query successful. Found {len(query_response.matches)} matches:"
        )
        for i, match in enumerate(query_response.matches):
            logger.info(f"  Match {i+1}: ID={match.id}, Score={match.score:.4f}")
            logger.info(f"    Text: {match.metadata.get('text', 'N/A')}")
            logger.info(f"    Category: {match.metadata.get('category', 'N/A')}")

        return True

    except Exception as e:
        logger.error(f"❌ Pinecone storage/retrieval test failed: {e}")
        return False


async def test_mem0_integration():
    """Test Mem0 integration if available."""
    logger.info("🔍 Testing Mem0 integration...")

    try:
        # Try to import and test Mem0
        import mem0

        # Initialize Mem0 with Pinecone
        config = {
            "vector_store": {
                "provider": "pinecone",
                "config": {
                    "api_key": os.getenv("PINECONE_API_KEY"),
                    "index_name": "athlea-memory",
                    "dimension": 3072,
                    "metric": "cosine",
                },
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "api_key": os.getenv("OPENAI_API_KEY"),
                    "model": "text-embedding-3-large",
                },
            },
        }

        memory = mem0.Memory(config)

        # Test storing a memory
        test_memory = "User mentioned they prefer morning workouts and have experience with powerlifting"
        result = memory.add(test_memory, user_id="test_user_123")
        logger.info(f"✅ Mem0 memory added: {result}")

        # Test searching memories
        search_results = memory.search(
            "morning workout preferences", user_id="test_user_123"
        )
        logger.info(f"✅ Mem0 search successful. Found {len(search_results)} results:")
        for i, result in enumerate(search_results):
            logger.info(f"  Result {i+1}: {result}")

        return True

    except ImportError:
        logger.warning("⚠️ Mem0 library not installed. Skipping Mem0 tests.")
        logger.info("To install Mem0, run: pip install mem0ai")
        return False
    except Exception as e:
        logger.error(f"❌ Mem0 test failed: {e}")
        return False


async def test_langchain_pinecone():
    """Test LangChain + Pinecone integration."""
    logger.info("🔍 Testing LangChain + Pinecone integration...")

    try:
        from langchain_pinecone import PineconeVectorStore
        from langchain_openai import OpenAIEmbeddings
        from langchain.schema import Document

        # Initialize embeddings
        embeddings = OpenAIEmbeddings(
            openai_api_key=os.getenv("OPENAI_API_KEY"), model="text-embedding-3-large"
        )

        # Initialize vector store
        vector_store = PineconeVectorStore(
            index_name="athlea-memory",
            embedding=embeddings,
            pinecone_api_key=os.getenv("PINECONE_API_KEY"),
            namespace="langchain_test",
        )

        # Test documents
        test_docs = [
            Document(
                page_content="User has been training for 3 years and focuses on strength building",
                metadata={"user_id": "test_user_123", "category": "training_history"},
            ),
            Document(
                page_content="User prefers compound movements like deadlifts and squats",
                metadata={
                    "user_id": "test_user_123",
                    "category": "exercise_preference",
                },
            ),
        ]

        # Add documents
        ids = vector_store.add_documents(test_docs)
        logger.info(f"✅ LangChain added {len(ids)} documents to Pinecone")

        # Wait for indexing
        await asyncio.sleep(2)

        # Test similarity search
        results = vector_store.similarity_search(
            "strength training experience", k=2, namespace="langchain_test"
        )

        logger.info(f"✅ LangChain similarity search found {len(results)} results:")
        for i, doc in enumerate(results):
            logger.info(f"  Result {i+1}: {doc.page_content[:100]}...")

        return True

    except ImportError as e:
        logger.warning(f"⚠️ LangChain Pinecone integration not available: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ LangChain + Pinecone test failed: {e}")
        return False


async def cleanup_test_data(index):
    """Clean up test data from Pinecone."""
    logger.info("🧹 Cleaning up test data...")

    if not index:
        logger.warning("⚠️ No index available for cleanup")
        return

    try:
        # Delete test namespace
        index.delete(delete_all=True, namespace="test_coaching")
        index.delete(delete_all=True, namespace="langchain_test")
        logger.info("✅ Test data cleaned up")
    except Exception as e:
        logger.warning(f"⚠️ Cleanup warning (non-critical): {e}")


async def main():
    """Run all tests."""
    logger.info("🚀 Starting comprehensive Pinecone and Mem0 integration tests...")
    logger.info("=" * 80)

    # Track test results
    results = {}

    # Test 1: Environment Variables
    results["env_vars"] = test_environment_variables()
    if not results["env_vars"]:
        logger.error("❌ Environment variables test failed. Cannot continue.")
        return

    print("\n" + "=" * 80)

    # Test 2: Pinecone Connection
    index, results["pinecone_connection"] = await test_pinecone_connection()

    print("\n" + "=" * 80)

    # Test 3: OpenAI Embeddings
    embedding, results["openai_embeddings"] = await test_openai_embeddings()

    print("\n" + "=" * 80)

    # Test 4: Pinecone Storage/Retrieval
    results["pinecone_storage"] = await test_pinecone_storage_retrieval(
        index, embedding
    )

    print("\n" + "=" * 80)

    # Test 5: LangChain Integration
    results["langchain_pinecone"] = await test_langchain_pinecone()

    print("\n" + "=" * 80)

    # Test 6: Mem0 Integration
    results["mem0"] = await test_mem0_integration()

    print("\n" + "=" * 80)

    # Cleanup
    await cleanup_test_data(index)

    print("\n" + "=" * 80)

    # Summary
    logger.info("📊 TEST SUMMARY:")
    logger.info("=" * 50)

    passed = 0
    total = 0

    for test_name, result in results.items():
        if result is not None:
            total += 1
            if result:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.info(f"❌ {test_name}: FAILED")
        else:
            logger.info(f"⚠️ {test_name}: SKIPPED")

    logger.info("=" * 50)
    logger.info(f"OVERALL: {passed}/{total} tests passed")

    if results.get("pinecone_connection") and results.get("openai_embeddings"):
        logger.info("🎉 Basic Pinecone + OpenAI integration is working!")
        logger.info("You should now see records in your Pinecone dashboard.")
    else:
        logger.error("❌ Basic integration is not working. Check the errors above.")

    return results


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
