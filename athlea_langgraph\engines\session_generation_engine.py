"""
Session Generation Engine

Core engine that coordinates coaches to create intelligent, adaptive weekly training plans.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

# Import our models and components
from .session_state_models import (
    FitnessProfile,
    TrainingSession,
    SessionFeedback,
    WeeklyPlan,
    create_fitness_profile_from_onboarding,
)

from .coach_intelligence import (
    DomainCoach,
    DailyFocus,
    WeeklyFocus,
    StrengthCoach,
    RunningCoach,
    create_coach,
)

from ..states.onboarding_state import OnboardingState, PlanDetails

logger = logging.getLogger(__name__)


class PeriodizationModel(BaseModel):
    """Model for determining training periodization and weekly focus"""

    def get_weekly_focus(
        self,
        plan_details: PlanDetails,
        week_number: int,
        user_fitness_profile: FitnessProfile,
        start_date_override: Optional[datetime] = None,
    ) -> WeeklyFocus:
        """Determine weekly focus based on plan and periodization with current date handling"""

        # Use provided start date or calculate from current date
        if start_date_override:
            start_date = start_date_override
        else:
            # Start from the beginning of the current week (Monday)
            current_date = datetime.now()
            days_since_monday = current_date.weekday()
            week_start = current_date - timedelta(days=days_since_monday)
            start_date = week_start + timedelta(weeks=week_number - 1)

        end_date = start_date + timedelta(days=6)

        # Determine weekly theme based on plan phase
        weekly_theme = self._determine_weekly_theme(plan_details, week_number)

        # Create daily focuses
        daily_focuses = self._create_daily_focuses(
            plan_details, week_number, user_fitness_profile
        )

        # Calculate weekly load
        total_weekly_load = sum(focus.volume_target for focus in daily_focuses.values())

        # Determine intensity distribution
        intensity_distribution = self._calculate_intensity_distribution(
            plan_details, week_number
        )

        # Primary adaptations for the week
        primary_adaptations = self._determine_primary_adaptations(
            plan_details, week_number
        )

        return WeeklyFocus(
            week_number=week_number,
            start_date=start_date,
            end_date=end_date,
            weekly_theme=weekly_theme,
            primary_adaptations=primary_adaptations,
            daily_focuses=daily_focuses,
            total_weekly_load=total_weekly_load,
            intensity_distribution=intensity_distribution,
        )

    def _determine_weekly_theme(
        self, plan_details: PlanDetails, week_number: int
    ) -> str:
        """Determine the theme for the week based on plan phases"""

        # Find which phase we're in
        total_weeks = self._estimate_plan_duration_weeks(plan_details)

        if week_number <= 2:
            return "Foundation Building"
        elif week_number <= total_weeks * 0.6:
            return "Base Development"
        elif week_number <= total_weeks * 0.8:
            return "Intensity Focus"
        elif week_number == total_weeks:
            return "Peak Performance"
        else:
            return "Progression Phase"

    def _create_daily_focuses(
        self, plan_details: PlanDetails, week_number: int, user_profile: FitnessProfile
    ) -> Dict[int, DailyFocus]:
        """Create daily training focuses for the week"""

        # Get user time constraints
        time_constraints = user_profile.time_constraints or {}
        weekly_sessions = time_constraints.get("sessions_per_week", 4)
        daily_minutes = time_constraints.get("daily_minutes", 60)

        # Determine which disciplines to include based on plan
        disciplines = plan_details.disciplines

        # Create a weekly schedule
        daily_focuses = {}

        # Pattern: 4-5 training days with 2-3 rest days
        training_days = [1, 2, 4, 5, 6][
            :weekly_sessions
        ]  # Monday, Tuesday, Thursday, Friday, Saturday

        for day in range(1, 8):  # 1-7 for Monday-Sunday
            if day in training_days:
                focus = self._create_training_day_focus(
                    day, disciplines, daily_minutes, week_number
                )
            else:
                focus = self._create_rest_day_focus(day)

            daily_focuses[day] = focus

        return daily_focuses

    def _create_training_day_focus(
        self, day: int, disciplines: List[str], daily_minutes: int, week_number: int
    ) -> DailyFocus:
        """Create focus for a training day"""

        # Map disciplines to our domain names
        domain_mapping = {
            "Strength Training": "strength",
            "Running": "running",
            "Cycling": "cycling",
            "General Fitness": "strength",  # default to strength
        }

        # Determine primary domain for the day
        day_patterns = {
            1: ("strength", "Start week with strength"),  # Monday
            2: ("running", "Cardio development"),  # Tuesday
            3: ("recovery", "Active recovery"),  # Wednesday
            4: ("strength", "Mid-week strength"),  # Thursday
            5: ("running", "End week cardio"),  # Friday
            6: ("strength", "Weekend strength"),  # Saturday
            7: ("recovery", "Sunday recovery"),  # Sunday
        }

        primary_domain, description = day_patterns.get(
            day, ("strength", "Training day")
        )

        # Override with plan disciplines if specified
        if disciplines:
            available_domains = [domain_mapping.get(d, "strength") for d in disciplines]
            if primary_domain not in available_domains and available_domains:
                primary_domain = available_domains[0]

        # Determine intensity based on week progression
        intensity_target = self._determine_daily_intensity(day, week_number)

        # Add secondary domains
        secondary_domains = []
        if daily_minutes > 45 and len(disciplines) > 1:
            secondary_domains = [d for d in available_domains if d != primary_domain][
                :1
            ]

        return DailyFocus(
            day=day,
            primary_domain=primary_domain,
            secondary_domains=secondary_domains,
            intensity_target=intensity_target,
            volume_target=daily_minutes,
            focus_description=description,
            recovery_priority=(day == 7),  # Sunday is recovery priority
        )

    def _create_rest_day_focus(self, day: int) -> DailyFocus:
        """Create focus for a rest day"""
        return DailyFocus(
            day=day,
            primary_domain="recovery",
            secondary_domains=[],
            intensity_target="low",
            volume_target=30,  # light activity
            focus_description="Rest and recovery",
            recovery_priority=True,
        )

    def _determine_daily_intensity(self, day: int, week_number: int) -> str:
        """Determine intensity for a specific day"""

        # Basic pattern: moderate most days with 1-2 high days per week
        base_intensities = {
            1: "moderate",  # Monday
            2: "high",  # Tuesday - first high day
            3: "low",  # Wednesday - recovery
            4: "moderate",  # Thursday
            5: "high",  # Friday - second high day
            6: "moderate",  # Saturday
            7: "low",  # Sunday - recovery
        }

        # Adjust based on week progression
        if week_number == 1:
            # First week: all moderate or low
            return "moderate" if day in [1, 4] else "low"
        elif week_number % 4 == 0:
            # Every 4th week: recovery week
            return "low" if base_intensities[day] == "high" else base_intensities[day]
        else:
            return base_intensities[day]

    def _calculate_intensity_distribution(
        self, plan_details: PlanDetails, week_number: int
    ) -> Dict[str, float]:
        """Calculate weekly intensity distribution"""

        if week_number % 4 == 0:  # Recovery week
            return {"low": 0.7, "moderate": 0.3, "high": 0.0}
        elif week_number <= 2:  # Early weeks
            return {"low": 0.4, "moderate": 0.5, "high": 0.1}
        else:  # Progressive weeks
            return {"low": 0.3, "moderate": 0.4, "high": 0.3}

    def _determine_primary_adaptations(
        self, plan_details: PlanDetails, week_number: int
    ) -> List[str]:
        """Determine what adaptations to focus on this week"""

        # Extract from plan description or use defaults
        base_adaptations = ["general_fitness", "strength", "endurance"]

        if "strength" in plan_details.plan_type.lower():
            base_adaptations = ["strength", "muscle_mass", "power"]
        elif "endurance" in plan_details.plan_type.lower():
            base_adaptations = ["aerobic_capacity", "endurance", "efficiency"]

        return base_adaptations

    def _estimate_plan_duration_weeks(self, plan_details: PlanDetails) -> int:
        """Estimate plan duration in weeks"""
        # Parse duration string or use default
        duration = plan_details.duration.lower()

        if "week" in duration:
            try:
                return int(duration.split()[0])
            except:
                pass
        elif "month" in duration:
            try:
                months = int(duration.split()[0])
                return months * 4
            except:
                pass

        return 12  # default 12 weeks


class CoachCoordinationSystem(BaseModel):
    """Handles coordination between different domain coaches"""

    async def coordinate_daily_sessions(
        self,
        coaches: Dict[str, DomainCoach],
        user_profile: FitnessProfile,
        daily_focus: DailyFocus,
        week_context: WeeklyFocus,
    ) -> List[TrainingSession]:
        """Coordinate coaches to create complementary daily sessions"""

        sessions = []
        total_daily_load = 0

        # Priority order based on daily focus
        coach_priority = self._determine_coach_priority(daily_focus)

        for domain in coach_priority:
            if domain in coaches and total_daily_load < daily_focus.volume_target:
                coach = coaches[domain]

                # Coach generates session considering existing sessions
                session = await coach.generate_session(
                    user_profile=user_profile,
                    daily_focus=daily_focus,
                    week_context=week_context,
                    other_sessions=sessions,
                )

                if session and self._validate_session_compatibility(session, sessions):
                    sessions.append(session)
                    total_daily_load += session.duration_minutes

                    logger.info(
                        f"[CoachCoordination] Added {domain} session ({session.duration_minutes}min)"
                    )

        return sessions

    def _determine_coach_priority(self, daily_focus: DailyFocus) -> List[str]:
        """Determine order of coaches based on daily focus"""
        priority = [daily_focus.primary_domain]
        priority.extend(daily_focus.secondary_domains)

        # Always consider recovery if it's a recovery day
        if daily_focus.recovery_priority and "recovery" not in priority:
            priority.append("recovery")

        return priority

    def _validate_session_compatibility(
        self, new_session: TrainingSession, existing_sessions: List[TrainingSession]
    ) -> bool:
        """Validate that a new session is compatible with existing sessions"""

        # Check for equipment conflicts
        new_equipment = set(new_session.equipment_needed)
        for session in existing_sessions:
            existing_equipment = set(session.equipment_needed)
            if new_equipment & existing_equipment:
                logger.warning(
                    f"Equipment conflict detected: {new_equipment & existing_equipment}"
                )
                # Allow for now, but log the warning

        # Check for excessive training load in same domain
        same_domain_sessions = [
            s for s in existing_sessions if s.domain == new_session.domain
        ]
        if len(same_domain_sessions) >= 2:
            logger.warning(f"Multiple {new_session.domain} sessions in one day")
            return False

        # Check total duration
        total_duration = (
            sum(s.duration_minutes for s in existing_sessions)
            + new_session.duration_minutes
        )
        if total_duration > 120:  # max 2 hours per day
            logger.warning(
                f"Total daily duration exceeds 2 hours: {total_duration} minutes"
            )
            return False

        return True


class SessionGenerationEngine(BaseModel):
    """Core engine for generating intelligent, adaptive sessions"""

    coaches: Dict[str, DomainCoach] = Field(default_factory=dict)
    periodization_model: PeriodizationModel = Field(default_factory=PeriodizationModel)
    coordination_system: CoachCoordinationSystem = Field(
        default_factory=CoachCoordinationSystem
    )

    def __init__(self, **data):
        super().__init__(**data)
        self._initialize_coaches()
        logger.info("[SessionEngine] Initialized session generation engine")

    def _initialize_coaches(self):
        """Initialize domain coaches"""
        if not self.coaches:
            domains = ["strength", "running"]  # Start with basic domains
            for domain in domains:
                try:
                    self.coaches[domain] = create_coach(domain)
                    logger.info(f"[SessionEngine] Initialized {domain} coach")
                except Exception as e:
                    logger.error(
                        f"[SessionEngine] Failed to initialize {domain} coach: {e}"
                    )

    async def generate_weekly_sessions(
        self,
        user_profile: FitnessProfile,
        plan_details: PlanDetails,
        week_number: int,
        previous_performance: List[SessionFeedback] = None,
        target_start_date: Optional[datetime] = None,
    ) -> WeeklyPlan:
        """Generate a full week of sessions with cross-domain coordination"""

        try:
            logger.info(
                f"[SessionEngine] Generating weekly plan for week {week_number} with real-time date coordination"
            )

            # 1. Analyze user's current state and adaptation
            current_state = await self._analyze_user_state(
                user_profile, previous_performance or []
            )

            # 2. Determine weekly training load and focus with current date awareness
            weekly_focus = self.periodization_model.get_weekly_focus(
                plan_details, week_number, user_profile, target_start_date
            )

            logger.info(
                f"[SessionEngine] Week {week_number} runs from {weekly_focus.start_date.strftime('%Y-%m-%d')} to {weekly_focus.end_date.strftime('%Y-%m-%d')}"
            )

            logger.info(f"[SessionEngine] Weekly theme: {weekly_focus.weekly_theme}")

            # 3. Coordinate between coaches to create complementary sessions
            weekly_sessions = {}
            total_sessions_generated = 0

            for day in range(1, 8):  # Monday to Sunday
                daily_focus = weekly_focus.daily_focuses[day]

                logger.info(
                    f"[SessionEngine] Generating sessions for day {day} ({daily_focus.primary_domain} focus)"
                )

                # Coach coordination meeting
                daily_sessions = (
                    await self.coordination_system.coordinate_daily_sessions(
                        coaches=self.coaches,
                        user_profile=user_profile,
                        daily_focus=daily_focus,
                        week_context=weekly_focus,
                    )
                )

                if daily_sessions:
                    weekly_sessions[f"day_{day}"] = daily_sessions
                    total_sessions_generated += len(daily_sessions)
                    logger.info(
                        f"[SessionEngine] Generated {len(daily_sessions)} sessions for day {day}"
                    )

            # 4. Calculate weekly metrics
            weekly_volume = self._calculate_weekly_volume(weekly_sessions)
            coach_coordination_notes = self._generate_coordination_notes(
                weekly_sessions
            )

            # 5. Create weekly plan
            weekly_plan = WeeklyPlan(
                week_number=week_number,
                start_date=weekly_focus.start_date,
                end_date=weekly_focus.end_date,
                sessions=weekly_sessions,
                daily_focuses={
                    day: focus.focus_description
                    for day, focus in weekly_focus.daily_focuses.items()
                },
                weekly_volume=weekly_volume,
                weekly_intensity_distribution={},  # TODO: Calculate from actual sessions
                total_training_load=sum(weekly_volume.values()),
                weekly_focus=weekly_focus.weekly_theme,
                rationale=self._generate_week_rationale(weekly_focus, weekly_sessions),
                coach_coordination_notes=coach_coordination_notes,
                adaptation_notes=f"Focus on {', '.join(weekly_focus.primary_adaptations)}",
            )

            logger.info(
                f"[SessionEngine] Generated weekly plan with {total_sessions_generated} total sessions"
            )

            return weekly_plan

        except Exception as e:
            logger.error(f"[SessionEngine] Error generating weekly sessions: {e}")
            # Return a basic weekly plan as fallback
            return self._create_fallback_weekly_plan(week_number, user_profile)

    async def _analyze_user_state(
        self, user_profile: FitnessProfile, previous_performance: List[SessionFeedback]
    ) -> Dict[str, Any]:
        """Analyze user's current state and adaptation"""

        # Calculate completion rate
        if previous_performance:
            completion_rate = sum(1 for f in previous_performance if f.completed) / len(
                previous_performance
            )
        else:
            completion_rate = 1.0  # Assume 100% for new users

        # Calculate average difficulty rating
        difficulty_ratings = [
            f.difficulty_rating for f in previous_performance if f.difficulty_rating
        ]
        avg_difficulty = (
            sum(difficulty_ratings) / len(difficulty_ratings)
            if difficulty_ratings
            else 5.0
        )

        # Determine adaptation phase
        adaptation_phase = "initial" if not previous_performance else "progressing"

        return {
            "completion_rate": completion_rate,
            "average_difficulty_rating": avg_difficulty,
            "adaptation_phase": adaptation_phase,
            "needs_intensity_adjustment": avg_difficulty > 8 or avg_difficulty < 3,
            "motivation_level": "high" if completion_rate > 0.8 else "moderate",
        }

    def _calculate_weekly_volume(
        self, weekly_sessions: Dict[str, List[TrainingSession]]
    ) -> Dict[str, int]:
        """Calculate total volume by domain for the week"""
        volume_by_domain = {}

        for day_sessions in weekly_sessions.values():
            for session in day_sessions:
                domain = session.domain
                volume_by_domain[domain] = (
                    volume_by_domain.get(domain, 0) + session.duration_minutes
                )

        return volume_by_domain

    def _generate_coordination_notes(
        self, weekly_sessions: Dict[str, List[TrainingSession]]
    ) -> str:
        """Generate notes about how coaches coordinated"""

        total_sessions = sum(len(sessions) for sessions in weekly_sessions.values())
        domains_used = set()

        for day_sessions in weekly_sessions.values():
            for session in day_sessions:
                domains_used.add(session.domain)

        coordination_notes = [
            f"Generated {total_sessions} sessions across {len(domains_used)} domains",
            f"Domains included: {', '.join(sorted(domains_used))}",
        ]

        # Check for coach coordination patterns
        multi_domain_days = sum(
            1
            for sessions in weekly_sessions.values()
            if len(set(s.domain for s in sessions)) > 1
        )

        if multi_domain_days > 0:
            coordination_notes.append(
                f"Cross-domain coordination on {multi_domain_days} days"
            )

        return ". ".join(coordination_notes)

    def _generate_week_rationale(
        self,
        weekly_focus: WeeklyFocus,
        weekly_sessions: Dict[str, List[TrainingSession]],
    ) -> str:
        """Generate rationale for the weekly plan"""

        rationale_parts = [
            f"Week {weekly_focus.week_number} follows {weekly_focus.weekly_theme} approach",
            f"Targeting {', '.join(weekly_focus.primary_adaptations)} adaptations",
            f"Total weekly load: {weekly_focus.total_weekly_load} minutes",
        ]

        # Add session distribution info
        total_sessions = sum(len(sessions) for sessions in weekly_sessions.values())
        training_days = sum(1 for sessions in weekly_sessions.values() if sessions)

        rationale_parts.append(
            f"{total_sessions} sessions across {training_days} training days"
        )

        return ". ".join(rationale_parts)

    def _create_fallback_weekly_plan(
        self, week_number: int, user_profile: FitnessProfile
    ) -> WeeklyPlan:
        """Create a basic fallback weekly plan if generation fails"""

        start_date = datetime.now() + timedelta(weeks=week_number - 1)
        end_date = start_date + timedelta(days=6)

        return WeeklyPlan(
            week_number=week_number,
            start_date=start_date,
            end_date=end_date,
            sessions={},  # Empty sessions
            daily_focuses={},
            weekly_volume={},
            weekly_focus="Basic Training Week",
            rationale="Fallback plan generated due to system error",
            coach_coordination_notes="No coordination available",
            adaptation_notes="Please try regenerating the plan",
        )

    async def generate_basic_week(self, user_id: str, week_number: int = 1) -> Dict:
        """Generate a basic weekly plan with current date coordination"""

        # Use current week start for more accurate date handling
        current_week_start = self._get_current_week_start()
        start_date = current_week_start + timedelta(weeks=week_number - 1)
        end_date = start_date + timedelta(days=6)

        # Basic weekly structure with enhanced metadata
        weekly_plan = {
            "week_number": week_number,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "weekly_focus": "Foundation Building",
            "sessions": {
                "day_1": [{"domain": "strength", "duration": 45, "type": "full_body"}],
                "day_2": [{"domain": "running", "duration": 30, "type": "base"}],
                "day_4": [{"domain": "strength", "duration": 45, "type": "upper_body"}],
                "day_5": [{"domain": "running", "duration": 35, "type": "tempo"}],
            },
            "total_sessions": 4,
            "weekly_volume": {"strength": 90, "running": 65},
            "rationale": f"Week {week_number} basic training plan with strength and cardio focus",
            "generated_at": self._get_current_timestamp(),
            "date_coordination": "real_time",
        }

        logger.info(
            f"[SessionEngine] Generated basic week {week_number} for user {user_id} with dates {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        )
        return weekly_plan

    def _get_current_week_start(self) -> datetime:
        """Get the start of the current week (Monday) for accurate date coordination"""
        current_date = datetime.now()
        days_since_monday = current_date.weekday()
        return current_date - timedelta(days=days_since_monday)

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format for real-time coordination"""
        from datetime import timezone

        return datetime.now(timezone.utc).isoformat()

    async def update_weekly_plan_dates(
        self, weekly_plan: WeeklyPlan, new_start_date: datetime
    ) -> WeeklyPlan:
        """Update a weekly plan with new dates while preserving all session structure"""
        # Calculate the offset in days
        original_start = weekly_plan.start_date
        date_offset = (new_start_date - original_start).days

        logger.info(
            f"[SessionEngine] Updating weekly plan dates: {original_start.strftime('%Y-%m-%d')} -> {new_start_date.strftime('%Y-%m-%d')} (offset: {date_offset} days)"
        )

        # Create updated plan with new dates
        updated_plan = WeeklyPlan(
            week_number=weekly_plan.week_number,
            start_date=new_start_date,
            end_date=new_start_date + timedelta(days=6),
            sessions={},
            daily_focuses=weekly_plan.daily_focuses,
            weekly_volume=weekly_plan.weekly_volume,
            weekly_intensity_distribution=weekly_plan.weekly_intensity_distribution,
            total_training_load=weekly_plan.total_training_load,
            weekly_focus=weekly_plan.weekly_focus,
            rationale=f"{weekly_plan.rationale} [Updated: {self._get_current_timestamp()}]",
            coach_coordination_notes=f"{weekly_plan.coach_coordination_notes} [Date updated: {self._get_current_timestamp()}]",
            adaptation_notes=weekly_plan.adaptation_notes,
            previous_week_feedback_summary=weekly_plan.previous_week_feedback_summary,
        )

        # Update session dates while preserving all session data
        for day_key, sessions in weekly_plan.sessions.items():
            updated_sessions = []
            for session in sessions:
                # Calculate new session date
                new_session_date = session.date + timedelta(days=date_offset)

                # Create updated session with preserved data
                updated_session = TrainingSession(
                    session_id=session.session_id,
                    date=new_session_date,
                    domain=session.domain,
                    session_type=session.session_type,
                    duration_minutes=session.duration_minutes,
                    intensity_level=session.intensity_level,
                    difficulty_level=session.difficulty_level,
                    session_data=session.session_data,
                    generating_coach=session.generating_coach,
                    coach_rationale=session.coach_rationale,
                    expected_benefits=session.expected_benefits,
                    prerequisites=session.prerequisites,
                    estimated_exertion=session.estimated_exertion,
                    equipment_needed=session.equipment_needed,
                    location_type=session.location_type,
                    adaptation_focus=session.adaptation_focus,
                    coordination_notes=f"{session.coordination_notes} [Rescheduled: {self._get_current_timestamp()}]",
                    status=session.status,
                    user_modifications=session.user_modifications,
                )
                updated_sessions.append(updated_session)

            updated_plan.sessions[day_key] = updated_sessions

        return updated_plan

    def get_current_training_week(self, plan_start_date: datetime) -> int:
        """Calculate which week of training the user is currently in"""
        current_date = datetime.now()
        days_since_start = (current_date - plan_start_date).days
        current_week = (days_since_start // 7) + 1

        logger.info(f"[SessionEngine] Current training week: {current_week}")
        return max(1, current_week)  # Ensure week is at least 1


# Helper functions for integration with onboarding
async def generate_first_week_from_onboarding(
    onboarding_state: OnboardingState,
) -> WeeklyPlan:
    """Generate first week of sessions from completed onboarding"""

    # Create fitness profile from onboarding data
    fitness_profile = create_fitness_profile_from_onboarding(onboarding_state)

    # Get plan details
    plan_details = onboarding_state.get("generated_plan")
    if not plan_details:
        raise ValueError("No generated plan found in onboarding state")

    # Initialize session engine
    engine = SessionGenerationEngine()

    # Generate first week
    weekly_plan = await engine.generate_weekly_sessions(
        user_profile=fitness_profile,
        plan_details=plan_details,
        week_number=1,
        previous_performance=[],
    )

    return weekly_plan
