"""
n8n Workflow Automation Client Service

Provides centralized n8n API communication for the Athlea coaching system.
Handles workflow creation, execution, monitoring, and error management.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from enum import Enum

import aiohttp
from aiohttp import ClientTimeout, ClientError

from ..config import config

logger = logging.getLogger(__name__)


class WorkflowExecutionStatus(str, Enum):
    """n8n workflow execution status values."""

    RUNNING = "running"
    SUCCESS = "success"
    ERROR = "error"
    WAITING = "waiting"
    CANCELED = "canceled"
    CRASHED = "crashed"
    NEW = "new"


class N8nClientError(Exception):
    """Base exception for n8n client errors."""

    pass


class N8nAPIError(N8nClientError):
    """Exception for n8n API errors."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict] = None,
    ):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class N8nTimeoutError(N8nClientError):
    """Exception for n8n request timeouts."""

    pass


class N8nClient:
    """Client for communicating with n8n workflow automation API."""

    def __init__(self, n8n_config: Optional[Any] = None):
        """
        Initialize the n8n client.

        Args:
            n8n_config: n8n configuration object. If None, uses global config.
        """
        self.config = n8n_config or config.n8n
        self._session: Optional[aiohttp.ClientSession] = None

        if not self.config.is_enabled:
            logger.warning(
                "[n8n Client] n8n integration is disabled. API key not provided."
            )

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create an aiohttp session with proper configuration."""
        if self._session is None or self._session.closed:
            timeout = ClientTimeout(total=self.config.timeout_seconds)
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }

            # Add API key if available
            if self.config.api_key:
                headers["X-N8N-API-KEY"] = self.config.api_key

            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers,
                raise_for_status=False,  # Handle status codes manually
            )

        return self._session

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the n8n API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: Request body data
            params: URL query parameters

        Returns:
            Response data as dictionary

        Raises:
            N8nAPIError: For API errors
            N8nTimeoutError: For timeout errors
            N8nClientError: For other client errors
        """
        if not self.config.is_enabled:
            raise N8nClientError("n8n client is disabled. API key not configured.")

        url = f"{self.config.api_url.rstrip('/')}/{endpoint.lstrip('/')}"
        session = await self._get_session()

        for attempt in range(self.config.max_retries + 1):
            try:
                logger.debug(f"[n8n Client] {method} {url} (attempt {attempt + 1})")

                async with session.request(
                    method=method, url=url, json=data, params=params
                ) as response:
                    response_data = (
                        await response.json()
                        if response.content_type == "application/json"
                        else {}
                    )

                    if response.status >= 400:
                        error_msg = f"n8n API error: {response.status} - {response_data.get('message', 'Unknown error')}"
                        raise N8nAPIError(error_msg, response.status, response_data)

                    logger.debug(f"[n8n Client] Request successful: {response.status}")
                    return response_data

            except asyncio.TimeoutError:
                if attempt == self.config.max_retries:
                    raise N8nTimeoutError(
                        f"Request timed out after {self.config.max_retries + 1} attempts"
                    )
                logger.warning(
                    f"[n8n Client] Timeout on attempt {attempt + 1}, retrying..."
                )
                await asyncio.sleep(2**attempt)  # Exponential backoff

            except ClientError as e:
                if attempt == self.config.max_retries:
                    raise N8nClientError(f"Client error: {str(e)}")
                logger.warning(
                    f"[n8n Client] Client error on attempt {attempt + 1}: {e}, retrying..."
                )
                await asyncio.sleep(2**attempt)

            except Exception as e:
                logger.error(f"[n8n Client] Unexpected error: {str(e)}")
                raise N8nClientError(f"Unexpected error: {str(e)}")

    async def list_workflows(self) -> List[Dict[str, Any]]:
        """
        Get all available workflows.

        Returns:
            List of workflow dictionaries
        """
        logger.info("[n8n Client] Fetching all workflows")
        response = await self._make_request("GET", "workflows")
        workflows = response.get("data", [])
        logger.info(f"[n8n Client] Found {len(workflows)} workflows")
        return workflows

    async def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get a specific workflow by ID.

        Args:
            workflow_id: The workflow identifier

        Returns:
            Workflow data dictionary
        """
        logger.info(f"[n8n Client] Fetching workflow: {workflow_id}")
        return await self._make_request("GET", f"workflows/{workflow_id}")

    async def create_workflow(self, name: str, workflow_data: Dict[str, Any]) -> str:
        """
        Create a new workflow.

        Args:
            name: Name for the new workflow
            workflow_data: Workflow definition data

        Returns:
            Created workflow ID
        """
        logger.info(f"[n8n Client] Creating workflow: {name}")

        payload = {"name": name, "active": True, **workflow_data}

        response = await self._make_request("POST", "workflows", data=payload)
        workflow_id = response.get("id")

        if not workflow_id:
            raise N8nClientError("Failed to create workflow - no ID returned")

        logger.info(f"[n8n Client] Created workflow {name} with ID: {workflow_id}")
        return workflow_id

    async def execute_workflow(
        self, workflow_id: str, input_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Execute a workflow.

        Args:
            workflow_id: The workflow to execute
            input_data: Input data for the workflow execution

        Returns:
            Execution ID
        """
        logger.info(f"[n8n Client] Executing workflow: {workflow_id}")

        payload = {"workflowId": workflow_id, "inputData": input_data or {}}

        response = await self._make_request(
            "POST", f"workflows/{workflow_id}/execute", data=payload
        )
        execution_id = response.get("data", {}).get("executionId") or response.get(
            "executionId"
        )

        if not execution_id:
            raise N8nClientError(
                "Failed to execute workflow - no execution ID returned"
            )

        logger.info(
            f"[n8n Client] Started execution {execution_id} for workflow {workflow_id}"
        )
        return execution_id

    async def get_execution_status(self, execution_id: str) -> Dict[str, Any]:
        """
        Get the status of a workflow execution.

        Args:
            execution_id: The execution identifier

        Returns:
            Execution status data
        """
        logger.debug(f"[n8n Client] Checking execution status: {execution_id}")
        return await self._make_request("GET", f"executions/{execution_id}")

    async def wait_for_execution(
        self, execution_id: str, timeout_seconds: int = 300, poll_interval: int = 2
    ) -> Dict[str, Any]:
        """
        Wait for a workflow execution to complete.

        Args:
            execution_id: The execution to wait for
            timeout_seconds: Maximum time to wait
            poll_interval: How often to check status

        Returns:
            Final execution data

        Raises:
            N8nTimeoutError: If execution doesn't complete within timeout
        """
        logger.info(f"[n8n Client] Waiting for execution {execution_id} to complete")

        start_time = asyncio.get_event_loop().time()

        while True:
            execution_data = await self.get_execution_status(execution_id)
            status = execution_data.get("finished", False)

            if status:
                final_status = execution_data.get("status", "unknown")
                logger.info(
                    f"[n8n Client] Execution {execution_id} completed with status: {final_status}"
                )
                return execution_data

            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed > timeout_seconds:
                raise N8nTimeoutError(
                    f"Execution {execution_id} did not complete within {timeout_seconds} seconds"
                )

            await asyncio.sleep(poll_interval)

    async def cancel_execution(self, execution_id: str) -> bool:
        """
        Cancel a running workflow execution.

        Args:
            execution_id: The execution to cancel

        Returns:
            True if successfully canceled
        """
        logger.info(f"[n8n Client] Canceling execution: {execution_id}")

        try:
            await self._make_request("POST", f"executions/{execution_id}/stop")
            logger.info(f"[n8n Client] Execution {execution_id} canceled")
            return True
        except N8nAPIError as e:
            logger.error(f"[n8n Client] Failed to cancel execution {execution_id}: {e}")
            return False

    async def close(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.debug("[n8n Client] Session closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()


def create_n8n_client(n8n_config: Optional[Any] = None) -> N8nClient:
    """
    Create an n8n client instance with configuration.

    Args:
        n8n_config: n8n configuration object. If None, uses global config.

    Returns:
        Configured N8nClient instance
    """
    return N8nClient(n8n_config)


def validate_n8n_config() -> bool:
    """
    Validate that n8n configuration is present and valid.

    Returns:
        True if configuration is valid, False otherwise
    """
    n8n_config = config.n8n

    if not n8n_config.api_url:
        logger.error("[n8n Client] Missing n8n API URL")
        return False

    if not n8n_config.is_enabled:
        logger.warning("[n8n Client] n8n integration is disabled (no API key)")
        return False

    logger.info(f"[n8n Client] Configuration valid - URL: {n8n_config.api_url}")
    return True


def get_n8n_config() -> Dict[str, Any]:
    """
    Get the current n8n configuration.

    Returns:
        Dictionary containing the configuration values
    """
    n8n_config = config.n8n
    return {
        "api_url": n8n_config.api_url,
        "webhook_base_url": n8n_config.webhook_base_url,
        "environment": n8n_config.environment,
        "timeout_seconds": n8n_config.timeout_seconds,
        "max_retries": n8n_config.max_retries,
        "is_enabled": n8n_config.is_enabled,
        "has_api_key": bool(n8n_config.api_key),
    }
