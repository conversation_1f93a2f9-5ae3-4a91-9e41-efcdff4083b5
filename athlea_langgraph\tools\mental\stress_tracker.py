"""
Stress Level Tracker Tool

Evidence-based stress monitoring and management tool that tracks stress levels,
identifies patterns and triggers, and provides personalized coping strategies
based on validated stress management interventions.
"""

import logging
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from ..base_tool import BaseDomainTool
from ..schemas.mental_schemas import (
    StressLevel,
    StressTrackerInput,
    StressTrackerOutput,
)

logger = logging.getLogger(__name__)


class StressLevelTracker(BaseDomainTool):
    """
    Comprehensive stress tracking and management tool.

    Based on validated stress assessment frameworks including:
    - Perceived Stress Scale (PSS) methodology
    - Stress and Coping Theory (Lazarus & Folkman)
    - Cognitive-Behavioral Stress Management principles
    - Mindfulness-Based Stress Reduction (MBSR) techniques
    """

    domain: str = "mental_training"
    name: str = "stress_level_tracker"
    description: str = (
        "Track and analyze stress levels, identify triggers and patterns, "
        "provide evidence-based stress management strategies and coping techniques"
    )

    def __init__(self):
        super().__init__()

        # Stress level categorization thresholds
        self.stress_categories = {
            StressLevel.MINIMAL: (1, 2),
            StressLevel.LOW: (3, 4),
            StressLevel.MODERATE: (5, 6),
            StressLevel.HIGH: (7, 8),
            StressLevel.SEVERE: (9, 10),
        }

        # Common stress triggers database
        self.common_triggers = {
            "work": ["deadlines", "workload", "meetings", "colleagues", "pressure"],
            "personal": ["relationships", "family", "health", "finances", "future"],
            "environmental": ["noise", "crowds", "weather", "traffic", "technology"],
            "physical": ["fatigue", "pain", "illness", "hunger", "discomfort"],
            "emotional": ["anxiety", "sadness", "anger", "frustration", "overwhelm"],
        }

        # Evidence-based coping strategies
        self.coping_strategies = {
            "immediate": [
                "Deep breathing (4-7-8 technique): inhale 4, hold 7, exhale 8",
                "Progressive muscle relaxation: tense and release muscle groups",
                "Grounding technique: 5 things you see, 4 you hear, 3 you feel",
                "Take 5-minute walk or light movement",
                "Practice mindful observation of thoughts without judgment",
                "Use cold water on wrists or splash face",
            ],
            "short_term": [
                "15-minute meditation or mindfulness practice",
                "Journal about stressful thoughts and feelings",
                "Call a supportive friend or family member",
                "Engage in physical exercise or stretching",
                "Listen to calming music or nature sounds",
                "Practice gratitude by listing 3 positive things",
            ],
            "long_term": [
                "Develop regular meditation or mindfulness practice",
                "Establish consistent sleep schedule",
                "Create work-life boundaries and time management",
                "Build strong social support network",
                "Regular physical exercise routine",
                "Consider professional counseling or therapy",
            ],
        }

        # Stress management techniques by category
        self.management_techniques = {
            StressLevel.MINIMAL: {
                "maintenance": [
                    "Continue current healthy habits",
                    "Practice preventive stress management",
                    "Maintain work-life balance",
                ]
            },
            StressLevel.LOW: {
                "prevention": [
                    "Regular check-ins with stress levels",
                    "Practice daily relaxation techniques",
                    "Maintain social connections",
                ]
            },
            StressLevel.MODERATE: {
                "active_management": [
                    "Implement daily stress reduction activities",
                    "Practice time management techniques",
                    "Address stress triggers proactively",
                ]
            },
            StressLevel.HIGH: {
                "intervention": [
                    "Daily stress management practices essential",
                    "Consider professional support",
                    "Modify high-stress situations where possible",
                ]
            },
            StressLevel.SEVERE: {
                "urgent_care": [
                    "Immediate stress reduction measures needed",
                    "Professional mental health consultation recommended",
                    "Consider temporary reduction in stressors",
                ]
            },
        }

    async def track_stress_level(
        self, stress_input: StressTrackerInput
    ) -> StressTrackerOutput:
        """Track and analyze stress level with personalized recommendations."""
        try:
            # Categorize stress level
            stress_category = self._categorize_stress_level(stress_input.stress_level)

            # Analyze stress trends (would use historical data in real implementation)
            stress_trend = self._analyze_stress_trend(stress_input)

            # Identify trigger patterns
            trigger_patterns = self._identify_trigger_patterns(
                stress_input.stress_triggers
            )

            # Evaluate coping strategies effectiveness
            effective_strategies = self._evaluate_coping_strategies(
                stress_input.coping_strategies_used, stress_input.stress_level
            )

            # Generate personalized recommendations
            recommendations = self._generate_stress_recommendations(
                stress_category, stress_input
            )
            warning_signs = self._identify_warning_signs(stress_input)
            immediate_techniques = self._get_immediate_relief_techniques(
                stress_category
            )
            long_term_strategies = self._get_long_term_strategies(
                stress_category, stress_input
            )

            return StressTrackerOutput(
                stress_category=stress_category,
                stress_trend=stress_trend,
                trigger_patterns=trigger_patterns,
                effective_coping_strategies=effective_strategies,
                stress_management_recommendations=recommendations,
                warning_signs=warning_signs,
                immediate_relief_techniques=immediate_techniques,
                long_term_strategies=long_term_strategies,
            )

        except Exception as e:
            logger.error(f"Error in stress tracking: {e}")
            raise

    def _categorize_stress_level(self, stress_level: int) -> StressLevel:
        """Categorize stress level based on 1-10 scale."""
        for category, (min_val, max_val) in self.stress_categories.items():
            if min_val <= stress_level <= max_val:
                return category
        return StressLevel.MODERATE  # Default fallback

    def _analyze_stress_trend(self, stress_input: StressTrackerInput) -> str:
        """Analyze stress trend based on available information."""
        # In a real implementation, this would analyze historical data
        # For now, use intensity_change and duration to infer trend

        if stress_input.intensity_change:
            if "increasing" in stress_input.intensity_change.lower():
                if stress_input.duration_hours and stress_input.duration_hours > 4:
                    return "Escalating stress over several hours - intervention needed"
                else:
                    return "Recently increasing stress levels"
            elif "decreasing" in stress_input.intensity_change.lower():
                return "Positive trend - stress levels decreasing"
            else:
                return "Stable stress levels - monitor for changes"

        # Analyze duration
        if stress_input.duration_hours:
            if stress_input.duration_hours > 24:
                return "Prolonged stress lasting over 24 hours"
            elif stress_input.duration_hours > 8:
                return "Extended stress period - needs attention"
            else:
                return "Recent stress episode"

        return "Current stress assessment - trend data limited"

    def _identify_trigger_patterns(self, stress_triggers: List[str]) -> List[str]:
        """Identify patterns in stress triggers."""
        patterns = []

        # Categorize triggers
        trigger_categories = {category: 0 for category in self.common_triggers.keys()}

        for trigger in stress_triggers:
            trigger_lower = trigger.lower()
            for category, keywords in self.common_triggers.items():
                if any(keyword in trigger_lower for keyword in keywords):
                    trigger_categories[category] += 1

        # Identify dominant categories
        max_count = (
            max(trigger_categories.values()) if trigger_categories.values() else 0
        )
        if max_count > 0:
            dominant_categories = [
                category
                for category, count in trigger_categories.items()
                if count == max_count
            ]

            for category in dominant_categories:
                if category == "work":
                    patterns.append("Work-related stressors are primary concern")
                elif category == "personal":
                    patterns.append("Personal life stressors are significant")
                elif category == "environmental":
                    patterns.append("Environmental factors contributing to stress")
                elif category == "physical":
                    patterns.append("Physical discomfort increasing stress levels")
                elif category == "emotional":
                    patterns.append("Emotional stressors requiring attention")

        # Multiple trigger analysis
        if len(stress_triggers) >= 3:
            patterns.append(
                "Multiple stress triggers present - comprehensive approach needed"
            )
        elif len(stress_triggers) >= 2:
            patterns.append("Multiple stressors identified - prioritize management")

        return patterns if patterns else ["Single stress trigger identified"]

    def _evaluate_coping_strategies(
        self, strategies_used: List[str], stress_level: int
    ) -> List[str]:
        """Evaluate effectiveness of coping strategies used."""
        effective_strategies = []

        if not strategies_used:
            return [
                "No coping strategies attempted - consider implementing stress management techniques"
            ]

        # Analyze strategy types
        strategy_categories = {
            "breathing": ["breathing", "breath", "inhale", "exhale"],
            "physical": ["exercise", "walk", "movement", "stretch"],
            "mindfulness": ["meditation", "mindful", "awareness", "present"],
            "social": ["talk", "friend", "family", "support"],
            "cognitive": ["thinking", "reframe", "perspective", "positive"],
            "distraction": ["music", "hobby", "activity", "entertainment"],
        }

        for strategy in strategies_used:
            strategy_lower = strategy.lower()

            # Check if strategy aligns with evidence-based approaches
            for category, keywords in strategy_categories.items():
                if any(keyword in strategy_lower for keyword in keywords):
                    if category == "breathing":
                        effective_strategies.append(
                            f"Breathing techniques ({strategy}) - highly effective for immediate relief"
                        )
                    elif category == "physical":
                        effective_strategies.append(
                            f"Physical activity ({strategy}) - excellent for stress reduction"
                        )
                    elif category == "mindfulness":
                        effective_strategies.append(
                            f"Mindfulness practice ({strategy}) - proven stress management tool"
                        )
                    elif category == "social":
                        effective_strategies.append(
                            f"Social support ({strategy}) - valuable coping resource"
                        )
                    elif category == "cognitive":
                        effective_strategies.append(
                            f"Cognitive strategy ({strategy}) - helpful for reframing stress"
                        )
                    elif category == "distraction":
                        effective_strategies.append(
                            f"Healthy distraction ({strategy}) - useful for temporary relief"
                        )

        # If stress level is still high despite strategies, suggest modifications
        if stress_level >= 7 and strategies_used:
            effective_strategies.append(
                "Consider combining multiple strategies or seeking additional support"
            )

        return (
            effective_strategies
            if effective_strategies
            else ["Applied strategies may need adjustment for better effectiveness"]
        )

    def _generate_stress_recommendations(
        self, stress_category: StressLevel, stress_input: StressTrackerInput
    ) -> List[str]:
        """Generate personalized stress management recommendations."""
        recommendations = []

        # Get category-specific techniques
        category_techniques = self.management_techniques.get(stress_category, {})
        for technique_type, techniques in category_techniques.items():
            recommendations.extend(techniques[:2])  # Add top 2 from each type

        # Context-specific recommendations
        if stress_input.context:
            context_lower = stress_input.context.lower()
            if "work" in context_lower:
                recommendations.append(
                    "Consider workplace stress management strategies"
                )
                recommendations.append("Practice micro-breaks during work hours")
            elif "relationship" in context_lower:
                recommendations.append(
                    "Consider communication strategies for relationship stress"
                )
                recommendations.append("Practice setting healthy boundaries")
            elif "health" in context_lower:
                recommendations.append(
                    "Address physical health concerns affecting stress"
                )
                recommendations.append("Consider stress-health connection approaches")

        # Duration-based recommendations
        if stress_input.duration_hours:
            if stress_input.duration_hours > 24:
                recommendations.append(
                    "Chronic stress patterns require comprehensive intervention"
                )
            elif stress_input.duration_hours > 8:
                recommendations.append("Extended stress periods need active management")

        # Symptom-specific recommendations
        physical_symptoms = ["headache", "tension", "fatigue", "stomach", "muscle"]
        emotional_symptoms = ["anxious", "overwhelmed", "irritable", "sad", "angry"]

        has_physical = any(
            symptom in " ".join(stress_input.stress_symptoms).lower()
            for symptom in physical_symptoms
        )
        has_emotional = any(
            symptom in " ".join(stress_input.stress_symptoms).lower()
            for symptom in emotional_symptoms
        )

        if has_physical:
            recommendations.append(
                "Address physical stress symptoms through relaxation techniques"
            )
        if has_emotional:
            recommendations.append(
                "Practice emotional regulation and mindfulness techniques"
            )

        return recommendations[:6]  # Limit to manageable number

    def _identify_warning_signs(self, stress_input: StressTrackerInput) -> List[str]:
        """Identify warning signs to monitor for stress escalation."""
        warning_signs = []

        # High stress level warnings
        if stress_input.stress_level >= 8:
            warning_signs.append("Very high stress levels - monitor for escalation")
            warning_signs.append("Risk of stress becoming overwhelming")

        # Duration warnings
        if stress_input.duration_hours and stress_input.duration_hours > 12:
            warning_signs.append("Prolonged stress exposure - risk of chronic stress")

        # Multiple trigger warnings
        if len(stress_input.stress_triggers) >= 3:
            warning_signs.append("Multiple stressors may compound effects")

        # Symptom-based warnings
        concerning_symptoms = [
            "panic",
            "overwhelm",
            "can't cope",
            "breaking point",
            "exhausted",
            "hopeless",
            "trapped",
        ]

        for symptom in stress_input.stress_symptoms:
            if any(concern in symptom.lower() for concern in concerning_symptoms):
                warning_signs.append(f"Concerning symptom reported: {symptom}")

        # Escalation warning
        if (
            stress_input.intensity_change
            and "increasing" in stress_input.intensity_change.lower()
        ):
            warning_signs.append(
                "Stress levels increasing - early intervention important"
            )

        # Coping strategy warnings
        if not stress_input.coping_strategies_used:
            warning_signs.append(
                "No coping strategies being used - may lead to stress accumulation"
            )

        return (
            warning_signs
            if warning_signs
            else ["Monitor for any changes in stress patterns"]
        )

    def _get_immediate_relief_techniques(
        self, stress_category: StressLevel
    ) -> List[str]:
        """Get immediate stress relief techniques based on stress level."""
        techniques = []

        if stress_category in [StressLevel.HIGH, StressLevel.SEVERE]:
            # Priority techniques for high stress
            techniques.extend(
                [
                    "STOP technique: Stop, Take a breath, Observe, Proceed mindfully",
                    "Box breathing: 4 counts in, 4 hold, 4 out, 4 hold",
                    "Cold water on wrists or face for immediate physiological calm",
                ]
            )

        # Add general immediate techniques
        techniques.extend(self.coping_strategies["immediate"][:3])

        return techniques

    def _get_long_term_strategies(
        self, stress_category: StressLevel, stress_input: StressTrackerInput
    ) -> List[str]:
        """Get long-term stress management strategies."""
        strategies = []

        # Category-specific long-term strategies
        if stress_category in [StressLevel.HIGH, StressLevel.SEVERE]:
            strategies.extend(
                [
                    "Develop comprehensive stress management plan",
                    "Consider professional counseling or therapy",
                    "Evaluate and modify major stressors where possible",
                ]
            )
        elif stress_category == StressLevel.MODERATE:
            strategies.extend(
                [
                    "Establish daily stress prevention routines",
                    "Build resilience through regular self-care",
                    "Develop better work-life balance",
                ]
            )

        # Add general long-term strategies
        strategies.extend(self.coping_strategies["long_term"][:3])

        # Context-specific long-term strategies
        if len(stress_input.stress_triggers) >= 2:
            strategies.append("Address multiple stress sources systematically")

        return strategies
