import pytest
import asyncio
import os
from typing import Generator


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_mongodb_uri() -> str:
    """Provide test MongoDB URI for tests that need database access."""
    return os.getenv("MONGODB_URI", "mongodb://localhost:27017/test_athlea")


@pytest.fixture(scope="session")
def test_user_profile() -> dict:
    """Provide a standard test user profile for tests."""
    return {
        "name": "Test User",
        "age": 30,
        "fitness_level": "intermediate",
        "goals": ["build muscle", "lose weight"],
        "preferences": {
            "workout_time": "morning",
            "workout_duration": "60 minutes",
        },
        "restrictions": {
            "injuries": [],
            "dietary": [],
        },
    }
