"""
Agent Tool Access Controller

Manages access control for tool-agent interactions based on domain and permissions.
"""

import logging
from typing import Any, Dict, List

from .contract_manager import ToolContractViolation, get_contract_manager

logger = logging.getLogger(__name__)


class AgentToolAccessController:
    """Controls agent access to tools based on domain and permissions."""

    def __init__(self):
        self._contract_manager = get_contract_manager()

    def check_access(
        self, agent_domain: str, tool_name: str, agent_permissions: List[str]
    ) -> bool:
        """
        Check if an agent can access a specific tool.

        Args:
            agent_domain: Agent's primary domain
            tool_name: Name of the tool to access
            agent_permissions: Agent's current permissions

        Returns:
            bool: True if access is allowed

        Raises:
            ToolContractViolation: If access is denied
        """
        return self._contract_manager.validate_agent_tool_access(
            agent_domain, tool_name, agent_permissions
        )

    def get_allowed_tools(
        self, agent_domain: str, agent_permissions: List[str]
    ) -> List[str]:
        """
        Get list of tools an agent is allowed to access.

        Args:
            agent_domain: Agent's primary domain
            agent_permissions: Agent's current permissions

        Returns:
            List of allowed tool names
        """
        return self._contract_manager.get_agent_allowed_tools(
            agent_domain, agent_permissions
        )

    def get_domain_tools(self, domain: str) -> List[str]:
        """
        Get all tools available for a specific domain.

        Args:
            domain: Domain name

        Returns:
            List of tool names in the domain
        """
        return self._contract_manager.get_domain_tools(domain)

    def validate_permissions(
        self, required_permissions: List[str], agent_permissions: List[str]
    ) -> bool:
        """
        Validate that an agent has all required permissions.

        Args:
            required_permissions: Permissions required for operation
            agent_permissions: Agent's current permissions

        Returns:
            bool: True if agent has all required permissions
        """
        missing_permissions = set(required_permissions) - set(agent_permissions)
        if missing_permissions:
            logger.warning(f"Agent missing permissions: {list(missing_permissions)}")
            return False
        return True
