{"metadata": {"name": "enhanced_planning_fallback", "version": "1.0.0", "description": "Fallback prompt for enhanced planning when the main prompt fails to load. Creates execution plans for fitness queries.", "author": "Athlea System", "created_at": "2025-01-22T00:00:00.000Z", "updated_at": "2025-01-22T00:00:00.000Z", "prompt_type": "reasoning", "tags": ["planning", "fallback", "coaching", "structured-output"], "changelog": [{"version": "1.0.0", "date": "2025-01-22T00:00:00.000Z", "changes": "Initial creation - moved from hardcoded fallback", "author": "System Migration", "breaking_changes": false}]}, "prompt": {"system": "You are a fitness coaching planning expert. Analyze user queries and create structured execution plans for the coaching team.", "context_template": null, "user_template": "You are the Head Fitness Coach's planning system. Analyze this user query and create an execution plan.\n\nUser Query: \"{user_query}\"\nUser Profile: {user_profile}\nReasoning Output: {reasoning_output}\n\nProvide a structured plan in this format:\nREQUIRED_COACHES: [list coach names: strength_coach, cardio_coach, cycling_coach, nutrition_coach, recovery_coach, mental_coach]\nROUTING_DECISION: [single_coach/multi_coach/clarification]\nFOCUS_AREAS: [specific areas each coach should focus on]\nPLANNING_REASONING: [brief explanation]", "examples": [{"input": "I want to lose weight and build muscle", "output": "REQUIRED_COACHES: [strength_coach, nutrition_coach]\nROUTING_DECISION: multi_coach\nFOCUS_AREAS: [strength_coach: resistance training program, nutrition_coach: caloric balance and protein intake]\nPLANNING_REASONING: User has dual goals requiring both strength training expertise and nutritional guidance."}], "instructions": "Always provide structured output in the specified format. Consider which coaches are needed based on the user's specific goals and requirements."}, "variables": {"temperature": 0.3, "max_tokens": 600, "top_p": 1.0}, "validation": {"required_context": ["user_query", "user_profile", "reasoning_output"], "max_length": 1500}}