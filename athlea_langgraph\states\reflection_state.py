"""
Reflection-enhanced state definition for coaching graph implementation.

Extends the base AgentState with reflection capabilities for Phase 1
reasoning enhancement including safety validation and iterative improvement.
"""

from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage
from typing_extensions import TypedDict

from .state import AgentState, messages_reducer


class ReflectionMetadata(TypedDict):
    """Metadata for tracking reflection iterations and quality."""

    reflection_count: int
    max_reflections: int
    reflection_feedback: Optional[str]
    safety_validated: bool
    quality_score: Optional[float]
    improvement_areas: List[str]
    reflection_history: List[Dict[str, Any]]


class SafetyValidation(TypedDict):
    """Safety validation results for fitness coaching."""

    injury_risk_score: Optional[float]  # 0-1 scale
    form_safety_validated: bool
    contraindications_checked: bool
    progressive_overload_safe: bool
    safety_warnings: List[str]
    safety_recommendations: List[str]


class ReflectionAgentState(TypedDict):
    """
    Enhanced AgentState with reflection capabilities.

    Adds reflection-specific fields while maintaining compatibility
    with existing coaching graph infrastructure.
    """

    # Core AgentState fields with proper annotations
    messages: Annotated[List[BaseMessage], messages_reducer]
    user_query: Optional[str]
    user_profile: Optional[Dict[str, Any]]
    routing_decision: Optional[Union[str, List[str]]]
    pending_agents: Optional[List[str]]
    plan: Optional[List[str]]
    current_step: Optional[int]
    domain_contributions: Dict[str, str]
    required_domains: List[str]
    completed_domains: List[str]
    aggregated_plan: Optional[str]
    proceed_to_generation: bool
    current_plan: Optional[Any]
    is_onboarding: bool

    # Specialist coach responses
    strength_response: Optional[str]
    running_response: Optional[str]
    cardio_response: Optional[str]
    cycling_response: Optional[str]
    nutrition_response: Optional[str]
    recovery_response: Optional[str]
    mental_response: Optional[str]

    # Other node outputs
    reasoning_output: Optional[str]
    clarification_output: Optional[str]
    aggregated_response: Optional[str]

    # Reflection tracking
    reflection_enabled: bool
    reflection_metadata: ReflectionMetadata

    # Original response before reflection
    original_response: Optional[str]

    # Reflection loop state
    current_reflection_agent: Optional[str]  # Which agent is being reflected upon
    reflection_prompt: Optional[str]
    reflection_criteria: List[str]

    # Safety validation
    safety_validation: SafetyValidation

    # Quality tracking
    response_quality_before: Optional[float]
    response_quality_after: Optional[float]

    # Configuration
    reflection_config: Dict[str, Any]


def create_initial_reflection_metadata() -> ReflectionMetadata:
    """Create initial reflection metadata with default values."""
    return {
        "reflection_count": 0,
        "max_reflections": 2,  # Default max iterations
        "reflection_feedback": None,
        "safety_validated": False,
        "quality_score": None,
        "improvement_areas": [],
        "reflection_history": [],
    }


def create_initial_safety_validation() -> SafetyValidation:
    """Create initial safety validation with default values."""
    return {
        "injury_risk_score": None,
        "form_safety_validated": False,
        "contraindications_checked": False,
        "progressive_overload_safe": False,
        "safety_warnings": [],
        "safety_recommendations": [],
    }


def create_reflection_config(
    max_reflections: int = 2,
    safety_threshold: float = 0.8,
    quality_threshold: float = 0.7,
    enable_safety_validation: bool = True,
    reflection_criteria: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """Create reflection configuration with customizable parameters."""

    default_criteria = [
        "accuracy",
        "safety",
        "completeness",
        "clarity",
        "actionability",
    ]

    return {
        "max_reflections": max_reflections,
        "safety_threshold": safety_threshold,
        "quality_threshold": quality_threshold,
        "enable_safety_validation": enable_safety_validation,
        "reflection_criteria": reflection_criteria or default_criteria,
        "timeout_seconds": 30,  # Prevent infinite reflection loops
        "enable_caching": True,  # Cache reflection results
        "log_reflections": True,  # Log for analytics
    }


def enhance_state_with_reflection(
    base_state: AgentState, reflection_config: Optional[Dict[str, Any]] = None
) -> ReflectionAgentState:
    """
    Enhance an existing AgentState with reflection capabilities.

    Args:
        base_state: Existing AgentState to enhance
        reflection_config: Optional reflection configuration

    Returns:
        ReflectionAgentState with reflection fields initialized
    """

    config = reflection_config or create_reflection_config()

    enhanced_state = ReflectionAgentState(
        {
            **base_state,
            "reflection_enabled": True,
            "reflection_metadata": create_initial_reflection_metadata(),
            "original_response": None,
            "current_reflection_agent": None,
            "reflection_prompt": None,
            "reflection_criteria": config["reflection_criteria"],
            "safety_validation": create_initial_safety_validation(),
            "response_quality_before": None,
            "response_quality_after": None,
            "reflection_config": config,
        }
    )

    # Update max reflections from config
    enhanced_state["reflection_metadata"]["max_reflections"] = config["max_reflections"]

    return enhanced_state


# Reducers for reflection fields
def reflection_metadata_reducer(
    left: ReflectionMetadata, right: ReflectionMetadata
) -> ReflectionMetadata:
    """Reducer for reflection metadata that merges updates."""
    result = left.copy()
    result.update(right)

    # Append to reflection history
    if right.get("reflection_feedback"):
        history_entry = {
            "iteration": right.get("reflection_count", 0),
            "feedback": right.get("reflection_feedback"),
            "timestamp": str(
                type(object)
                .__dict__.get("datetime", {})
                .get("now", lambda: "unknown")()
            ),
            "improvement_areas": right.get("improvement_areas", []),
        }
        result["reflection_history"].append(history_entry)

    return result


def safety_validation_reducer(
    left: SafetyValidation, right: SafetyValidation
) -> SafetyValidation:
    """Reducer for safety validation that merges updates."""
    result = left.copy()
    result.update(right)

    # Merge safety warnings and recommendations
    if right.get("safety_warnings"):
        result["safety_warnings"] = list(
            set(left.get("safety_warnings", []) + right.get("safety_warnings", []))
        )

    if right.get("safety_recommendations"):
        result["safety_recommendations"] = list(
            set(
                left.get("safety_recommendations", [])
                + right.get("safety_recommendations", [])
            )
        )

    return result


def reflection_config_reducer(
    left: Dict[str, Any], right: Dict[str, Any]
) -> Dict[str, Any]:
    """Reducer for reflection configuration that merges updates."""
    result = left.copy()
    result.update(right)
    return result
