#!/usr/bin/env python3
"""
Phase 3: Testing Architecture - Advanced Test Runner
Supports 8 execution modes with comprehensive testing capabilities.
"""

import argparse
import json
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

# Change to project root directory
project_root = Path(__file__).parent.parent.parent
os.chdir(project_root)


class Phase3TestRunner:
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()

    def run_command(self, cmd: List[str], description: str = "") -> Dict[str, Any]:
        """Run a command and capture results."""
        print(f"\n{'='*60}")
        print(f"Running: {description or ' '.join(cmd)}")
        print(f"{'='*60}")

        start_time = time.time()
        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300  # 5 minute timeout
            )
            duration = time.time() - start_time

            print(f"Exit Code: {result.returncode}")
            if result.stdout:
                print(f"STDOUT:\n{result.stdout}")
            if result.stderr:
                print(f"STDERR:\n{result.stderr}")

            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": duration,
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": "Test timed out after 5 minutes",
                "duration": time.time() - start_time,
            }
        except Exception as e:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "duration": time.time() - start_time,
            }

    def run_agent_tests(self) -> Dict[str, Any]:
        """Run all agent-specific unit tests."""
        return self.run_command(
            ["python", "-m", "pytest", "tests/agents/", "-v", "--tb=short"],
            "Agent Tests - Domain-specific unit tests",
        )

    def run_tool_tests(self) -> Dict[str, Any]:
        """Run all tool-specific unit tests."""
        return self.run_command(
            ["python", "-m", "pytest", "tests/tools/", "-v", "--tb=short"],
            "Tool Tests - Phase 2 aligned tool testing",
        )

    def run_integration_tests(self) -> Dict[str, Any]:
        """Run end-to-end workflow tests."""
        return self.run_command(
            ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short"],
            "Integration Tests - End-to-end workflows",
        )

    def run_synthetic_tests(self) -> Dict[str, Any]:
        """Run synthetic coaching session tests."""
        return self.run_command(
            ["python", "-m", "pytest", "tests/synthetic/", "-v", "--tb=short"],
            "Synthetic Tests - Automated coaching session health checks",
        )

    def run_fast_tests(self) -> Dict[str, Any]:
        """Run fast subset of tests (unit tests only)."""
        return self.run_command(
            [
                "python",
                "-m",
                "pytest",
                "tests/agents/",
                "tests/tools/",
                "tests/unit/",
                "-v",
                "--tb=short",
                "-x",
            ],
            "Fast Tests - Unit tests only with fail-fast",
        )

    def run_coverage_report(self) -> Dict[str, Any]:
        """Run tests with coverage reporting."""
        return self.run_command(
            [
                "python",
                "-m",
                "pytest",
                "tests/",
                "--cov=athlea_langgraph",
                "--cov-report=html",
                "--cov-report=term-missing",
                "-v",
            ],
            "Coverage Report - Full test suite with coverage analysis",
        )

    def run_health_checks(self) -> Dict[str, Any]:
        """Run health checks with synthetic testing."""
        return self.run_command(
            [
                "python",
                "-m",
                "pytest",
                "tests/synthetic/",
                "-v",
                "--tb=short",
                "--capture=no",
            ],
            "Health Checks - System health monitoring with live output",
        )

    def run_performance_benchmarks(self) -> Dict[str, Any]:
        """Run performance benchmarks across all test categories."""
        return self.run_command(
            [
                "python",
                "-m",
                "pytest",
                "tests/",
                "-v",
                "--tb=short",
                "--benchmark-only",
                "--benchmark-sort=mean",
            ],
            "Performance Benchmarks - Response time and throughput testing",
        )

    def print_summary(self):
        """Print test execution summary."""
        total_duration = time.time() - self.start_time

        print(f"\n{'='*80}")
        print(f"PHASE 3 TEST EXECUTION SUMMARY")
        print(f"{'='*80}")
        print(f"Total Execution Time: {total_duration:.2f} seconds")
        print(f"Modes Executed: {len(self.test_results)}")

        success_count = sum(
            1 for result in self.test_results.values() if result["success"]
        )
        failure_count = len(self.test_results) - success_count

        print(f"Successful Modes: {success_count}")
        print(f"Failed Modes: {failure_count}")

        print(f"\nDetailed Results:")
        for mode, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            duration = result["duration"]
            print(f"  {mode}: {status} ({duration:.2f}s)")

            if not result["success"] and result["stderr"]:
                print(f"    Error: {result['stderr'][:100]}...")

        print(f"\n{'='*80}")

        return failure_count == 0


def main():
    parser = argparse.ArgumentParser(
        description="Phase 3: Testing Architecture - Advanced Test Runner"
    )
    parser.add_argument(
        "mode",
        choices=[
            "agents",
            "tools",
            "integration",
            "synthetic",
            "fast",
            "coverage",
            "health",
            "performance",
            "all",
        ],
        help="Test execution mode",
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    runner = Phase3TestRunner()

    print(f"Phase 3 Testing Architecture - Advanced Test Runner")
    print(f"Mode: {args.mode}")
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    if args.mode == "agents":
        runner.test_results["Agent Tests"] = runner.run_agent_tests()

    elif args.mode == "tools":
        runner.test_results["Tool Tests"] = runner.run_tool_tests()

    elif args.mode == "integration":
        runner.test_results["Integration Tests"] = runner.run_integration_tests()

    elif args.mode == "synthetic":
        runner.test_results["Synthetic Tests"] = runner.run_synthetic_tests()

    elif args.mode == "fast":
        runner.test_results["Fast Tests"] = runner.run_fast_tests()

    elif args.mode == "coverage":
        runner.test_results["Coverage Report"] = runner.run_coverage_report()

    elif args.mode == "health":
        runner.test_results["Health Checks"] = runner.run_health_checks()

    elif args.mode == "performance":
        runner.test_results["Performance Benchmarks"] = (
            runner.run_performance_benchmarks()
        )

    elif args.mode == "all":
        # Run all test modes in sequence
        runner.test_results["Agent Tests"] = runner.run_agent_tests()
        runner.test_results["Tool Tests"] = runner.run_tool_tests()
        runner.test_results["Integration Tests"] = runner.run_integration_tests()
        runner.test_results["Synthetic Tests"] = runner.run_synthetic_tests()
        runner.test_results["Coverage Report"] = runner.run_coverage_report()
        runner.test_results["Health Checks"] = runner.run_health_checks()

    # Print summary and exit with appropriate code
    success = runner.print_summary()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
