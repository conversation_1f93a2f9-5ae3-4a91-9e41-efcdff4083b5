#!/usr/bin/env python3
"""
Test Actual GraphRAG System - Real LangGraph Execution

This runs the actual comprehensive coaching graph with real GraphRAG integration
to demonstrate live system behavior and responses.
"""

import asyncio
import logging
from langchain_core.messages import HumanMessage

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_actual_system():
    """Test the actual comprehensive coaching graph with GraphRAG."""

    print("🚀 TESTING ACTUAL COMPREHENSIVE GRAPHRAG SYSTEM")
    print("=" * 80)
    print("Running REAL LangGraph with Azure Cognitive Search + Cosmos DB")
    print("")

    try:
        # Import the actual graph
        from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
            create_comprehensive_coaching_graph,
        )

        # Create the graph with test configuration
        config = {
            "user_id": "test_user_hybrid",
            "thread_id": "test_thread_hybrid",
            "enable_memory": False,  # Disable for testing
            "use_react_agents": True,
            "max_iterations": 3,
            "enable_human_feedback": False,  # Disable for automated testing
            "enable_reflexion": False,
            "complexity_threshold": 0.6,
        }

        print("🏗️ Creating comprehensive coaching graph...")
        graph = await create_comprehensive_coaching_graph(config)
        print("✅ Graph created successfully!")

        # Test a simple research query
        test_query = (
            "What does the latest research say about protein timing for muscle growth?"
        )

        print(f"\n{'='*60}")
        print(f"🧪 TEST: Research Query")
        print(f"📝 Query: {test_query}")
        print("=" * 60)

        # Create initial state
        initial_state = {
            "messages": [HumanMessage(content=test_query)],
            "user_query": test_query,
            "user_id": config["user_id"],
            "thread_id": config["thread_id"],
            "user_profile": None,
            "coach_responses": {},
            "active_coaches": [],
            "execution_steps": [],
            "tool_calls_made": [],
            "debug_info": {},
        }

        print(f"\n🔄 Executing graph...")

        # Run the graph
        final_state = await graph.ainvoke(initial_state)

        # Display results
        print("\n📊 EXECUTION RESULTS:")
        print("-" * 40)

        # GraphRAG information
        knowledge_context = final_state.get("knowledge_context")
        needs_knowledge = final_state.get("needs_knowledge_retrieval", False)
        knowledge_confidence = final_state.get("knowledge_confidence", 0)
        vector_results = final_state.get("vector_results", [])
        graph_results = final_state.get("graph_results", [])

        print(f"🧠 Knowledge Assessment:")
        print(f"  • Needs Knowledge: {needs_knowledge}")
        print(f"  • Confidence: {knowledge_confidence}")
        print(f"  • Context Available: {bool(knowledge_context)}")
        if knowledge_context:
            print(f"  • Context Length: {len(knowledge_context)} chars")
            print(f"  • Context Preview: {knowledge_context[:100]}...")

        print(f"\n🔍 GraphRAG Results:")
        print(f"  • Vector Results: {len(vector_results)} documents")
        print(f"  • Graph Results: {len(graph_results)} relationships")

        if vector_results:
            print(
                f"  • Top Vector Result: {vector_results[0].get('title', 'N/A')[:50]}..."
            )

        if graph_results:
            print(f"  • Top Graph Connection: {str(graph_results[0])[:50]}...")

        # Coach responses
        coach_responses = final_state.get("coach_responses", {})
        final_response = final_state.get("final_response", "")
        active_coaches = final_state.get("active_coaches", [])
        execution_steps = final_state.get("execution_steps", [])

        print(f"\n🤖 Coach Information:")
        print(f"  • Active Coaches: {active_coaches}")
        print(f"  • Coach Responses: {len(coach_responses)}")
        print(f"  • Execution Steps: {execution_steps}")

        print(f"\n💬 Final Response:")
        print(f"  • Length: {len(final_response)} characters")
        if final_response:
            print(f"  • Preview: {final_response[:200]}...")
        else:
            print("  • No final response generated")

        # Performance info
        complexity_score = final_state.get("complexity_score", 0)
        execution_path = final_state.get("execution_path", "unknown")

        print(f"\n⚡ Performance Metrics:")
        print(f"  • Complexity Score: {complexity_score}")
        print(f"  • Execution Path: {execution_path}")
        print(f"  • Total Steps: {len(execution_steps)}")

        # Show actual messages if available
        messages = final_state.get("messages", [])
        if messages:
            print(f"\n📨 Message History ({len(messages)} messages):")
            for i, msg in enumerate(messages[-3:], 1):  # Show last 3 messages
                print(f"  {i}. {msg.__class__.__name__}: {msg.content[:100]}...")

        print(f"\n{'='*80}")
        print("✅ ACTUAL SYSTEM TEST COMPLETE")
        print("=" * 80)

    except Exception as e:
        print(f"❌ ERROR: {e}")
        logger.exception("Full error details:")


if __name__ == "__main__":
    asyncio.run(test_actual_system())
