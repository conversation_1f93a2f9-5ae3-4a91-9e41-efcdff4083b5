"""
Configuration validation utilities.
"""

import os
from typing import List, Dict, Any, Optional
from pydantic import ValidationError

from .base import BaseConfig, Environment


class ConfigValidationError(Exception):
    """Configuration validation error."""

    pass


def validate_config(config: BaseConfig) -> List[str]:
    """
    Validate configuration and return list of validation errors.

    Args:
        config: Configuration instance to validate

    Returns:
        List of validation error messages
    """
    errors = []

    try:
        if not config.azure_openai.api_key:
            errors.append("Azure OpenAI API key is required")
        if not config.azure_openai.endpoint:
            errors.append("Azure OpenAI endpoint is required")

        if not config.azure_maps.subscription_key:
            errors.append("Azure Maps subscription key is required")

        if not config.azure_search.service_name:
            errors.append("Azure Search service name is required")
        if not config.azure_search.api_key:
            errors.append("Azure Search API key is required")

        if not config.embedding.api_key:
            errors.append("Embedding API key is required")
        if not config.embedding.endpoint:
            errors.append("Embedding endpoint is required")

        if not config.database.mongodb_uri:
            errors.append("MongoDB URI is required")

        if not config.airtable.api_key:
            errors.append("Airtable API key is required")

    except ValidationError as e:
        errors.extend([f"Validation error: {error['msg']}" for error in e.errors()])

    return errors


def get_config_for_environment(environment: str = None) -> BaseConfig:
    """
    Get configuration for specified environment.

    Args:
        environment: Environment name (development, staging, production)

    Returns:
        Configuration instance for the environment
    """
    if not environment:
        environment = os.environ.get("ENVIRONMENT", "development")

    env = Environment(environment.lower())

    if env == Environment.DEVELOPMENT:
        from .development import config
    elif env == Environment.PRODUCTION:
        from .production import config
    else:
        config = BaseConfig()

    return config
