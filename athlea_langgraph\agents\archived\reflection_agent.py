"""
Reflection Agent for Coaching Response Critique and Safety Validation.

Implements LangGraph's reflection pattern specifically for fitness coaching,
providing safety-focused critique and iterative improvement suggestions.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.reflection_state import (
    ReflectionAgentState,
    ReflectionMetadata,
    SafetyValidation,
)


class FitnessReflectionAgent:
    """
    Specialized reflection agent for fitness coaching safety and quality validation.

    Provides domain-specific critique focusing on:
    - Exercise form and technique safety
    - Injury risk assessment
    - Progressive overload validation
    - Nutritional safety and contraindications
    - Recovery protocol appropriateness
    """

    def __init__(self, model_name: str = "gpt-4"):
        """Initialize the reflection agent with specified model."""
        self.llm = create_azure_chat_openai(model=model_name)
        self.logger = logging.getLogger(__name__)

        # Safety criteria weights for scoring
        self.safety_weights = {
            "injury_risk": 0.4,
            "form_safety": 0.3,
            "contraindications": 0.2,
            "progressive_overload": 0.1,
        }

        # Domain-specific reflection prompts
        self.domain_prompts = self._initialize_domain_prompts()

    def _initialize_domain_prompts(self) -> Dict[str, str]:
        """Initialize domain-specific reflection prompt templates."""
        return {
            "strength": """
            You are a strength training safety expert reviewing a coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Exercise form and technique accuracy
            - Weight progression safety
            - Injury risk assessment
            - Equipment safety considerations
            - Contraindications for movements
            
            Evaluate the response for potential safety issues and provide specific improvement suggestions.
            """,
            "nutrition": """
            You are a sports nutrition safety expert reviewing a nutrition coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Allergen identification and warnings
            - Supplement safety and interactions
            - Dietary restriction compliance
            - Medical condition considerations
            - Caloric and macronutrient safety ranges
            
            Evaluate the response for nutritional safety and accuracy.
            """,
            "cardio": """
            You are a cardiovascular training safety expert reviewing a cardio coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Training intensity appropriateness
            - Heart rate zone safety
            - Recovery time adequacy
            - Environmental considerations
            - Medical contraindications
            
            Evaluate the response for cardiovascular training safety.
            """,
            "recovery": """
            You are a recovery and injury prevention expert reviewing a recovery coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Sleep recommendation safety
            - Mobility exercise appropriateness
            - Injury risk identification
            - Recovery protocol effectiveness
            - Pain management guidance
            
            Evaluate the response for recovery safety and effectiveness.
            """,
            "mental": """
            You are a sports psychology expert reviewing a mental coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Psychological safety and support
            - Stress management appropriateness
            - Goal setting realism
            - Mental health considerations
            - Motivation technique safety
            
            Evaluate the response for psychological safety and effectiveness.
            """,
            "general": """
            You are a general fitness safety expert reviewing a coaching response.
            
            CRITICAL SAFETY FOCUS:
            - Overall safety recommendations
            - Accuracy of fitness information
            - Appropriateness for stated fitness level
            - Clear and actionable guidance
            - Risk mitigation strategies
            
            Evaluate the response for general fitness safety and quality.
            """,
        }

    async def reflect_on_response(
        self,
        original_response: str,
        user_query: str,
        user_profile: Dict[str, Any],
        coaching_domain: str = "general",
        reflection_criteria: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Perform reflection analysis on a coaching response.

        Args:
            original_response: The original coaching response to reflect upon
            user_query: The original user query/question
            user_profile: User's fitness profile and limitations
            coaching_domain: The domain of coaching (strength, nutrition, etc.)
            reflection_criteria: Specific criteria to focus on

        Returns:
            Dictionary containing reflection feedback and safety analysis
        """

        self.logger.info(f"Starting reflection analysis for {coaching_domain} domain")

        try:
            # Generate reflection prompt
            reflection_prompt = self._create_reflection_prompt(
                original_response=original_response,
                user_query=user_query,
                user_profile=user_profile,
                coaching_domain=coaching_domain,
                reflection_criteria=reflection_criteria or [],
            )

            # Get reflection from LLM
            reflection_result = await self._get_llm_reflection(reflection_prompt)

            # Perform safety validation
            safety_validation = await self._validate_safety(
                original_response=original_response,
                user_profile=user_profile,
                coaching_domain=coaching_domain,
                reflection_feedback=reflection_result.get("feedback", ""),
            )

            # Calculate quality scores
            quality_scores = self._calculate_quality_scores(
                reflection_result=reflection_result, safety_validation=safety_validation
            )

            return {
                "reflection_feedback": reflection_result.get("feedback", ""),
                "improvement_areas": reflection_result.get("improvement_areas", []),
                "safety_validation": safety_validation,
                "quality_score": quality_scores.get("overall_quality", 0.0),
                "safety_score": quality_scores.get("safety_score", 0.0),
                "recommendations": reflection_result.get("recommendations", []),
                "should_regenerate": reflection_result.get("should_regenerate", False),
                "reflection_metadata": {
                    "domain": coaching_domain,
                    "timestamp": datetime.utcnow().isoformat(),
                    "criteria_used": reflection_criteria or [],
                    "user_profile_considered": bool(user_profile),
                },
            }

        except Exception as e:
            self.logger.error(f"Error during reflection analysis: {e}")
            return self._create_fallback_reflection(original_response)

    def _create_reflection_prompt(
        self,
        original_response: str,
        user_query: str,
        user_profile: Dict[str, Any],
        coaching_domain: str,
        reflection_criteria: List[str],
    ) -> str:
        """Create a domain-specific reflection prompt."""

        domain_prompt = self.domain_prompts.get(
            coaching_domain, self.domain_prompts["general"]
        )

        # Extract user limitations and context
        user_context = self._extract_user_context(user_profile)
        criteria_focus = (
            ", ".join(reflection_criteria)
            if reflection_criteria
            else "safety, accuracy, completeness"
        )

        prompt = f"""
        {domain_prompt}
        
        ORIGINAL USER QUERY:
        {user_query}
        
        USER PROFILE CONTEXT:
        {user_context}
        
        ORIGINAL COACHING RESPONSE TO REVIEW:
        {original_response}
        
        REFLECTION CRITERIA TO FOCUS ON:
        {criteria_focus}
        
        Please provide a thorough reflection analysis with the following structure:
        
        1. SAFETY ASSESSMENT (Critical Priority):
           - Identify any potential safety risks or concerns
           - Rate injury risk level (low/medium/high)
           - Check for contraindications based on user profile
           - Validate form/technique instructions if applicable
        
        2. ACCURACY VERIFICATION:
           - Verify factual correctness of information provided
           - Check appropriateness for user's fitness level
           - Validate recommendations against best practices
        
        3. COMPLETENESS EVALUATION:
           - Assess if the response fully addresses the user's query
           - Identify any important information that was missed
           - Check for necessary warnings or disclaimers
        
        4. CLARITY AND ACTIONABILITY:
           - Evaluate how clear and understandable the response is
           - Assess how actionable the recommendations are
           - Check for specific, measurable guidance
        
        5. IMPROVEMENT RECOMMENDATIONS:
           - Specific suggestions for improving the response
           - Additional safety considerations to include
           - Better ways to communicate the information
        
        6. REGENERATION DECISION:
           - Should the response be regenerated? (yes/no)
           - If yes, what are the critical issues that need addressing?
        
        Provide your reflection in a structured format that focuses primarily on user safety while maintaining coaching quality.
        """

        return prompt

    def _extract_user_context(self, user_profile: Dict[str, Any]) -> str:
        """Extract relevant user context for reflection."""
        context_parts = []

        if user_profile.get("fitness_level"):
            context_parts.append(f"Fitness Level: {user_profile['fitness_level']}")

        if user_profile.get("goals"):
            goals = (
                user_profile["goals"]
                if isinstance(user_profile["goals"], list)
                else [user_profile["goals"]]
            )
            context_parts.append(f"Goals: {', '.join(goals)}")

        if user_profile.get("limitations"):
            limitations = (
                user_profile["limitations"]
                if isinstance(user_profile["limitations"], list)
                else [user_profile["limitations"]]
            )
            context_parts.append(f"Limitations/Injuries: {', '.join(limitations)}")

        if user_profile.get("medical_conditions"):
            conditions = (
                user_profile["medical_conditions"]
                if isinstance(user_profile["medical_conditions"], list)
                else [user_profile["medical_conditions"]]
            )
            context_parts.append(f"Medical Conditions: {', '.join(conditions)}")

        if user_profile.get("allergies"):
            allergies = (
                user_profile["allergies"]
                if isinstance(user_profile["allergies"], list)
                else [user_profile["allergies"]]
            )
            context_parts.append(f"Allergies: {', '.join(allergies)}")

        if user_profile.get("experience_years"):
            context_parts.append(
                f"Experience: {user_profile['experience_years']} years"
            )

        return (
            "\n".join(context_parts)
            if context_parts
            else "No specific user profile information available."
        )

    async def _get_llm_reflection(self, reflection_prompt: str) -> Dict[str, Any]:
        """Get reflection feedback from the LLM."""

        messages = [
            SystemMessage(
                content="You are a fitness coaching safety expert providing detailed reflection analysis."
            ),
            HumanMessage(content=reflection_prompt),
        ]

        try:
            response = await self.llm.ainvoke(messages)
            reflection_content = response.content

            # Parse the reflection response
            return self._parse_reflection_response(reflection_content)

        except Exception as e:
            self.logger.error(f"Error getting LLM reflection: {e}")
            return {
                "feedback": "Error occurred during reflection analysis",
                "improvement_areas": ["technical_issue"],
                "recommendations": ["Please try again"],
                "should_regenerate": True,
            }

    def _parse_reflection_response(self, reflection_content: str) -> Dict[str, Any]:
        """Parse the LLM reflection response into structured data."""

        # Simple parsing - in production, you might want more sophisticated parsing
        improvement_areas = []
        recommendations = []
        should_regenerate = False

        # Extract improvement areas (keywords that suggest issues)
        safety_keywords = ["safety", "risk", "injury", "dangerous", "contraindication"]
        accuracy_keywords = ["incorrect", "inaccurate", "wrong", "error", "mistake"]
        completeness_keywords = [
            "missing",
            "incomplete",
            "forgot",
            "omitted",
            "lacking",
        ]

        content_lower = reflection_content.lower()

        for keyword in safety_keywords:
            if keyword in content_lower:
                improvement_areas.append("safety")
                break

        for keyword in accuracy_keywords:
            if keyword in content_lower:
                improvement_areas.append("accuracy")
                break

        for keyword in completeness_keywords:
            if keyword in content_lower:
                improvement_areas.append("completeness")
                break

        # Check if regeneration is recommended
        regeneration_indicators = [
            "regenerate",
            "rewrite",
            "start over",
            "critical issue",
            "unsafe",
        ]
        should_regenerate = any(
            indicator in content_lower for indicator in regeneration_indicators
        )

        # Extract recommendations (lines that contain "recommend", "suggest", "should")
        lines = reflection_content.split("\n")
        for line in lines:
            if any(
                word in line.lower()
                for word in ["recommend", "suggest", "should", "consider"]
            ):
                recommendations.append(line.strip())

        return {
            "feedback": reflection_content,
            "improvement_areas": list(set(improvement_areas)),
            "recommendations": recommendations[:5],  # Limit to top 5
            "should_regenerate": should_regenerate,
        }

    async def _validate_safety(
        self,
        original_response: str,
        user_profile: Dict[str, Any],
        coaching_domain: str,
        reflection_feedback: str,
    ) -> SafetyValidation:
        """Perform detailed safety validation."""

        safety_validation = {
            "injury_risk_score": 0.8,  # Default to safe unless issues found
            "form_safety_validated": True,
            "contraindications_checked": True,
            "progressive_overload_safe": True,
            "safety_warnings": [],
            "safety_recommendations": [],
        }

        # Analyze for safety issues based on reflection feedback
        feedback_lower = reflection_feedback.lower()
        response_lower = original_response.lower()

        # Check for injury risk indicators
        injury_risk_indicators = [
            "high risk",
            "dangerous",
            "injury",
            "unsafe",
            "contraindicated",
        ]
        for indicator in injury_risk_indicators:
            if indicator in feedback_lower:
                safety_validation["injury_risk_score"] = max(
                    0.0, safety_validation["injury_risk_score"] - 0.2
                )
                safety_validation["safety_warnings"].append(
                    f"Potential {indicator} identified"
                )

        # Domain-specific safety checks
        if coaching_domain == "strength":
            if "form" in feedback_lower and any(
                word in feedback_lower for word in ["poor", "incorrect", "wrong"]
            ):
                safety_validation["form_safety_validated"] = False
                safety_validation["safety_warnings"].append(
                    "Form technique concerns identified"
                )

        elif coaching_domain == "nutrition":
            user_allergies = user_profile.get("allergies", [])
            if user_allergies:
                for allergy in user_allergies:
                    if allergy.lower() in response_lower:
                        safety_validation["contraindications_checked"] = False
                        safety_validation["safety_warnings"].append(
                            f"Potential allergen conflict: {allergy}"
                        )

        # Add safety recommendations based on domain
        if safety_validation["injury_risk_score"] < 0.7:
            safety_validation["safety_recommendations"].extend(
                [
                    "Consider adding more safety disclaimers",
                    "Include proper warm-up recommendations",
                    "Emphasize the importance of proper form",
                ]
            )

        return safety_validation

    def _calculate_quality_scores(
        self, reflection_result: Dict[str, Any], safety_validation: SafetyValidation
    ) -> Dict[str, float]:
        """Calculate quality and safety scores based on reflection results."""

        # Safety score based on validation results
        safety_score = safety_validation.get("injury_risk_score", 0.8)

        # Reduce score for safety issues
        if not safety_validation.get("form_safety_validated", True):
            safety_score -= 0.1
        if not safety_validation.get("contraindications_checked", True):
            safety_score -= 0.15
        if not safety_validation.get("progressive_overload_safe", True):
            safety_score -= 0.1

        # Quality score based on improvement areas
        base_quality = 0.8
        improvement_areas = reflection_result.get("improvement_areas", [])

        # Penalize quality for each improvement area
        quality_penalties = {
            "safety": 0.2,
            "accuracy": 0.15,
            "completeness": 0.1,
            "clarity": 0.05,
        }

        for area in improvement_areas:
            penalty = quality_penalties.get(area, 0.05)
            base_quality -= penalty

        # Overall quality combines safety and content quality
        overall_quality = (safety_score * 0.6) + (base_quality * 0.4)

        return {
            "safety_score": max(0.0, min(1.0, safety_score)),
            "content_quality": max(0.0, min(1.0, base_quality)),
            "overall_quality": max(0.0, min(1.0, overall_quality)),
        }

    def _create_fallback_reflection(self, original_response: str) -> Dict[str, Any]:
        """Create a basic fallback reflection when errors occur."""
        return {
            "reflection_feedback": "Unable to perform detailed reflection analysis due to technical issues.",
            "improvement_areas": ["technical_issue"],
            "safety_validation": {
                "injury_risk_score": 0.5,
                "form_safety_validated": False,
                "contraindications_checked": False,
                "progressive_overload_safe": False,
                "safety_warnings": ["Technical issue prevented safety validation"],
                "safety_recommendations": ["Manual review recommended"],
            },
            "quality_score": 0.5,
            "safety_score": 0.5,
            "recommendations": ["Please retry reflection analysis"],
            "should_regenerate": False,
            "reflection_metadata": {
                "domain": "unknown",
                "timestamp": datetime.utcnow().isoformat(),
                "criteria_used": [],
                "user_profile_considered": False,
                "error": True,
            },
        }


# Node function for LangGraph integration
async def reflection_node(state: ReflectionAgentState) -> Dict[str, Any]:
    """
    LangGraph node function for reflection analysis.

    Analyzes the most recent coaching response and provides
    reflection feedback and safety validation.
    """

    logger = logging.getLogger(__name__)
    logger.info("--- Reflection Node Execution ---")

    try:
        # Check if reflection is enabled and needed
        if not state.get("reflection_enabled", False):
            logger.info("Reflection disabled, skipping reflection node")
            return {"reflection_metadata": {"reflection_count": 0, "skipped": True}}

        reflection_metadata = state.get("reflection_metadata", {})
        max_reflections = reflection_metadata.get("max_reflections", 2)
        current_count = reflection_metadata.get("reflection_count", 0)

        if current_count >= max_reflections:
            logger.info(f"Maximum reflections ({max_reflections}) reached, skipping")
            return {
                "reflection_metadata": {
                    "reflection_count": current_count,
                    "max_reached": True,
                }
            }

        # Get the response to reflect upon
        messages = state.get("messages", [])
        if not messages:
            logger.warning("No messages found for reflection")
            return {
                "reflection_metadata": {
                    "reflection_count": current_count,
                    "error": "no_messages",
                }
            }

        # Find the most recent AI response
        ai_response = None
        user_query = None

        for i in range(len(messages) - 1, -1, -1):
            message = messages[i]
            if isinstance(message, AIMessage) and ai_response is None:
                ai_response = message.content
            elif isinstance(message, HumanMessage) and user_query is None:
                user_query = message.content

            if ai_response and user_query:
                break

        if not ai_response:
            logger.warning("No AI response found for reflection")
            return {
                "reflection_metadata": {
                    "reflection_count": current_count,
                    "error": "no_ai_response",
                }
            }

        # Determine coaching domain from state
        current_agent = state.get("current_reflection_agent", "general")
        coaching_domain = (
            current_agent.replace("_coach", "").replace("_agent", "")
            if current_agent
            else "general"
        )

        # Initialize reflection agent
        reflection_agent = FitnessReflectionAgent()

        # Perform reflection
        reflection_result = await reflection_agent.reflect_on_response(
            original_response=ai_response,
            user_query=user_query or "No specific query available",
            user_profile=state.get("user_profile", {}),
            coaching_domain=coaching_domain,
            reflection_criteria=state.get("reflection_criteria", []),
        )

        # Update reflection metadata
        updated_metadata = {
            **reflection_metadata,
            "reflection_count": current_count + 1,
            "reflection_feedback": reflection_result["reflection_feedback"],
            "safety_validated": reflection_result["safety_validation"][
                "injury_risk_score"
            ]
            >= 0.7,
            "quality_score": reflection_result["quality_score"],
            "improvement_areas": reflection_result["improvement_areas"],
        }

        # Prepare state updates
        updates = {
            "reflection_metadata": updated_metadata,
            "safety_validation": reflection_result["safety_validation"],
            "original_response": (
                ai_response if current_count == 0 else state.get("original_response")
            ),
            "response_quality_before": (
                reflection_result["quality_score"]
                if current_count == 0
                else state.get("response_quality_before")
            ),
        }

        logger.info(
            f"Reflection completed. Quality: {reflection_result['quality_score']:.2f}, Safety: {reflection_result['safety_score']:.2f}"
        )

        return updates

    except Exception as e:
        logger.error(f"Error in reflection node: {e}")
        return {
            "reflection_metadata": {
                "reflection_count": current_count + 1,
                "error": str(e),
                "reflection_feedback": "Error occurred during reflection analysis",
            }
        }
