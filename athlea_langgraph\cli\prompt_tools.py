"""
CLI tools for prompt management and LangSmith integration
Usage: python -m athlea_langgraph.cli.prompt_tools [command] [args]
"""

import argparse
import sys
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from prompt_system import prompt_registry


class PromptCLI:
    """Command-line interface for prompt management"""

    def __init__(self):
        self.registry = prompt_registry

    def list_prompts(self):
        """List all available prompts"""
        print("📋 Available Prompts:")
        print("=" * 50)
        
        for name, prompt in self.registry.prompts.items():
            tested_marker = "✅" if prompt.metadata.tested_in_langsmith else "⏳"
            print(f"{tested_marker} {name} (v{prompt.metadata.version})")
            print(f"   Description: {prompt.metadata.description}")
            print(f"   Type: {prompt.metadata.prompt_type}")
            print(f"   Tags: {', '.join(prompt.metadata.tags)}")
            print()

    def export_for_langsmith(self, prompt_name: str, output_file: Optional[str] = None):
        """Export prompt to LangSmith format"""
        try:
            if output_file is None:
                output_file = f"langsmith_exports/{prompt_name}_langsmith.txt"
            
            # Ensure output directory exists
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            
            content = self.registry.export_for_langsmith(prompt_name, output_file)
            
            print(f"🚀 Exported '{prompt_name}' for LangSmith testing")
            print(f"📁 File: {output_file}")
            print("=" * 50)
            print("📋 COPY THIS TO LANGSMITH PLAYGROUND:")
            print("=" * 50)
            print(content)
            print("=" * 50)
            print("🎯 Next steps:")
            print("1. Copy the content above to LangSmith Playground")
            print("2. Test and iterate in LangSmith")
            print(f"3. Use 'import-from-langsmith {prompt_name}' to bring optimizations back")
            
        except Exception as e:
            print(f"❌ Error exporting prompt: {e}")

    def import_from_langsmith(self, prompt_name: str, langsmith_file: str, version_bump: str = "patch"):
        """Import optimized prompt from LangSmith"""
        try:
            # Read LangSmith content
            with open(langsmith_file, 'r', encoding='utf-8') as f:
                langsmith_content = f.read()
            
            # Import into registry
            self.registry.import_from_langsmith(prompt_name, langsmith_content, version_bump)
            
            # Save updated prompt back to JSON
            self.registry.save_to_json(prompt_name)
            
            print(f"✅ Successfully imported LangSmith optimizations for '{prompt_name}'")
            print(f"🔄 Updated JSON file with new version")
            print("🎯 Next steps:")
            print("1. Restart LangGraph dev server to pick up changes")
            print("2. Test the updated prompt in LangGraph Studio")
            print("3. Run regression tests to validate improvements")
            
        except Exception as e:
            print(f"❌ Error importing prompt: {e}")

    def show_prompt(self, prompt_name: str, format_type: str = "langraph"):
        """Show prompt in specified format"""
        try:
            prompt = self.registry.get_prompt(prompt_name)
            
            print(f"📋 Prompt: {prompt_name} (v{prompt.metadata.version})")
            print("=" * 50)
            
            if format_type == "langsmith":
                content = self.registry.get_langsmith_format(prompt_name)
                print("🎯 LangSmith Format (with {{}} variables):")
            else:
                content = prompt.template.system
                print("🔧 LangGraph Format (with {} variables):")
            
            print("=" * 50)
            print(content)
            print("=" * 50)
            
            # Show metadata
            print(f"📊 Metadata:")
            print(f"   Version: {prompt.metadata.version}")
            print(f"   Tested in LangSmith: {prompt.metadata.tested_in_langsmith}")
            print(f"   Temperature: {prompt.variables.temperature}")
            print(f"   Max Tokens: {prompt.variables.max_tokens}")
            
        except Exception as e:
            print(f"❌ Error showing prompt: {e}")

    def validate_prompt(self, prompt_name: str):
        """Validate prompt formatting and variables"""
        try:
            prompt = self.registry.get_prompt(prompt_name)
            
            print(f"🔍 Validating prompt: {prompt_name}")
            print("=" * 50)
            
            # Check for variable consistency
            langraph_content = prompt.template.system
            langsmith_content = self.registry.get_langsmith_format(prompt_name)
            
            # Extract variables
            import re
            langraph_vars = set(re.findall(r'\{(\w+)\}', langraph_content))
            langsmith_vars = set(re.findall(r'\{\{(\w+)\}\}', langsmith_content))
            
            print(f"✅ LangGraph variables found: {langraph_vars}")
            print(f"✅ LangSmith variables found: {langsmith_vars}")
            
            # Check for unmapped variables
            mapped_vars = set(self.registry.VARIABLE_MAPPINGS["langraph_to_langsmith"].keys())
            unmapped = []
            for var in langraph_vars:
                if f"{{{var}}}" not in mapped_vars:
                    unmapped.append(var)
            
            if unmapped:
                print(f"⚠️  Unmapped variables: {unmapped}")
                print("   These will not convert properly to LangSmith format")
            else:
                print("✅ All variables properly mapped")
            
            print("✅ Validation complete")
            
        except Exception as e:
            print(f"❌ Error validating prompt: {e}")

    def create_test_scenario(self, prompt_name: str):
        """Create a test scenario file for the prompt"""
        try:
            prompt = self.registry.get_prompt(prompt_name)
            
            # Create test scenario based on examples
            test_scenarios = {
                "prompt_name": prompt_name,
                "version": prompt.metadata.version,
                "test_cases": []
            }
            
            # Use existing examples if available
            if prompt.template.examples:
                for i, example in enumerate(prompt.template.examples):
                    test_scenarios["test_cases"].append({
                        "test_name": f"example_{i+1}",
                        "user_input": example.get("user", ""),
                        "expected_elements": ["coaching_advice", "appropriate_tone"],
                        "avoid_elements": ["medical_advice", "inappropriate_content"]
                    })
            else:
                # Create default test cases
                test_scenarios["test_cases"] = [
                    {
                        "test_name": "basic_coaching_query",
                        "user_input": "I need help with my training",
                        "expected_elements": ["coaching_advice", "supportive_tone"],
                        "avoid_elements": ["medical_advice"]
                    }
                ]
            
            # Save test scenario
            import json
            output_file = f"tests/prompt_quality/scenarios/{prompt_name}_scenarios.json"
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(test_scenarios, f, indent=2)
            
            print(f"✅ Created test scenarios: {output_file}")
            print("🎯 Use these scenarios to test prompt consistency")
            
        except Exception as e:
            print(f"❌ Error creating test scenarios: {e}")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Athlea Prompt Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all prompts
  python -m athlea_langgraph.cli.prompt_tools list

  # Export mental coach for LangSmith
  python -m athlea_langgraph.cli.prompt_tools export psychology_mindset_coach

  # Show prompt in LangSmith format
  python -m athlea_langgraph.cli.prompt_tools show psychology_mindset_coach --format langsmith

  # Import optimized prompt from LangSmith
  python -m athlea_langgraph.cli.prompt_tools import psychology_mindset_coach mental_coach_optimized.txt

  # Validate prompt formatting
  python -m athlea_langgraph.cli.prompt_tools validate psychology_mindset_coach
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # List command
    subparsers.add_parser('list', help='List all available prompts')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export prompt for LangSmith')
    export_parser.add_argument('prompt_name', help='Name of prompt to export')
    export_parser.add_argument('-o', '--output', help='Output file path')
    
    # Import command
    import_parser = subparsers.add_parser('import', help='Import prompt from LangSmith')
    import_parser.add_argument('prompt_name', help='Name of prompt to update')
    import_parser.add_argument('langsmith_file', help='File containing LangSmith content')
    import_parser.add_argument('--version-bump', choices=['patch', 'minor', 'major'], 
                              default='patch', help='Version bump type')
    
    # Show command
    show_parser = subparsers.add_parser('show', help='Show prompt content')
    show_parser.add_argument('prompt_name', help='Name of prompt to show')
    show_parser.add_argument('--format', choices=['langraph', 'langsmith'], 
                            default='langraph', help='Format to display')
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate prompt formatting')
    validate_parser.add_argument('prompt_name', help='Name of prompt to validate')
    
    # Test scenarios command
    test_parser = subparsers.add_parser('create-tests', help='Create test scenarios')
    test_parser.add_argument('prompt_name', help='Name of prompt to create tests for')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = PromptCLI()
    
    if args.command == 'list':
        cli.list_prompts()
    elif args.command == 'export':
        cli.export_for_langsmith(args.prompt_name, args.output)
    elif args.command == 'import':
        cli.import_from_langsmith(args.prompt_name, args.langsmith_file, args.version_bump)
    elif args.command == 'show':
        cli.show_prompt(args.prompt_name, args.format)
    elif args.command == 'validate':
        cli.validate_prompt(args.prompt_name)
    elif args.command == 'create-tests':
        cli.create_test_scenario(args.prompt_name)


if __name__ == "__main__":
    main() 