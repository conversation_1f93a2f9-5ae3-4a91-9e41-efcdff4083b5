#!/usr/bin/env python3
"""
Test runner for MCP servers.
Runs all tests and provides a comprehensive report.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print("=" * 60)

    try:
        result = subprocess.run(
            cmd, cwd=Path(__file__).parent, capture_output=True, text=True, check=False
        )

        if result.stdout:
            print("STDOUT:")
            print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")

        return result.returncode == 0

    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False


def main():
    """Run all MCP server tests."""
    print("🚀 Starting MCP Server Test Suite")
    print(f"Working directory: {Path.cwd()}")

    # Ensure we're in the right directory
    if not Path("mcp_servers").exists():
        print(
            "❌ Error: mcp_servers directory not found. Please run from python-langgraph directory."
        )
        sys.exit(1)

    # Test configurations
    test_configs = [
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/strength_mcp/tests/",
                "-v",
                "--tb=short",
            ],
            "description": "Strength MCP Server Tests",
        },
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/nutrition_mcp/tests/",
                "-v",
                "--tb=short",
            ],
            "description": "Nutrition MCP Server Tests",
        },
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/cardio_mcp/tests/",
                "-v",
                "--tb=short",
            ],
            "description": "Cardio MCP Server Tests",
        },
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/external_mcp/tests/",
                "-v",
                "--tb=short",
            ],
            "description": "External MCP Server Tests",
        },
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/test_integration.py",
                "-v",
                "--tb=short",
                "-m",
                "integration",
            ],
            "description": "MCP Server Integration Tests",
        },
        {
            "cmd": [
                "python",
                "-m",
                "pytest",
                "mcp_servers/",
                "-v",
                "--tb=short",
                "--cov=mcp_servers",
                "--cov-report=html",
                "--cov-report=term",
            ],
            "description": "All Tests with Coverage Report",
        },
    ]

    # Run individual test suites
    results = []
    for config in test_configs[:-1]:  # Exclude coverage test for now
        success = run_command(config["cmd"], config["description"])
        results.append((config["description"], success))

    # Run coverage test if all others passed
    all_passed = all(result[1] for result in results)
    if all_passed:
        print("\n🎉 All individual test suites passed! Running coverage analysis...")
        success = run_command(test_configs[-1]["cmd"], test_configs[-1]["description"])
        results.append((test_configs[-1]["description"], success))
    else:
        print("\n⚠️  Some tests failed. Skipping coverage analysis.")

    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed = 0
    failed = 0

    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} - {description}")
        if success:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {len(results)} test suites")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")

    if failed == 0:
        print("\n🎉 All tests passed! Your MCP servers are working correctly.")
        return 0
    else:
        print(f"\n❌ {failed} test suite(s) failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
