"""
External Services Domain Schemas

Pydantic schemas for strict input/output validation of external service tools.
These schemas enforce tool-agent contracts and automatic validation.
"""

from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class LocationSearchInput(BaseModel):
    """Input schema for location search using Azure Maps."""

    query: str = Field(..., description="Location search query")
    country_filter: Optional[str] = Field(None, description="ISO country code filter")
    category_filter: Optional[List[str]] = Field(None, description="Category filters")
    radius_km: Optional[float] = Field(10, description="Search radius in kilometers")
    max_results: Optional[int] = Field(5, description="Maximum number of results")
    include_details: Optional[bool] = Field(
        True, description="Include detailed information"
    )


class WebSearchInput(BaseModel):
    """Input schema for web search using Google Search API."""

    query: str = Field(..., description="Search query string")
    search_type: Optional[str] = Field("web", description="Type of search")
    date_range: Optional[str] = Field("all", description="Time range for results")
    site_filter: Optional[str] = Field(None, description="Site filter")
    max_results: Optional[int] = Field(10, description="Maximum results")
    safe_search: Optional[bool] = Field(True, description="Enable safe search")


class AirtableQueryInput(BaseModel):
    """Input schema for Airtable queries."""

    base_id: str = Field(..., description="Airtable base identifier")
    table_name: str = Field(..., description="Table name to query")
    filter_formula: Optional[str] = Field(None, description="Filter formula")
    fields: Optional[List[str]] = Field(None, description="Specific fields to retrieve")
    sort: Optional[List[Dict[str, str]]] = Field(None, description="Sort configuration")
    max_records: Optional[int] = Field(20, description="Maximum records to return")


class WeatherInput(BaseModel):
    """Input schema for weather data requests."""

    location: str = Field(..., description="Location for weather data")
    data_type: Optional[str] = Field("current", description="Type of weather data")
    forecast_days: Optional[int] = Field(5, description="Number of forecast days")
    include_hourly: Optional[bool] = Field(False, description="Include hourly data")
    units: Optional[str] = Field("metric", description="Unit system")
    weather_alerts: Optional[bool] = Field(True, description="Include weather alerts")


class WikipediaSearchInput(BaseModel):
    """Input schema for Wikipedia searches."""

    query: str = Field(..., description="Wikipedia search query")
    language: Optional[str] = Field("en", description="Language code")
    max_results: Optional[int] = Field(3, description="Maximum articles")
    summary_sentences: Optional[int] = Field(3, description="Summary sentences")
    include_images: Optional[bool] = Field(False, description="Include images")


class AirtableQuerySchema(BaseModel):
    """Schema for Airtable query tool."""

    base_id: str = Field(..., description="Airtable base ID")
    table_name: str = Field(..., description="Name of the table to query")
    filter_formula: Optional[str] = Field(None, description="Airtable filter formula")
    fields: Optional[List[str]] = Field(None, description="Specific fields to retrieve")
    max_records: Optional[int] = Field(
        100, ge=1, le=100, description="Maximum number of records to return"
    )
    sort: Optional[List[dict]] = Field(
        None, description="Sort configuration with field and direction"
    )
    view: Optional[str] = Field(None, description="View name to use for the query")
