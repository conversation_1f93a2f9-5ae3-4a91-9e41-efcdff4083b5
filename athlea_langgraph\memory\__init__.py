"""
Advanced Memory System for LangGraph Integration

Phase 2 Implementation with:
- Multi-domain memory separation
- Advanced retrieval pipelines
- Memory summarization engine
- Intelligent decay mechanisms
- Comprehensive analytics and monitoring
"""

from .advanced_memory_manager import AdvancedMemoryManager
from .advanced_retrieval import (
    AdvancedRetrievalPipeline,
    RankedMemory,
    RetrievalContext,
    SearchConfig,
)
from .analytics import AnalyticsConfig, MemoryAnalyticsEngine
from .decay_manager import DecayConfig, MemoryDecayManager

# Phase 2: Advanced Memory Components
from .domain_manager import DomainMemoryManager

# Core adapters
from .mem0_adapter import Mem0Adapter, MemoryEntry, MemoryType

# Legacy compatibility
from .mongo_memory import MongoSaver

# Schema exports
from .schemas import (
    AdvancedMemoryMetadata,
    AnalyticsReport,
    CoachingDomain,
    DateRange,
    DomainClassification,
    HealthStatus,
    HealthStatusLevel,
    ImportanceDecayMode,
    ImportanceScore,
    MemoryAnalyticsEvent,
    MemoryDomainMetadata,
    MemoryOperationType,
    MemoryReference,
    PerformanceMetrics,
    RetrievalMetrics,
    StorageMetrics,
    SummaryLevel,
    SummaryMetadata,
)
from .summarization_engine import MemorySummarizationEngine, SummarizationConfig

__all__ = [
    # Core components
    "Mem0Adapter",
    "MemoryType",
    "MemoryEntry",
    "MongoSaver",
    # Phase 2 managers
    "DomainMemoryManager",
    "AdvancedRetrievalPipeline",
    "MemorySummarizationEngine",
    "MemoryDecayManager",
    "MemoryAnalyticsEngine",
    "AdvancedMemoryManager",
    # Configuration classes
    "SearchConfig",
    "RetrievalContext",
    "SummarizationConfig",
    "DecayConfig",
    "AnalyticsConfig",
    # Data classes
    "RankedMemory",
    "CoachingDomain",
    "DomainClassification",
    "MemoryDomainMetadata",
    "MemoryAnalyticsEvent",
    "MemoryOperationType",
    "RetrievalMetrics",
    "StorageMetrics",
    "PerformanceMetrics",
    "AnalyticsReport",
    "HealthStatus",
    "HealthStatusLevel",
    "DateRange",
    "AdvancedMemoryMetadata",
    "SummaryLevel",
    "ImportanceScore",
    "ImportanceDecayMode",
    "SummaryMetadata",
    "MemoryReference",
]
