# Configuration and Constants

This document describes the centralized configuration and constants management system for the Athlea Python LangGraph Coaching System.

## Overview

Our system uses a unified approach to manage both dynamic configuration (from environment variables) and static configuration (application-wide constants). This provides a single source of truth, enhances maintainability, and ensures type safety.

-   **Configuration (`config`)**: A type-safe system using Pydantic models to manage settings from environment variables.
-   **Constants (`constants`)**: A module for centralizing default values, enums, and message templates, replacing "magic values."

---

## 1. Dynamic Configuration (`athlea_langgraph/config`)

The configuration system uses Pydantic models to provide type-safe, validated, and environment-specific settings. This replaces scattered `os.getenv()` calls with a structured, testable approach.

### Configuration Usage

Accessing configuration is simple and type-safe:

```python
from athlea_langgraph.config import config

# Access Azure OpenAI configuration
api_key = config.azure_openai.api_key
endpoint = config.azure_openai.endpoint

# Access database configuration
mongodb_uri = config.database.mongodb_uri
```

### Configuration Structure

The system uses a main `BaseConfig` class that composes multiple service-specific configurations:

-   `AzureOpenAIConfig`: Settings for Azure OpenAI.
-   `AzureMapsConfig`: Settings for Azure Maps.
-   `AzureSearchConfig`: Settings for Azure Search.
-   `DatabaseConfig`: Settings for MongoDB.
-   `AirtableConfig`: Settings for Airtable.
-   `Mem0Config`: Settings for the Mem0 memory service.

All configuration is driven by environment variables, which are documented in the `.env.example` file. This approach allows for easy switching between `development`, `staging`, and `production` environments.

### Benefits of this Approach

1.  **Type Safety**: Pydantic provides runtime type checking.
2.  **Centralization**: All configuration is managed in one place.
3.  **Validation**: Clear error messages for missing or invalid variables.
4.  **Testability**: Easy to mock configurations for unit tests.
5.  **IDE Support**: Better autocomplete and error detection.

---

## 2. Static Constants (`athlea_langgraph/constants`)

The constants module provides a centralized location for values that are fixed within the application. This avoids "magic numbers" and "magic strings," making the code cleaner and easier to maintain.

The module is organized into three submodules:

-   **`enums.py`**: For type-safe enumerations.
-   **`defaults.py`**: For default values (e.g., timeouts, API retries).
-   **`messages.py`**: For user-facing message templates.

### Using Constants

#### Enums
Enums provide type safety for categorical data.

**Before:**
```python
# Prone to typos, no validation
workout_intensity = "moderate"
```

**After:**
```python
from athlea_langgraph.constants import IntensityLevel

# Type-safe, provides autocompletion
workout_intensity = IntensityLevel.MODERATE
```
*Available Enums*: `IntensityLevel`, `TimeUnit`, `HTTPContentType`, `LogLevel`, `OperationStatus`.

#### Default Values
Default values centralize configuration for things like timeouts and API parameters.

**Before:**
```python
# Magic number, hard to find and update
response = await client.get("/api/data", timeout=30)
```

**After:**
```python
from athlea_langgraph.constants import DEFAULT_API_TIMEOUT_SECONDS

# Centralized, self-documenting
response = await client.get(
    "/api/data", 
    timeout=DEFAULT_API_TIMEOUT_SECONDS
)
```
*Available Defaults*: Timeouts, training defaults (sets, reps), API retry counts, streaming buffer sizes.

#### Message Templates
Message templates ensure consistent communication for errors, success messages, and coaching responses.

**Before:**
```python
# Inconsistent, hard to manage
error_msg = f"The operation timed out after 30 seconds"
```

**After:**
```python
from athlea_langgraph.constants import ERROR_MESSAGES

# Centralized, allows for parameterization
error_msg = ERROR_MESSAGES["TIMEOUT"].format(timeout=30)
```
*Available Messages*: Error messages, success messages, coaching responses, thinking indicators.

By using this combined configuration and constants system, we ensure that the application is robust, easy to maintain, and simple to configure for different environments. 