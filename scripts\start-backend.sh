#!/bin/bash

# Athlea Backend Startup Script (Bash version)
# Simple script to start the FastAPI backend server

echo "🚀 Starting Athlea Backend Server..."
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "athlea_langgraph/api/main.py" ]; then
    echo "❌ Backend main.py not found. Make sure you're in the project root directory."
    exit 1
fi

# Set default port if not provided
export PORT=${PORT:-8000}
export HOST=${HOST:-0.0.0.0}

echo "🌐 Server will start on $HOST:$PORT"
echo "📡 API endpoints will be available at:"
echo "   • Health: http://localhost:$PORT/api/health"
echo "   • Coaching: http://localhost:$PORT/api/coaching"
echo "   • Onboarding: http://localhost:$PORT/api/onboarding"
echo "=================================================="
echo "⚡ Starting server... (Press Ctrl+C to stop)"
echo ""

# Start the server
python -m athlea_langgraph.api.main 