"""
Utilities module for athlea_langgraph package.

This module provides various utility functions and classes for the coaching system,
including performance monitoring, streaming helpers, embeddings, and prompt management.
"""

from .embedding_util import (
    extract_deployment_id_from_url,
    generate_embedding,
    get_embedding_config_for_mem0,
    test_embedding_util,
)
from .performance_monitor import PerformanceMetrics, PerformanceMonitor
from .prompt_loader import (
    PromptLoader,
    PromptNotFoundError,
    PromptValidationError,
    PromptVersionError,
    get_prompt_loader,
    load_prompt,
)
from .prompt_migration import (
    PromptExtractor,
    PromptMigrator,
    create_sample_prompts,
    run_migration,
)

# Prompt management system
from .prompt_models import (
    ChangelogEntry,
    PromptConfig,
    PromptContent,
    PromptExample,
    PromptMetadata,
    PromptType,
    PromptValidation,
    PromptVariables,
)
from .streaming_helpers import (
    ProgressTracker,
    StreamEvent,
    StreamEventType,
    StreamingCoordinator,
    format_specialist_response,
)
from .validate_streaming_format import *

__all__ = [
    # Performance monitoring
    "PerformanceMonitor",
    "PerformanceMetrics",
    # Streaming helpers
    "StreamingCoordinator",
    "StreamEvent",
    "StreamEventType",
    "ProgressTracker",
    "format_specialist_response",
    # Embedding utilities
    "generate_embedding",
    "get_embedding_config_for_mem0",
    "extract_deployment_id_from_url",
    "test_embedding_util",
    # Prompt management - Models
    "PromptConfig",
    "PromptMetadata",
    "PromptContent",
    "PromptVariables",
    "PromptValidation",
    "PromptType",
    "ChangelogEntry",
    "PromptExample",
    # Prompt management - Loader
    "PromptLoader",
    "PromptNotFoundError",
    "PromptValidationError",
    "PromptVersionError",
    "get_prompt_loader",
    "load_prompt",
    # Prompt management - Migration
    "PromptExtractor",
    "PromptMigrator",
    "run_migration",
    "create_sample_prompts",
]
