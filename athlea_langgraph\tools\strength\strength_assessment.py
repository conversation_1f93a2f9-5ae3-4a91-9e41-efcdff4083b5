"""
Comprehensive Strength Assessment Tool

This tool provides a complete strength assessment system including:
- Movement screening and functional assessment
- Experience level evaluation
- Strength testing protocols
- Equipment availability assessment
- Goal-specific recommendations
- Imbalance detection and injury risk assessment
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator

from ..base_tool import BaseDomainTool

logger = logging.getLogger(__name__)


class MovementScreenInput(BaseModel):
    """Input schema for movement screening assessment."""

    overhead_squat_quality: int = Field(
        ..., ge=1, le=5, description="Overhead squat quality (1=poor, 5=excellent)"
    )
    single_leg_balance: int = Field(
        ..., ge=1, le=5, description="Single leg balance quality (1=poor, 5=excellent)"
    )
    shoulder_mobility: int = Field(
        ..., ge=1, le=5, description="Shoulder mobility quality (1=poor, 5=excellent)"
    )
    hip_mobility: int = Field(
        ..., ge=1, le=5, description="Hip mobility quality (1=poor, 5=5excellent)"
    )
    core_stability: int = Field(
        ..., ge=1, le=5, description="Core stability quality (1=poor, 5=excellent)"
    )
    pain_areas: List[str] = Field(
        default=[], description="Areas with current pain or discomfort"
    )
    previous_injuries: List[str] = Field(default=[], description="History of injuries")


class StrengthTestingInput(BaseModel):
    """Input schema for strength testing protocols."""

    bodyweight_kg: float = Field(..., gt=0, description="User's bodyweight in kg")
    squat_1rm: Optional[float] = Field(None, ge=0, description="Back squat 1RM in kg")
    bench_press_1rm: Optional[float] = Field(
        None, ge=0, description="Bench press 1RM in kg"
    )
    deadlift_1rm: Optional[float] = Field(None, ge=0, description="Deadlift 1RM in kg")
    overhead_press_1rm: Optional[float] = Field(
        None, ge=0, description="Overhead press 1RM in kg"
    )
    pull_up_max: Optional[int] = Field(None, ge=0, description="Maximum pull-ups")
    push_up_max: Optional[int] = Field(None, ge=0, description="Maximum push-ups")
    plank_max_seconds: Optional[int] = Field(
        None, ge=0, description="Maximum plank hold in seconds"
    )


class ExperienceInput(BaseModel):
    """Input schema for experience level assessment."""

    training_years: float = Field(
        ..., ge=0, description="Years of strength training experience"
    )
    training_frequency: int = Field(
        ..., ge=1, le=7, description="Training sessions per week"
    )
    previous_programs: List[str] = Field(
        default=[], description="Previous training programs completed"
    )
    competition_experience: bool = Field(
        default=False, description="Has competition experience"
    )
    coaching_history: bool = Field(default=False, description="Has worked with a coach")
    technique_confidence: int = Field(
        ..., ge=1, le=5, description="Self-rated technique confidence (1=low, 5=high)"
    )


class EquipmentInput(BaseModel):
    """Input schema for equipment availability assessment."""

    gym_access: bool = Field(..., description="Has access to a full gym")
    home_equipment: List[str] = Field(
        default=[], description="Available home equipment"
    )
    preferred_setting: str = Field(
        ..., description="Preferred training environment (gym/home/<USER>"
    )
    budget_constraints: bool = Field(
        default=False, description="Has budget constraints for equipment"
    )


class GoalsInput(BaseModel):
    """Input schema for training goals assessment."""

    primary_goals: List[str] = Field(
        ..., min_length=1, description="Primary training goals"
    )
    secondary_goals: List[str] = Field(
        default=[], description="Secondary training goals"
    )
    timeline: str = Field(..., description="Target timeline for goals")
    motivation_level: int = Field(
        ..., ge=1, le=5, description="Self-rated motivation level (1=low, 5=high)"
    )
    time_availability: int = Field(
        ..., ge=1, description="Available training time per session (minutes)"
    )


class StrengthAssessmentInput(BaseModel):
    """Complete strength assessment input schema."""

    user_id: str = Field(..., description="Unique user identifier")
    age: int = Field(..., ge=13, le=100, description="User age")
    gender: str = Field(..., description="User gender")
    movement_screen: MovementScreenInput
    strength_testing: StrengthTestingInput
    experience: ExperienceInput
    equipment: EquipmentInput
    goals: GoalsInput
    assessment_date: datetime = Field(default_factory=datetime.now)


class MovementScreenResults(BaseModel):
    """Movement screening assessment results."""

    overall_score: float = Field(
        ..., description="Overall movement quality score (1-5)"
    )
    risk_areas: List[str] = Field(..., description="Identified risk areas")
    mobility_priorities: List[str] = Field(
        ..., description="Priority mobility improvements"
    )
    stability_priorities: List[str] = Field(
        ..., description="Priority stability improvements"
    )
    corrective_exercises: List[Dict[str, Any]] = Field(
        ..., description="Recommended corrective exercises"
    )
    red_flags: List[str] = Field(
        ..., description="Areas requiring professional attention"
    )


class StrengthStandardsResults(BaseModel):
    """Strength standards comparison results."""

    squat_rating: str = Field(..., description="Squat strength rating")
    bench_rating: str = Field(..., description="Bench press strength rating")
    deadlift_rating: str = Field(..., description="Deadlift strength rating")
    overhead_press_rating: str = Field(
        ..., description="Overhead press strength rating"
    )
    overall_strength_level: str = Field(
        ..., description="Overall strength classification"
    )
    strength_ratios: Dict[str, float] = Field(
        ..., description="Relative strength ratios"
    )
    imbalances: List[str] = Field(..., description="Identified strength imbalances")
    population_percentile: int = Field(
        ..., description="Strength percentile for age/gender"
    )


class ExperienceResults(BaseModel):
    """Experience level assessment results."""

    experience_classification: str = Field(
        ..., description="Experience level classification"
    )
    readiness_score: float = Field(..., description="Training readiness score (1-10)")
    technical_competency: str = Field(..., description="Technical skill assessment")
    program_complexity_recommendation: str = Field(
        ..., description="Recommended program complexity"
    )
    supervision_needs: str = Field(..., description="Level of supervision needed")


class RecommendationsOutput(BaseModel):
    """Comprehensive assessment recommendations."""

    immediate_priorities: List[str] = Field(..., description="Immediate focus areas")
    program_type_recommendation: str = Field(
        ..., description="Recommended program type"
    )
    training_frequency_recommendation: int = Field(
        ..., description="Recommended training frequency"
    )
    session_duration_recommendation: int = Field(
        ..., description="Recommended session duration"
    )
    progression_timeline: Dict[str, str] = Field(
        ..., description="Suggested progression timeline"
    )
    equipment_recommendations: List[str] = Field(
        ..., description="Equipment purchase priorities"
    )
    professional_referrals: List[str] = Field(
        ..., description="Recommended professional consultations"
    )


class StrengthAssessmentOutput(BaseModel):
    """Complete strength assessment output schema."""

    user_id: str
    assessment_id: str
    assessment_date: datetime
    movement_screen: MovementScreenResults
    strength_standards: StrengthStandardsResults
    experience_assessment: ExperienceResults
    recommendations: RecommendationsOutput
    overall_assessment_score: float = Field(
        ..., description="Overall assessment score (1-100)"
    )
    next_reassessment_date: datetime = Field(
        ..., description="Recommended next assessment date"
    )


class StrengthAssessmentTool(BaseDomainTool):
    """
    Comprehensive strength assessment tool providing detailed evaluation
    of movement quality, strength levels, experience, and personalized recommendations.
    """

    domain: str = "strength_training"
    name: str = "strength_assessment"
    description: str = (
        "Comprehensive strength and movement assessment with personalized recommendations"
    )

    def __init__(self):
        super().__init__()
        self.strength_standards = self._load_strength_standards()
        self.corrective_exercises = self._load_corrective_exercises()

    def _load_strength_standards(self) -> Dict[str, Dict[str, Dict[str, float]]]:
        """Load strength standards database."""
        return {
            "male": {
                "squat": {
                    "untrained": 1.0,
                    "novice": 1.25,
                    "intermediate": 1.5,
                    "advanced": 1.75,
                    "elite": 2.0,
                },
                "bench_press": {
                    "untrained": 0.75,
                    "novice": 1.0,
                    "intermediate": 1.25,
                    "advanced": 1.5,
                    "elite": 1.75,
                },
                "deadlift": {
                    "untrained": 1.25,
                    "novice": 1.5,
                    "intermediate": 1.75,
                    "advanced": 2.0,
                    "elite": 2.25,
                },
                "overhead_press": {
                    "untrained": 0.5,
                    "novice": 0.65,
                    "intermediate": 0.8,
                    "advanced": 0.95,
                    "elite": 1.1,
                },
            },
            "female": {
                "squat": {
                    "untrained": 0.75,
                    "novice": 1.0,
                    "intermediate": 1.25,
                    "advanced": 1.5,
                    "elite": 1.75,
                },
                "bench_press": {
                    "untrained": 0.5,
                    "novice": 0.65,
                    "intermediate": 0.8,
                    "advanced": 1.0,
                    "elite": 1.2,
                },
                "deadlift": {
                    "untrained": 1.0,
                    "novice": 1.25,
                    "intermediate": 1.5,
                    "advanced": 1.75,
                    "elite": 2.0,
                },
                "overhead_press": {
                    "untrained": 0.35,
                    "novice": 0.5,
                    "intermediate": 0.6,
                    "advanced": 0.75,
                    "elite": 0.9,
                },
            },
        }

    def _load_corrective_exercises(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load corrective exercise database."""
        return {
            "poor_overhead_squat": [
                {
                    "name": "Goblet Squat",
                    "sets": 3,
                    "reps": "8-12",
                    "description": "Improve squat pattern and ankle mobility",
                    "progression": "Increase weight or depth",
                },
                {
                    "name": "Wall Ankle Mobility",
                    "sets": 3,
                    "reps": "10 each leg",
                    "description": "Improve ankle dorsiflexion",
                    "progression": "Increase distance from wall",
                },
            ],
            "poor_shoulder_mobility": [
                {
                    "name": "Band Pull-Aparts",
                    "sets": 3,
                    "reps": "15-20",
                    "description": "Improve posterior chain activation",
                    "progression": "Increase band resistance",
                },
                {
                    "name": "Doorway Chest Stretch",
                    "sets": 3,
                    "reps": "30-60 seconds each arm",
                    "description": "Improve chest and anterior shoulder flexibility",
                    "progression": "Increase stretch duration",
                },
            ],
            "poor_hip_mobility": [
                {
                    "name": "90/90 Hip Stretch",
                    "sets": 3,
                    "reps": "30-60 seconds each side",
                    "description": "Improve hip internal/external rotation",
                    "progression": "Add reach and rotation",
                },
                {
                    "name": "Couch Stretch",
                    "sets": 3,
                    "reps": "30-60 seconds each leg",
                    "description": "Improve hip flexor flexibility",
                    "progression": "Increase depth and duration",
                },
            ],
            "poor_core_stability": [
                {
                    "name": "Dead Bug",
                    "sets": 3,
                    "reps": "5-8 each side",
                    "description": "Improve core stability and coordination",
                    "progression": "Add band resistance or longer holds",
                },
                {
                    "name": "Bird Dog",
                    "sets": 3,
                    "reps": "5-8 each side",
                    "description": "Improve posterior chain stability",
                    "progression": "Add resistance or unstable surface",
                },
            ],
        }

    def assess_movement_screen(
        self, movement_input: MovementScreenInput
    ) -> MovementScreenResults:
        """Assess movement screen results and provide recommendations."""
        scores = [
            movement_input.overhead_squat_quality,
            movement_input.single_leg_balance,
            movement_input.shoulder_mobility,
            movement_input.hip_mobility,
            movement_input.core_stability,
        ]

        overall_score = sum(scores) / len(scores)

        risk_areas = []
        mobility_priorities = []
        stability_priorities = []
        corrective_exercises = []
        red_flags = []

        # Analyze individual movement patterns
        if movement_input.overhead_squat_quality <= 2:
            risk_areas.append("overhead_squat_dysfunction")
            mobility_priorities.extend(["ankle_mobility", "hip_mobility"])
            corrective_exercises.extend(
                self.corrective_exercises["poor_overhead_squat"]
            )

        if movement_input.shoulder_mobility <= 2:
            risk_areas.append("shoulder_impingement_risk")
            mobility_priorities.append("shoulder_mobility")
            corrective_exercises.extend(
                self.corrective_exercises["poor_shoulder_mobility"]
            )

        if movement_input.hip_mobility <= 2:
            risk_areas.append("hip_dysfunction")
            mobility_priorities.append("hip_mobility")
            corrective_exercises.extend(self.corrective_exercises["poor_hip_mobility"])

        if movement_input.core_stability <= 2:
            risk_areas.append("core_instability")
            stability_priorities.append("core_stability")
            corrective_exercises.extend(
                self.corrective_exercises["poor_core_stability"]
            )

        if movement_input.single_leg_balance <= 2:
            risk_areas.append("balance_deficits")
            stability_priorities.append("unilateral_stability")

        # Check for red flags
        if movement_input.pain_areas:
            red_flags.append("current_pain_reported")

        if len([s for s in scores if s <= 2]) >= 3:
            red_flags.append("multiple_movement_dysfunctions")

        return MovementScreenResults(
            overall_score=overall_score,
            risk_areas=risk_areas,
            mobility_priorities=list(set(mobility_priorities)),
            stability_priorities=list(set(stability_priorities)),
            corrective_exercises=corrective_exercises,
            red_flags=red_flags,
        )

    def assess_strength_standards(
        self, strength_input: StrengthTestingInput, gender: str
    ) -> StrengthStandardsResults:
        """Assess strength levels against population standards."""
        bodyweight = strength_input.bodyweight_kg
        gender_standards = self.strength_standards.get(
            gender.lower(), self.strength_standards["male"]
        )

        def get_strength_rating(lift_1rm: Optional[float], lift_type: str) -> str:
            if not lift_1rm:
                return "not_tested"

            ratio = lift_1rm / bodyweight
            standards = gender_standards[lift_type]

            if ratio >= standards["elite"]:
                return "elite"
            elif ratio >= standards["advanced"]:
                return "advanced"
            elif ratio >= standards["intermediate"]:
                return "intermediate"
            elif ratio >= standards["novice"]:
                return "novice"
            else:
                return "untrained"

        squat_rating = get_strength_rating(strength_input.squat_1rm, "squat")
        bench_rating = get_strength_rating(
            strength_input.bench_press_1rm, "bench_press"
        )
        deadlift_rating = get_strength_rating(strength_input.deadlift_1rm, "deadlift")
        overhead_rating = get_strength_rating(
            strength_input.overhead_press_1rm, "overhead_press"
        )

        # Calculate strength ratios
        strength_ratios = {}
        if strength_input.squat_1rm and strength_input.bench_press_1rm:
            strength_ratios["squat_to_bench"] = (
                strength_input.squat_1rm / strength_input.bench_press_1rm
            )

        if strength_input.deadlift_1rm and strength_input.squat_1rm:
            strength_ratios["deadlift_to_squat"] = (
                strength_input.deadlift_1rm / strength_input.squat_1rm
            )

        if strength_input.bench_press_1rm and strength_input.overhead_press_1rm:
            strength_ratios["bench_to_ohp"] = (
                strength_input.bench_press_1rm / strength_input.overhead_press_1rm
            )

        # Detect imbalances
        imbalances = []
        if "squat_to_bench" in strength_ratios:
            if strength_ratios["squat_to_bench"] < 1.2:
                imbalances.append("squat_weakness_relative_to_bench")
            elif strength_ratios["squat_to_bench"] > 2.0:
                imbalances.append("bench_weakness_relative_to_squat")

        if "deadlift_to_squat" in strength_ratios:
            if strength_ratios["deadlift_to_squat"] < 1.1:
                imbalances.append("deadlift_weakness_relative_to_squat")
            elif strength_ratios["deadlift_to_squat"] > 1.4:
                imbalances.append("squat_weakness_relative_to_deadlift")

        # Determine overall strength level
        tested_lifts = [
            r
            for r in [squat_rating, bench_rating, deadlift_rating, overhead_rating]
            if r != "not_tested"
        ]
        if tested_lifts:
            level_scores = {
                "untrained": 1,
                "novice": 2,
                "intermediate": 3,
                "advanced": 4,
                "elite": 5,
            }
            avg_score = sum(level_scores[rating] for rating in tested_lifts) / len(
                tested_lifts
            )

            if avg_score >= 4.5:
                overall_level = "elite"
            elif avg_score >= 3.5:
                overall_level = "advanced"
            elif avg_score >= 2.5:
                overall_level = "intermediate"
            elif avg_score >= 1.5:
                overall_level = "novice"
            else:
                overall_level = "untrained"
        else:
            overall_level = "untested"

        return StrengthStandardsResults(
            squat_rating=squat_rating,
            bench_rating=bench_rating,
            deadlift_rating=deadlift_rating,
            overhead_press_rating=overhead_rating,
            overall_strength_level=overall_level,
            strength_ratios=strength_ratios,
            imbalances=imbalances,
            population_percentile=self._calculate_percentile(overall_level),
        )

    def assess_experience(self, experience_input: ExperienceInput) -> ExperienceResults:
        """Assess training experience and readiness."""
        # Calculate experience classification
        years = experience_input.training_years
        frequency = experience_input.training_frequency
        technique = experience_input.technique_confidence

        experience_score = (
            min(years / 5, 1.0) * 0.4  # Years capped at 5 for scoring
            + min(frequency / 4, 1.0) * 0.3  # Frequency capped at 4 for scoring
            + technique / 5 * 0.3
        )

        if experience_score >= 0.8 and years >= 3:
            classification = "advanced"
        elif experience_score >= 0.6 and years >= 1:
            classification = "intermediate"
        elif experience_score >= 0.4 or years >= 0.5:
            classification = "novice"
        else:
            classification = "beginner"

        # Calculate readiness score
        readiness_factors = [
            experience_input.training_frequency / 7 * 10,  # Frequency component
            experience_input.technique_confidence * 2,  # Technique component
            min(experience_input.training_years, 3) / 3 * 10,  # Experience component
        ]

        if experience_input.coaching_history:
            readiness_factors.append(2)
        if experience_input.competition_experience:
            readiness_factors.append(1)

        readiness_score = min(sum(readiness_factors) / len(readiness_factors), 10)

        # Determine technical competency
        if technique >= 4:
            technical_competency = "high"
        elif technique >= 3:
            technical_competency = "moderate"
        else:
            technical_competency = "developing"

        # Program complexity recommendation
        if classification == "advanced" and technique >= 4:
            program_complexity = "complex_periodization"
        elif classification in ["intermediate", "advanced"]:
            program_complexity = "moderate_periodization"
        else:
            program_complexity = "linear_progression"

        # Supervision needs
        if technique <= 2 or classification == "beginner":
            supervision = "close_supervision_recommended"
        elif technique == 3 or classification == "novice":
            supervision = "periodic_form_checks"
        else:
            supervision = "minimal_supervision"

        return ExperienceResults(
            experience_classification=classification,
            readiness_score=readiness_score,
            technical_competency=technical_competency,
            program_complexity_recommendation=program_complexity,
            supervision_needs=supervision,
        )

    def _calculate_percentile(self, strength_level: str) -> int:
        """Calculate approximate population percentile for strength level."""
        percentiles = {
            "untested": 0,
            "untrained": 10,
            "novice": 35,
            "intermediate": 65,
            "advanced": 85,
            "elite": 95,
        }
        return percentiles.get(strength_level, 0)

    def generate_recommendations(
        self,
        movement_results: MovementScreenResults,
        strength_results: StrengthStandardsResults,
        experience_results: ExperienceResults,
        equipment_input: EquipmentInput,
        goals_input: GoalsInput,
    ) -> RecommendationsOutput:
        """Generate comprehensive recommendations based on assessment results."""

        immediate_priorities = []

        # Movement screen priorities
        if movement_results.red_flags:
            immediate_priorities.append("address_movement_dysfunctions")
        if movement_results.overall_score < 3:
            immediate_priorities.append("corrective_exercise_program")

        # Strength priorities
        if len(strength_results.imbalances) >= 2:
            immediate_priorities.append("correct_strength_imbalances")
        if strength_results.overall_strength_level == "untrained":
            immediate_priorities.append("basic_strength_development")

        # Experience priorities
        if experience_results.technical_competency == "developing":
            immediate_priorities.append("technique_development")
        if experience_results.readiness_score < 5:
            immediate_priorities.append("training_consistency")

        # Program type recommendation
        if "corrective_exercise_program" in immediate_priorities:
            program_type = "corrective_movement_focus"
        elif strength_results.overall_strength_level in ["untrained", "novice"]:
            program_type = "basic_strength_building"
        elif "strength" in goals_input.primary_goals:
            program_type = "strength_focused"
        elif "muscle_gain" in goals_input.primary_goals:
            program_type = "hypertrophy_focused"
        else:
            program_type = "general_fitness"

        # Training frequency recommendation
        if experience_results.experience_classification == "beginner":
            frequency = min(
                3, goals_input.time_availability // 120
            )  # Max 3x/week for beginners
        elif experience_results.experience_classification == "novice":
            frequency = min(4, goals_input.time_availability // 100)
        else:
            frequency = min(6, goals_input.time_availability // 90)

        frequency = max(2, frequency)  # Minimum 2x/week

        # Session duration recommendation
        if experience_results.experience_classification == "beginner":
            session_duration = min(60, goals_input.time_availability)
        elif experience_results.experience_classification == "novice":
            session_duration = min(75, goals_input.time_availability)
        else:
            session_duration = min(90, goals_input.time_availability)

        # Progression timeline
        timeline_mapping = {
            "beginner": {
                "technique_focus": "4-6_weeks",
                "strength_gains": "8-12_weeks",
            },
            "novice": {
                "strength_gains": "6-8_weeks",
                "intermediate_progressions": "12-16_weeks",
            },
            "intermediate": {
                "strength_gains": "4-6_weeks",
                "advanced_techniques": "8-12_weeks",
            },
            "advanced": {"strength_gains": "2-4_weeks", "peaking_phases": "6-8_weeks"},
        }

        progression_timeline = timeline_mapping.get(
            experience_results.experience_classification, timeline_mapping["beginner"]
        )

        # Equipment recommendations
        equipment_recs = []
        if not equipment_input.gym_access:
            if "strength" in goals_input.primary_goals:
                equipment_recs.extend(
                    ["adjustable_dumbbells", "barbell_set", "power_rack"]
                )
            else:
                equipment_recs.extend(["resistance_bands", "adjustable_dumbbells"])

        # Professional referrals
        referrals = []
        if movement_results.red_flags:
            referrals.append("movement_specialist_or_physical_therapist")
        if experience_results.supervision_needs == "close_supervision_recommended":
            referrals.append("qualified_strength_coach")
        if (
            goals_input.primary_goals
            and "competition" in str(goals_input.primary_goals).lower()
        ):
            referrals.append("sport_specific_coach")

        return RecommendationsOutput(
            immediate_priorities=immediate_priorities,
            program_type_recommendation=program_type,
            training_frequency_recommendation=frequency,
            session_duration_recommendation=session_duration,
            progression_timeline=progression_timeline,
            equipment_recommendations=equipment_recs,
            professional_referrals=referrals,
        )

    async def assess_strength(
        self, assessment_input: StrengthAssessmentInput
    ) -> StrengthAssessmentOutput:
        """
        Perform comprehensive strength assessment.

        Args:
            assessment_input: Complete assessment input data

        Returns:
            StrengthAssessmentOutput: Comprehensive assessment results and recommendations
        """
        try:
            logger.info(
                f"Starting strength assessment for user {assessment_input.user_id}"
            )

            # Perform individual assessments
            movement_results = self.assess_movement_screen(
                assessment_input.movement_screen
            )
            strength_results = self.assess_strength_standards(
                assessment_input.strength_testing, assessment_input.gender
            )
            experience_results = self.assess_experience(assessment_input.experience)

            # Generate recommendations
            recommendations = self.generate_recommendations(
                movement_results,
                strength_results,
                experience_results,
                assessment_input.equipment,
                assessment_input.goals,
            )

            # Calculate overall assessment score
            movement_score = movement_results.overall_score * 20  # 0-100 scale
            strength_score = self._calculate_percentile(
                strength_results.overall_strength_level
            )
            experience_score = experience_results.readiness_score * 10  # 0-100 scale

            overall_score = (movement_score + strength_score + experience_score) / 3

            # Calculate next assessment date (3-6 months based on experience)
            from datetime import timedelta

            if experience_results.experience_classification == "beginner":
                next_assessment = assessment_input.assessment_date + timedelta(
                    weeks=12
                )  # 3 months
            else:
                next_assessment = assessment_input.assessment_date + timedelta(
                    weeks=24
                )  # 6 months

            # Generate unique assessment ID
            import uuid

            assessment_id = str(uuid.uuid4())

            logger.info(
                f"Completed strength assessment for user {assessment_input.user_id}"
            )

            return StrengthAssessmentOutput(
                user_id=assessment_input.user_id,
                assessment_id=assessment_id,
                assessment_date=assessment_input.assessment_date,
                movement_screen=movement_results,
                strength_standards=strength_results,
                experience_assessment=experience_results,
                recommendations=recommendations,
                overall_assessment_score=overall_score,
                next_reassessment_date=next_assessment,
            )

        except Exception as e:
            logger.error(f"Error in strength assessment: {str(e)}")
            raise
