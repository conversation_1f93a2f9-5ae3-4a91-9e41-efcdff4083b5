"""
Shared pytest fixtures and utilities for MCP server testing.
This module provides common test infrastructure for all MCP servers.
"""

import asyncio
from typing import Any, Callable, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from mcp.server import Server
from mcp.server.session import ServerSession

# MCP imports
from mcp.types import (
    CallToolRequest,
    CallToolRequestParams,
    ClientCapabilities,
    EmbeddedResource,
    GetPromptRequest,
    ImageContent,
    Implementation,
    InitializeRequest,
    InitializeRequestParams,
    ListPromptsRequest,
    ListToolsRequest,
    TextContent,
    Tool,
)


class MockMCPClient:
    """Mock MCP client for testing server interactions."""

    def __init__(self, server: Server):
        self.server = server
        self.session = None

    async def initialize(self) -> Dict[str, Any]:
        """Initialize the MCP session."""
        params = InitializeRequestParams(
            protocolVersion="2024-11-05",
            capabilities=ClientCapabilities(tools={}),
            clientInfo=Implementation(name="test-client", version="1.0.0"),
        )
        request = InitializeRequest(method="initialize", params=params)
        return await self.server._initialize_handler(request)

    async def list_tools(self) -> List[Tool]:
        """List all available tools."""
        request = ListToolsRequest()
        return await self.server._list_tools_handler(request)

    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> List[Any]:
        """Call a specific tool."""
        params = CallToolRequestParams(name=name, arguments=arguments)
        request = CallToolRequest(method="tools/call", params=params)
        return await self.server._call_tool_handler(request)

    async def list_prompts(self) -> List[Any]:
        """List all available prompts."""
        request = ListPromptsRequest()
        return await self.server._list_prompts_handler(request)


@pytest.fixture
def mcp_client_factory():
    """Factory for creating MockMCPClient instances."""

    def _create_client(server: Server) -> MockMCPClient:
        return MockMCPClient(server)

    return _create_client


@pytest.fixture
def mock_list_tools_request():
    """Provides a ListToolsRequest."""
    return ListToolsRequest()


@pytest.fixture
def mock_call_tool_request_factory():
    """Factory for creating CallToolRequest objects."""

    def _create_request(name: str, arguments: Dict[str, Any]) -> CallToolRequest:
        params = CallToolRequestParams(name=name, arguments=arguments)
        return CallToolRequest(method="tools/call", params=params)

    return _create_request


@pytest.fixture
def mock_initialize_request():
    """Provides an InitializeRequest."""
    params = InitializeRequestParams(
        protocolVersion="2024-11-05",
        capabilities=ClientCapabilities(tools={}),
        clientInfo=Implementation(name="test-client", version="1.0.0"),
    )
    return InitializeRequest(method="initialize", params=params)


@pytest.fixture
def sample_tool_arguments():
    """Provides sample arguments for testing different tool types."""
    return {
        "strength": {
            "search_strength_exercises": {
                "query": "bench press",
                "muscle_group": "chest",
            },
            "generate_strength_program": {
                "goal": "hypertrophy",
                "days_per_week": 3,
                "experience_level": "intermediate",
            },
        },
        "nutrition": {
            "calculate_daily_calories": {
                "age": 30,
                "sex": "male",
                "height": 180,
                "weight": 75,
                "activity_level": "sedentary",
            },
            "generate_meal_plan": {
                "calorie_target": 2000,
                "dietary_restrictions": ["vegetarian"],
                "preferences": ["high protein"],
            },
            "track_hydration": {"amount_ml": 500, "time_of_day": "morning"},
        },
        "cardio": {
            "generate_running_route": {
                "start_location": "Central Park",
                "distance_km": 5,
                "route_preference": "scenic",
            },
            "calculate_pace_zones": {
                "recent_run_pace_min_km": 6.0,
                "heart_rate_max": 180,
            },
            "calculate_vo2max_estimate": {
                "distance_meters": 5000,
                "duration_minutes": 25,
                "age": 30,
            },
            "plan_interval_workout": {
                "goal": "speed",
                "total_duration_minutes": 45,
                "intensity_level": "high",
            },
        },
        "external": {
            "search_locations": {
                "query": "coffee shop",
                "latitude": 40.7128,
                "longitude": -74.0060,
            },
            "search_web": {"query": "python programming"},
            "query_airtable": {
                "base_id": "appTestBase",
                "table_name": "tblTestTable",
                "formula": "{Name} = 'Test'",
            },
            "get_weather_data": {"location": "London"},
            "search_wikipedia": {"query": "Albert Einstein"},
            "find_nearby_facilities": {
                "latitude": 34.0522,
                "longitude": -118.2437,
                "place_type": "gym",
                "radius_meters": 5000,
            },
            "calculate_travel_carbon": {
                "distance_km": 100,
                "mode_of_transport": "car",
                "fuel_type": "petrol",
            },
        },
    }


@pytest.fixture
def sample_tool_responses():
    """Provides sample responses for testing different tool types."""
    return {
        "strength": {
            "search_strength_exercises": {
                "exercises": [{"name": "Bench Press", "reps": 10}]
            },
            "generate_strength_program": {"program": "Day 1: Workout A"},
        },
        "nutrition": {
            "calculate_daily_calories": {"calories": 2500},
            "generate_meal_plan": {
                "plan": "Breakfast: Oats, Lunch: Salad, Dinner: Chicken"
            },
            "track_hydration": {"status": "Hydration goal met"},
        },
        "cardio": {
            "generate_running_route": {"route_details": "5k loop around the park"},
            "calculate_pace_zones": {"zones": {"zone1": "slow"}},
            "calculate_vo2max_estimate": {"vo2max": 45.0},
            "plan_interval_workout": {"workout_plan": "4x800m repeats"},
        },
        "external": {
            "search_locations": {"results": [{"address": "123 Main St"}]},
            "search_web": {"results": [{"title": "Search Result"}]},
            "query_airtable": {"records": [{"id": "rec1"}]},
            "get_weather_data": {"temp_c": 20},
            "search_wikipedia": {"summary": "Article summary"},
            "find_nearby_facilities": {"places": [{"name": "Gym A"}]},
            "calculate_travel_carbon": {"co2_kg": 10.5},
        },
    }


def assert_valid_mcp_tool(tool: Tool) -> None:
    """Assert that a tool object is valid according to MCP specification."""
    assert isinstance(tool, Tool)
    assert hasattr(tool, "name")
    assert hasattr(tool, "description")
    assert hasattr(tool, "inputSchema")
    assert isinstance(tool.name, str)
    assert isinstance(tool.description, str)
    assert tool.inputSchema is not None


def assert_valid_mcp_response(response: Any) -> None:
    """Assert that a tool response is valid according to MCP specification."""
    # Handle ServerResult wrapping CallToolResult
    if hasattr(response, "root"):
        response = response.root

    # Handle CallToolResult
    if hasattr(response, "content"):
        content_list = response.content
    else:
        content_list = response

    assert isinstance(content_list, list)
    assert len(content_list) > 0

    for item in content_list:
        # Response items should be TextContent, ImageContent, or EmbeddedResource
        assert isinstance(item, (TextContent, ImageContent, EmbeddedResource))

        if isinstance(item, TextContent):
            assert hasattr(item, "text")
            assert isinstance(item.text, str)


@pytest.fixture
def mcp_validators():
    """Provides validation functions for MCP objects."""
    return {"tool": assert_valid_mcp_tool, "response": assert_valid_mcp_response}


@pytest.fixture
def performance_timer():
    """Provides a context manager for timing test operations."""
    import time
    from contextlib import contextmanager

    @contextmanager
    def timer():
        start_time = time.time()
        yield lambda: time.time() - start_time

    return timer


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Pytest markers for organizing tests
pytestmark = [pytest.mark.asyncio, pytest.mark.mcp]
