"""
Streaming Coaching API Endpoint

FastAPI endpoint that streams ReAct coaching responses to the frontend.
Provides real-time streaming of the Thought→Action→Observation cycle.
"""

import asyncio
import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from pydantic import BaseModel, Field

from ..agents.base_agent import convert_to_base_messages
from ..agents.react_coach_executor import create_react_coach_node
from ..agents.specialized_coaches import (
    CARDIO_COACH_PROMPT,
    CYCLING_COACH_PROMPT,
    MENTAL_COACH_PROMPT,
    NUTRITION_COACH_PROMPT,
    RECOVERY_COACH_PROMPT,
    STRENGTH_COACH_PROMPT,
    get_tools_manager,
)

logger = logging.getLogger(__name__)

app = FastAPI()

# Coach configurations
COACH_CONFIGS = {
    "strength": {
        "prompt": STRENGTH_COACH_PROMPT,
        "tools_method": "get_strength_coach_tools",
    },
    "cardio": {
        "prompt": CARDIO_COACH_PROMPT,
        "tools_method": "get_cardio_coach_tools",
    },
    "cycling": {
        "prompt": CYCLING_COACH_PROMPT,
        "tools_method": "get_cycling_coach_tools",
    },
    "nutrition": {
        "prompt": NUTRITION_COACH_PROMPT,
        "tools_method": "get_nutrition_coach_tools",
    },
    "recovery": {
        "prompt": RECOVERY_COACH_PROMPT,
        "tools_method": "get_recovery_coach_tools",
    },
    "mental": {
        "prompt": MENTAL_COACH_PROMPT,
        "tools_method": "get_mental_coach_tools",
    },
}


class CoachingRequest(BaseModel):
    """Request model for coaching sessions."""

    message: str = Field(..., description="User's message to the coach")
    coach_type: str = Field(
        ...,
        description="Type of coach (strength, cardio, cycling, nutrition, recovery, mental)",
    )
    user_profile: Optional[Dict[str, Any]] = Field(
        None, description="User profile information"
    )
    conversation_history: Optional[List[Union[Dict[str, Any], BaseMessage]]] = Field(
        default_factory=list,
        description="Previous conversation messages - can be dicts with 'role' and 'content' or BaseMessage objects",
    )
    session_id: Optional[str] = Field(
        None, description="Session identifier for continuity"
    )


class StreamingCoachingResponse:
    """Handles streaming of ReAct coaching responses."""

    def __init__(self, coach_type: str, request: CoachingRequest):
        self.coach_type = coach_type
        self.request = request
        self.coach_node = None

    async def initialize_coach(self):
        """Initialize the ReAct coach for the specified type."""
        if self.coach_type not in COACH_CONFIGS:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid coach type. Available: {list(COACH_CONFIGS.keys())}",
            )

        config = COACH_CONFIGS[self.coach_type]

        # Get tools manager and tools for this coach type
        tools_manager = await get_tools_manager()
        tools_method = getattr(tools_manager, config["tools_method"])
        tools = tools_method()

        # Create ReAct-enabled coach node
        self.coach_node = await create_react_coach_node(
            coach_name=f"{self.coach_type}_coach",
            coach_prompt=config["prompt"],
            tools=tools,
            max_iterations=8,  # Allow more iterations for complex queries
        )

        logger.info(f"Initialized {self.coach_type} coach with {len(tools)} tools")

    async def stream_response(self) -> AsyncGenerator[str, None]:
        """Stream the ReAct coaching response with real-time updates."""
        try:
            # Initialize coach if not already done
            if not self.coach_node:
                await self.initialize_coach()

            # Send initial status
            yield self._format_stream_message(
                "status",
                {
                    "type": "session_started",
                    "coach_type": self.coach_type,
                    "message": f"Starting {self.coach_type} coaching session...",
                },
            )

            # Prepare state for the coach
            messages = [HumanMessage(content=self.request.message)]

            # Add conversation history if provided
            if self.request.conversation_history:
                # Convert history to proper message format using the utility function
                converted_history = convert_to_base_messages(
                    self.request.conversation_history
                )
                # Insert history before the current message
                messages = converted_history + messages

            state = {
                "messages": messages,
                "user_profile": self.request.user_profile,
            }

            # Send thinking status
            yield self._format_stream_message(
                "status",
                {"type": "thinking", "message": "Coach is analyzing your request..."},
            )

            # Execute the coach node (this will run the full ReAct cycle)
            result = await self.coach_node(state)

            if "messages" in result and result["messages"]:
                response_message = result["messages"][0]

                # Stream the final response in chunks for better UX
                response_content = response_message.content

                # Send response start
                yield self._format_stream_message(
                    "response_start",
                    {
                        "coach_type": self.coach_type,
                        "total_length": len(response_content),
                    },
                )

                # Stream response in chunks
                chunk_size = 50  # Characters per chunk
                for i in range(0, len(response_content), chunk_size):
                    chunk = response_content[i : i + chunk_size]
                    yield self._format_stream_message(
                        "response_chunk",
                        {
                            "chunk": chunk,
                            "position": i,
                            "is_final": i + chunk_size >= len(response_content),
                        },
                    )

                    # Small delay for streaming effect
                    await asyncio.sleep(0.05)

                # Send completion status
                yield self._format_stream_message(
                    "status",
                    {
                        "type": "completed",
                        "message": "Coaching session completed successfully",
                        "response_length": len(response_content),
                    },
                )

            else:
                # Handle error case
                yield self._format_stream_message(
                    "error",
                    {
                        "message": "No response generated from coach",
                        "coach_type": self.coach_type,
                    },
                )

        except Exception as e:
            logger.error(f"Error in streaming coaching response: {e}")
            yield self._format_stream_message(
                "error",
                {
                    "message": f"An error occurred: {str(e)}",
                    "coach_type": self.coach_type,
                },
            )

    def _format_stream_message(self, event_type: str, data: Dict[str, Any]) -> str:
        """Format a message for Server-Sent Events (SSE) streaming."""
        message = {
            "event": event_type,
            "data": data,
            "timestamp": asyncio.get_event_loop().time(),
        }
        return f"data: {json.dumps(message)}\n\n"


@app.post("/coaching/stream")
async def stream_coaching_session(request: CoachingRequest):
    """
    Stream a ReAct coaching session with real-time updates.

    This endpoint provides:
    - Real-time streaming of coaching responses
    - Support for all specialized coach types
    - Conversation history continuity
    - User profile integration
    - Error handling and status updates
    """
    try:
        # Validate coach type
        if request.coach_type not in COACH_CONFIGS:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid coach type '{request.coach_type}'. Available: {list(COACH_CONFIGS.keys())}",
            )

        # Create streaming response handler
        streaming_handler = StreamingCoachingResponse(request.coach_type, request)

        # Return streaming response
        return StreamingResponse(
            streaming_handler.stream_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        logger.error(f"Error setting up streaming coaching session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/coaching/coaches")
async def get_available_coaches():
    """Get list of available coach types and their descriptions."""
    coaches = {}

    for coach_type, config in COACH_CONFIGS.items():
        # Extract description from prompt (first few lines)
        prompt_lines = config["prompt"].split("\n")
        description = ""
        for line in prompt_lines:
            if line.strip() and not line.startswith("You are"):
                description = line.strip()
                break

        coaches[coach_type] = {
            "name": f"{coach_type.title()} Coach",
            "description": description or f"Specialized {coach_type} coaching",
            "specialties": _get_coach_specialties(coach_type),
        }

    return {"coaches": coaches, "total_count": len(coaches)}


def _get_coach_specialties(coach_type: str) -> list:
    """Get specialties for each coach type."""
    specialties_map = {
        "strength": [
            "Resistance training",
            "Powerlifting",
            "Bodybuilding",
            "Functional strength",
        ],
        "cardio": ["Running", "Endurance training", "Heart rate zones", "HIIT"],
        "cycling": [
            "Road cycling",
            "Mountain biking",
            "Power training",
            "Route planning",
        ],
        "nutrition": [
            "Sports nutrition",
            "Meal planning",
            "Supplements",
            "Weight management",
        ],
        "recovery": [
            "Sleep optimization",
            "Active recovery",
            "Injury prevention",
            "Stress management",
        ],
        "mental": [
            "Goal setting",
            "Motivation",
            "Confidence building",
            "Mental resilience",
        ],
    }
    return specialties_map.get(coach_type, [])


@app.get("/coaching/health")
async def health_check():
    """Health check endpoint for the coaching API."""
    try:
        # Test tools manager initialization
        tools_manager = await get_tools_manager()

        return {
            "status": "healthy",
            "service": "coaching_stream_api",
            "coaches_available": list(COACH_CONFIGS.keys()),
            "tools_initialized": True,
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "service": "coaching_stream_api",
        }


# CORS middleware for frontend integration
@app.middleware("http")
async def add_cors_header(request: Request, call_next):
    """Add CORS headers for frontend integration."""
    response = await call_next(request)
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    return response


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
