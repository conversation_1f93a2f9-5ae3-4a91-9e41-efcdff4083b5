"""
Simple test script to verify memory integration with coaching agents.

This script tests:
1. Basic memory functionality
2. User profile persistence
3. Interaction history tracking
4. Session continuity
"""

import asyncio
import logging
import os
from datetime import datetime

from athlea_langgraph.memory.mongo_memory import MongoMemoryStore, MongoSaver

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_basic_memory_components():
    """Test the basic MongoSaver and MongoMemoryStore functionality."""
    print("🧪 Testing Basic Memory Components")
    print("-" * 40)

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ MONGODB_URI not set")
        return False

    try:
        # Test MongoSaver (checkpointing)
        print("Testing MongoSaver...")
        saver = MongoSaver(
            mongodb_uri, database="test_db", collection="test_checkpoints"
        )

        test_state = {
            "messages": ["Hello", "How are you?"],
            "user_id": "test_user",
            "timestamp": datetime.now().isoformat(),
        }

        await saver.save_state("test_thread_001", test_state)
        loaded_state = await saver.load_state("test_thread_001")

        assert loaded_state == test_state, "State save/load failed"
        print("✅ MongoSaver working correctly")

        # Test MongoMemoryStore
        print("Testing MongoMemoryStore...")
        memory_store = MongoMemoryStore(
            mongodb_uri, database="test_db", collection="test_memories"
        )

        test_memory = {
            "content": "User likes morning workouts and prefers strength training",
            "user_preferences": {
                "workout_time": "morning",
                "preferred_type": "strength",
            },
            "timestamp": datetime.now().isoformat(),
        }

        await memory_store.put("user:test_001", "preferences", test_memory)
        retrieved_memory = await memory_store.get("user:test_001", "preferences")

        assert retrieved_memory == test_memory, "Memory put/get failed"
        print("✅ MongoMemoryStore working correctly")

        # Test search functionality
        search_results = await memory_store.search("user:test_001", "workout morning")
        assert len(search_results) >= 1, "Memory search failed"
        print("✅ Memory search working correctly")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Memory component test failed: {e}")
        return False


async def test_user_profile_management():
    """Test user profile creation and updates."""
    print("\n👤 Testing User Profile Management")
    print("-" * 40)

    mongodb_uri = os.getenv("MONGODB_URI")

    try:
        memory_store = MongoMemoryStore(
            mongodb_uri, database="test_db", collection="test_memories"
        )
        user_id = "test_user_profile"
        namespace = f"user:{user_id}"

        # Create initial profile
        initial_profile = {
            "name": "Test User",
            "age": 25,
            "fitness_level": "beginner",
            "goals": ["lose weight"],
            "created_at": datetime.now().isoformat(),
        }

        await memory_store.put(namespace, "profile", initial_profile)
        print("✅ Initial profile created")

        # Update profile
        profile_updates = {
            "fitness_level": "intermediate",
            "goals": ["lose weight", "build muscle"],
            "last_updated": datetime.now().isoformat(),
        }

        # Simulate profile update (merge)
        existing_profile = await memory_store.get(namespace, "profile")
        existing_profile.update(profile_updates)
        await memory_store.put(namespace, "profile", existing_profile)

        # Verify update
        updated_profile = await memory_store.get(namespace, "profile")
        assert (
            updated_profile["fitness_level"] == "intermediate"
        ), "Profile update failed"
        assert len(updated_profile["goals"]) == 2, "Goals update failed"
        print("✅ Profile update working correctly")

        return True

    except Exception as e:
        print(f"❌ Profile test failed: {e}")
        return False


async def test_interaction_history():
    """Test interaction history tracking."""
    print("\n💬 Testing Interaction History")
    print("-" * 40)

    mongodb_uri = os.getenv("MONGODB_URI")

    try:
        memory_store = MongoMemoryStore(
            mongodb_uri, database="test_db", collection="test_memories"
        )
        user_id = "test_user_history"
        namespace = f"user:{user_id}"

        # Simulate multiple interactions
        interactions = [
            {
                "content": "User asked about workout plans. Provided beginner strength training routine.",
                "user_query": "I want to start working out",
                "response": "Here's a beginner strength training plan...",
                "type": "coaching_interaction",
                "timestamp": datetime.now().isoformat(),
            },
            {
                "content": "User asked about nutrition. Discussed protein intake and meal timing.",
                "user_query": "What should I eat after workouts?",
                "response": "Focus on protein within 30 minutes...",
                "type": "coaching_interaction",
                "timestamp": datetime.now().isoformat(),
            },
        ]

        # Save interactions
        for i, interaction in enumerate(interactions):
            key = f"interaction_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}"
            await memory_store.put(namespace, key, interaction)

        print("✅ Interactions saved")

        # Test search for relevant interactions
        workout_interactions = await memory_store.search(namespace, "workout plan")
        nutrition_interactions = await memory_store.search(
            namespace, "nutrition protein"
        )

        assert len(workout_interactions) >= 1, "Workout interaction search failed"
        assert len(nutrition_interactions) >= 1, "Nutrition interaction search failed"
        print("✅ Interaction search working correctly")

        return True

    except Exception as e:
        print(f"❌ Interaction history test failed: {e}")
        return False


async def test_session_continuity():
    """Test session state persistence."""
    print("\n🔄 Testing Session Continuity")
    print("-" * 40)

    mongodb_uri = os.getenv("MONGODB_URI")

    try:
        saver = MongoSaver(
            mongodb_uri, database="test_db", collection="test_checkpoints"
        )
        thread_id = "test_session_continuity"

        # Simulate session state at different points
        session_states = [
            {
                "messages": ["User: I want to start working out"],
                "current_step": 1,
                "plan": ["strength_coach", "nutrition_coach"],
                "user_profile": {"fitness_level": "beginner"},
            },
            {
                "messages": [
                    "User: I want to start working out",
                    "Coach: Great! Let's create a plan...",
                ],
                "current_step": 2,
                "plan": ["strength_coach", "nutrition_coach"],
                "strength_response": "Here's your strength training plan...",
                "user_profile": {"fitness_level": "beginner"},
            },
        ]

        # Save different session states
        for i, state in enumerate(session_states):
            await saver.save_state(f"{thread_id}_step_{i}", state)

        print("✅ Session states saved")

        # Test state retrieval
        for i, expected_state in enumerate(session_states):
            loaded_state = await saver.load_state(f"{thread_id}_step_{i}")
            assert loaded_state == expected_state, f"Session state {i} retrieval failed"

        print("✅ Session state retrieval working correctly")

        return True

    except Exception as e:
        print(f"❌ Session continuity test failed: {e}")
        return False


async def run_all_tests():
    """Run all memory integration tests."""
    print("🚀 Starting Memory Integration Tests")
    print("=" * 50)

    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ Error: MONGODB_URI environment variable not set")
        print("Please set your MongoDB connection string:")
        print("export MONGODB_URI='your_mongodb_connection_string'")
        return

    tests = [
        ("Basic Memory Components", test_basic_memory_components),
        ("User Profile Management", test_user_profile_management),
        ("Interaction History", test_interaction_history),
        ("Session Continuity", test_session_continuity),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1

    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Memory integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")


if __name__ == "__main__":
    asyncio.run(run_all_tests())
