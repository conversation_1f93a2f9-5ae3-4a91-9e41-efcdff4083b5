# Coach Prompt Schema and Design Philosophy

This directory contains the JSON configuration files for all specialized coaching agents within the Athlea LangGraph system. Each file defines a coach's identity, expertise, communication style, and operational guidelines through a structured system prompt.

## Coach Categories

Coaches are broadly categorized into:

1.  **Functional Domain Coaches:** These coaches possess deep expertise in a core area of fitness, health, or performance. They provide foundational knowledge and can support multiple sport-specific coaches. Examples include:
    *   `endurance_coach.json`
    *   `strength_power_coach.json`
    *   `nutrition_hydration_coach.json`
    *   `injury_prevention_recovery_coach.json`
    *   `psychology_mindset_coach.json`

2.  **Sport-Specific & General Fitness Coaches:** These coaches focus on the unique demands of a particular sport or general fitness goals. They often integrate knowledge from various functional domains. Examples include:
    *   `running_coach.json`
    *   `general_fitness_coach.json`
    *   `cycling_coach.json` (and others to be added like `rowing_coach.json`, `triathlon_coach.json`, `tennis_coach.json`)

## Prompt Structure

Each coach's JSON file follows a standardized schema, with the core coaching intelligence defined within the `prompt.system` field. This system prompt is structured with the following key sections:

*   **Role & Identity:** Defines the coach's persona and primary role.
*   **Knowledge Domains:** Lists the specific areas of expertise, sub-domains, and relevant concepts. This should be informed by `functional_domains_anchors.json` for consistency with the knowledge graph where applicable.
*   **Coaching Methodology:** Outlines the coach's approach to assessment, planning, and intervention.
*   **Communication Approach:** Specifies the coach's tone, response structure, and how they balance technical information with user understanding.
*   **Training Prescription Framework:** Details how the coach should structure advice, plans, or recommendations within their domain (e.g., exercise sets/reps, nutritional macros, mental skill exercises).
*   **Athlete/Client Development Guidance:** Provides differentiated advice for various experience levels or goals.
*   **Integration with Other Domains:** Describes how the coach should interact with or refer to other specialized coaches.
*   **Safety Protocols:** Lists critical safety guidelines, red flags, and when to advise users to consult human professionals.
*   **Ethical Standards:** Outlines the ethical considerations for the coach.
*   **Adaptive Coaching:** Describes how the coach should adapt advice to common limitations or changing circumstances.
*   **Tools:** Lists the specialized tools available to the coach and briefly how they should be used.

## Core Operating Principle: Nuanced Reasoning (Understand Before Solving)

A fundamental principle infused into all coach prompts, particularly reinforced through the `examples` section, is **nuanced reasoning**. This means:

**Do's:**

*   **Acknowledge and Validate:** Start by acknowledging the user's stated need or question.
*   **Seek to Understand the Underlying Goal:** When a user says "I need X," the coach's priority is to understand *why* they need X. What is the actual underlying goal or problem they are trying to solve?
*   **Gather Essential Context:** Before offering solutions or using tools, ask clarifying questions to understand the user's experience level, current habits, resources, limitations, and specific circumstances.
*   **Prioritize Safety:** Always consider safety implications. The "Safety Protocols" section of the prompt is crucial.
*   **Educate and Empower:** Explain the rationale behind recommendations. Help the user understand the principles involved.
*   **Collaborate:** Frame the interaction as a partnership. Use phrases like "How does that sound?" or "Would you be open to exploring...?"
*   **Use Tools Appropriately:** Tools should be used *after* sufficient context is gathered and it's clear how the tool will help achieve the user's *clarified* goal.
*   **Structure Examples Carefully:** The `examples` in each JSON file should model this multi-turn, context-gathering dialogue before a solution or tool is deployed. Show the assistant probing, then receiving more information, then acting.

**Don'ts:**

*   **Don't Immediately Jump to Solutions:** Avoid taking a user's self-prescribed solution ("I need a plan for X") at face value without deeper inquiry.
*   **Don't Prescribe Blindly:** Never offer detailed plans, use complex tools, or give highly specific advice without first understanding the user's individual context, goals, and potential risks.
*   **Don't Overwhelm with Jargon:** While coaches are experts, they must communicate in an accessible way, explaining technical terms.
*   **Don't Make Assumptions:** Clarify ambiguities rather than guessing.

## Future Reference and Development

When creating new coach prompts or modifying existing ones:

1.  Adhere to the standardized JSON schema and system prompt structure.
2.  Ensure the "Knowledge Domains" align with established functional domains or sport-specific requirements.
3.  Critically review and update the "Safety Protocols" and "Ethical Standards" for each coach's specific domain.
4.  Craft the `examples` to explicitly demonstrate the "understand before solving" nuanced reasoning. These examples are key to guiding the LLM's behavior.
5.  Remember that the goal is to create AI coaches that are not just knowledgeable but also responsible, safe, and genuinely helpful in a way that empowers the user. 