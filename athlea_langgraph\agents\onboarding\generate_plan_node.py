import logging
from typing import Any, Dict, List

from langchain_core.messages import AIMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import (
    ExampleSession,
    OnboardingState,
    PlanDetails,
    PlanPhase,
    SidebarStateData,
)
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)

# JSON Schema for PlanPhase
PLAN_PHASE_SCHEMA = {
    "type": "object",
    "properties": {
        "phaseName": {
            "type": "string",
            "description": "Name of the training phase (e.g., 'Base Building', 'Peak Week')",
        },
        "duration": {
            "type": "string",
            "description": "Duration of the phase (e.g., '4 weeks')",
        },
        "description": {
            "type": "string",
            "description": "Detailed description of the phase's focus, key goals, and typical weekly structure.",
        },
    },
    "required": ["phaseName", "duration", "description"],
}

# JSON Schema for ExampleSession
EXAMPLE_SESSION_SCHEMA = {
    "type": "object",
    "properties": {
        "SessionName": {
            "type": "string",
            "description": "A descriptive name for the example session.",
        },
        "sessionType": {
            "type": "string",
            "description": "Type of session (e.g., 'Endurance', 'Tempo', 'Intervals', 'Strength', 'Recovery'). Infer from plan/phase.",
        },
        "Duration": {
            "type": "string",
            "description": "Estimated duration of the session (e.g., '60 minutes', '90 minutes').",
        },
        "SessionDescription": {
            "type": "string",
            "description": "Brief description of the session's main activity or purpose.",
        },
    },
    "required": ["SessionName", "sessionType", "Duration", "SessionDescription"],
}

# Main JSON Schema for PlanDetails
PLAN_DETAILS_SCHEMA = {
    "title": "PlanDetails",
    "description": "Schema for a personalized fitness plan structure.",
    "type": "object",
    "properties": {
        "planId": {
            "type": "string",
            "description": "Unique identifier for the plan (generate a UUID string)",
            "format": "uuid",
        },
        "name": {
            "type": "string",
            "description": "Catchy and descriptive name for the generated fitness plan.",
        },
        "description": {
            "type": "string",
            "description": "Detailed description of the plan, its goals, and target audience.",
        },
        "duration": {
            "type": "string",
            "description": "Total duration of the plan (e.g., '12 weeks'). Estimate based on user info.",
        },
        "level": {
            "type": "string",
            "description": "Recommended fitness level (e.g., 'Beginner', 'Intermediate', 'Advanced'). Infer from user info.",
        },
        "planType": {
            "type": "string",
            "description": "Primary type of plan (e.g., 'Running', 'Strength', 'Weight Loss'). Infer from user goals.",
        },
        "disciplines": {
            "type": "array",
            "items": {"type": "string"},
            "description": "List of disciplines involved (e.g., ['Running', 'Strength']). Infer from user goals.",
        },
        "rationale": {
            "type": "string",
            "description": "Explanation of why this plan structure and content is suitable for the user based on their provided information.",
        },
        "phases": {
            "type": "array",
            "items": PLAN_PHASE_SCHEMA,
            "description": "List of training phases. Generate 1-3 simple placeholder phases.",
        },
        "exampleSessions": {
            "type": "array",
            "items": EXAMPLE_SESSION_SCHEMA,
            "description": "List of 2-3 example training sessions relevant to the plan's start.",
        },
    },
    "required": [
        "planId",
        "name",
        "description",
        "duration",
        "level",
        "planType",
        "disciplines",
        "rationale",
        "phases",
        "exampleSessions",
    ],
}


class GeneratePlanNode:
    """Node for generating a personalized fitness plan"""

    def __init__(self):
        self.llm = create_azure_chat_openai(temperature=0.3)
        self.planning_prompt_template = None  # Will be loaded lazily

        # Fallback prompt template
        self.fallback_prompt_template = """You are an expert fitness coach. Based on the user's goals and the summarized information, generate a personalized fitness plan structure. 

User Goals: {user_goals}

User Information Summary:
 - {summary_string}
    
Respond ONLY with the structured plan details conforming to the provided JSON schema. Create a unique planId (UUID format), infer planType, level, and disciplines from the goals/summary. Generate a suitable name, description, total duration, and rationale. Create 1-3 phases with names, durations, and DETAILED descriptions covering focus and weekly structure. Generate 2-3 diverse EXAMPLE sessions (using the exampleSessionSchema structure) that fit within the first phase, including SessionName, sessionType, Duration, and SessionDescription."""

    async def _get_planning_prompt_template(self) -> str:
        """Load the planning prompt template lazily"""
        if self.planning_prompt_template is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/fitness_plan_generation"
                )
                self.planning_prompt_template = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding fitness plan generation template prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding fitness plan generation template prompt: {e}"
                )
                self.planning_prompt_template = self.fallback_prompt_template

        return self.planning_prompt_template

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Generate a personalized fitness plan based on gathered information"""
        logger.info("[Node: generatePlanNode] Running plan generation")

        current_sidebar_data = state.get("sidebar_data") or SidebarStateData()

        # Bind the JSON schema to the LLM
        model_with_structured_output = self.llm.with_structured_output(
            PLAN_DETAILS_SCHEMA
        )

        # Format summary items for the prompt
        summary_string = ""
        if current_sidebar_data.summary_items:
            summary_string = "\n - ".join(
                [
                    f"{item.category}: {item.details}"
                    for item in current_sidebar_data.summary_items
                ]
            )
        else:
            summary_string = "No summary available."

        # Format user goals
        user_goals = ""
        if current_sidebar_data.goals and current_sidebar_data.goals.list:
            user_goals = ", ".join(current_sidebar_data.goals.list)
        else:
            user_goals = "Not specified"

        # Get the planning prompt template (loads lazily if needed)
        planning_template = await self._get_planning_prompt_template()

        # Prepare the system prompt using the template
        system_prompt = planning_template.format(
            user_goals=user_goals, summary_string=summary_string
        )

        logger.info(
            "[Node: generatePlanNode] Calling LLM for structured plan generation."
        )

        # Create a streaming LLM with tags for generating feedback message
        streaming_llm = self.llm.with_config(
            {"tags": ["final_response", "generatePlan"]}
        )

        try:
            # First, provide streaming feedback that plan generation is starting
            planning_message = "Perfect! I have all the information I need. Let me create your personalized fitness plan..."

            # Stream the feedback message
            planning_response_content = ""
            async for chunk in streaming_llm.astream(
                [
                    SystemMessage(
                        content=f"Respond with exactly this message: {planning_message}"
                    )
                ]
            ):
                if hasattr(chunk, "content") and chunk.content:
                    planning_response_content += chunk.content

            # Now generate the actual structured plan (this won't stream but provides the structured data)
            generated_plan_object = await model_with_structured_output.ainvoke(
                [SystemMessage(content=system_prompt)]
            )

            # Convert to PlanDetails object
            plan_data = generated_plan_object
            final_plan = PlanDetails(
                plan_id=plan_data.get("planId", ""),
                name=plan_data.get("name", ""),
                description=plan_data.get("description", ""),
                duration=plan_data.get("duration", ""),
                level=plan_data.get("level", ""),
                plan_type=plan_data.get("planType", ""),
                disciplines=plan_data.get("disciplines", []),
                rationale=plan_data.get("rationale", ""),
                phases=[
                    PlanPhase(
                        phase_name=phase.get("phaseName", ""),
                        duration=phase.get("duration", ""),
                        description=phase.get("description", ""),
                    )
                    for phase in plan_data.get("phases", [])
                ],
                example_sessions=[
                    ExampleSession(
                        session_name=session.get("SessionName", ""),
                        session_type=session.get("sessionType", ""),
                        duration=session.get("Duration", ""),
                        session_description=session.get("SessionDescription", ""),
                    )
                    for session in plan_data.get("exampleSessions", [])
                ],
            )

            logger.info(
                f"[Node: generatePlanNode] Successfully generated structured plan: {final_plan.name}"
            )

            # Create the combined message with both planning feedback and completion
            final_message_content = (
                planning_response_content
                + f"\n\n🎉 Your personalized plan '{final_plan.name}' has been created! Check the sidebar to see all the details."
            )
            summary_message = AIMessage(content=final_message_content)

            # Prepare the updated sidebar data including the plan
            updated_sidebar_data = SidebarStateData(
                current_stage="complete",
                goals=current_sidebar_data.goals,
                summary_items=current_sidebar_data.summary_items,
                generated_plan=final_plan,
                sport_suggestions=current_sidebar_data.sport_suggestions,
                selected_sport=current_sidebar_data.selected_sport,
                selected_sports=current_sidebar_data.selected_sports,
            )

            # Return the state update
            return {
                "messages": [summary_message],
                "generated_plan": final_plan,
                "sidebar_data": updated_sidebar_data,
                "onboarding_stage": "complete",
            }

        except Exception as error:
            logger.error(f"[Node: generatePlanNode] Error generating plan: {error}")

            error_sidebar_data = SidebarStateData(
                current_stage="error",
                goals=current_sidebar_data.goals,
                summary_items=current_sidebar_data.summary_items,
                generated_plan=current_sidebar_data.generated_plan,
                sport_suggestions=current_sidebar_data.sport_suggestions,
                selected_sport=current_sidebar_data.selected_sport,
                selected_sports=current_sidebar_data.selected_sports,
            )

            # Stream error message
            error_message_content = ""
            try:
                error_streaming_llm = self.llm.with_config(
                    {"tags": ["final_response", "generatePlan"]}
                )
                async for chunk in error_streaming_llm.astream(
                    [
                        SystemMessage(
                            content="Respond with exactly this message: I apologize, but I encountered an error while generating your plan. Please try again."
                        )
                    ]
                ):
                    if hasattr(chunk, "content") and chunk.content:
                        error_message_content += chunk.content
            except:
                error_message_content = "Error generating plan."

            error_message = AIMessage(content=error_message_content)

            return {
                "messages": [error_message],
                "sidebar_data": error_sidebar_data,
                "onboarding_stage": "error",
            }


# Create the node instance
generate_plan_node = GeneratePlanNode()
