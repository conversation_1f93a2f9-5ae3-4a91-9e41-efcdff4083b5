"""
Pydantic schemas for Azure Maps API tool.
"""

from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, model_validator


class Position(BaseModel):
    """Geographic position with lat/lon coordinates."""

    lat: float = Field(..., ge=-90, le=90, description="Latitude in decimal degrees")
    lon: float = Field(..., ge=-180, le=180, description="Longitude in decimal degrees")


class Address(BaseModel):
    """Address information from Azure Maps."""

    freeform_address: Optional[str] = Field(
        None, description="Complete formatted address"
    )
    street_name: Optional[str] = Field(None, description="Street name")
    municipality: Optional[str] = Field(None, description="City or municipality")
    municipality_subdivision: Optional[str] = Field(
        None, description="Neighborhood or district"
    )
    country_subdivision: Optional[str] = Field(None, description="State or province")
    country_code: Optional[str] = Field(None, description="ISO country code")
    country: Optional[str] = Field(None, description="Country name")
    local_name: Optional[str] = Field(None, description="Local name")


class POIInfo(BaseModel):
    """Point of Interest information."""

    name: str = Field(..., description="Name of the POI")
    categories: List[str] = Field(default_factory=list, description="POI categories")
    category_set: Optional[List[Dict[str, Any]]] = Field(
        None, description="Category set IDs"
    )


class WeatherMetric(BaseModel):
    """Weather measurement with value and unit."""

    value: float = Field(..., description="Measurement value")
    unit: str = Field(..., description="Unit of measurement")


# Input Schemas
class AzureMapsInput(BaseModel):
    """Input schema for Azure Maps API calls."""

    command: Literal["geocode", "weather", "nearby", "routes"] = Field(
        ..., description="Azure Maps command to execute"
    )

    # For geocode command
    address: Optional[str] = Field(None, description="Address to geocode")

    # For weather, nearby, routes commands
    lat: Optional[float] = Field(None, ge=-90, le=90, description="Latitude")
    lon: Optional[float] = Field(None, ge=-180, le=180, description="Longitude")

    # For nearby command
    query: Optional[str] = Field(None, description="Search query for nearby places")
    radius: Optional[int] = Field(
        5000, ge=100, le=50000, description="Search radius in meters"
    )

    @model_validator(mode="after")
    def validate_command_requirements(self):
        """Validate command-specific requirements."""
        command = self.command

        if command == "geocode":
            if not self.address:
                raise ValueError("Geocode command requires an address")

        elif command in ["weather", "nearby", "routes"]:
            if self.lat is None or self.lon is None:
                raise ValueError(f"{command} command requires latitude and longitude")

        if command == "nearby":
            if not self.query:
                raise ValueError("Nearby command requires a search query")

        return self


# Output Schemas
class GeocodeResult(BaseModel):
    """Geocoding result with position and address."""

    position: Position
    address: Address
    confidence: Optional[str] = Field(
        None, description="Confidence level of the result"
    )


class LocationResult(BaseModel):
    """Location search result from nearby or routes search."""

    id: str = Field(..., description="Unique identifier")
    type: str = Field(..., description="Result type")
    score: float = Field(..., description="Relevance score")
    distance: Optional[float] = Field(
        None, description="Distance from search point in km"
    )
    position: Position
    address: Address
    poi: Optional[POIInfo] = Field(None, description="Point of interest information")


class WeatherResult(BaseModel):
    """Current weather conditions."""

    temperature: WeatherMetric = Field(..., description="Current temperature")
    humidity: WeatherMetric = Field(..., description="Relative humidity")
    wind_speed: WeatherMetric = Field(..., description="Wind speed")
    precipitation: WeatherMetric = Field(..., description="Precipitation amount")
    condition: str = Field(..., description="Weather condition description")
    feels_like: WeatherMetric = Field(..., description="Feels like temperature")


class AzureMapsOutput(BaseModel):
    """Output schema for Azure Maps API responses."""

    command: str = Field(..., description="Command that was executed")
    success: bool = Field(..., description="Whether the command succeeded")

    # For geocode command
    geocode_result: Optional[GeocodeResult] = Field(
        None, description="Geocoding result"
    )

    # For weather command
    weather_result: Optional[WeatherResult] = Field(None, description="Weather data")

    # For nearby and routes commands
    locations: Optional[List[LocationResult]] = Field(
        None, description="Found locations"
    )
    location_count: Optional[int] = Field(None, description="Number of locations found")

    # Metadata
    search_radius: Optional[int] = Field(
        None, description="Search radius used in meters"
    )
    search_query: Optional[str] = Field(None, description="Search query used")
    api_status: str = Field(..., description="Azure Maps API response status")
    execution_time_ms: Optional[float] = Field(
        None, description="Execution time in milliseconds"
    )


class AzureMapsError(BaseModel):
    """Error response schema for Azure Maps API failures."""

    command: str = Field(..., description="Command that failed")
    error_type: str = Field(..., description="Type of error encountered")
    message: str = Field(..., description="Human-readable error message")
    api_status: Optional[str] = Field(
        None, description="Azure Maps API status if available"
    )
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retry")
