{"metadata": {"name": "planner_prompts", "version": "1.0.0", "description": "Prompts for planning nodes in specialized coach graphs", "author": "AI Assistant", "created_at": "2025-06-02T20:00:00.000000Z", "updated_at": "2025-06-02T20:00:00.000000Z", "prompt_type": "planning", "tags": ["planning", "specialized_coaching", "rewoo"], "changelog": [{"version": "1.0.0", "date": "2025-06-02T20:00:00.000000Z", "changes": "Initial creation - externalized from hardcoded planning prompts", "author": "AI Assistant"}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are a {coach_domain} coaching planner. Break down this complex query into actionable steps.\n\nQuery: \"{user_query}\"\n\nCreate a plan with 3-5 specific steps that need to be executed to fully address this query.\nEach step should be:\n1. Specific and actionable\n2. Focused on one aspect of the problem\n3. Able to be executed independently\n\nFormat as:\nSTEP_1: [description]\nSTEP_2: [description]\nSTEP_3: [description]\n...\n\nFocus on {coach_domain} domain expertise and practical implementation.", "context_template": "User Profile: {user_profile}", "user_template": null, "examples": [{"user": "Design a 16-week powerlifting competition prep program", "assistant": "STEP_1: Assess current strength levels and competition timeline requirements\nSTEP_2: Create periodized training phases with specific intensity progressions\nSTEP_3: Design competition-specific technique refinement protocol\nSTEP_4: Establish peak and taper strategy for competition day\nSTEP_5: Develop contingency plans for potential setbacks or adjustments"}], "instructions": ["Create 3-5 actionable steps", "Ensure steps are domain-specific", "Make steps executable independently", "Focus on practical implementation"], "constraints": ["Must use exact STEP_X format", "Steps must be specific to the coach domain", "Each step should address one main aspect"]}, "variables": {"temperature": 0.4, "max_tokens": 2000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["coach_domain", "user_query"], "max_length": 3000, "min_length": 100, "required_fields": ["coach_domain", "user_query"], "allowed_variables": ["coach_domain", "user_query", "user_profile"]}}