"""
Intelligent Coach System

Multi-domain coaches with memory, rationale, and coordination capabilities.
Built on top of existing session generation tools.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, ConfigDict

# Import existing session generation tools
from ..tools.external.session_generation import SessionGenerationTool
from ..schemas.session_generation_schemas import (
    SessionGenerationInput,
    SessionGenerationOutput,
    StrengthSession,
    RunningSession,
    CyclingSession,
    RecoverySession,
)

# Import our enhanced state models
from .session_state_models import (
    FitnessProfile,
    TrainingSession,
    SessionFeedback,
    WeeklyPlan,
    CoachMemory,
    convert_session_to_training_session,
)

logger = logging.getLogger(__name__)


class DailyFocus(BaseModel):
    """Focus for a specific training day"""

    day: int  # 1-7 (Monday-Sunday)
    primary_domain: str  # main training focus
    secondary_domains: List[str] = Field(default_factory=list)  # supporting domains
    intensity_target: str  # low, moderate, high
    volume_target: int  # total minutes
    focus_description: str
    recovery_priority: bool = False


class WeeklyFocus(BaseModel):
    """Weekly training focus and periodization"""

    week_number: int
    start_date: datetime
    end_date: datetime
    weekly_theme: str  # e.g., "Base Building", "Intensity Phase", "Recovery Week"
    primary_adaptations: List[str]  # what we're trying to improve
    daily_focuses: Dict[int, DailyFocus]  # day -> focus
    total_weekly_load: int  # total training minutes
    intensity_distribution: Dict[str, float]  # intensity -> percentage


class DomainCoach(BaseModel):
    """Base class for intelligent domain coaches"""

    coach_id: str
    domain: str
    expertise_level: str = "expert"

    # Memory System
    memory: CoachMemory

    # Session Generation Tool (will be initialized separately)
    _session_tool: Optional[SessionGenerationTool] = None

    # Performance Tracking
    session_count: int = 0
    last_session_date: Optional[datetime] = None

    model_config = ConfigDict(
        arbitrary_types_allowed=True, underscore_attrs_are_private=True
    )

    def initialize_session_tool(self):
        """Initialize the session generation tool"""
        if not self._session_tool:
            self._session_tool = SessionGenerationTool()

    async def generate_session(
        self,
        user_profile: FitnessProfile,
        daily_focus: DailyFocus,
        week_context: WeeklyFocus,
        other_sessions: List[TrainingSession] = None,
        date: datetime = None,
    ) -> Optional[TrainingSession]:
        """Generate domain-specific session with intelligence and context awareness"""

        # Skip if not relevant for this day
        if (
            self.domain != daily_focus.primary_domain
            and self.domain not in daily_focus.secondary_domains
        ):
            return None

        if date is None:
            date = datetime.now() + timedelta(days=daily_focus.day - 1)

        try:
            # Initialize session tool if needed
            self.initialize_session_tool()

            # Analyze context and constraints
            context_analysis = self._analyze_training_context(
                user_profile, daily_focus, other_sessions or []
            )

            # Generate session using existing tool
            session_input = self._create_session_input(
                user_profile, daily_focus, context_analysis, date
            )

            logger.info(
                f"[{self.coach_id}] Generating {self.domain} session for day {daily_focus.day}"
            )

            session_output = await self._session_tool.generate_session(session_input)

            if not session_output.success:
                logger.error(
                    f"[{self.coach_id}] Session generation failed: {session_output.message}"
                )
                return None

            # Extract the generated session
            generated_session = self._extract_session_from_output(session_output)

            # Generate coach rationale
            rationale = self._generate_rationale(
                daily_focus, context_analysis, generated_session
            )

            # Convert to enhanced TrainingSession
            training_session = convert_session_to_training_session(
                generated_session, self.coach_id, rationale, date
            )

            # Add coach-specific enhancements
            training_session = self._enhance_session(
                training_session, user_profile, daily_focus
            )

            # Update coach memory
            self._record_session_generation(training_session, daily_focus, week_context)

            logger.info(
                f"[{self.coach_id}] Successfully generated session: {training_session.session_id}"
            )

            return training_session

        except Exception as e:
            logger.error(f"[{self.coach_id}] Error generating session: {e}")
            return None

    def _analyze_training_context(
        self,
        user_profile: FitnessProfile,
        daily_focus: DailyFocus,
        other_sessions: List[TrainingSession],
    ) -> Dict[str, Any]:
        """Analyze training context for intelligent session generation"""

        # Calculate current training load from other sessions
        current_load = sum(session.duration_minutes for session in other_sessions)
        available_time = max(0, daily_focus.volume_target - current_load)

        # Check for equipment conflicts
        equipment_used = set()
        for session in other_sessions:
            equipment_used.update(session.equipment_needed)

        # Analyze user preferences from memory
        user_preferences = self.memory.user_preferences.get(user_profile.user_id, {})

        # Check limitations and constraints
        limitations = user_profile.current_limitations + self.memory.user_limitations

        return {
            "available_time_minutes": available_time,
            "equipment_conflicts": list(equipment_used),
            "user_preferences": user_preferences,
            "limitations": limitations,
            "is_primary_domain": self.domain == daily_focus.primary_domain,
            "intensity_constraint": daily_focus.intensity_target,
            "recovery_priority": daily_focus.recovery_priority,
        }

    def _create_session_input(
        self,
        user_profile: FitnessProfile,
        daily_focus: DailyFocus,
        context_analysis: Dict[str, Any],
        date: datetime,
    ) -> SessionGenerationInput:
        """Create session input for the existing session generation tool"""

        # Base input parameters
        base_params = {
            "command": self.domain,
            "date": date.strftime("%Y-%m-%d"),
            "intensity": daily_focus.intensity_target,
            "duration": min(
                context_analysis["available_time_minutes"], 90
            ),  # max 90 minutes
        }

        # Add domain-specific parameters
        domain_params = self._get_domain_specific_params(user_profile, daily_focus)
        base_params.update(domain_params)

        return SessionGenerationInput(**base_params)

    def _get_domain_specific_params(
        self, user_profile: FitnessProfile, daily_focus: DailyFocus
    ) -> Dict[str, Any]:
        """Get domain-specific parameters - override in subclasses"""
        return {}

    def _extract_session_from_output(
        self, output: SessionGenerationOutput
    ) -> Union[StrengthSession, RunningSession, CyclingSession, RecoverySession]:
        """Extract session from output based on domain"""
        if self.domain == "strength" and output.strength_session:
            return output.strength_session
        elif self.domain == "running" and output.running_session:
            return output.running_session
        elif self.domain == "cycling" and output.cycling_session:
            return output.cycling_session
        elif self.domain == "recovery" and output.recovery_session:
            return output.recovery_session
        else:
            raise ValueError(f"No {self.domain} session found in output")

    def _generate_rationale(
        self,
        daily_focus: DailyFocus,
        context_analysis: Dict[str, Any],
        generated_session: Any,
    ) -> str:
        """Generate detailed rationale for session choice"""

        rationale_parts = []

        # Focus alignment
        if context_analysis["is_primary_domain"]:
            rationale_parts.append(f"Primary {self.domain} focus for the day")
        else:
            rationale_parts.append(
                f"Supporting {self.domain} work complementing {daily_focus.primary_domain}"
            )

        # Intensity reasoning
        rationale_parts.append(
            f"Targeting {daily_focus.intensity_target} intensity to align with weekly periodization"
        )

        # Time constraints
        if context_analysis["available_time_minutes"] < 60:
            rationale_parts.append(
                f"Condensed session due to limited time ({context_analysis['available_time_minutes']} min available)"
            )

        # User considerations
        if context_analysis["limitations"]:
            rationale_parts.append(
                f"Modified for limitations: {', '.join(context_analysis['limitations'][:2])}"
            )

        # Recovery considerations
        if daily_focus.recovery_priority:
            rationale_parts.append("Lower intensity to prioritize recovery")

        return ". ".join(rationale_parts) + "."

    def _enhance_session(
        self,
        training_session: TrainingSession,
        user_profile: FitnessProfile,
        daily_focus: DailyFocus,
    ) -> TrainingSession:
        """Add coach-specific enhancements to the training session"""

        # Add expected benefits
        training_session.expected_benefits = self._determine_expected_benefits(
            training_session, daily_focus
        )

        # Set difficulty level based on user profile and intensity
        training_session.difficulty_level = self._estimate_difficulty(
            training_session, user_profile
        )

        # Add adaptation focus
        training_session.adaptation_focus = self._determine_adaptation_focus(
            training_session, daily_focus
        )

        return training_session

    def _determine_expected_benefits(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Determine expected benefits - override in subclasses"""
        return [f"Improved {self.domain} fitness", "Enhanced training adaptation"]

    def _estimate_difficulty(
        self, session: TrainingSession, user_profile: FitnessProfile
    ) -> int:
        """Estimate difficulty level for the user"""
        # Base difficulty from intensity
        base_difficulty = {"low": 3, "moderate": 5, "high": 8}.get(
            str(session.intensity_level).lower(), 5
        )

        # Adjust for user experience
        if user_profile.training_experience_years:
            if user_profile.training_experience_years > 5:
                base_difficulty = max(1, base_difficulty - 1)
            elif user_profile.training_experience_years < 1:
                base_difficulty = min(10, base_difficulty + 1)

        return base_difficulty

    def _determine_adaptation_focus(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Determine what adaptations this session targets"""
        return [f"{self.domain}_adaptation", "general_fitness"]

    def _record_session_generation(
        self,
        session: TrainingSession,
        daily_focus: DailyFocus,
        week_context: WeeklyFocus,
    ):
        """Record session generation in coach memory"""
        self.memory.sessions_created.append(session.session_id)
        self.session_count += 1
        self.last_session_date = session.date

        # Record coordination context
        coordination_record = {
            "session_id": session.session_id,
            "daily_focus": daily_focus.focus_description,
            "weekly_context": week_context.weekly_theme,
            "generated_at": datetime.now().isoformat(),
        }
        self.memory.coordination_history.append(coordination_record)

    async def adapt_based_on_feedback(
        self, feedback: SessionFeedback
    ) -> Dict[str, Any]:
        """Adapt coaching based on user feedback"""

        adaptations = {}

        # Update user preferences based on feedback
        user_id = feedback.user_id
        if user_id not in self.memory.user_preferences:
            self.memory.user_preferences[user_id] = {}

        user_prefs = self.memory.user_preferences[user_id]

        # Learn from difficulty ratings
        if feedback.difficulty_rating is not None:
            if feedback.difficulty_rating > 8:  # too hard
                user_prefs["intensity_preference"] = (
                    user_prefs.get("intensity_preference", 1.0) * 0.9
                )
                adaptations["intensity_adjustment"] = "decrease"
            elif feedback.difficulty_rating < 3:  # too easy
                user_prefs["intensity_preference"] = (
                    user_prefs.get("intensity_preference", 1.0) * 1.1
                )
                adaptations["intensity_adjustment"] = "increase"

        # Learn from enjoyment ratings
        if feedback.enjoyment_rating is not None:
            if feedback.enjoyment_rating > 7:
                # Mark as successful pattern
                pattern = {
                    "session_type": "high_enjoyment",
                    "feedback": feedback.dict(),
                    "recorded_at": datetime.now().isoformat(),
                }
                self.memory.successful_patterns.append(pattern)
                adaptations["pattern_learned"] = "high_enjoyment"

        # Update completion rate
        total_sessions = len(self.memory.sessions_created)
        if total_sessions > 0:
            completed_count = sum(
                1
                for sid in self.memory.sessions_created
                if any(f.session_id == sid and f.completed for f in [feedback])
            )  # simplified calculation
            self.memory.session_completion_rate = completed_count / total_sessions

        return adaptations


# Specialized Coach Classes
class StrengthCoach(DomainCoach):
    """Specialized coach for strength training"""

    def __init__(self):
        # Create memory for this coach
        memory = CoachMemory(coach_id="strength_coach", domain="strength")
        super().__init__(
            coach_id="strength_coach",
            domain="strength",
            expertise_level="expert",
            memory=memory,
        )

    def _get_domain_specific_params(
        self, user_profile: FitnessProfile, daily_focus: DailyFocus
    ) -> Dict[str, Any]:
        """Strength-specific parameters"""

        # Determine training category based on focus
        training_category = "Full Body"  # default
        if "upper" in daily_focus.focus_description.lower():
            training_category = "Upper Body"
        elif "lower" in daily_focus.focus_description.lower():
            training_category = "Lower Body"
        elif "power" in daily_focus.focus_description.lower():
            training_category = "Power Development"

        return {
            "training_category": training_category,
            "skill_category": "General Strength",
            "intensity_measure": "RPE",
            "stress_level_measure": "Subjective",
        }

    def _determine_expected_benefits(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Strength-specific benefits"""
        benefits = ["Increased muscular strength", "Improved body composition"]

        if daily_focus.intensity_target == "high":
            benefits.append("Enhanced maximal strength")
        elif daily_focus.intensity_target == "moderate":
            benefits.extend(["Muscular endurance", "Hypertrophy adaptation"])
        else:
            benefits.extend(["Movement quality", "Recovery enhancement"])

        return benefits

    def _determine_adaptation_focus(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Strength-specific adaptations"""
        focus_areas = ["strength", "muscle_mass"]

        if "power" in daily_focus.focus_description.lower():
            focus_areas.append("power_development")
        if "endurance" in daily_focus.focus_description.lower():
            focus_areas.append("muscular_endurance")

        return focus_areas


class RunningCoach(DomainCoach):
    """Specialized coach for running"""

    def __init__(self):
        # Create memory for this coach
        memory = CoachMemory(coach_id="running_coach", domain="running")
        super().__init__(
            coach_id="running_coach",
            domain="running",
            expertise_level="expert",
            memory=memory,
        )

    def _get_domain_specific_params(
        self, user_profile: FitnessProfile, daily_focus: DailyFocus
    ) -> Dict[str, Any]:
        """Running-specific parameters"""

        # Determine distance based on intensity and available time
        base_distance = 5.0  # km
        if daily_focus.intensity_target == "low":
            distance = min(
                base_distance * 1.5, daily_focus.volume_target / 8
            )  # 8 min/km pace
        elif daily_focus.intensity_target == "high":
            distance = min(
                base_distance * 0.7, daily_focus.volume_target / 5
            )  # 5 min/km pace
        else:
            distance = min(
                base_distance, daily_focus.volume_target / 6.5
            )  # 6.5 min/km pace

        return {
            "distance": max(2.0, distance),  # minimum 2km
            "run_category": "General Aerobic",
            "skill_category": "Intermediate",
            "intensity_measure": "% HRR",
            "stress_level_measure": "RPE",
        }

    def _determine_expected_benefits(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Running-specific benefits"""
        benefits = ["Improved cardiovascular fitness", "Enhanced aerobic capacity"]

        if daily_focus.intensity_target == "high":
            benefits.extend(["Lactate threshold improvement", "VO2 max enhancement"])
        elif daily_focus.intensity_target == "moderate":
            benefits.extend(["Aerobic base building", "Running economy"])
        else:
            benefits.extend(["Active recovery", "Movement efficiency"])

        return benefits


class CyclingCoach(DomainCoach):
    """Specialized coach for cycling"""

    def __init__(self):
        # Create memory for this coach
        memory = CoachMemory(coach_id="cycling_coach", domain="cycling")
        super().__init__(
            coach_id="cycling_coach",
            domain="cycling",
            expertise_level="expert",
            memory=memory,
        )

    def _get_domain_specific_params(
        self, user_profile: FitnessProfile, daily_focus: DailyFocus
    ) -> Dict[str, Any]:
        """Cycling-specific parameters"""

        return {
            "location": "Indoor",  # default to indoor for consistency
            "cycling_category": ["Road"],
            "skill_category": "Basic Riding",
            "distance": daily_focus.volume_target
            * 0.5,  # approximate distance based on time
        }

    def _determine_expected_benefits(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Cycling-specific benefits"""
        benefits = ["Improved leg strength", "Enhanced cardiovascular fitness"]

        if daily_focus.intensity_target == "high":
            benefits.extend(["FTP improvement", "Power development"])
        elif daily_focus.intensity_target == "moderate":
            benefits.extend(["Aerobic capacity", "Endurance base"])
        else:
            benefits.extend(["Active recovery", "Pedaling efficiency"])

        return benefits


class RecoveryCoach(DomainCoach):
    """Specialized coach for recovery sessions"""

    def __init__(self):
        # Create memory for this coach
        memory = CoachMemory(coach_id="recovery_coach", domain="recovery")
        super().__init__(
            coach_id="recovery_coach",
            domain="recovery",
            expertise_level="expert",
            memory=memory,
        )

    def _get_domain_specific_params(
        self, user_profile: FitnessProfile, daily_focus: DailyFocus
    ) -> Dict[str, Any]:
        """Recovery-specific parameters"""

        # Determine recovery focus based on other training
        recovery_focus = "Active Recovery"
        if daily_focus.recovery_priority:
            recovery_focus = "Deep Recovery"

        return {
            "recovery_focus": recovery_focus,
            "duration": min(daily_focus.volume_target, 60),  # max 60 minutes recovery
        }

    def _determine_expected_benefits(
        self, session: TrainingSession, daily_focus: DailyFocus
    ) -> List[str]:
        """Recovery-specific benefits"""
        return [
            "Enhanced recovery",
            "Stress reduction",
            "Improved flexibility",
            "Better sleep quality",
        ]


# Coach Factory
def create_coach(domain: str) -> DomainCoach:
    """Factory function to create domain-specific coaches"""
    coaches = {
        "strength": StrengthCoach,
        "running": RunningCoach,
        "cycling": CyclingCoach,
        "recovery": RecoveryCoach,
    }

    if domain not in coaches:
        raise ValueError(f"Unknown domain: {domain}. Available: {list(coaches.keys())}")

    return coaches[domain]()
