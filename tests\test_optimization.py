#!/usr/bin/env python3
"""
Test script for coaching graph optimization.

This script tests both the original and optimized graphs to validate:
1. Functionality - ensure optimized graph produces correct results
2. Performance - measure latency and LLM call reductions
3. Cost efficiency - track API usage differences
"""

import asyncio
import time
import logging
from typing import Dict, Any, List
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import both graph versions
from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
)
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_coaching_graph,
)

# Import state
from athlea_langgraph.states.state import AgentState


class PerformanceTracker:
    """Track performance metrics for graph execution."""

    def __init__(self, graph_name: str):
        self.graph_name = graph_name
        self.start_time = None
        self.end_time = None
        self.llm_calls = 0
        self.nodes_executed = []
        self.total_latency = 0

    def start(self):
        self.start_time = time.time()

    def end(self):
        self.end_time = time.time()
        self.total_latency = self.end_time - self.start_time

    def add_node(self, node_name: str):
        self.nodes_executed.append(node_name)

    def get_summary(self) -> Dict[str, Any]:
        return {
            "graph": self.graph_name,
            "total_latency": round(self.total_latency, 2),
            "nodes_executed": len(self.nodes_executed),
            "node_path": " → ".join(self.nodes_executed),
            "estimated_llm_calls": self._estimate_llm_calls(),
        }

    def _estimate_llm_calls(self) -> int:
        """Estimate LLM calls based on nodes executed."""
        llm_heavy_nodes = [
            "early_intent_classifier",
            "planning",
            "complexity_assessment",
            "knowledge_assessment",
            "intelligence_hub",
            "graphrag_retrieval",
            "strength_coach",
            "cardio_coach",
            "nutrition_coach",
            "recovery_coach",
            "mental_coach",
            "cycling_coach",
            "aggregation",
            "smart_executor",
        ]

        return sum(
            1
            for node in self.nodes_executed
            if any(heavy in node for heavy in llm_heavy_nodes)
        )


async def test_query(graph, query: str, tracker: PerformanceTracker) -> Dict[str, Any]:
    """Test a single query against a graph."""
    logger.info(f"\n{'='*60}")
    logger.info(f"Testing {tracker.graph_name}: {query}")
    logger.info(f"{'='*60}")

    # Create initial state
    initial_state = {
        "user_query": query,
        "messages": [],
        "user_profile": {"experience_level": "beginner", "goals": ["fitness"]},
        "execution_steps": [],
    }

    tracker.start()

    try:
        # Execute the graph
        config = {"configurable": {"thread_id": "test_thread"}}
        result = await graph.ainvoke(initial_state, config=config)

        tracker.end()

        # Extract execution path
        if "execution_steps" in result:
            tracker.nodes_executed = result["execution_steps"]

        # Log results
        final_response = result.get("final_response", "No response")
        logger.info(f"✅ {tracker.graph_name} completed")
        logger.info(f"   Latency: {tracker.total_latency:.2f}s")
        logger.info(
            f"   Nodes: {len(tracker.nodes_executed)} ({' → '.join(tracker.nodes_executed)})"
        )
        logger.info(f"   Response: {final_response[:100]}...")

        return {
            "success": True,
            "response": final_response,
            "result": result,
            "performance": tracker.get_summary(),
        }

    except Exception as e:
        tracker.end()
        logger.error(f"❌ {tracker.graph_name} failed: {e}")

        return {"success": False, "error": str(e), "performance": tracker.get_summary()}


async def run_comparison_tests():
    """Run comprehensive comparison tests."""
    logger.info("🚀 Starting Coaching Graph Optimization Comparison Tests")

    # Test queries covering different scenarios
    test_queries = [
        # Simple coaching questions (should benefit most from optimization)
        "How do I do proper squats?",
        "What should I eat before a workout?",
        "How much sleep do I need for recovery?",
        # Multi-domain questions (should still be optimized)
        "I want to build muscle and lose fat, help me plan nutrition and training",
        "Create a complete fitness plan for a beginner",
        # Research-heavy questions (should use GraphRAG appropriately)
        "What does the latest research say about protein timing?",
        "Show me scientific evidence for high-intensity interval training benefits",
        # Simple greetings (should route to greeting immediately)
        "Hello, I'm new here",
        "Thanks for the help!",
    ]

    # Create graph configurations
    config = {
        "user_id": "test_user",
        "enable_memory": False,  # Disable for consistent testing
        "use_react_agents": True,
        "max_iterations": 3,
        "enable_human_feedback": False,
    }

    logger.info("📊 Creating graphs...")

    # Create both graph versions
    try:
        original_graph = await create_comprehensive_coaching_graph(config)
        optimized_graph = await create_optimized_coaching_graph(config)
        logger.info("✅ Both graphs created successfully")
    except Exception as e:
        logger.error(f"❌ Failed to create graphs: {e}")
        return

    # Run tests
    results = {"original": [], "optimized": [], "summary": {}}

    for i, query in enumerate(test_queries, 1):
        logger.info(f"\n📝 Test {i}/{len(test_queries)}: {query[:50]}...")

        # Test original graph
        original_tracker = PerformanceTracker("Original Graph")
        original_result = await test_query(original_graph, query, original_tracker)
        results["original"].append(original_result)

        # Test optimized graph
        optimized_tracker = PerformanceTracker("Optimized Graph")
        optimized_result = await test_query(optimized_graph, query, optimized_tracker)
        results["optimized"].append(optimized_result)

        # Compare results
        if original_result["success"] and optimized_result["success"]:
            original_perf = original_result["performance"]
            optimized_perf = optimized_result["performance"]

            latency_improvement = (
                (original_perf["total_latency"] - optimized_perf["total_latency"])
                / original_perf["total_latency"]
                * 100
            )

            node_reduction = (
                (original_perf["nodes_executed"] - optimized_perf["nodes_executed"])
                / original_perf["nodes_executed"]
                * 100
            )

            llm_reduction = (
                (
                    original_perf["estimated_llm_calls"]
                    - optimized_perf["estimated_llm_calls"]
                )
                / max(original_perf["estimated_llm_calls"], 1)
                * 100
            )

            logger.info(f"📈 IMPROVEMENTS:")
            logger.info(f"   Latency: {latency_improvement:+.1f}%")
            logger.info(f"   Nodes: {node_reduction:+.1f}%")
            logger.info(f"   LLM calls: {llm_reduction:+.1f}%")

        # Small delay between tests
        await asyncio.sleep(1)

    # Generate summary report
    await generate_summary_report(results)


async def generate_summary_report(results: Dict[str, Any]):
    """Generate comprehensive summary report."""
    logger.info(f"\n{'='*80}")
    logger.info("📊 OPTIMIZATION SUMMARY REPORT")
    logger.info(f"{'='*80}")

    original_results = [r for r in results["original"] if r["success"]]
    optimized_results = [r for r in results["optimized"] if r["success"]]

    if not original_results or not optimized_results:
        logger.error("❌ Insufficient successful test results for comparison")
        return

    # Calculate average improvements
    total_latency_improvement = 0
    total_node_reduction = 0
    total_llm_reduction = 0

    for orig, opt in zip(original_results, optimized_results):
        orig_perf = orig["performance"]
        opt_perf = opt["performance"]

        latency_improvement = (
            (orig_perf["total_latency"] - opt_perf["total_latency"])
            / orig_perf["total_latency"]
            * 100
        )

        node_reduction = (
            (orig_perf["nodes_executed"] - opt_perf["nodes_executed"])
            / orig_perf["nodes_executed"]
            * 100
        )

        llm_reduction = (
            (orig_perf["estimated_llm_calls"] - opt_perf["estimated_llm_calls"])
            / max(orig_perf["estimated_llm_calls"], 1)
            * 100
        )

        total_latency_improvement += latency_improvement
        total_node_reduction += node_reduction
        total_llm_reduction += llm_reduction

    num_tests = len(original_results)
    avg_latency_improvement = total_latency_improvement / num_tests
    avg_node_reduction = total_node_reduction / num_tests
    avg_llm_reduction = total_llm_reduction / num_tests

    # Print summary
    logger.info(f"✅ OPTIMIZATION RESULTS ({num_tests} successful tests):")
    logger.info(f"")
    logger.info(f"🚀 Average Latency Improvement: {avg_latency_improvement:+.1f}%")
    logger.info(f"📉 Average Node Reduction: {avg_node_reduction:+.1f}%")
    logger.info(f"💰 Average LLM Call Reduction: {avg_llm_reduction:+.1f}%")
    logger.info(f"")

    # Detailed breakdown
    logger.info("📋 DETAILED BREAKDOWN:")
    original_avg_latency = (
        sum(r["performance"]["total_latency"] for r in original_results) / num_tests
    )
    optimized_avg_latency = (
        sum(r["performance"]["total_latency"] for r in optimized_results) / num_tests
    )

    original_avg_nodes = (
        sum(r["performance"]["nodes_executed"] for r in original_results) / num_tests
    )
    optimized_avg_nodes = (
        sum(r["performance"]["nodes_executed"] for r in optimized_results) / num_tests
    )

    original_avg_llm = (
        sum(r["performance"]["estimated_llm_calls"] for r in original_results)
        / num_tests
    )
    optimized_avg_llm = (
        sum(r["performance"]["estimated_llm_calls"] for r in optimized_results)
        / num_tests
    )

    logger.info(
        f"   Original Graph: {original_avg_latency:.2f}s, {original_avg_nodes:.1f} nodes, {original_avg_llm:.1f} LLM calls"
    )
    logger.info(
        f"   Optimized Graph: {optimized_avg_latency:.2f}s, {optimized_avg_nodes:.1f} nodes, {optimized_avg_llm:.1f} LLM calls"
    )
    logger.info(f"")

    # Success metrics
    original_success_rate = len(original_results) / len(results["original"]) * 100
    optimized_success_rate = len(optimized_results) / len(results["optimized"]) * 100

    logger.info(f"✅ SUCCESS RATES:")
    logger.info(f"   Original Graph: {original_success_rate:.1f}%")
    logger.info(f"   Optimized Graph: {optimized_success_rate:.1f}%")
    logger.info(f"")

    # Expected cost savings
    logger.info(f"💡 EXPECTED BENEFITS:")
    logger.info(f"   - {avg_latency_improvement:.0f}% faster response times")
    logger.info(f"   - {avg_llm_reduction:.0f}% reduction in LLM API costs")
    logger.info(f"   - {avg_node_reduction:.0f}% fewer processing steps")
    logger.info(f"   - Improved user experience with faster responses")
    logger.info(f"   - Reduced infrastructure costs")

    logger.info(f"\n{'='*80}")
    logger.info("🎉 OPTIMIZATION TEST COMPLETE!")
    logger.info(f"{'='*80}")


if __name__ == "__main__":
    asyncio.run(run_comparison_tests())
