"""
Sleep Optimization Tool

Provides comprehensive sleep quality assessment, optimal bedtime calculations,
sleep hygiene protocols, and environmental optimization recommendations.
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import httpx
from langchain_core.tools import BaseTool as LangChainBaseTool, tool
from pydantic import BaseModel, Field as PydanticField

from ..base_tool import BaseDomainTool
from ..schemas.recovery_schemas import SleepAssessmentInput, SleepOptimizationOutput

logger = logging.getLogger(__name__)


class SleepOptimizationTool(BaseDomainTool):
    """
    Advanced sleep optimization with evidence-based interventions,
    chronotype assessment, sleep disorder screening, and environmental factors.
    """

    domain: str = "recovery"
    name: str = "sleep_optimization_tool"  # Ensure this matches the original tool name
    description: str = (
        "Comprehensive sleep optimization with chronotype assessment, sleep disorder "
        "screening, CBT-I techniques, environmental factors, and wearable integration"
    )

    def __init__(self):
        super().__init__()

        # Morningness-Eveningness Questionnaire (MEQ) scoring
        self.chronotype_profiles = {
            "definite_morning": {
                "optimal_bedtime_range": ("21:00", "22:30"),
                "optimal_wake_range": ("06:00", "07:30"),
                "peak_alertness": "morning",
                "recommendations": [
                    "Take advantage of morning light exposure immediately upon waking",
                    "Schedule demanding tasks for 8-10 AM",
                    "Avoid caffeine after 2 PM to protect sleep",
                    "Wind down earlier with dim lighting after 8 PM",
                ],
            },
            "moderate_morning": {
                "optimal_bedtime_range": ("22:00", "23:30"),
                "optimal_wake_range": ("06:30", "08:00"),
                "peak_alertness": "mid_morning",
                "recommendations": [
                    "Get 15-30 minutes morning light within 2 hours of waking",
                    "Schedule important work for 9-11 AM",
                    "Limit caffeine after 3 PM",
                    "Begin wind-down routine by 9 PM",
                ],
            },
            "neutral": {
                "optimal_bedtime_range": ("22:30", "00:00"),
                "optimal_wake_range": ("07:00", "08:30"),
                "peak_alertness": "mid_day",
                "recommendations": [
                    "Maintain consistent sleep schedule within 30 minutes",
                    "Use bright light therapy in morning if needed",
                    "Avoid caffeine after 4 PM",
                    "Flexible wind-down routine starting 1-2 hours before bed",
                ],
            },
            "moderate_evening": {
                "optimal_bedtime_range": ("23:00", "00:30"),
                "optimal_wake_range": ("07:30", "09:00"),
                "peak_alertness": "late_afternoon",
                "recommendations": [
                    "Use bright light box 30 minutes upon waking",
                    "Schedule demanding tasks for 2-4 PM",
                    "Avoid caffeine after 5 PM",
                    "Use blue light blocking glasses after sunset",
                ],
            },
            "definite_evening": {
                "optimal_bedtime_range": ("00:00", "01:30"),
                "optimal_wake_range": ("08:00", "09:30"),
                "peak_alertness": "evening",
                "recommendations": [
                    "Consider delayed sleep phase if possible",
                    "Use morning light therapy upon waking",
                    "Avoid stimulating activities before noon",
                    "Create very dark sleep environment",
                ],
            },
        }

        # Sleep disorder screening questionnaires
        self.sleep_disorder_screening = {
            "sleep_apnea_risk": {
                "high_risk_indicators": [
                    "BMI > 35",
                    "neck circumference > 17 inches (men) or 16 inches (women)",
                    "loud snoring",
                    "witnessed breathing pauses",
                    "morning headaches",
                    "excessive daytime sleepiness despite adequate sleep time",
                ],
                "screening_questions": [
                    "Do you snore loudly?",
                    "Do you often feel tired during the day?",
                    "Has anyone observed you stop breathing during sleep?",
                    "Do you have high blood pressure?",
                ],
                "recommendations": [
                    "Consult sleep specialist for formal evaluation",
                    "Consider sleep study (polysomnography)",
                    "Maintain healthy weight",
                    "Avoid alcohol and sedatives before bed",
                    "Sleep on side rather than back",
                ],
            },
            "insomnia_severity": {
                "cognitive_factors": [
                    "Racing thoughts at bedtime",
                    "Worry about sleep performance",
                    "Catastrophic thinking about sleep loss",
                    "Hypervigilance about sleep environment",
                ],
                "behavioral_factors": [
                    "Inconsistent sleep schedule",
                    "Excessive time in bed when not sleeping",
                    "Stimulating activities before bed",
                    "Clock watching during night awakenings",
                ],
                "cbt_i_techniques": [
                    "Sleep restriction therapy",
                    "Stimulus control therapy",
                    "Cognitive restructuring",
                    "Relaxation training",
                    "Sleep hygiene education",
                ],
            },
            "circadian_rhythm_disorders": {
                "delayed_sleep_phase": [
                    "Difficulty falling asleep before 2-6 AM",
                    "Difficulty waking before 10 AM-1 PM",
                    "Normal sleep quality when schedule is followed",
                    "Problems with conventional work/school schedules",
                ],
                "advanced_sleep_phase": [
                    "Sleep onset between 6-9 PM",
                    "Wake time between 1-5 AM",
                    "Afternoon sleepiness",
                    "More common in older adults",
                ],
            },
        }

        # Evidence-based CBT-I techniques database
        self.cbt_i_interventions = {
            "sleep_restriction": {
                "description": "Limit time in bed to actual sleep time to increase sleep efficiency",
                "protocol": [
                    "Calculate average total sleep time from sleep diary",
                    "Set initial time in bed = total sleep time + 30 minutes",
                    "Maintain consistent wake time regardless of sleep quality",
                    "Gradually increase time in bed as sleep efficiency improves (>85%)",
                    "Minimum time in bed should never be less than 5 hours",
                ],
                "contraindications": [
                    "History of seizures or bipolar disorder",
                    "Significant daytime safety concerns",
                    "Substance abuse disorders",
                ],
            },
            "stimulus_control": {
                "description": "Re-establish bed and bedroom as strong cues for sleep",
                "protocol": [
                    "Go to bed only when sleepy",
                    "Use bed only for sleep and intimacy",
                    "If unable to sleep within 15-20 minutes, leave bedroom",
                    "Return to bed only when sleepy again",
                    "Maintain consistent wake time regardless of sleep duration",
                    "Avoid daytime napping",
                ],
            },
            "cognitive_restructuring": {
                "description": "Address unhelpful thoughts and beliefs about sleep",
                "common_sleep_myths": [
                    "I need exactly 8 hours of sleep to function",
                    "Poor sleep will ruin my next day",
                    "I must fall asleep within 15 minutes",
                    "Waking during the night is abnormal",
                ],
                "helpful_cognitions": [
                    "Sleep needs vary between individuals",
                    "One poor night's sleep is manageable",
                    "Brief awakenings are normal part of sleep",
                    "Sleep will come naturally when body is ready",
                ],
            },
            "relaxation_techniques": {
                "progressive_muscle_relaxation": [
                    "Start with toes, tense for 5 seconds then release",
                    "Progress systematically through all muscle groups",
                    "Focus on contrast between tension and relaxation",
                    "Practice daily for 15-20 minutes",
                ],
                "4_7_8_breathing": [
                    "Inhale through nose for 4 counts",
                    "Hold breath for 7 counts",
                    "Exhale through mouth for 8 counts",
                    "Repeat cycle 4 times",
                ],
                "body_scan_meditation": [
                    "Focus attention on different body parts sequentially",
                    "Notice sensations without trying to change them",
                    "Return attention gently when mind wanders",
                    "Practice for 10-30 minutes",
                ],
            },
        }

        # Environmental optimization based on research
        self.environmental_factors = {
            "temperature": {
                "optimal_range": "60-67°F (15.6-19.4°C)",
                "physiological_basis": "Core body temperature drops before sleep onset",
                "interventions": [
                    "Use breathable bedding materials (cotton, bamboo, linen)",
                    "Consider cooling mattress pad for hot sleepers",
                    "Take warm bath 90 minutes before bed to aid temperature drop",
                    "Keep feet warm with socks if circulation is poor",
                ],
            },
            "light_exposure": {
                "circadian_timing": {
                    "morning": "Bright light (10,000 lux) for 30 minutes upon waking",
                    "afternoon": "Natural outdoor light for 15-30 minutes",
                    "evening": "Dim light (<50 lux) 2 hours before bedtime",
                    "night": "Red light (<5 lux) for safety if needed",
                },
                "blue_light_impact": "Suppresses melatonin production for 2-3 hours",
                "interventions": [
                    "Use blue light blocking glasses after sunset",
                    "Install f.lux or night mode on devices",
                    "Blackout curtains or eye mask for complete darkness",
                    "Cover LED lights on electronics",
                ],
            },
            "sound_environment": {
                "optimal_noise_level": "<35 dB (equivalent to whisper)",
                "masking_sounds": [
                    "White noise (equal energy across frequencies)",
                    "Pink noise (lower frequency emphasis)",
                    "Nature sounds (rain, ocean waves)",
                    "Brown noise (deeper, more bass-heavy)",
                ],
                "interventions": [
                    "Use earplugs rated for sleep (25-33 dB reduction)",
                    "Sound machine with timer function",
                    "Address external noise sources when possible",
                    "Consider acoustic treatments for bedroom",
                ],
            },
            "air_quality": {
                "optimal_conditions": {
                    "humidity": "30-50%",
                    "co2_levels": "<1000 ppm",
                    "allergen_control": "HEPA filtration",
                },
                "interventions": [
                    "Use humidifier or dehumidifier as needed",
                    "Open window for fresh air when weather permits",
                    "HEPA air purifier for allergen removal",
                    "Regular cleaning to reduce dust mites",
                ],
            },
        }

        # Wearable device integration parameters
        self.wearable_integration = {
            "hrv_interpretation": {
                "high_hrv": "Good recovery, maintain current sleep schedule",
                "low_hrv": "Poor recovery, prioritize sleep extension and quality",
                "declining_trend": "Increase recovery focus, consider sleep debt",
            },
            "sleep_stage_optimization": {
                "deep_sleep_deficit": [
                    "Ensure adequate sleep duration",
                    "Optimize bedroom temperature (cooler)",
                    "Consider magnesium supplementation",
                    "Avoid alcohol which fragments deep sleep",
                ],
                "rem_sleep_deficit": [
                    "Maintain consistent sleep schedule",
                    "Avoid REM-suppressing medications",
                    "Consider dream recall practices",
                    "Address anxiety or stress factors",
                ],
            },
        }

    def _assess_chronotype(
        self, bedtime: str, wake_time: str, sleep_quality: int
    ) -> str:
        """Assess chronotype based on sleep timing and preferences."""
        # Simple chronotype assessment based on natural sleep timing
        bedtime_hour = int(bedtime.split(":")[0])
        wake_hour = int(wake_time.split(":")[0])

        # Adjust for overnight sleep
        if bedtime_hour > 12:
            bedtime_hour -= 24

        sleep_midpoint = (bedtime_hour + wake_hour) / 2

        if sleep_midpoint < 2.5:
            return "definite_morning"
        elif sleep_midpoint < 3.5:
            return "moderate_morning"
        elif sleep_midpoint < 4.5:
            return "neutral"
        elif sleep_midpoint < 5.5:
            return "moderate_evening"
        else:
            return "definite_evening"

    def _screen_sleep_disorders(self, assessment: SleepAssessmentInput) -> List[str]:
        """Screen for potential sleep disorders based on assessment."""
        risk_factors = []

        # Sleep apnea screening
        if (
            assessment.time_to_fall_asleep < 5
            and assessment.sleep_quality_rating < 6
            and assessment.morning_alertness < 6
        ):
            risk_factors.append(
                "Consider sleep apnea evaluation - rapid sleep onset with poor quality"
            )

        # Insomnia screening
        if assessment.time_to_fall_asleep > 30 or assessment.night_awakenings > 2:
            risk_factors.append("Insomnia symptoms present - consider CBT-I techniques")

        # Circadian rhythm disorder screening
        bedtime_hour = int(assessment.bedtime.split(":")[0])
        if bedtime_hour >= 2 and bedtime_hour < 6:  # Check for bedtimes 2 AM to 5:XX AM
            risk_factors.append("Delayed sleep phase pattern - consider chronotherapy")

        return risk_factors

    async def _get_environmental_data(
        self, location: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get environmental data affecting sleep."""
        # This would integrate with weather APIs
        # For now, return default recommendations
        return {
            "temperature_recommendation": "Maintain bedroom temperature 60-67°F",
            "humidity_recommendation": "Keep humidity between 30-50%",
            "air_quality_status": "good",
            "sunrise_time": "06:30",
            "sunset_time": "19:30",
        }

    def _generate_cbt_i_recommendations(
        self, assessment: SleepAssessmentInput
    ) -> List[str]:
        """Generate CBT-I technique recommendations based on assessment."""
        recommendations = []

        # Sleep restriction for poor efficiency
        sleep_efficiency = self._calculate_sleep_efficiency(assessment)
        if sleep_efficiency < 85:
            recommendations.extend(
                [
                    "Consider sleep restriction therapy - limit time in bed to actual sleep time",
                    "Maintain consistent wake time regardless of sleep quality",
                    "Gradually increase time in bed as sleep efficiency improves",
                ]
            )

        # Stimulus control for sleep onset issues
        if assessment.time_to_fall_asleep > 30:
            recommendations.extend(
                [
                    "Practice stimulus control - leave bed if not asleep within 15-20 minutes",
                    "Use bed only for sleep and intimacy",
                    "Return to bed only when feeling sleepy",
                ]
            )

        # Cognitive restructuring for anxiety
        if assessment.sleep_quality_rating < 5:
            recommendations.extend(
                [
                    "Challenge catastrophic thoughts about sleep loss",
                    "Remember that sleep needs vary between individuals",
                    "Practice acceptance of occasional poor sleep nights",
                ]
            )

        # Relaxation techniques
        recommendations.extend(
            [
                "Practice 4-7-8 breathing technique before bed",
                "Try progressive muscle relaxation for 15-20 minutes",
                "Use body scan meditation to release physical tension",
            ]
        )

        return recommendations[:6]

    def _calculate_sleep_efficiency(self, assessment: SleepAssessmentInput) -> float:
        """Calculate sleep efficiency percentage."""
        total_time_in_bed = self._calculate_sleep_duration(
            assessment.bedtime, assessment.wake_time
        )

        # Estimate time awake (sleep onset + night awakenings)
        estimated_awake_time = (
            assessment.time_to_fall_asleep + assessment.night_awakenings * 15
        ) / 60  # Convert to hours

        actual_sleep_time = max(0, total_time_in_bed - estimated_awake_time)
        sleep_efficiency = (actual_sleep_time / total_time_in_bed) * 100

        return min(100, max(0, sleep_efficiency))

    def _calculate_sleep_duration(self, bedtime: str, wake_time: str) -> float:
        """Calculate total sleep duration in hours."""
        try:
            bedtime_dt = datetime.strptime(bedtime, "%H:%M")
            wake_time_dt = datetime.strptime(wake_time, "%H:%M")

            # Handle overnight sleep
            if wake_time_dt < bedtime_dt:
                wake_time_dt += timedelta(days=1)

            duration = wake_time_dt - bedtime_dt
            return duration.total_seconds() / 3600
        except ValueError:
            return 8.0  # Default assumption

    async def optimize_sleep(  # Ensure this method name matches the original tool
        self, assessment: SleepAssessmentInput
    ) -> SleepOptimizationOutput:
        """Generate comprehensive sleep optimization with advanced techniques."""
        try:
            # Assess chronotype
            chronotype = self._assess_chronotype(
                assessment.bedtime,
                assessment.wake_time,
                assessment.sleep_quality_rating,
            )
            chronotype_profile = self.chronotype_profiles[chronotype]

            # Screen for sleep disorders
            disorder_risks = self._screen_sleep_disorders(assessment)

            # Get environmental recommendations
            env_data = await self._get_environmental_data()

            # Calculate metrics
            sleep_efficiency = self._calculate_sleep_efficiency(assessment)
            sleep_debt = max(
                0,
                8.0
                - self._calculate_sleep_duration(
                    assessment.bedtime, assessment.wake_time
                ),
            )

            # Generate CBT-I recommendations
            cbt_i_recs = self._generate_cbt_i_recommendations(assessment)

            # Comprehensive recommendations
            comprehensive_recommendations = []

            # Add chronotype-specific recommendations
            comprehensive_recommendations.extend(
                chronotype_profile["recommendations"][:2]
            )

            # Add CBT-I techniques
            comprehensive_recommendations.extend(cbt_i_recs[:2])

            # Add environmental optimizations
            comprehensive_recommendations.extend(
                [
                    "Optimize bedroom temperature to 60-67°F for better deep sleep",
                    "Use blackout curtains and blue light blocking glasses",
                ]
            )

            # Add disorder-specific recommendations
            if disorder_risks:
                comprehensive_recommendations.extend(disorder_risks[:2])

            return SleepOptimizationOutput(
                sleep_efficiency=round(sleep_efficiency, 1),
                sleep_debt=round(sleep_debt, 1),
                optimal_bedtime=chronotype_profile["optimal_bedtime_range"][0],
                optimal_wake_time=assessment.wake_time,
                sleep_hygiene_recommendations=comprehensive_recommendations[:8],
                environmental_optimizations=[
                    "Maintain bedroom temperature 60-67°F",
                    "Use blackout curtains for complete darkness",
                    "Keep humidity between 30-50%",
                    "Use white noise machine to mask disruptions",
                    "Remove electronic devices from bedroom",
                    "Invest in comfortable, supportive mattress",
                ],
                pre_sleep_routine=[
                    f"Begin wind-down routine 1-2 hours before {chronotype_profile['optimal_bedtime_range'][0]}",
                    "Practice 4-7-8 breathing technique",
                    "Do progressive muscle relaxation",
                    "Read book or listen to calming music",
                    "Take warm bath 90 minutes before bed",
                    "Dim all lights to <50 lux",
                ],
                lifestyle_adjustments=[
                    f"Expose yourself to bright light upon waking ({chronotype_profile['peak_alertness']} type)",
                    "Maintain consistent sleep schedule within 15 minutes",
                    "Avoid caffeine 6+ hours before bedtime",
                    "Exercise regularly but not within 3 hours of bedtime",
                    "Manage stress with meditation or journaling",
                    "Consider sleep specialist consultation if problems persist",
                ],
            )

        except Exception as e:
            logger.error(f"Error in comprehensive sleep optimization: {e}")
            raise


# Create a standalone instance of the tool
sleep_optimization_tool = SleepOptimizationTool()


@tool(args_schema=SleepAssessmentInput)
async def optimize_sleep(
    bedtime: str,
    wake_time: str,
    sleep_quality_rating: int,
    time_to_fall_asleep: int,
    night_awakenings: int,
    morning_alertness: int,
    caffeine_intake: Optional[int] = 0,
    screen_time_before_bed: Optional[int] = 0,
    sleep_environment_issues: List[str] = [],
) -> str:
    """
    Analyzes sleep habits and provides personalized recommendations for sleep optimization. Use this tool when a user asks for help improving their sleep.
    """
    try:
        input_data = SleepAssessmentInput(
            bedtime=bedtime,
            wake_time=wake_time,
            sleep_quality_rating=sleep_quality_rating,
            time_to_fall_asleep=time_to_fall_asleep,
            night_awakenings=night_awakenings,
            morning_alertness=morning_alertness,
            caffeine_intake=caffeine_intake,
            screen_time_before_bed=screen_time_before_bed,
            sleep_environment_issues=sleep_environment_issues,
        )
        result = await sleep_optimization_tool.optimize_sleep(input_data)
        return result.model_dump_json(indent=2)
    except Exception as e:
        logger.error(f"Error in optimize_sleep tool: {e}")
        return f'{{"error": "Failed to optimize sleep: {str(e)}"}}'
