#!/bin/bash
# lint-check.sh - Comprehensive linting for python-langgraph (non-failing version)

# Add Python 3.9 user bin to PATH for linting tools (macOS compatibility)
export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"

echo "🔍 Running Python linting suite..."

FAILED=0

echo "📝 Checking code formatting with Black..."
if poetry run black --check .; then
    echo "✅ Black formatting check passed"
else
    echo "❌ Black formatting issues found. Run 'poetry run black .' to fix"
    FAILED=1
fi

echo "📦 Checking import sorting with isort..."
if poetry run isort --check-only .; then
    echo "✅ isort check passed"
else
    echo "❌ Import sorting issues found. Run 'poetry run isort .' to fix"
    FAILED=1
fi

echo "🔧 Running type checking with MyPy..."
if poetry run mypy athlea_langgraph/; then
    echo "✅ MyPy type checking passed"
else
    echo "❌ Type checking issues found"
    FAILED=1
fi

if [ $FAILED -eq 0 ]; then
    echo "🎉 All linting checks passed!"
else
    echo "⚠️  Some linting checks failed. See output above for details."
fi 