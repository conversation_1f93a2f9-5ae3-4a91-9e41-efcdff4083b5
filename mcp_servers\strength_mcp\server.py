"""
Strength Training MCP Server

Exposes strength training tools via Model Context Protocol (MCP).
Includes exercise database, progression tracking, and strength assessment tools.

Enhanced with comprehensive logging for debugging and monitoring.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolResult,
    EmbeddedResource,
    GetPromptResult,
    ImageContent,
    InitializeResult,
    Prompt,
    PromptArgument,
    Resource,
    ServerCapabilities,
)
from mcp.types import TextContent
from mcp.types import TextContent as TextContentType
from mcp.types import Tool

# Import strength training tools
from athlea_langgraph.tools.strength import (
    StrengthAssessmentTool,
    StrengthExerciseDatabase,
)

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("/tmp/strength_mcp_server.log", mode="a"),
    ],
)

logger = logging.getLogger("strength_mcp_server")

# Initialize domain tools
exercise_db = StrengthExerciseDatabase()
assessment_tool = StrengthAssessmentTool()

app = Server("strength-mcp-server")

# Log server startup
logger.info("🏋️  Strength MCP Server initializing...")
logger.info(
    "📋 Available tools: search_strength_exercises, assess_strength_level, get_exercise_progression"
)


@app.list_tools()
async def list_tools() -> List[Tool]:
    """List all available strength domain tools."""
    logger.info("🔧 Client requested tool list")

    tools = [
        Tool(
            name="search_strength_exercises",
            description="Search for strength training exercises based on muscle groups, equipment, or exercise type",
            inputSchema={
                "type": "object",
                "properties": {
                    "muscle_groups": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Target muscle groups (e.g., chest, back, legs)",
                    },
                    "equipment": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Available equipment (e.g., barbell, dumbbell, bodyweight)",
                    },
                    "exercise_type": {
                        "type": "string",
                        "description": "Type of exercise (e.g., compound, isolation)",
                    },
                    "difficulty": {
                        "type": "string",
                        "description": "Difficulty level (beginner, intermediate, advanced)",
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of exercises to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50,
                    },
                },
                "required": [],
            },
        ),
        Tool(
            name="assess_strength_level",
            description="Assess user's strength level and provide recommendations",
            inputSchema={
                "type": "object",
                "properties": {
                    "assessment_data": {
                        "type": "string",
                        "description": "JSON string with strength assessment data",
                    }
                },
                "required": ["assessment_data"],
            },
        ),
        Tool(
            name="get_exercise_progression",
            description="Get progression strategies for specific exercises",
            inputSchema={
                "type": "object",
                "properties": {
                    "exercise_name": {
                        "type": "string",
                        "description": "Name of the exercise",
                    },
                    "current_level": {
                        "type": "string",
                        "description": "Current performance level",
                    },
                    "goal": {"type": "string", "description": "Progression goal"},
                },
                "required": ["exercise_name"],
            },
        ),
    ]

    logger.info(f"📤 Returning {len(tools)} tools to client")
    return tools


@app.call_tool()
async def call_tool(name: str, arguments: dict) -> CallToolResult:
    """Handle tool execution."""
    logger.info(f"🔥 Tool called: {name}")
    logger.info(f"📥 Arguments received: {arguments}")

    try:
        if name == "search_strength_exercises":
            logger.info("🔍 Executing strength exercise search...")

            # Create search input from arguments
            search_input = {
                "muscle_groups": arguments.get("muscle_groups", []),
                "equipment": arguments.get("equipment", []),
                "exercise_type": arguments.get("exercise_type", ""),
                "difficulty": arguments.get("difficulty", ""),
                "limit": arguments.get("limit", 10),
            }

            results = await exercise_db.search_exercises(search_input)
            logger.info(
                f"✅ Exercise search completed. Found {len(results.get('exercises', []))} exercises"
            )

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(results, indent=2))]
            )

        elif name == "get_exercise_progression":
            logger.info("📈 Getting exercise progression strategies...")

            # Create progression input from arguments
            progression_input = {
                "exercise_name": arguments.get("exercise_name"),
                "current_level": arguments.get("current_level", "beginner"),
                "goal": arguments.get("goal", "strength"),
            }

            result = await exercise_db.get_exercise_progression(progression_input)
            logger.info("✅ Exercise progression strategies retrieved")

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "assess_strength_level":
            logger.info("🔍 Executing strength level assessment...")

            # Parse assessment data from JSON string
            from athlea_langgraph.tools.strength.strength_assessment import (
                StrengthAssessmentInput,
            )

            assessment_data = json.loads(arguments["assessment_data"])
            assessment_input = StrengthAssessmentInput(**assessment_data)

            result = await assessment_tool.assess_strength(assessment_input)
            logger.info("✅ Strength level assessment completed")

            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=json.dumps(result.dict(), indent=2, default=str),
                    )
                ]
            )

        else:
            logger.error(f"❌ Unknown tool requested: {name}")
            return CallToolResult(
                content=[TextContent(type="text", text=f"Unknown tool: {name}")]
            )

    except Exception as e:
        logger.error(f"💥 Error executing tool {name}: {str(e)}", exc_info=True)
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error executing {name}: {str(e)}")]
        )


@app.list_prompts()
async def list_prompts() -> List[Prompt]:
    """List available strength coaching prompts."""
    logger.info("📝 Client requested prompt list")
    prompts = [
        Prompt(
            name="strength_exercise_recommendation",
            description="Generate strength exercise recommendations based on user goals and equipment",
            arguments=[
                PromptArgument(
                    name="muscle_groups",
                    description="Target muscle groups",
                    required=True,
                ),
                PromptArgument(
                    name="equipment", description="Available equipment", required=True
                ),
                PromptArgument(
                    name="experience_level",
                    description="User experience level",
                    required=True,
                ),
            ],
        ),
        Prompt(
            name="strength_progression_advice",
            description="Provide progression advice for strength training",
            arguments=[
                PromptArgument(
                    name="current_exercise",
                    description="Current exercise",
                    required=True,
                ),
                PromptArgument(
                    name="current_performance",
                    description="Current performance metrics",
                    required=True,
                ),
                PromptArgument(
                    name="goals", description="Training goals", required=True
                ),
            ],
        ),
        Prompt(
            name="strength_workout_template",
            description="Template for creating strength workouts",
            arguments=[
                PromptArgument(
                    name="muscle_group",
                    description="Target muscle group",
                    required=True,
                ),
                PromptArgument(
                    name="equipment", description="Available equipment", required=False
                ),
            ],
        ),
    ]
    logger.info(f"📤 Returning {len(prompts)} prompts to client")
    return prompts


@app.get_prompt()
async def get_prompt(name: str, arguments: Optional[Dict[str, str]]) -> GetPromptResult:
    """Get strength coaching prompts."""
    logger.info(f"📝 Prompt requested: {name}")
    logger.info(f"📥 Prompt arguments: {arguments}")

    if name == "strength_exercise_recommendation":
        muscle_groups = arguments.get("muscle_groups", "") if arguments else ""
        equipment = arguments.get("equipment", "") if arguments else ""
        experience_level = arguments.get("experience_level", "") if arguments else ""

        prompt_text = f"""
        As a strength training expert, recommend exercises for the following parameters:
        
        Target Muscle Groups: {muscle_groups}
        Available Equipment: {equipment}
        Experience Level: {experience_level}
        
        Please provide:
        1. 3-5 primary exercises targeting the specified muscle groups
        2. Exercise descriptions with proper form cues
        3. Progression recommendations for the user's experience level
        4. Safety considerations and common mistakes to avoid
        
        Format your response with clear exercise names, sets/reps recommendations, and detailed coaching cues.
        """

        logger.info("✅ Prompt generated successfully")
        return GetPromptResult(
            description="Strength exercise recommendations",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    elif name == "strength_progression_advice":
        current_exercise = arguments.get("current_exercise", "") if arguments else ""
        current_performance = (
            arguments.get("current_performance", "") if arguments else ""
        )
        goals = arguments.get("goals", "") if arguments else ""

        prompt_text = f"""
        As a strength coach, provide progression advice for:
        
        Current Exercise: {current_exercise}
        Current Performance: {current_performance}
        Goals: {goals}
        
        Please analyze and provide:
        1. Assessment of current performance level
        2. Specific progression steps (load, volume, technique)
        3. Timeline for progressions
        4. Alternative exercises if plateau occurs
        5. Deload and recovery considerations
        
        Provide actionable, week-by-week progression recommendations.
        """

        logger.info("✅ Prompt generated successfully")
        return GetPromptResult(
            description="Strength progression advice",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    elif name == "strength_workout_template":
        muscle_group = (
            arguments.get("muscle_group", "full body") if arguments else "full body"
        )
        equipment = (
            arguments.get("equipment", "basic gym equipment")
            if arguments
            else "basic gym equipment"
        )

        prompt_text = f"""
        Create a strength workout targeting {muscle_group} using {equipment}.
        Include:
        - Warm-up exercises
        - Main exercises with sets and reps
        - Cool-down routine
        - Safety considerations
        """

        logger.info("✅ Prompt generated successfully")
        return GetPromptResult(
            description=f"Strength workout template for {muscle_group}",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    else:
        logger.error(f"❌ Unknown prompt requested: {name}")
        raise ValueError(f"Unknown prompt: {name}")


async def main():
    """Run the strength MCP server with enhanced logging."""
    logger.info("🚀 Starting Strength MCP Server...")
    logger.info("📡 Waiting for client connections on stdio...")

    try:
        async with stdio_server() as (read_stream, write_stream):
            logger.info("✅ Server streams established")
            logger.info("🔗 Running server with client connection...")

            await app.run(
                read_stream,
                write_stream,
                protocolVersion="2024-11-05",
                serverInfo={"name": "strength-mcp-server", "version": "0.1.0"},
            )
    except Exception as e:
        logger.error(f"💥 Server error: {str(e)}", exc_info=True)
        raise
    finally:
        logger.info("🛑 Strength MCP Server shutting down...")


if __name__ == "__main__":
    logger.info("🏋️  Strength MCP Server starting from command line...")
    asyncio.run(main())
