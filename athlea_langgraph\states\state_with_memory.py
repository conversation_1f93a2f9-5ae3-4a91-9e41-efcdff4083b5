"""
Enhanced State definition with Memory Support

Extends the original state to include memory-related fields for both
short-term (session) and long-term (cross-session) memory management.
"""

from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, Field, ConfigDict
from typing_extensions import TypedDict


class MemoryEnhancedGraphState(BaseModel):
    """
    Enhanced GraphState that includes memory-related fields.

    Extends the original GraphState with:
    - Memory context from long-term storage
    - User interaction history
    - Personalization data
    """

    # Original fields
    messages: List[BaseMessage] = Field(default_factory=list)
    user_profile: Optional[Dict[str, Any]] = None
    routing_decision: Optional[Union[str, List[str]]] = None
    pending_agents: Optional[List[str]] = None
    plan: Optional[List[str]] = None
    current_step: Optional[int] = None

    # Plan generation related fields
    domain_contributions: Dict[str, str] = Field(default_factory=dict)
    required_domains: List[str] = Field(default_factory=list)
    completed_domains: List[str] = Field(default_factory=list)
    aggregated_plan: Optional[str] = None
    proceed_to_generation: bool = False
    current_plan: Optional[Any] = None

    # Onboarding related fields
    is_onboarding: bool = False

    # Specialist coach responses
    strength_response: Optional[str] = None
    running_response: Optional[str] = None
    cardio_response: Optional[str] = None
    cycling_response: Optional[str] = None
    nutrition_response: Optional[str] = None
    recovery_response: Optional[str] = None
    mental_response: Optional[str] = None

    # Other node outputs
    reasoning_output: Optional[str] = None
    clarification_output: Optional[str] = None
    aggregated_response: Optional[str] = None

    # Memory-enhanced fields
    memory_context: Optional[Dict[str, Any]] = Field(
        default=None, description="Context from long-term memory retrieval"
    )
    relevant_memories: List[Dict[str, Any]] = Field(
        default_factory=list, description="Relevant memories for current interaction"
    )
    user_preferences: Dict[str, Any] = Field(
        default_factory=dict, description="User preferences from memory"
    )
    interaction_history: List[Dict[str, Any]] = Field(
        default_factory=list, description="Recent interaction summaries"
    )
    personalization_data: Dict[str, Any] = Field(
        default_factory=dict, description="Data used for personalizing responses"
    )

    model_config = ConfigDict(arbitrary_types_allowed=True)


class MemoryEnhancedAgentState(TypedDict):
    """TypedDict version of MemoryEnhancedGraphState for LangGraph compatibility."""

    # Original fields
    messages: List[BaseMessage]
    user_query: Optional[str]
    user_profile: Optional[Dict[str, Any]]
    routing_decision: Optional[Union[str, List[str]]]
    pending_agents: Optional[List[str]]
    plan: Optional[List[str]]
    current_step: Optional[int]
    domain_contributions: Dict[str, str]
    required_domains: List[str]
    completed_domains: List[str]
    aggregated_plan: Optional[str]
    proceed_to_generation: bool
    current_plan: Optional[Any]
    is_onboarding: bool

    # Specialist coach responses
    strength_response: Optional[str]
    running_response: Optional[str]
    cardio_response: Optional[str]
    cycling_response: Optional[str]
    nutrition_response: Optional[str]
    recovery_response: Optional[str]
    mental_response: Optional[str]

    # Other node outputs
    reasoning_output: Optional[str]
    clarification_output: Optional[str]
    aggregated_response: Optional[str]

    # Memory-enhanced fields
    memory_context: Optional[Dict[str, Any]]
    relevant_memories: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    interaction_history: List[Dict[str, Any]]
    personalization_data: Dict[str, Any]


# Memory-specific utility functions
def memory_context_reducer(
    left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    """Reducer for memory context that merges new context with existing."""
    if not left and not right:
        return None
    if not left:
        return right
    if not right:
        return left

    merged = left.copy()
    merged.update(right)
    return merged


def relevant_memories_reducer(
    left: List[Dict[str, Any]], right: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Reducer for relevant memories that appends new memories."""
    return left + right


def user_preferences_reducer(
    left: Dict[str, Any], right: Dict[str, Any]
) -> Dict[str, Any]:
    """Reducer for user preferences that merges preferences."""
    result = left.copy()
    result.update(right)
    return result


def interaction_history_reducer(
    left: List[Dict[str, Any]], right: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Reducer for interaction history that appends new interactions."""
    # Keep only the most recent 10 interactions
    combined = left + right
    return combined[-10:] if len(combined) > 10 else combined


def personalization_data_reducer(
    left: Dict[str, Any], right: Dict[str, Any]
) -> Dict[str, Any]:
    """Reducer for personalization data that merges data."""
    result = left.copy()
    result.update(right)
    return result
