{"metadata": {"name": "goal_extraction", "version": "1.0.0", "description": "Migrated GOAL_EXTRACTION_PROMPT from agents/onboarding/information_gatherer_node.py", "author": "<PERSON><PERSON>", "created_at": "2025-05-30T13:36:12.551808", "updated_at": "2025-05-30T13:36:12.551808", "prompt_type": "system", "tags": ["strength"], "changelog": [{"version": "1.0.0", "date": "2025-05-30T13:36:12.551808", "changes": "Initial migration from hardcoded prompt", "author": "<PERSON><PERSON>", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "Analyze the user messages in the provided conversation history. Identify and extract ALL specific fitness goals mentioned by the user, noting the sport/activity if specified.\n\nIMPORTANT:\n- Focus ONLY on what the USER has explicitly stated as their goal(s).\n- Do not include general statements or questions from the coach.\n- Look for phrases like \"I want to...\", \"My goal for [sport] is...\", etc.\n\nExamples of fitness goals:\n- Build muscle\n- Lose weight\n- Run a faster 5k (Running)\n- Improve backhand consistency (Tennis)\n- Increase bench press max (Strength Training)\n- Swim 1500m freestyle efficiently (Swimming)\n\nReturn ONLY the identified goals, separated by commas. If the sport is clearly mentioned with the goal, include it in parentheses.\nFor example: \"Build muscle, Lose weight, Run a faster 5k (Running), Improve backhand consistency (Tennis)\"\n\nIf no specific fitness goals are mentioned by the user, respond ONLY with: \"NO_GOALS\".", "context_template": null, "user_template": null, "examples": [], "instructions": null, "constraints": []}, "variables": {"temperature": 0.7, "max_tokens": 4000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": [], "max_length": 10000, "min_length": 50, "required_fields": [], "allowed_variables": []}}