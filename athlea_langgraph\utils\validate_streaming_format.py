#!/usr/bin/env python3
"""
Streaming Format Validation and Correction Script

This script validates and corrects the streaming output format to ensure it matches
what the frontend expects. It checks:
1. Correct SSE event format
2. Proper agent name mapping
3. Tool result structure with artifacts and sources
4. Token streaming with correct agent identification
5. Sidebar updates and other frontend-expected events

Based on analysis of /athlea-web/app/chat/[id]/page.tsx requirements.
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class StreamEventRequirements:
    """Requirements based on frontend expectations from chat/[id]/page.tsx"""

    # Event types the frontend expects
    EXPECTED_EVENT_TYPES = {
        "agent_start",
        "token",
        "tool_result",
        "need_input",
        "sidebar_update",
        "complete",
        "done",
        "error",
        "history_loaded",
    }

    # Agent name mapping (frontend domain expectations)
    FRONTEND_AGENT_MAPPING = {
        "reasoning": "reasoning",
        "planning": "planner",
        "planner": "planner",
        "head_coach": "Athlea",
        "clarification": "Athlea",
        "strength_coach": "strength",
        "running_coach": "running",
        "cardio_coach": "cardio",
        "cycling_coach": "cycling",
        "nutrition_coach": "nutrition",
        "recovery_coach": "recovery",
        "mental_coach": "mental",
        "aggregate_responses": "Athlea",
        "finalizer": "finalizer",
        "map": "map",
        "general": "general",
    }


class StreamingFormatValidator:
    """Validates and corrects streaming format for frontend compatibility."""

    def __init__(self):
        self.requirements = StreamEventRequirements()
        self.event_count = 0
        self.validation_errors = []

    def format_sse_event(self, event_type: str, data: Dict[str, Any]) -> str:
        """Format data as properly structured Server-Sent Events."""
        # Ensure event type is valid
        if event_type not in self.requirements.EXPECTED_EVENT_TYPES:
            logger.warning(f"Unknown event type: {event_type}")

        # Ensure proper JSON serialization
        try:
            json_data = json.dumps(data, ensure_ascii=False, separators=(",", ":"))
        except (TypeError, ValueError) as e:
            logger.error(f"Failed to serialize event data: {e}")
            json_data = json.dumps({"error": "Serialization failed"})

        return f"data: {json_data}\n\n"

    def validate_agent_name(self, agent_name: Optional[str]) -> Optional[str]:
        """Validate and correct agent name to match frontend expectations."""
        if not agent_name:
            return None

        # Normalize agent name
        normalized = agent_name.lower().strip()

        # Check direct mapping
        if normalized in self.requirements.FRONTEND_AGENT_MAPPING:
            return self.requirements.FRONTEND_AGENT_MAPPING[normalized]

        # Try partial matches for compound names
        for (
            backend_name,
            frontend_name,
        ) in self.requirements.FRONTEND_AGENT_MAPPING.items():
            if backend_name in normalized or normalized in backend_name:
                logger.info(f"Mapped agent '{agent_name}' -> '{frontend_name}'")
                return frontend_name

        # Default fallback
        logger.warning(f"Unknown agent name '{agent_name}', defaulting to 'general'")
        return "general"

    def validate_token_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and correct token event structure."""
        corrected_data = event_data.copy()

        # Ensure required fields
        if "agent" not in corrected_data:
            logger.error("Token event missing 'agent' field")
            corrected_data["agent"] = "general"

        if "content" not in corrected_data:
            logger.error("Token event missing 'content' field")
            corrected_data["content"] = ""

        # Validate agent name
        corrected_data["agent"] = self.validate_agent_name(corrected_data["agent"])

        # Ensure content is string
        if not isinstance(corrected_data["content"], str):
            corrected_data["content"] = str(corrected_data.get("content", ""))

        return corrected_data

    def validate_tool_result_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and correct tool_result event structure."""
        corrected_data = event_data.copy()

        # Ensure required fields
        required_fields = ["agent", "toolName", "content"]
        for field in required_fields:
            if field not in corrected_data:
                logger.error(f"Tool result event missing '{field}' field")
                if field == "agent":
                    corrected_data[field] = "general"
                elif field == "toolName":
                    corrected_data[field] = "unknown_tool"
                else:
                    corrected_data[field] = None

        # Validate agent name
        corrected_data["agent"] = self.validate_agent_name(corrected_data["agent"])

        # Validate tool content structure for azure_search_graph
        if corrected_data.get("toolName") == "azure_search_graph":
            corrected_data = self._validate_azure_search_tool_content(corrected_data)

        return corrected_data

    def _validate_azure_search_tool_content(
        self, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate azure_search_graph tool content structure."""
        content = event_data.get("content")

        if not content:
            logger.warning("Azure search tool result has empty content")
            return event_data

        # Try to parse content if it's a string
        if isinstance(content, str):
            try:
                parsed_content = json.loads(content)
                content = parsed_content
            except json.JSONDecodeError:
                logger.warning("Failed to parse azure search tool content as JSON")
                return event_data

        # Validate expected structure
        if isinstance(content, dict):
            expected_fields = ["query", "results", "meta"]
            for field in expected_fields:
                if field not in content:
                    logger.warning(f"Azure search content missing '{field}' field")

            # Validate meta structure for frontend compatibility
            if "meta" in content and isinstance(content["meta"], dict):
                meta = content["meta"]

                # Ensure relevance_assessments for frontend processing
                if "relevance_assessments" not in meta and "results" in content:
                    logger.info(
                        "Adding empty relevance_assessments for frontend compatibility"
                    )
                    meta["relevance_assessments"] = []

                # Ensure required meta fields
                required_meta_fields = {
                    "originalResultCount": 0,
                    "filteredResultCount": 0,
                    "relevanceFiltered": 0,
                    "credibilityEvaluated": False,
                }

                for field, default in required_meta_fields.items():
                    if field not in meta:
                        logger.info(
                            f"Adding missing meta field '{field}' with default value"
                        )
                        meta[field] = default

        return event_data

    def validate_agent_start_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and correct agent_start event structure."""
        corrected_data = event_data.copy()

        if "agent" not in corrected_data:
            logger.error("Agent start event missing 'agent' field")
            corrected_data["agent"] = "general"

        # Validate agent name
        corrected_data["agent"] = self.validate_agent_name(corrected_data["agent"])

        return corrected_data

    def validate_need_input_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and correct need_input event structure."""
        corrected_data = event_data.copy()

        # Ensure prompt field exists
        if "prompt" not in corrected_data:
            corrected_data["prompt"] = "Please provide input."

        # Ensure field exists if expected
        if "field" not in corrected_data:
            corrected_data["field"] = "general_input"

        return corrected_data

    def validate_sidebar_update_event(
        self, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate and correct sidebar_update event structure."""
        corrected_data = event_data.copy()

        if "sidebarData" not in corrected_data:
            logger.warning("Sidebar update event missing 'sidebarData' field")
            corrected_data["sidebarData"] = {}

        return corrected_data

    def validate_sse_event(
        self, event_type: str, event_data: Dict[str, Any]
    ) -> tuple[str, Dict[str, Any]]:
        """Validate and correct a complete SSE event."""
        self.event_count += 1

        logger.info(f"Validating event #{self.event_count}: {event_type}")

        # Validate event type
        if event_type not in self.requirements.EXPECTED_EVENT_TYPES:
            logger.warning(f"Unknown event type '{event_type}', allowing but flagging")

        # Validate event data based on type
        corrected_data = event_data.copy()

        if event_type == "token":
            corrected_data = self.validate_token_event(corrected_data)
        elif event_type == "tool_result":
            corrected_data = self.validate_tool_result_event(corrected_data)
        elif event_type == "agent_start":
            corrected_data = self.validate_agent_start_event(corrected_data)
        elif event_type == "need_input":
            corrected_data = self.validate_need_input_event(corrected_data)
        elif event_type == "sidebar_update":
            corrected_data = self.validate_sidebar_update_event(corrected_data)
        elif event_type in ["complete", "done"]:
            # These events typically just need a message
            if "message" not in corrected_data:
                corrected_data["message"] = "Processing completed"
        elif event_type == "error":
            # Error events need a message
            if "message" not in corrected_data:
                corrected_data["message"] = "An error occurred"

        return event_type, corrected_data

    async def validate_stream(
        self, event_stream: AsyncGenerator[str, None]
    ) -> AsyncGenerator[str, None]:
        """Validate and correct an entire event stream."""
        logger.info("Starting stream validation...")

        async for raw_event in event_stream:
            try:
                # Parse SSE format
                if not raw_event.strip():
                    continue

                # Handle different SSE formats
                if raw_event.startswith("data: "):
                    data_content = raw_event[6:].strip()
                    try:
                        event_data = json.loads(data_content)
                        event_type = event_data.get("type", "unknown")

                        # Validate and correct
                        validated_type, validated_data = self.validate_sse_event(
                            event_type, event_data
                        )

                        # Re-format as proper SSE
                        corrected_event = self.format_sse_event(
                            validated_type, validated_data
                        )
                        yield corrected_event

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse event data: {e}")
                        # Yield error event
                        error_event = self.format_sse_event(
                            "error", {"message": f"Failed to parse event: {str(e)}"}
                        )
                        yield error_event

                elif raw_event.startswith("event: "):
                    # Handle multi-line SSE format
                    # This is more complex parsing - for now just pass through
                    yield raw_event
                else:
                    # Unknown format, try to handle gracefully
                    logger.warning(f"Unknown event format: {raw_event[:50]}...")
                    yield raw_event

            except Exception as e:
                logger.error(f"Error validating event: {e}")
                error_event = self.format_sse_event(
                    "error", {"message": f"Validation error: {str(e)}"}
                )
                yield error_event

        logger.info(
            f"Stream validation completed. Processed {self.event_count} events."
        )

    def generate_test_events(self) -> List[tuple[str, Dict[str, Any]]]:
        """Generate test events to validate format compatibility."""
        test_events = [
            # Agent start event
            ("agent_start", {"agent": "strength_coach"}),
            # Token events
            ("token", {"agent": "strength", "content": "Hello "}),
            ("token", {"agent": "strength", "content": "world!"}),
            # Tool result event
            (
                "tool_result",
                {
                    "agent": "general",
                    "toolName": "azure_search_graph",
                    "content": {
                        "query": "test query",
                        "results": [
                            {
                                "title": "Test Result",
                                "content": "Test content",
                                "qualityScore": 0.85,
                                "source": "test.com",
                                "url": "https://test.com",
                            }
                        ],
                        "meta": {
                            "originalResultCount": 1,
                            "filteredResultCount": 1,
                            "relevanceFiltered": 0,
                            "credibilityEvaluated": False,
                            "relevance_assessments": [],
                        },
                    },
                },
            ),
            # Need input event
            (
                "need_input",
                {"prompt": "What is your fitness goal?", "field": "fitness_goal"},
            ),
            # Sidebar update event
            (
                "sidebar_update",
                {
                    "sidebarData": {
                        "selectedSport": "running",
                        "sportSuggestions": [
                            {"label": "Running", "value": "running"},
                            {"label": "Cycling", "value": "cycling"},
                        ],
                    }
                },
            ),
            # Complete event
            ("complete", {"message": "Session completed successfully"}),
        ]

        return test_events


async def main():
    """Main function to test and demonstrate the validation."""
    print("🔍 Streaming Format Validation Script")
    print("=" * 50)

    validator = StreamingFormatValidator()

    # Test individual event validation
    print("\n📝 Testing Individual Event Validation:")
    print("-" * 30)

    test_events = validator.generate_test_events()

    for event_type, event_data in test_events:
        print(f"\n🧪 Testing {event_type} event:")

        # Validate
        validated_type, validated_data = validator.validate_sse_event(
            event_type, event_data
        )

        # Format as SSE
        sse_formatted = validator.format_sse_event(validated_type, validated_data)

        print(f"  Original: {event_data}")
        print(f"  Validated: {validated_data}")
        print(f"  SSE Format: {sse_formatted.strip()}")

    print(f"\n✅ Validation complete. Processed {validator.event_count} events.")

    # Test agent name mapping
    print("\n🤖 Testing Agent Name Mapping:")
    print("-" * 30)

    test_agent_names = [
        "strength_coach",
        "head_coach",
        "planning",
        "reasoning",
        "unknown_agent",
        "cardio_coach",
        "nutrition_coach",
    ]

    for agent_name in test_agent_names:
        validated_name = validator.validate_agent_name(agent_name)
        print(f"  '{agent_name}' -> '{validated_name}'")

    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
