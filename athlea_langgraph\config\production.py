"""
Production environment configuration.
"""

from pydantic import ConfigDict
from .base import BaseConfig, Environment


class ProductionConfig(BaseConfig):
    """Production-specific configuration."""

    environment: Environment = Environment.PRODUCTION
    debug: bool = False

    model_config = ConfigDict(env_file=".env", case_sensitive=False)


# Global production configuration instance
config = ProductionConfig()
