# LangGraph Studio Setup for Athlea Coaching Agent

This guide will help you set up and use the Athlea Coaching Agent in [LangGraph Studio](https://github.com/langchain-ai/langgraph-studio) for visual debugging and development.

## 🎯 What is LangGraph Studio?

LangGraph Studio is a specialized IDE for developing LLM applications that provides:
- **Visual Graph Representation**: See your coaching agent's flow visually
- **Step-by-Step Debugging**: Execute nodes one at a time
- **State Inspection**: View and edit the agent state at any point
- **Human-in-the-Loop**: Add interrupts for human input during execution
- **Thread Management**: Manage conversation threads and sessions
- **Live Code Editing**: Modify code and see changes in real-time

## 📋 Prerequisites

### Required Software
1. **Docker Desktop** or **Orbstack** (running)
   - Download Docker Desktop: https://www.docker.com/products/docker-desktop/
   - Or Orbstack (recommended for Mac): https://orbstack.dev/

2. **LangGraph Studio**
   - Download from: https://github.com/langchain-ai/langgraph-studio/releases
   - Note: Desktop app is macOS only
   - Alternative: Use web version with local LangGraph server

### System Requirements
- macOS (for desktop app) or any OS (for web version)
- Docker Compose version 2.22.0+
- Python 3.11+

## 🚀 Setup Instructions

### Step 1: Verify Your Project Structure

Ensure your project has the required files:

```
python-langgraph/
├── langgraph.json          # ✅ Studio configuration
├── athlea_langgraph/
│   ├── studio_graph.py     # ✅ Studio-compatible graph
│   ├── coaching_graph_with_memory.py
│   ├── streaming.py
│   └── ...
├── .env.local             # ✅ Environment variables
├── pyproject.toml         # ✅ Dependencies
└── poetry.lock
```

### Step 2: Configure Environment Variables

Your `.env.local` file should include:

```bash
# Required for Azure OpenAI
AZURE_OPENAI_API_KEY=your_key_here
AZURE_OPENAI_ENDPOINT=your_endpoint_here
AZURE_DEPLOYMENT_NAME=your_deployment_name

# Required for LangSmith (auto-configured by Studio)
LANGSMITH_API_KEY=your_langsmith_key
LANGSMITH_TRACING=true

# MongoDB (use host.docker.internal for Docker)
MONGODB_URI=mongodb://host.docker.internal:27017
# Or use your cloud MongoDB URI
```

### Step 3: Open Project in LangGraph Studio

1. **Launch LangGraph Studio**
2. **Login with LangSmith** account
3. **Select Project Folder**: Choose this `python-langgraph` folder
4. **Wait for Setup**: Studio will build the Docker environment

### Step 4: Select and Configure the Graph

1. **Select Graph**: Choose `coaching_agent` from the dropdown
2. **Configure Settings**: 
   - `user_id`: Set to your test user ID
   - `mongodb_uri`: Use `mongodb://host.docker.internal:27017` for local MongoDB
   - `enable_memory`: Toggle memory features on/off
   - `thread_id`: Unique identifier for the conversation thread

## 🎮 Using LangGraph Studio

### Basic Operations

#### 1. Start a New Conversation
- Set the **Input** to your coaching query:
  ```json
  {
    "user_query": "I want to start strength training",
    "messages": []
  }
  ```
- Click **Submit** to run the graph

#### 2. View Graph Execution
- Watch nodes light up as they execute
- See the flow: `reasoning` → `planning` → `head_coach` → specialists → `aggregation`

#### 3. Inspect State
- Click on any node to see its state
- View outputs, reasoning, and decisions at each step

### Advanced Features

#### 1. Add Interrupts
- **All Nodes**: Click "Interrupt on all" to step through execution
- **Specific Nodes**: Hover over a node and click the `+` button
- **Human Input**: Use the `human_input` node for interactive sessions

#### 2. Edit State
- Click the "pencil" icon on any step
- Modify the state (e.g., change user query, add context)
- Click **Fork** to create a new execution path

#### 3. Human-in-the-Loop Example
```json
{
  "user_query": "Help me with my fitness goals",
  "awaiting_human_input": true,
  "human_input_prompt": "What specific goals do you have?"
}
```

#### 4. Thread Management
- Create new threads with the `+` button
- Switch between threads to compare different conversations
- Each thread maintains its own state and history

## 🧪 Available Configurations

### Memory Enabled (Default)
```json
{
  "user_id": "your_user_id",
  "mongodb_uri": "mongodb://host.docker.internal:27017",
  "enable_memory": true,
  "thread_id": "coaching_session_1"
}
```

### Memory Disabled (Testing)
```json
{
  "user_id": "test_user",
  "enable_memory": false,
  "thread_id": "test_session"
}
```

### Custom Specialist Focus
```json
{
  "user_id": "strength_focus_user",
  "enable_memory": true,
  "specialist_priority": "strength"
}
```

## 🎯 Testing Scenarios

### 1. Basic Coaching Flow
**Input**: "I want to improve my fitness"
**Expected Path**: reasoning → planning → clarification → aggregation

### 2. Specialist Routing
**Input**: "How do I build muscle mass?"
**Expected Path**: reasoning → planning → strength_coach → aggregation

### 3. Multi-Domain Query
**Input**: "I need help with strength training and nutrition"
**Expected Path**: reasoning → planning → multiple specialists → aggregation

### 4. Clarification Needed
**Input**: "Help me get fit"
**Expected Path**: reasoning → planning → clarification (interrupt for more details)

## 🔧 Troubleshooting

### Common Issues

#### 1. Docker Not Running
**Error**: "Cannot connect to Docker daemon"
**Solution**: Start Docker Desktop or Orbstack

#### 2. MongoDB Connection Issues
**Error**: "MongoDB connection failed"
**Solution**: 
- For local MongoDB: Use `mongodb://host.docker.internal:27017`
- For cloud MongoDB: Use your cloud connection string

#### 3. Missing Dependencies
**Error**: "Module not found"
**Solution**: Check `pyproject.toml` and ensure all dependencies are listed

#### 4. LangSmith Authentication
**Error**: "LangSmith API key not found"
**Solution**: Login through LangGraph Studio - it handles this automatically

### Debug Mode

Enable debug logging by setting:
```bash
export LANGCHAIN_VERBOSE=true
export LANGCHAIN_TRACING_V2=true
```

## 🎬 Example Workflows

### Workflow 1: Step-by-Step Debugging
1. Add interrupts to all nodes
2. Input: "I want to start running"
3. Step through each node execution
4. Inspect state changes at each step
5. Modify state if needed

### Workflow 2: Human-in-the-Loop Coaching
1. Input: "I need a workout plan"
2. Let it execute to clarification node
3. Provide additional details when prompted
4. Continue execution with enriched context

### Workflow 3: A/B Testing Different Approaches
1. Create Thread A with one approach
2. Fork at decision point
3. Create Thread B with modified state
4. Compare results between threads

## 📚 Additional Resources

- [LangGraph Studio Documentation](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/)
- [LangGraph Studio GitHub](https://github.com/langchain-ai/langgraph-studio)
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [LangSmith for Debugging](https://docs.smith.langchain.com/)

## 🎉 Ready to Debug!

Your Athlea Coaching Agent is now ready for visual debugging in LangGraph Studio! You can:

✅ **Visualize** the complete coaching flow  
✅ **Debug** issues step-by-step  
✅ **Test** different coaching scenarios  
✅ **Optimize** agent performance  
✅ **Add** human-in-the-loop features  

Happy debugging! 🚀 