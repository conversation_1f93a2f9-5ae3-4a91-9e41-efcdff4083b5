"""
Recovery Domain Schemas

Pydantic models for recovery tools including mobility, sleep optimization,
wellness tracking, and injury prevention.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class ExerciseTypeFilter(str, Enum):
    """Exercise type filters for recovery protocols."""

    STRETCHING = "stretching"
    STRENGTH = "strength"
    CARDIO = "cardio"
    BODYWEIGHT = "body weight"


class MobilityArea(str, Enum):
    """Target mobility areas."""

    NECK = "neck"
    SHOULDERS = "shoulders"
    THORACIC_SPINE = "back"
    LUMBAR_SPINE = "lower_back"
    HIPS = "upper legs"
    KNEES = "lower legs"
    ANKLES = "lower legs"
    FULL_BODY = "full_body"


class ActivityType(str, Enum):
    """Activity types for personalized mobility protocols."""

    RUNNING = "running"
    CYCLING = "cycling"
    STRENGTH_TRAINING = "strength_training"
    SWIMMING = "swimming"
    DESK_WORK = "desk_work"
    GENERAL_FITNESS = "general_fitness"
    SPORT_SPECIFIC = "sport_specific"


class RecoveryGoal(str, Enum):
    """Recovery goals for protocol generation."""

    FLEXIBILITY = "flexibility"
    MOBILITY = "mobility"
    PAIN_RELIEF = "pain_relief"
    INJURY_PREVENTION = "injury_prevention"
    PERFORMANCE_ENHANCEMENT = "performance_enhancement"
    STRESS_RELIEF = "stress_relief"


# Mobility & Stretching Schemas
class MobilityProtocolInput(BaseModel):
    """Input for mobility protocol generation."""

    target_areas: List[MobilityArea] = Field(
        ..., description="Body areas to target for mobility work"
    )
    activity_type: ActivityType = Field(
        ..., description="Primary activity or sport type"
    )
    session_duration: int = Field(
        default=15, ge=5, le=60, description="Session duration in minutes"
    )
    experience_level: str = Field(
        default="intermediate",
        description="Experience level: beginner, intermediate, expert",
    )
    recovery_goals: List[RecoveryGoal] = Field(
        default=[RecoveryGoal.FLEXIBILITY], description="Primary recovery goals"
    )
    available_equipment: List[str] = Field(
        default=["body weight"], description="Available equipment for exercises"
    )
    time_of_day: Optional[str] = Field(
        default="morning",
        description="When the session will be performed: morning, afternoon, evening",
    )


class MobilityExercise(BaseModel):
    """Individual mobility exercise details."""

    name: str = Field(..., description="Exercise name")
    target_muscle: str = Field(..., description="Primary target muscle/area")
    equipment: str = Field(..., description="Required equipment")
    duration: str = Field(..., description="Recommended duration or repetitions")
    instructions: List[str] = Field(..., description="Step-by-step instructions")
    benefits: List[str] = Field(..., description="Exercise benefits")
    difficulty: str = Field(..., description="Difficulty level")
    gif_url: Optional[str] = Field(None, description="Visual demonstration URL")


class MobilityProtocolOutput(BaseModel):
    """Output for mobility protocol generation."""

    protocol_name: str = Field(..., description="Name of the mobility protocol")
    total_duration: int = Field(..., description="Total session duration in minutes")
    target_areas: List[str] = Field(..., description="Targeted body areas")
    warm_up_exercises: List[MobilityExercise] = Field(
        ..., description="Warm-up exercises"
    )
    main_exercises: List[MobilityExercise] = Field(
        ..., description="Main mobility exercises"
    )
    cool_down_exercises: List[MobilityExercise] = Field(
        ..., description="Cool-down exercises"
    )
    progression_tips: List[str] = Field(..., description="Tips for progression")
    frequency_recommendation: str = Field(..., description="Recommended frequency")


# Sleep Optimization Schemas
class SleepAssessmentInput(BaseModel):
    """Input for sleep quality assessment."""

    bedtime: str = Field(..., description="Usual bedtime (HH:MM format)")
    wake_time: str = Field(..., description="Usual wake time (HH:MM format)")
    sleep_quality_rating: int = Field(
        ..., ge=1, le=10, description="Sleep quality rating (1-10)"
    )
    time_to_fall_asleep: int = Field(
        ..., ge=0, le=120, description="Time to fall asleep in minutes"
    )
    night_awakenings: int = Field(
        ..., ge=0, le=10, description="Number of times awakened per night"
    )
    morning_alertness: int = Field(
        ..., ge=1, le=10, description="Morning alertness rating (1-10)"
    )
    caffeine_intake: Optional[int] = Field(
        0, ge=0, description="Daily caffeine intake in mg"
    )
    screen_time_before_bed: Optional[int] = Field(
        0, ge=0, le=300, description="Screen time before bed in minutes"
    )
    sleep_environment_issues: List[str] = Field(
        default=[], description="Sleep environment concerns"
    )


class SleepOptimizationOutput(BaseModel):
    """Output for sleep optimization recommendations."""

    sleep_efficiency: float = Field(
        ..., description="Calculated sleep efficiency percentage"
    )
    sleep_debt: float = Field(..., description="Estimated sleep debt in hours")
    optimal_bedtime: str = Field(..., description="Recommended bedtime")
    optimal_wake_time: str = Field(..., description="Recommended wake time")
    sleep_hygiene_recommendations: List[str] = Field(
        ..., description="Sleep hygiene improvement recommendations"
    )
    environmental_optimizations: List[str] = Field(
        ..., description="Sleep environment improvements"
    )
    pre_sleep_routine: List[str] = Field(
        ..., description="Recommended pre-sleep routine steps"
    )
    lifestyle_adjustments: List[str] = Field(
        ..., description="Lifestyle changes for better sleep"
    )


# Wellness Tracking Schemas
class WellnessAssessmentInput(BaseModel):
    """Input for daily wellness assessment."""

    energy_level: int = Field(
        ..., ge=1, le=10, description="Current energy level (1-10)"
    )
    mood_rating: int = Field(..., ge=1, le=10, description="Current mood rating (1-10)")
    stress_level: int = Field(
        ..., ge=1, le=10, description="Current stress level (1-10)"
    )
    muscle_soreness: int = Field(
        ..., ge=1, le=10, description="Muscle soreness level (1-10)"
    )
    sleep_quality: int = Field(
        ..., ge=1, le=10, description="Last night's sleep quality (1-10)"
    )
    motivation_level: int = Field(
        ..., ge=1, le=10, description="Training motivation level (1-10)"
    )
    hydration_liters: Optional[float] = Field(
        2.0, ge=0, le=10, description="Daily water intake in liters"
    )
    training_readiness: Optional[int] = Field(
        None, ge=1, le=10, description="Self-assessed training readiness (1-10)"
    )
    pain_areas: List[str] = Field(
        default=[], description="Areas experiencing pain or discomfort"
    )


class WellnessTrackingOutput(BaseModel):
    """Output for wellness tracking analysis."""

    overall_wellness_score: int = Field(
        ..., ge=1, le=100, description="Overall wellness score (1-100)"
    )
    training_readiness_score: int = Field(
        ..., ge=1, le=100, description="Training readiness score (1-100)"
    )
    recovery_recommendations: List[str] = Field(
        ..., description="Personalized recovery recommendations"
    )
    warning_signs: List[str] = Field(..., description="Warning signs to monitor")
    suggested_modifications: List[str] = Field(
        ..., description="Training modifications based on wellness"
    )
    follow_up_actions: List[str] = Field(
        ..., description="Recommended follow-up actions"
    )


# Injury Prevention Schemas
class MovementScreenInput(BaseModel):
    """Input for movement screen assessment."""

    primary_sport: str = Field(..., description="Primary sport or activity")
    training_frequency: int = Field(
        ..., ge=1, le=14, description="Training sessions per week"
    )
    injury_history: List[str] = Field(default=[], description="Previous injury history")
    current_pain_areas: List[str] = Field(
        default=[], description="Current areas of pain or discomfort"
    )
    movement_limitations: List[str] = Field(
        default=[], description="Known movement limitations"
    )
    dominant_side: str = Field(
        default="right", description="Dominant side: right or left"
    )
    age: Optional[int] = Field(
        None, ge=12, le=100, description="Age for age-appropriate recommendations"
    )


class MovementScreenOutput(BaseModel):
    """Output for movement screen analysis."""

    risk_assessment: str = Field(..., description="Overall injury risk assessment")
    identified_weaknesses: List[str] = Field(
        ..., description="Identified movement weaknesses"
    )
    corrective_exercises: List[MobilityExercise] = Field(
        ..., description="Corrective exercises to address weaknesses"
    )
    injury_prevention_strategies: List[str] = Field(
        ..., description="Sport-specific injury prevention strategies"
    )
    recommended_assessments: List[str] = Field(
        ..., description="Recommended professional assessments"
    )
    monitoring_points: List[str] = Field(
        ..., description="Key points to monitor during training"
    )
    progression_timeline: str = Field(
        ..., description="Recommended timeline for improvements"
    )
