"""
Memory schemas for advanced memory system features.
"""

from .analytics_schemas import (
    AnalyticsReport,
    DateRange,
    HealthStatus,
    HealthStatusLevel,
    MemoryAnalyticsEvent,
    MemoryOperationType,
    PerformanceMetrics,
    RetrievalMetrics,
    StorageMetrics,
)
from .domain_schemas import (
    DOMAIN_KEYWORDS,
    CoachingDomain,
    DomainClassification,
    MemoryDomainMetadata,
)
from .memory_metadata import (
    AdvancedMemoryMetadata,
    ImportanceDecayMode,
    ImportanceScore,
    MemoryReference,
    SummaryLevel,
    SummaryMetadata,
)

__all__ = [
    # Domain schemas
    "CoachingDomain",
    "DomainClassification",
    "MemoryDomainMetadata",
    "DOMAIN_KEYWORDS",
    # Analytics schemas
    "MemoryAnalyticsEvent",
    "MemoryOperationType",
    "RetrievalMetrics",
    "StorageMetrics",
    "PerformanceMetrics",
    "AnalyticsReport",
    "HealthStatus",
    "HealthStatusLevel",
    "DateRange",
    # Memory metadata schemas
    "AdvancedMemoryMetadata",
    "SummaryLevel",
    "ImportanceScore",
    "ImportanceDecayMode",
    "SummaryMetadata",
    "MemoryReference",
]
