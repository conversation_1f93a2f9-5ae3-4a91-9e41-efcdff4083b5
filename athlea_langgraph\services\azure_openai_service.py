"""
Azure OpenAI Service

Python translation of app/api/coaching/lib/services/azure-openai-service.ts
Provides centralized configuration for Azure OpenAI that's used across the application.
"""

from typing import Any, Dict, Optional
from langchain_openai import AzureChatOpenAI

from ..config import config


# Model deployment mapping for Azure OpenAI
MODEL_DEPLOYMENTS = {
    "gpt-4o": "gpt-4o",  # Most capable, expensive
    "gpt-4o-mini": "gpt-4o-mini",  # Good balance, fast
    "gpt-4.1-nano": "gpt-4.1-nano",  # Fastest, cheapest
}

# Predefined model configurations for different node types - UPDATED TO USE gpt-4.1-nano
NODE_MODEL_CONFIGS = {
    # Fast, cheap models for simple tasks
    "intent_classification": {
        "model": "gpt-4.1-nano",
        "temperature": 0.0,
        "max_tokens": 100,
    },
    "greeting_detection": {
        "model": "gpt-4.1-nano",
        "temperature": 0.0,
        "max_tokens": 50,
    },
    "routing": {
        "model": "gpt-4.1-nano",
        "temperature": 0.0,
        "max_tokens": 200,
    },
    # ALL MODELS ARE NOW gpt-4.1-nano TO PREVENT DEPLOYMENT ERRORS
    "planning": {
        "model": "gpt-4.1-nano",
        "temperature": 0.3,
        "max_tokens": 1000,
    },
    "aggregation": {
        "model": "gpt-4.1-nano",
        "temperature": 0.3,
        "max_tokens": 2000,
    },
    "coaching": {
        "model": "gpt-4.1-nano",
        "temperature": 0.7,
        "max_tokens": 4000,
    },
    "reasoning": {
        "model": "gpt-4.1-nano",
        "temperature": 0.1,
        "max_tokens": 2000,
    },
    "complex_planning": {
        "model": "gpt-4.1-nano",
        "temperature": 0.3,
        "max_tokens": 3000,
    },
    "safety_analysis": {
        "model": "gpt-4.1-nano",
        "temperature": 0.0,
        "max_tokens": 1500,
    },
}


def create_azure_chat_openai(
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: Optional[int] = None,
    streaming: bool = True,
    node_type: Optional[str] = None,
    **kwargs: Any,
) -> AzureChatOpenAI:
    """
    Create an Azure ChatOpenAI instance with smart model selection.

    Args:
        model: Specific model to use (overrides node_type selection)
        temperature: The temperature for the model (0.0 to 1.0)
        max_tokens: Maximum number of tokens to generate
        streaming: Whether to enable streaming
        node_type: Type of node (uses predefined optimal config)
        **kwargs: Additional arguments to pass to AzureChatOpenAI

    Returns:
        Configured AzureChatOpenAI instance

    Raises:
        ValueError: If required environment variables are missing
    """
    azure_config = config.azure_openai

    if not azure_config.api_key:
        raise ValueError(
            "AZURE_OPENAI_API_KEY environment variable is required. "
            "Please set it in your .env file."
        )

    if not azure_config.endpoint:
        raise ValueError(
            "AZURE_OPENAI_ENDPOINT environment variable is required. "
            "Please set it in your .env file."
        )

    # Smart model selection based on node type
    if node_type and node_type in NODE_MODEL_CONFIGS:
        config_override = NODE_MODEL_CONFIGS[node_type]
        model = model or config_override["model"]
        temperature = config_override.get("temperature", temperature)
        max_tokens = max_tokens or config_override.get("max_tokens")
        print(f"[Azure OpenAI Service] Using optimized config for {node_type}: {model}")

    # Default model selection
    if not model:
        model = azure_config.deployment_name

    # Get the deployment name for the model
    deployment_name = MODEL_DEPLOYMENTS.get(model, model)

    print(f"[Azure OpenAI Service] Endpoint: {azure_config.endpoint}")
    print(f"[Azure OpenAI Service] Model: {model} -> Deployment: {deployment_name}")
    print(f"[Azure OpenAI Service] API Version: {azure_config.api_version}")
    print(f"[Azure OpenAI Service] Temperature: {temperature}")
    print(f"[Azure OpenAI Service] Max Tokens: {max_tokens}")
    print(f"[Azure OpenAI Service] Streaming: {streaming}")

    return AzureChatOpenAI(
        azure_endpoint=azure_config.endpoint,
        azure_deployment=deployment_name,
        api_version=azure_config.api_version,
        api_key=azure_config.api_key,
        temperature=temperature,
        max_tokens=max_tokens,
        streaming=streaming,
        # Fix blocking retry issue - disable retries to prevent time.sleep calls
        max_retries=0,
        timeout=30,
        **kwargs,
    )


def get_azure_openai_config() -> Dict[str, str]:
    """
    Get the current Azure OpenAI configuration.

    Returns:
        Dictionary containing the configuration values
    """
    azure_config = config.azure_openai
    return {
        "endpoint": azure_config.endpoint,
        "deployment_name": azure_config.deployment_name,
        "api_version": azure_config.api_version,
        "has_api_key": bool(azure_config.api_key),
    }


def validate_azure_openai_config() -> bool:
    """
    Validate that all required Azure OpenAI configuration is present.

    Returns:
        True if configuration is valid, False otherwise
    """
    azure_config = config.azure_openai

    if not azure_config.api_key:
        print(
            "[Azure OpenAI Service] Missing required environment variable: AZURE_OPENAI_API_KEY"
        )
        return False

    if not azure_config.endpoint:
        print(
            "[Azure OpenAI Service] Missing required environment variable: AZURE_OPENAI_ENDPOINT"
        )
        return False

    return True
