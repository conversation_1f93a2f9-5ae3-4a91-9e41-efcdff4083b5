"""
Configuration package for Athlea LangGraph.
Provides centralized, type-safe configuration management.
"""

from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from .base import (
    BaseConfig,
    Environment,
    AzureOpenAIConfig,
    AzureMapsConfig,
    AzureSearchConfig,
    EmbeddingConfig,
    DatabaseConfig,
    AirtableConfig,
    Mem0Config,
    N8nConfig,
)
from .validation import (
    get_config_for_environment,
    validate_config,
    ConfigValidationError,
)
from .logging import setup_logging

# Global configuration instance
config = get_config_for_environment()

__all__ = [
    "BaseConfig",
    "Environment",
    "AzureOpenAIConfig",
    "AzureMapsConfig",
    "AzureSearchConfig",
    "EmbeddingConfig",
    "DatabaseConfig",
    "AirtableConfig",
    "Mem0Config",
    "N8nConfig",
    "config",
    "get_config_for_environment",
    "validate_config",
    "ConfigValidationError",
    "setup_logging",
]
