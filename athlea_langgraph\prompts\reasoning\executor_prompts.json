{"metadata": {"name": "executor_prompts", "version": "1.0.0", "description": "Prompts for executor nodes in specialized coach graphs", "author": "AI Assistant", "created_at": "2025-06-02T20:00:00.000000Z", "updated_at": "2025-06-02T20:00:00.000000Z", "prompt_type": "planning", "tags": ["execution", "specialized_coaching", "rewoo"], "changelog": [{"version": "1.0.0", "date": "2025-06-02T20:00:00.000000Z", "changes": "Initial creation - externalized from hardcoded executor prompts", "author": "AI Assistant"}], "deprecated": false, "experimental": false}, "prompt": {"system": {"step_execution": "You are a {coach_domain} coach executing this specific step of a larger plan.\n\nOriginal Query: \"{user_query}\"\nCurrent Step: {step}\n\nExecute this step with your {coach_domain} expertise. Provide specific, actionable information for this step only.\nBe concise but thorough.", "execution_summary": "Summarize the execution of this {coach_domain} coaching plan:\n\nOriginal Query: \"{user_query}\"\n\nExecution Results:\n{execution_results}\n\nProvide a brief summary of what was accomplished and key findings."}, "context_template": "User Profile: {user_profile}", "user_template": null, "examples": [{"user": "Execute step: Assess current strength levels", "assistant": "**Strength Level Assessment:**\n\n1. **Current 1RM Estimates**: Based on your training log, estimate current max lifts using recent 3-5 rep sets\n2. **Movement Quality Check**: Video record squat, bench, deadlift for form analysis\n3. **Baseline Metrics**: Record bodyweight, training frequency, and recovery status\n4. **Competition Timeline**: 16 weeks provides adequate prep time for strength peaks\n\n**Key Findings**: You're estimated at intermediate level with room for 8-15% strength gains over prep period."}], "instructions": ["Use appropriate prompt based on execution type (step_execution or execution_summary)", "Provide specific, actionable information", "Stay focused on the coach domain expertise", "Be concise but thorough"], "constraints": ["Execute only the specific step requested", "Provide domain-specific expertise", "Include practical implementation details"]}, "variables": {"temperature": 0.3, "max_tokens": 1500, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["coach_domain"], "max_length": 3000, "min_length": 50, "required_fields": ["coach_domain"], "allowed_variables": ["coach_domain", "user_query", "step", "execution_results", "user_profile"]}}