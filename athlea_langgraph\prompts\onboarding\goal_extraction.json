{"metadata": {"name": "goal_extraction", "version": "1.1.0", "description": "Extracts specific fitness goals from user conversation during onboarding. Focuses on explicit user statements about their athletic objectives.", "author": "Athlea System", "created_at": "2025-01-22T00:00:00.000Z", "updated_at": "2025-01-22T00:00:00.000Z", "prompt_type": "onboarding", "tags": ["onboarding", "goal-extraction", "fitness-goals", "parsing", "multi-sport"], "changelog": [{"version": "1.0.0", "date": "2025-05-30T13:36:12.551808", "changes": "Initial migration from hardcoded prompt", "author": "<PERSON><PERSON>", "breaking_changes": false}, {"version": "1.1.0", "date": "2025-01-22T00:00:00.000Z", "changes": "Moved to dedicated onboarding folder with enhanced examples and metadata", "author": "Athlea System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "Analyze the user messages in the provided conversation history. Identify and extract ALL specific fitness goals mentioned by the user, noting the sport/activity if specified.\n\nIMPORTANT:\n- Focus ONLY on what the USER has explicitly stated as their goal(s).\n- Do not include general statements or questions from the coach.\n- Look for phrases like \"I want to...\", \"My goal for [sport] is...\", \"I'm hoping to...\", \"I'd like to...\", etc.\n\nExamples of fitness goals:\n- Build muscle\n- Lose weight\n- Run a faster 5k (Running)\n- Improve backhand consistency (Tennis)\n- Increase bench press max (Strength Training)\n- Swim 1500m freestyle efficiently (Swimming)\n- Complete first triathlon (Triathlon)\n- Improve flexibility (Yoga/Stretching)\n- Train for marathon (Running)\n- Get stronger overall (General Strength)\n\nReturn ONLY the identified goals, separated by commas. If the sport is clearly mentioned with the goal, include it in parentheses.\nFor example: \"Build muscle, Lose weight, Run a faster 5k (Running), Improve backhand consistency (Tennis)\"\n\nIf no specific fitness goals are mentioned by the user, respond ONLY with: \"NO_GOALS\".", "context_template": "Conversation History:\n{conversation_history}", "user_template": null, "examples": [{"title": "Multi-sport goals example", "user_input": "I want to build muscle and also get better at running. My goal is to run a sub-25 minute 5K.", "expected_output": "Build muscle, Run a sub-25 minute 5K (Running)"}, {"title": "Sport-specific goals example", "user_input": "I play tennis and want to improve my backhand. Also hoping to get stronger overall.", "expected_output": "Improve backhand (Tennis), Get stronger overall"}, {"title": "No goals mentioned example", "user_input": "Hi there! I'm interested in fitness but not sure what I want to focus on yet.", "expected_output": "NO_GOALS"}], "instructions": "Extract only explicit fitness goals stated by the user. Include sport/activity in parentheses when clearly specified. Separate multiple goals with commas.", "constraints": ["Only extract goals explicitly stated by the user", "Do not include coach questions or suggestions", "Use comma separation for multiple goals", "Include sport/activity in parentheses when specified", "Return 'NO_GOALS' if no goals are mentioned"]}, "variables": {"temperature": 0.1, "max_tokens": 500, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["conversation_history"], "max_length": 5000, "min_length": 5, "required_fields": [], "allowed_variables": ["conversation_history"]}}