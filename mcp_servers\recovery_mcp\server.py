"""
Recovery Domain MCP Server

Exposes recovery, sleep, and stress management tools via Model Context Protocol (MCP).
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Call<PERSON><PERSON><PERSON><PERSON>ult,
    GetPromptR<PERSON>ult,
    InitializeR<PERSON>ult,
    Prompt,
    PromptArgument,
)
from mcp.types import TextContent
from mcp.types import TextContent as TextContentType
from mcp.types import Tool

# Import the actual tools and their schemas
from athlea_langgraph.tools.recovery import (
    MobilityProtocolGenerator,
    SleepOptimizationTool,
    WellnessTrackingTool,
)
from athlea_langgraph.tools.schemas.recovery_schemas import (
    MobilityProtocolInput,
    SleepAssessmentInput,
    WellnessAssessmentInput,
)

logger = logging.getLogger(__name__)

# Initialize the tools
mobility_protocol_generator = MobilityProtocolGenerator()
sleep_optimization_tool = SleepOptimizationTool()
wellness_tracking_tool = WellnessTrackingTool()

app = Server("recovery-mcp-server")

RECOVERY_TOOLS = [
    Tool(
        name="generate_mobility_protocol",
        description=MobilityProtocolGenerator.description,
        inputSchema=MobilityProtocolInput.model_json_schema(),
    ),
    Tool(
        name="optimize_sleep",
        description=SleepOptimizationTool.description,
        inputSchema=SleepAssessmentInput.model_json_schema(),
    ),
    Tool(
        name="assess_wellness",
        description=WellnessTrackingTool.description,
        inputSchema=WellnessAssessmentInput.model_json_schema(),
    ),
]


@app.list_tools()
async def list_tools() -> List[Tool]:
    return RECOVERY_TOOLS


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    try:
        logger.info(f"Recovery MCP: Calling tool {name} with arguments: {arguments}")
        result_data = None

        if name == "generate_mobility_protocol":
            input_data = MobilityProtocolInput(**arguments)
            # Ensure the method name matches what's in MobilityProtocolGenerator
            result_data = await mobility_protocol_generator.generate_protocol(
                input_data
            )
        elif name == "optimize_sleep":
            input_data = SleepAssessmentInput(**arguments)
            result_data = await sleep_optimization_tool.optimize_sleep(input_data)
        elif name == "assess_wellness":
            input_data = WellnessAssessmentInput(**arguments)
            # Ensure the method name matches what's in WellnessTrackingTool
            # Also handle optional parameters like sport, historical_data, hrv_data if needed
            sport = arguments.pop("sport", "general_fitness")
            # For historical_data and hrv_data, they are complex and might not be passed directly.
            # The tool's internal logic should handle their absence or future integration.
            result_data = await wellness_tracking_tool.assess_wellness(
                input_data, sport=sport
            )
        else:
            raise ValueError(f"Unknown tool: {name}")

        if hasattr(result_data, "model_dump_json"):
            response_text = result_data.model_dump_json(indent=2)
        elif isinstance(result_data, dict):
            response_text = json.dumps(result_data, indent=2)
        else:
            response_text = str(result_data)

        return CallToolResult(content=[TextContent(type="text", text=response_text)])
    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}", exc_info=True)
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=json.dumps(
                        {"error": str(e), "details": traceback.format_exc()}, indent=2
                    ),
                )
            ]
        )


@app.list_prompts()
async def list_prompts() -> List[Prompt]:
    return [
        Prompt(
            name="daily_recovery_check_in",
            description="Guide user through a daily recovery check-in process.",
            arguments=[
                PromptArgument(
                    name="user_id", description="User identifier", required=True
                ),
            ],
        ),
        Prompt(
            name="post_workout_recovery_advice",
            description="Provide recovery advice after a specific workout.",
            arguments=[
                PromptArgument(
                    name="workout_intensity",
                    description="Intensity of the workout",
                    required=True,
                ),
                PromptArgument(
                    name="workout_duration_minutes",
                    description="Duration of workout in minutes",
                    required=True,
                ),
                PromptArgument(
                    name="muscle_groups_worked",
                    description="Main muscle groups worked",
                    required=True,
                ),
            ],
        ),
    ]


@app.get_prompt()
async def get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
    prompt_text = ""
    description = ""
    if name == "daily_recovery_check_in":
        user_id = arguments.get("user_id", "User")
        description = "Daily recovery check-in prompt"
        prompt_text = f"""
        Hello {user_id}, let's check in on your recovery today.
        Consider using the `assess_wellness` tool with your current feelings:
        - How is your energy level (1-10)?
        - How is your mood (1-10)?
        - What is your stress level (1-10)?
        - What is your muscle soreness (1-10)?
        - How was your sleep quality (1-10)?
        - What is your motivation like (1-10)?
        - How many liters of water have you had?
        - Any specific pain areas (e.g., [''knees'', ''lower_back''])?
        
        If you'd like to optimize your sleep, you can use the `optimize_sleep` tool.
        And for mobility, try `generate_mobility_protocol`.
        """
    elif name == "post_workout_recovery_advice":
        workout_intensity = arguments.get("workout_intensity", "moderate")
        workout_duration = arguments.get("workout_duration_minutes", "60")
        muscles_worked = arguments.get("muscle_groups_worked", "full body")
        description = "Post-workout recovery advice prompt"
        prompt_text = f"""
        You just completed a {workout_intensity} intensity workout for {workout_duration} minutes, focusing on {muscles_worked}.
        To optimize your recovery:
        - Use `assess_wellness` to log how you feel and get initial recommendations.
        - Consider `generate_mobility_protocol` targeting the {muscles_worked} areas.
        - Ensure you use `optimize_sleep` tonight for best results.
        Remember hydration and nutrition are key.
        """
    else:
        raise ValueError(f"Unknown prompt: {name}")

    return GetPromptResult(
        description=description,
        messages=[TextContentType(type="text", text=prompt_text)],
    )


async def main():
    """Run the recovery MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            protocolVersion="2024-11-05",
            serverInfo={"name": "recovery-mcp-server", "version": "0.1.0"},
        )


import traceback

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
