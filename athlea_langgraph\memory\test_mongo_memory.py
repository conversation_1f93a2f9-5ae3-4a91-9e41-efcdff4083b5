import asyncio
import os

import pytest
from motor.motor_asyncio import AsyncIOMotorClient

# Import the classes from mongo_memory.py
from athlea_langgraph.memory.mongo_memory import MongoMemoryStore, MongoSaver

# Use a test connection string; default to localhost
MONGODB_URI = os.environ.get("MONGODB_URI", "mongodb://localhost:27017")

# Use a test database
TEST_DATABASE = "langgraph_test"


@pytest.mark.asyncio
async def test_mongo_saver_save_and_load():
    saver = MongoSaver(
        connection_string=MONGODB_URI, database=TEST_DATABASE, collection="checkpoints"
    )
    thread_id = "test_thread"
    test_state = {"data": "test_value"}

    # Save state
    await saver.save_state(thread_id, test_state)

    # Load state
    loaded_state = await saver.load_state(thread_id)

    assert (
        loaded_state == test_state
    ), f"Loaded state {loaded_state} does not match saved state {test_state}"


@pytest.mark.asyncio
async def test_mongo_memory_store_put_and_get():
    store = MongoMemoryStore(
        connection_string=MONGODB_URI, database=TEST_DATABASE, collection="memories"
    )
    namespace = "test_namespace"
    key = "test_key"
    value = {"content": "hello world", "meta": {"tag": "greeting"}}

    # Put the memory
    await store.put(namespace, key, value)

    # Get the memory
    retrieved = await store.get(namespace, key)

    assert (
        retrieved == value
    ), f"Retrieved memory {retrieved} does not match inserted {value}"


@pytest.mark.asyncio
async def test_mongo_memory_store_search():
    store = MongoMemoryStore(
        connection_string=MONGODB_URI, database=TEST_DATABASE, collection="memories"
    )
    namespace = "test_namespace_search"

    # Insert multiple documents
    memories = [
        {"content": "This is a test memory about AI."},
        {"content": "Another memory discussing machine learning."},
        {"content": "Irrelevant content that should not match."},
    ]

    # Insert memories with unique keys
    for idx, mem in enumerate(memories):
        await store.put(namespace, f"key_{idx}", mem)
        # Sleep a bit to ensure indexing; in production not needed
        await asyncio.sleep(0.1)

    # Now search for memories containing 'AI' or 'machine'
    results = await store.search(namespace, query="AI machine")

    # At least two should be returned (the first two)
    assert len(results) >= 2, f"Expected at least 2 results, found {len(results)}"

    # Check that results contain expected keywords
    contents = [r.get("content", "") for r in results]
    assert any("AI" in text for text in contents), "No result contains 'AI'"
    assert any(
        "machine" in text.lower() for text in contents
    ), "No result contains 'machine'"


if __name__ == "__main__":
    # Run the tests when executed directly
    asyncio.run(test_mongo_saver_save_and_load())
    asyncio.run(test_mongo_memory_store_put_and_get())
    asyncio.run(test_mongo_memory_store_search())
    print("All Mongo memory tests passed.")
