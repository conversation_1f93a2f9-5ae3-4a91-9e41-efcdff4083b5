"""
Phase 3: Individual Specialized Coach ReWOO+Reflection Implementation

This module implements the SpecializedCoachGraph framework that transforms individual
coaching agents from simple LLM calls into sophisticated multi-step reasoning systems
using domain-specific ReWOO+Reflection patterns.

Key Features:
- LLM-based Complexity Assessment Node (no keyword matching)
- Domain-Specific ReWOO Worker Patterns
- Individual coach multi-step reasoning capabilities
- Integration with existing ReWOO+Reflection infrastructure

This addresses Issue #26 to bring ReWOO+Reflection sophistication down to individual coach level.
"""

import asyncio
import logging
import time
import uuid
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from ...services.azure_openai_service import create_azure_chat_openai
from ...states.state import AgentState, add_messages
from ...utils.prompt_loader import PromptLoader

logger = logging.getLogger(__name__)

# Domain to prompt file mapping
COACH_DOMAIN_MAPPING = {
    "strength": "strength_coach",
    "nutrition": "nutrition_coach",
    "cardio": "cardio_coach",
    "cycling": "cycling_coach",
    "recovery": "recovery_coach",
    "mental": "mental_coach",
}

# Domain-specific examples for complexity assessment
DOMAIN_EXAMPLES = {
    "strength": {
        "simple_examples": [
            "How do I do a proper squat?",
            "What's the correct bench press form?",
            "How many reps should I do for strength?",
        ],
        "complex_examples": [
            "Design a 16-week powerlifting competition prep program with periodization",
            "Create a program to address my strength imbalances and injury history",
            "Optimize my training for both strength and hypertrophy with limited time",
        ],
    },
    "nutrition": {
        "simple_examples": [
            "How many calories should I eat?",
            "What should I eat before a workout?",
            "Is protein powder necessary?",
        ],
        "complex_examples": [
            "Create a periodized nutrition plan for endurance competition",
            "Design macro cycling strategy for body recomposition",
            "Optimize nutrition for shift work and training schedule",
        ],
    },
    "cardio": {
        "simple_examples": [
            "What's a good running pace for beginners?",
            "How long should I run?",
            "What's the difference between HIIT and steady state?",
        ],
        "complex_examples": [
            "Design a comprehensive marathon training plan",
            "Create a periodized cardio program for athletic performance",
            "Optimize training zones and recovery for competitive athlete",
        ],
    },
    "cycling": {
        "simple_examples": [
            "How do I adjust my bike seat height?",
            "What gear should I use for hills?",
            "How often should I cycle as a beginner?",
        ],
        "complex_examples": [
            "Design a periodized training plan for century ride",
            "Create power-based training for competitive cycling",
            "Optimize bike fit and training for triathlon performance",
        ],
    },
    "recovery": {
        "simple_examples": [
            "How much sleep do I need?",
            "What stretches should I do after working out?",
            "How do I prevent muscle soreness?",
        ],
        "complex_examples": [
            "Design a comprehensive recovery protocol for overtraining",
            "Create periodized recovery plan for competitive athlete",
            "Optimize sleep and recovery for shift work and training",
        ],
    },
    "mental": {
        "simple_examples": [
            "How do I stay motivated to work out?",
            "What should I do when I don't feel like training?",
            "How do I set fitness goals?",
        ],
        "complex_examples": [
            "Develop mental training program for competition performance",
            "Create systematic approach to overcoming training anxiety",
            "Design mindset coaching for long-term athletic development",
        ],
    },
}


class SpecializedCoachState(AgentState):
    """Enhanced state for individual specialized coach with ReWOO+Reflection capabilities."""

    # Complexity Assessment
    complexity_level: Optional[str] = None  # "simple", "moderate", "complex"
    complexity_score: Optional[float] = None  # 0.0-1.0
    complexity_reasoning: Optional[str] = None
    complexity_assessment_time: Optional[float] = None

    # Coach Domain Info
    coach_domain: Optional[str] = None
    coach_specialization: Optional[str] = None

    # Execution Path Tracking
    execution_path: Optional[str] = None  # "simple", "complex"

    # Simple Path Results
    simple_response: Optional[str] = None
    simple_execution_time: Optional[float] = None

    # Complex Path Results (ReWOO-based)
    rewoo_plan: Optional[Dict[str, Any]] = None
    worker_results: Dict[str, Any] = {}
    synthesis_result: Optional[str] = None
    complex_execution_time: Optional[float] = None

    # Domain Agent Results
    domain_agent_response: Optional[str] = None
    domain_agent_execution_time: Optional[float] = None
    previous_response: Optional[str] = None  # For tracking improvements

    # Planning Results (for complex path)
    plan_steps: List[str] = []
    plan_content: Optional[str] = None
    planner_execution_time: Optional[float] = None

    # Execution Results (for complex path)
    executor_results: Dict[str, Any] = {}
    executor_summary: Optional[str] = None
    executor_execution_time: Optional[float] = None

    # Enhanced Reflection Results
    reflection_applied: bool = False
    reflection_improvements: List[str] = []
    reflection_iteration: int = 0  # Track reflection iterations
    reflection_assessment: Optional[Dict[str, Any]] = None  # Full assessment results
    reflection_final: bool = False  # Whether this is the final reflection
    reflection_iterations_completed: int = 0  # Total iterations completed
    reflection_loop_triggered: bool = False  # Flag to trigger looping
    reflection_status: Optional[str] = None  # Status of reflection process

    # Improvement Tracking
    improvement_guidance: Optional[str] = None  # Specific improvement instructions
    improvement_applied: bool = False  # Whether improvement was applied
    needs_improvement: bool = False  # Flag indicating improvement needed
    is_improvement_iteration: bool = False  # Flag for improvement iterations

    # Performance Metrics
    total_execution_time: Optional[float] = None


class ComplexityAssessmentNode:
    """LLM-based complexity assessment node for coaching queries."""

    def __init__(self, coach_domain: str):
        """Initialize complexity assessment for a specific coaching domain."""
        self.coach_domain = coach_domain
        self.llm = create_azure_chat_openai(
            temperature=0.1
        )  # Low temp for consistent assessment

        # Load complexity assessment prompt
        self.prompt_loader = PromptLoader()

    async def assess_complexity(
        self, user_query: str, user_profile: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Assess query complexity using sophisticated LLM analysis."""

        start_time = time.time()

        # Create domain-specific complexity assessment prompt
        assessment_prompt = self._create_assessment_prompt(user_query, user_profile)

        try:
            # Get LLM assessment
            response = await self.llm.ainvoke([HumanMessage(content=assessment_prompt)])
            assessment_result = self._parse_assessment_response(response.content)

            assessment_time = time.time() - start_time

            return {
                **assessment_result,
                "assessment_time": assessment_time,
                "assessment_method": "llm_based",
                "coach_domain": self.coach_domain,
            }

        except Exception as e:
            logger.error(f"Error in complexity assessment: {e}")
            # Fallback to moderate complexity
            return {
                "complexity_level": "moderate",
                "complexity_score": 0.6,
                "reasoning": f"Assessment error, defaulting to moderate: {str(e)}",
                "needs_rewoo": True,
                "assessment_time": time.time() - start_time,
                "assessment_method": "fallback",
                "coach_domain": self.coach_domain,
            }

    def _create_assessment_prompt(
        self, user_query: str, user_profile: Optional[Dict] = None
    ) -> str:
        """Create sophisticated complexity assessment prompt using versioned JSON."""

        try:
            # Load the complexity assessment prompt from JSON
            prompt_config = self.prompt_loader.load_prompt("complexity_assessment")

            # Get domain-specific examples
            domain_context = DOMAIN_EXAMPLES.get(
                self.coach_domain, DOMAIN_EXAMPLES["strength"]
            )
            simple_examples = chr(10).join(
                "- " + ex for ex in domain_context["simple_examples"]
            )
            complex_examples = chr(10).join(
                "- " + ex for ex in domain_context["complex_examples"]
            )

            # Prepare context
            user_context = f"User Context: {user_profile}" if user_profile else ""

            # Format the prompt with variables
            assessment_prompt = prompt_config.prompt.system.format(
                coach_domain=self.coach_domain,
                simple_examples=simple_examples,
                complex_examples=complex_examples,
                user_query=user_query,
                user_context=user_context,
            )

            return assessment_prompt

        except Exception as e:
            logger.error(f"Error loading complexity assessment prompt: {e}")
            # Fallback to basic prompt
            return f"""You are an expert {self.coach_domain} coach analyzing the complexity of a coaching query.

Your task is to determine if this query requires:
1. SIMPLE (0.0-0.3): Direct, straightforward answer - basic technique, general advice, simple questions
2. MODERATE (0.4-0.6): Some analysis needed but manageable in one comprehensive response
3. COMPLEX (0.7-1.0): Multi-step reasoning, planning, analysis, or synthesis required

Query to assess: "{user_query}"

{f"User Context: {user_profile}" if user_profile else ""}

Respond EXACTLY in this format:
COMPLEXITY_SCORE: [0.0-1.0]
COMPLEXITY_LEVEL: [simple/moderate/complex]
NEEDS_REWOO: [true/false]
REASONING: [2-3 sentence explanation of your assessment]
"""

    def _parse_assessment_response(self, response_content: str) -> Dict[str, Any]:
        """Parse LLM assessment response."""

        lines = response_content.strip().split("\n")
        result = {
            "complexity_score": 0.5,
            "complexity_level": "moderate",
            "needs_rewoo": True,
            "reasoning": "Could not parse assessment response",
        }

        for line in lines:
            line = line.strip()
            if line.startswith("COMPLEXITY_SCORE:"):
                try:
                    score = float(line.split(":", 1)[1].strip())
                    result["complexity_score"] = max(0.0, min(1.0, score))
                except:
                    pass
            elif line.startswith("COMPLEXITY_LEVEL:"):
                level = line.split(":", 1)[1].strip().lower()
                if level in ["simple", "moderate", "complex"]:
                    result["complexity_level"] = level
            elif line.startswith("NEEDS_REWOO:"):
                needs = line.split(":", 1)[1].strip().lower()
                result["needs_rewoo"] = needs == "true"
            elif line.startswith("REASONING:"):
                result["reasoning"] = line.split(":", 1)[1].strip()

        return result


class DomainSpecificReWOOWorkers:
    """Domain-specific ReWOO worker patterns using existing coach prompts as base."""

    @staticmethod
    def get_domain_workers(coach_domain: str) -> List[Dict[str, Any]]:
        """Get domain-specific worker definitions using existing coach prompts."""

        logger.info(f"[Workers] Requesting workers for domain: {coach_domain}")

        # Load the existing coach prompt as the base system prompt
        prompt_loader = PromptLoader()

        try:
            # Use the existing coach prompt from COACH_DOMAIN_MAPPING
            prompt_name = COACH_DOMAIN_MAPPING.get(coach_domain, "strength_coach")
            coach_prompt_config = prompt_loader.load_prompt(prompt_name)
            base_coach_prompt = coach_prompt_config.prompt.system
            logger.info(f"[Workers] Using existing {prompt_name} prompt as worker base")
        except Exception as e:
            logger.warning(f"[Workers] Could not load {coach_domain} coach prompt: {e}")
            # Fallback to generic prompt
            base_coach_prompt = (
                f"You are a professional {coach_domain} coach providing expert advice."
            )

        # Define worker-specific task instructions that get added to the base coach prompt
        worker_tasks = _get_worker_tasks(coach_domain)

        # Create workers by combining base coach prompt with specific task instructions
        workers = []
        for worker_config in worker_tasks:
            # Combine the existing coach prompt with worker-specific task focus
            worker_prompt = f"""{base_coach_prompt}

=== SPECIALIZED WORKER TASK ===
{worker_config['task_instruction']}

User Query: {{user_query}}
User Profile: {{user_profile}}
{worker_config.get('dependency_context', '')}

{worker_config['task_focus']}"""

            workers.append(
                {
                    "worker_id": worker_config["worker_id"],
                    "name": worker_config["name"],
                    "description": worker_config["description"],
                    "focus_areas": worker_config["focus_areas"],
                    "dependencies": worker_config.get("dependencies", []),
                    "prompt_template": worker_prompt,
                }
            )

        logger.info(
            f"[Workers] Created {len(workers)} workers using existing {coach_domain} coach prompt"
        )
        return workers


def _get_worker_tasks(coach_domain: str) -> List[Dict[str, Any]]:
    """Get domain-specific worker task definitions that complement the existing coach prompts."""

    if coach_domain == "strength":
        return [
            {
                "worker_id": "exercise_selection",
                "name": "Exercise Selection Specialist",
                "description": "Focuses on exercise selection within strength training",
                "focus_areas": [
                    "exercise selection",
                    "movement patterns",
                    "muscle targeting",
                ],
                "dependencies": [],
                "task_instruction": "Your specialized task: Focus ONLY on exercise selection for this strength training query.",
                "task_focus": """Analyze and provide:
1. Primary exercises for the user's goals
2. Alternative exercises for equipment/limitation considerations  
3. Movement pattern balance and muscle targeting
4. Exercise progression/regression options

IMPORTANT: Focus ONLY on exercise selection - do not provide programming, periodization, or other advice.""",
            },
            {
                "worker_id": "program_structure",
                "name": "Program Structure Designer",
                "description": "Focuses on program design and periodization",
                "focus_areas": [
                    "program design",
                    "periodization",
                    "volume distribution",
                ],
                "dependencies": ["exercise_selection"],
                "task_instruction": "Your specialized task: Focus ONLY on program structure and periodization for this strength training query.",
                "dependency_context": "Exercise Selection Input: {exercise_selection}",
                "task_focus": """Based on the selected exercises, design:
1. Training frequency and split structure
2. Volume and intensity progression  
3. Periodization strategy (if applicable)
4. Session structure and organization

IMPORTANT: Focus ONLY on program structure - do not repeat exercise selection advice.""",
            },
            {
                "worker_id": "progression_planning",
                "name": "Progression Planning Specialist",
                "description": "Focuses on progression strategies and adaptations",
                "focus_areas": [
                    "progressive overload",
                    "adaptation protocols",
                    "plateau prevention",
                ],
                "dependencies": ["program_structure"],
                "task_instruction": "Your specialized task: Focus ONLY on progression planning for this strength training query.",
                "dependency_context": "Program Structure Input: {program_structure}",
                "task_focus": """Create progression strategy covering:
1. Progressive overload methods
2. Adaptation timeline expectations
3. Plateau identification and breakthrough strategies  
4. Deload and recovery integration

IMPORTANT: Focus ONLY on progression planning - do not repeat program structure advice.""",
            },
        ]

    elif coach_domain == "nutrition":
        return [
            {
                "worker_id": "nutritional_assessment",
                "name": "Nutritional Assessment Specialist",
                "description": "Focuses on nutritional status analysis",
                "focus_areas": [
                    "nutritional analysis",
                    "caloric needs",
                    "deficiency identification",
                ],
                "dependencies": [],
                "task_instruction": "Your specialized task: Focus ONLY on nutritional assessment for this nutrition query.",
                "task_focus": """Conduct nutritional assessment covering:
1. Caloric and macronutrient needs analysis
2. Current nutrition status evaluation
3. Potential deficiencies or imbalances
4. Goal-specific nutritional requirements

IMPORTANT: Focus ONLY on assessment - do not provide meal plans or supplementation advice.""",
            },
            {
                "worker_id": "macro_optimization",
                "name": "Macronutrient Optimization Expert",
                "description": "Focuses on macronutrient ratios and timing",
                "focus_areas": ["macro ratios", "nutrient timing", "goal optimization"],
                "dependencies": ["nutritional_assessment"],
                "task_instruction": "Your specialized task: Focus ONLY on macronutrient optimization for this nutrition query.",
                "dependency_context": "Nutritional Assessment Input: {nutritional_assessment}",
                "task_focus": """Optimize macronutrient strategy:
1. Optimal macro ratios for goals
2. Nutrient timing strategies
3. Training day vs rest day variations
4. Metabolic and performance considerations

IMPORTANT: Focus ONLY on macronutrient optimization - do not repeat assessment or provide meal plans.""",
            },
            {
                "worker_id": "meal_planning",
                "name": "Strategic Meal Planner",
                "description": "Focuses on practical meal planning",
                "focus_areas": [
                    "meal planning",
                    "food selection",
                    "practical implementation",
                ],
                "dependencies": ["macro_optimization"],
                "task_instruction": "Your specialized task: Focus ONLY on meal planning for this nutrition query.",
                "dependency_context": "Macro Optimization Input: {macro_optimization}",
                "task_focus": """Create meal planning strategy:
1. Meal structure and frequency
2. Food selection and preparation methods
3. Practical implementation strategies
4. Sustainability and adherence considerations

IMPORTANT: Focus ONLY on meal planning - do not repeat macro or assessment advice.""",
            },
        ]

    elif coach_domain == "cardio":
        return [
            {
                "worker_id": "fitness_assessment",
                "name": "Cardiovascular Fitness Assessor",
                "description": "Focuses on cardiovascular fitness evaluation",
                "focus_areas": [
                    "fitness testing",
                    "baseline establishment",
                    "capacity analysis",
                ],
                "dependencies": [],
                "task_instruction": "Your specialized task: Focus ONLY on fitness assessment for this cardio query.",
                "task_focus": """Conduct fitness assessment covering:
1. Current cardiovascular fitness level
2. Training history and experience
3. Physical limitations or considerations
4. Baseline metrics and testing recommendations

IMPORTANT: Focus ONLY on fitness assessment - do not provide training programs.""",
            },
            {
                "worker_id": "zone_optimization",
                "name": "Training Zone Optimizer",
                "description": "Focuses on training zone optimization",
                "focus_areas": [
                    "heart rate zones",
                    "intensity distribution",
                    "zone targeting",
                ],
                "dependencies": ["fitness_assessment"],
                "task_instruction": "Your specialized task: Focus ONLY on training zone optimization for this cardio query.",
                "dependency_context": "Fitness Assessment Input: {fitness_assessment}",
                "task_focus": """Optimize training zones:
1. Target heart rate zones for different adaptations
2. Intensity distribution recommendations
3. Zone-specific training protocols
4. Monitoring and adjustment strategies

IMPORTANT: Focus ONLY on training zones - do not repeat assessment or provide full programs.""",
            },
        ]

    else:
        # Generic workers for other domains
        return [
            {
                "worker_id": "assessment",
                "name": f"{coach_domain.title()} Assessment Specialist",
                "description": f"Focuses on {coach_domain} assessment",
                "focus_areas": ["assessment", "analysis", "goal identification"],
                "dependencies": [],
                "task_instruction": f"Your specialized task: Focus ONLY on {coach_domain} assessment for this query.",
                "task_focus": f"""Provide comprehensive assessment covering:
1. Current status and level
2. Goals and objectives analysis
3. Key areas for improvement  
4. Baseline recommendations

IMPORTANT: Focus ONLY on assessment - do not provide full programs or implementations.""",
            },
        ]


class SpecializedCoachReWOOExecutor:
    """Executes domain-specific ReWOO patterns for individual coaches."""

    def __init__(self, coach_domain: str, coach_tools: List[Any] = None):
        """Initialize ReWOO executor for specific coaching domain."""
        self.coach_domain = coach_domain
        self.coach_tools = coach_tools or []
        self.llm = create_azure_chat_openai()

        # Load coach prompt using proper domain mapping
        prompt_loader = PromptLoader()
        try:
            # Map domain to correct prompt file
            prompt_name = COACH_DOMAIN_MAPPING.get(coach_domain, "strength_coach")
            coach_prompt_config = prompt_loader.load_prompt(prompt_name)
            self.base_coach_prompt = coach_prompt_config.prompt.system
            logger.info(f"Loaded prompt for {coach_domain} from {prompt_name}.json")
        except Exception as e:
            logger.warning(f"Could not load {coach_domain} coach prompt: {e}")
            self.base_coach_prompt = (
                f"You are a professional {coach_domain} coach providing expert advice."
            )

    async def execute_rewoo_workflow(
        self, user_query: str, user_profile: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Execute complete ReWOO workflow for this coaching domain."""

        start_time = time.time()

        try:
            # Get domain workers
            workers = DomainSpecificReWOOWorkers.get_domain_workers(self.coach_domain)

            # Execute workers in dependency order
            worker_results = await self._execute_workers_sequence(
                workers, user_query, user_profile
            )

            # Synthesize results
            synthesis_result = await self._synthesize_worker_outputs(
                user_query, worker_results, user_profile
            )

            execution_time = time.time() - start_time

            return {
                "success": True,
                "worker_results": worker_results,
                "synthesis_result": synthesis_result,
                "execution_time": execution_time,
                "workers_executed": len(workers),
                "successful_workers": len(
                    [r for r in worker_results.values() if r.get("success", False)]
                ),
            }

        except Exception as e:
            logger.error(f"Error in ReWOO execution for {self.coach_domain}: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "fallback_response": await self._generate_fallback_response(
                    user_query, user_profile
                ),
            }

    async def _execute_workers_sequence(
        self, workers: List[Dict], user_query: str, user_profile: Optional[Dict]
    ) -> Dict[str, Any]:
        """Execute workers in dependency order."""

        worker_results = {}
        execution_order = self._calculate_execution_order(workers)

        logger.info(
            f"[ReWOO] Executing {len(workers)} workers in order: {execution_order}"
        )

        for worker_id in execution_order:
            worker = next(w for w in workers if w["worker_id"] == worker_id)

            try:
                # Check dependencies
                dependencies_met = all(
                    dep_id in worker_results
                    and worker_results[dep_id].get("success", False)
                    for dep_id in worker.get("dependencies", [])
                )

                if not dependencies_met:
                    worker_results[worker_id] = {
                        "success": False,
                        "error": "Dependencies not met",
                        "execution_time": 0,
                    }
                    continue

                # Execute worker
                result = await self._execute_single_worker(
                    worker, user_query, user_profile, worker_results
                )
                worker_results[worker_id] = result

                logger.info(
                    f"[ReWOO] Worker {worker_id} completed: {result.get('success', False)}"
                )

            except Exception as e:
                logger.error(f"[ReWOO] Worker {worker_id} failed: {e}")
                worker_results[worker_id] = {
                    "success": False,
                    "error": str(e),
                    "execution_time": 0,
                }

        return worker_results

    async def _execute_single_worker(
        self,
        worker: Dict,
        user_query: str,
        user_profile: Optional[Dict],
        previous_results: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Execute a single ReWOO worker."""

        start_time = time.time()

        try:
            # Prepare worker context with dependency results
            context_inputs = {}
            for dep_id in worker.get("dependencies", []):
                if dep_id in previous_results and previous_results[dep_id].get(
                    "success", False
                ):
                    context_inputs[dep_id] = previous_results[dep_id]["result"]

            # Create worker prompt
            worker_prompt = worker["prompt_template"].format(
                user_query=user_query, user_profile=user_profile or {}, **context_inputs
            )

            # Execute with LLM
            full_prompt = f"{self.base_coach_prompt}\n\n{worker_prompt}"
            response = await self.llm.ainvoke([HumanMessage(content=full_prompt)])

            execution_time = time.time() - start_time

            return {
                "success": True,
                "result": response.content,
                "worker_name": worker["name"],
                "focus_areas": worker["focus_areas"],
                "execution_time": execution_time,
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "worker_name": worker["name"],
                "execution_time": time.time() - start_time,
            }

    def _calculate_execution_order(self, workers: List[Dict]) -> List[str]:
        """Calculate execution order respecting dependencies."""

        remaining = {w["worker_id"]: w["dependencies"] for w in workers}
        order = []

        while remaining:
            # Find workers with satisfied dependencies
            ready = [
                wid for wid, deps in remaining.items() if all(d in order for d in deps)
            ]

            if not ready:
                # Handle circular dependencies by picking any remaining
                ready = [list(remaining.keys())[0]]

            for worker_id in ready:
                order.append(worker_id)
                del remaining[worker_id]

        return order

    async def _synthesize_worker_outputs(
        self,
        user_query: str,
        worker_results: Dict[str, Any],
        user_profile: Optional[Dict] = None,
    ) -> str:
        """Synthesize worker outputs into coherent coaching response."""

        successful_results = {
            wid: result
            for wid, result in worker_results.items()
            if result.get("success", False)
        }

        if not successful_results:
            return "I apologize, but I encountered issues with my analysis. Let me provide a direct response to your question."

        # Create synthesis prompt
        synthesis_context = f"""
Original Query: "{user_query}"
{f"User Profile: {user_profile}" if user_profile else ""}

Specialist Analysis Results:
"""

        for worker_id, result in successful_results.items():
            synthesis_context += f"\n{result['worker_name']}:\n{result['result']}\n"

        synthesis_prompt = f"""
{self.base_coach_prompt}

You are synthesizing expert analysis from multiple {self.coach_domain} specialists into a comprehensive, actionable coaching response.

{synthesis_context}

Create a comprehensive response that:
1. Integrates insights from all specialists
2. Provides clear, actionable recommendations
3. Maintains logical flow and coherence
4. Addresses the original query completely
5. Reflects your expertise as a {self.coach_domain} coach

Provide a complete, well-structured coaching response.
"""

        try:
            response = await self.llm.ainvoke([HumanMessage(content=synthesis_prompt)])
            return response.content
        except Exception as e:
            logger.error(f"Error in synthesis: {e}")
            return f"I've analyzed your {self.coach_domain} question from multiple expert perspectives, but encountered a technical issue in synthesis. Here are the key insights from my analysis..."

    async def _generate_fallback_response(
        self, user_query: str, user_profile: Optional[Dict] = None
    ) -> str:
        """Generate simple fallback response when ReWOO fails."""

        simple_prompt = f"""
{self.base_coach_prompt}

User Query: "{user_query}"
{f"User Profile: {user_profile}" if user_profile else ""}

Provide a helpful, direct response as a {self.coach_domain} coach.
"""

        try:
            response = await self.llm.ainvoke([HumanMessage(content=simple_prompt)])
            return response.content
        except Exception as e:
            return f"I apologize, but I'm experiencing technical difficulties. Please try rephrasing your {self.coach_domain} question."


async def _experimental_create_specialized_coach_graph(
    coach_domain: str, config: Optional[Dict[str, Any]] = None
) -> CompiledStateGraph:
    """
    Create a SpecializedCoachGraph with ReAct + Planning capabilities.

    Architecture:
    [Start] → ComplexityAssessment → Simple_Path: DomainAgent → Reflection → [End]
                                  └→ Complex_Path: Planner → Executor → DomainAgent → Reflection → [End]

    Args:
        coach_domain: The coaching domain (strength, nutrition, cardio, cycling, recovery, mental)
        config: Optional configuration parameters

    Returns:
        Compiled graph implementing sophisticated individual coach reasoning
    """

    if config is None:
        config = {}

    logger.info(f"Creating SpecializedCoachGraph for {coach_domain} domain")

    # Initialize components
    complexity_assessor = ComplexityAssessmentNode(coach_domain)
    rewoo_executor = SpecializedCoachReWOOExecutor(coach_domain)

    # Helper functions for reflection
    def _parse_reflection_assessment(assessment_content: str) -> Dict[str, Any]:
        """Parse the reflection assessment response into structured data."""

        lines = assessment_content.strip().split("\n")
        assessment = {
            "safety_score": 7,
            "evidence_score": 7,
            "personalization_score": 7,
            "clarity_score": 7,
            "completeness_score": 7,
            "professional_score": 7,
            "overall_score": 7,
            "needs_improvement": False,
            "improvement_priority": "low",
            "improvements": [],
        }

        current_improvements = []

        for line in lines:
            line = line.strip()
            if ":" in line:
                key, value = line.split(":", 1)
                key = key.strip().lower()
                value = value.strip()

                # Parse scores
                if key.endswith("_score"):
                    try:
                        score = int(value.split()[0])  # Get first number
                        assessment[key] = max(1, min(10, score))
                    except:
                        pass

                # Parse boolean values
                elif key == "needs_improvement":
                    assessment["needs_improvement"] = value.lower() == "true"

                # Parse priority
                elif key == "improvement_priority":
                    if value.lower() in ["high", "medium", "low"]:
                        assessment["improvement_priority"] = value.lower()

                # Parse improvements
                elif key.endswith("_improvement") and value.lower() != "none":
                    current_improvements.append(value)

                elif key == "specific_improvements":
                    current_improvements.append(value)

        assessment["improvements"] = current_improvements

        # Calculate overall score if not parsed
        if assessment["overall_score"] == 7:  # Default value
            scores = [
                assessment["safety_score"],
                assessment["evidence_score"],
                assessment["personalization_score"],
                assessment["clarity_score"],
                assessment["completeness_score"],
                assessment["professional_score"],
            ]
            assessment["overall_score"] = round(sum(scores) / len(scores), 1)

        return assessment

    def _create_improvement_guidance(
        assessment: Dict[str, Any], user_query: str, domain_response: str
    ) -> str:
        """Create specific improvement guidance for the domain agent."""

        guidance_parts = [
            f"REFLECTION FEEDBACK: Your previous response needs improvement.",
            f"Overall Quality Score: {assessment.get('overall_score', 'N/A')}/10",
            f"Priority: {assessment.get('improvement_priority', 'medium')}",
            "",
            "SPECIFIC IMPROVEMENTS NEEDED:",
        ]

        improvements = assessment.get("improvements", [])
        for i, improvement in enumerate(improvements, 1):
            guidance_parts.append(f"{i}. {improvement}")

        guidance_parts.extend(
            [
                "",
                "DIMENSION SCORES:",
                f"- Safety: {assessment.get('safety_score', 'N/A')}/10",
                f"- Evidence-based: {assessment.get('evidence_score', 'N/A')}/10",
                f"- Personalization: {assessment.get('personalization_score', 'N/A')}/10",
                f"- Clarity: {assessment.get('clarity_score', 'N/A')}/10",
                f"- Completeness: {assessment.get('completeness_score', 'N/A')}/10",
                f"- Professional: {assessment.get('professional_score', 'N/A')}/10",
                "",
                "Please revise your response addressing these specific improvements while maintaining your expertise and coaching approach.",
            ]
        )

        return "\n".join(guidance_parts)

    async def _apply_final_enhancement(
        response: str, assessment: Dict[str, Any], user_query: str, coach_domain: str
    ) -> str:
        """Apply final enhancement to response based on reflection assessment."""

        enhancement_prompt = f"""
You are a {coach_domain} coach applying final enhancements to your response based on quality reflection.

Original Query: "{user_query}"
Your Response: "{response}"

Reflection Assessment Summary:
- Overall Score: {assessment.get('overall_score', 'N/A')}/10
- Key Improvements: {'; '.join(assessment.get('improvements', ['None']))}

Apply subtle enhancements to improve quality while maintaining your coaching voice and approach.
Focus on the specific improvements identified without completely rewriting the response.

Provide the enhanced response:
"""

        try:
            rewoo_executor_runtime = SpecializedCoachReWOOExecutor(coach_domain)
            enhanced_response = await rewoo_executor_runtime.llm.ainvoke(
                [HumanMessage(content=enhancement_prompt)]
            )
            return enhanced_response.content
        except Exception as e:
            logger.error(f"[Reflection] Enhancement error: {e}")
            return response  # Return original if enhancement fails

    # Node implementations
    async def complexity_assessment_node(
        state: SpecializedCoachState,
    ) -> SpecializedCoachState:
        """LLM-based complexity assessment node."""

        # Get coach domain from state or fallback to the one passed to graph creation
        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )

        # Enhanced debugging
        logger.info(f"[Complexity Node] === DOMAIN DEBUGGING ===")
        logger.info(
            f"[Complexity Node] state.get('coach_domain'): {state.get('coach_domain')}"
        )
        logger.info(
            f"[Complexity Node] config.get('coach_domain'): {config.get('coach_domain')}"
        )
        logger.info(f"[Complexity Node] fallback coach_domain: {coach_domain}")
        logger.info(f"[Complexity Node] runtime_coach_domain: {runtime_coach_domain}")
        logger.info(f"[Complexity Node] === END DOMAIN DEBUGGING ===")

        logger.info(
            f"[Complexity Node] Assessing {runtime_coach_domain} query complexity"
        )

        # Extract user query with improved logic for LangGraph Studio
        # Start with existing messages
        messages = list(state.get("messages", []))
        user_query = ""

        # Method 1: Check for direct user input in state (various possible keys)
        user_input = (
            state.get("input")
            or state.get("user_query")
            or state.get("query")
            or state.get("text")
        )

        if user_input:
            user_query = str(user_input)
            logger.info(f"[Complexity Node] Found user input in state: {user_query}")

            # Add as HumanMessage to messages array if not already present
            if not any(
                isinstance(msg, HumanMessage) and msg.content == user_query
                for msg in messages
            ):
                new_message = HumanMessage(content=user_query)
                messages.append(new_message)
                logger.info(f"[Complexity Node] Added user input as HumanMessage")

        # Method 2: Extract from messages (both HumanMessage and string content)
        elif messages:
            logger.info(f"[Complexity Node] Checking {len(messages)} messages")
            for i, msg in enumerate(reversed(messages)):
                logger.info(
                    f"[Complexity Node] Message {i}: {type(msg)} - {getattr(msg, 'content', str(msg))[:100]}"
                )
                if isinstance(msg, HumanMessage) and msg.content.strip():
                    user_query = msg.content.strip()
                    logger.info(f"[Complexity Node] Found HumanMessage: {user_query}")
                    break
                elif isinstance(msg, dict) and msg.get("content"):
                    user_query = msg["content"].strip()
                    logger.info(f"[Complexity Node] Found dict message: {user_query}")
                    # Convert to HumanMessage
                    if not any(
                        isinstance(m, HumanMessage) and m.content == user_query
                        for m in messages
                    ):
                        new_message = HumanMessage(content=user_query)
                        messages.append(new_message)
                        logger.info(f"[Complexity Node] Converted dict to HumanMessage")
                    break
                elif isinstance(msg, str) and msg.strip():
                    user_query = msg.strip()
                    logger.info(f"[Complexity Node] Found string message: {user_query}")
                    # Convert to HumanMessage
                    if not any(
                        isinstance(m, HumanMessage) and m.content == user_query
                        for m in messages
                    ):
                        new_message = HumanMessage(content=user_query)
                        messages.append(new_message)
                        logger.info(
                            f"[Complexity Node] Converted string to HumanMessage"
                        )
                    break

        # Method 3: Check for input key directly (fallback)
        elif hasattr(state, "input") and state.input:
            user_query = str(state.input)
            logger.info(f"[Complexity Node] Found input: {user_query}")
            # Add as HumanMessage
            if not any(
                isinstance(msg, HumanMessage) and msg.content == user_query
                for msg in messages
            ):
                new_message = HumanMessage(content=user_query)
                messages.append(new_message)
                logger.info(f"[Complexity Node] Added input as HumanMessage")

        # Fallback with more specific message
        if not user_query or user_query.strip() == "":
            user_query = f"Hello, I need {runtime_coach_domain} coaching advice."
            logger.warning(f"[Complexity Node] Using fallback query: {user_query}")
            # Add fallback as HumanMessage
            if not any(
                isinstance(msg, HumanMessage) and msg.content == user_query
                for msg in messages
            ):
                new_message = HumanMessage(content=user_query)
                messages.append(new_message)
                logger.info(f"[Complexity Node] Added fallback as HumanMessage")
        else:
            logger.info(f"[Complexity Node] Using actual user query: {user_query}")

        logger.info(f"[Complexity Node] Final user query: {user_query}")

        # Use runtime coach domain for assessment
        complexity_assessor_runtime = ComplexityAssessmentNode(runtime_coach_domain)
        assessment = await complexity_assessor_runtime.assess_complexity(
            user_query, state.get("user_profile")
        )

        logger.info(
            f"[Complexity Node] Assessment: {assessment['complexity_level']} "
            f"(score: {assessment['complexity_score']:.3f}) - {assessment['reasoning']}"
        )

        return {
            **state,
            "complexity_level": assessment["complexity_level"],
            "complexity_score": assessment["complexity_score"],
            "complexity_reasoning": assessment["reasoning"],
            "complexity_assessment_time": assessment["assessment_time"],
            "coach_domain": runtime_coach_domain,  # Set the correct domain in state
            "user_query": user_query,  # Store the extracted query
            "messages": add_messages(
                messages,  # Use the updated messages list that includes user input
                [
                    AIMessage(
                        content=f"🔍 Analyzing {runtime_coach_domain} query complexity: {assessment['complexity_level']} "
                        f"({assessment['complexity_score']:.2f})",
                        name="complexity_assessor",
                    )
                ],
            ),
        }

    async def domain_agent_node(
        state: SpecializedCoachState,
    ) -> SpecializedCoachState:
        """Domain Agent - Core ReAct reasoning component for both simple and complex paths."""

        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )
        logger.info(
            f"[Domain Agent] Processing {runtime_coach_domain} coaching request"
        )
        start_time = time.time()

        user_query = state.get("user_query", "")
        execution_path = state.get("execution_path", "simple")

        try:
            # For simple path: Direct ReAct coaching
            if execution_path == "simple":
                rewoo_executor_runtime = SpecializedCoachReWOOExecutor(
                    runtime_coach_domain
                )
                response = await rewoo_executor_runtime._generate_fallback_response(
                    user_query, state.get("user_profile")
                )

                execution_time = time.time() - start_time

                return {
                    **state,
                    "domain_agent_response": response,
                    "domain_agent_execution_time": execution_time,
                    "messages": add_messages(
                        state.get("messages", []),
                        [
                            AIMessage(
                                content=f"🎯 {runtime_coach_domain.title()} Coach (ReAct): Direct coaching response",
                                name=f"{runtime_coach_domain}_domain_agent",
                            ),
                            AIMessage(
                                content=response, name=f"{runtime_coach_domain}_coach"
                            ),
                        ],
                    ),
                }

            # For complex path: Integrate executor results
            else:
                plan_results = state.get("executor_results", {})
                executor_summary = state.get("executor_summary", "")

                # Synthesize executor results into final coaching response
                if plan_results and executor_summary:
                    synthesis_prompt = f"""
{rewoo_executor.base_coach_prompt}

You are a {runtime_coach_domain} coach synthesizing execution results into a comprehensive coaching response.

Original Query: "{user_query}"
Execution Summary: {executor_summary}

Detailed Results:
{chr(10).join([f"- {task}: {result}" for task, result in plan_results.items()])}

Create a comprehensive, actionable coaching response that:
1. Integrates all execution results
2. Provides clear recommendations
3. Addresses the original query completely
4. Maintains your expertise as a {runtime_coach_domain} coach
"""

                    rewoo_executor_runtime = SpecializedCoachReWOOExecutor(
                        runtime_coach_domain
                    )
                    response_obj = await rewoo_executor_runtime.llm.ainvoke(
                        [HumanMessage(content=synthesis_prompt)]
                    )
                    response = response_obj.content
                else:
                    # Fallback if no executor results
                    rewoo_executor_runtime = SpecializedCoachReWOOExecutor(
                        runtime_coach_domain
                    )
                    response = await rewoo_executor_runtime._generate_fallback_response(
                        user_query, state.get("user_profile")
                    )

                execution_time = time.time() - start_time

                return {
                    **state,
                    "domain_agent_response": response,
                    "domain_agent_execution_time": execution_time,
                    "messages": add_messages(
                        state.get("messages", []),
                        [
                            AIMessage(
                                content=f"🧠 {runtime_coach_domain.title()} Coach (Solver): Integrated solution from plan execution",
                                name=f"{runtime_coach_domain}_domain_agent",
                            ),
                            AIMessage(
                                content=response, name=f"{runtime_coach_domain}_coach"
                            ),
                        ],
                    ),
                }

        except Exception as e:
            logger.error(f"[Domain Agent] Error: {e}")
            return {
                **state,
                "domain_agent_response": f"I apologize for the technical issue: {str(e)}",
                "domain_agent_execution_time": time.time() - start_time,
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"❌ Error in {runtime_coach_domain} domain agent: {str(e)}",
                            name=f"{runtime_coach_domain}_domain_agent",
                        )
                    ],
                ),
            }

    async def planner_node(state: SpecializedCoachState) -> SpecializedCoachState:
        """Planner - Creates multi-step plan for complex queries."""

        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )
        logger.info(f"[Planner] Creating plan for complex {runtime_coach_domain} query")
        start_time = time.time()

        user_query = state.get("user_query", "")

        try:
            # Load planning prompt from external file
            prompt_loader = PromptLoader()
            try:
                planner_config = prompt_loader.load_prompt("planner_prompts")
                planning_prompt = planner_config.prompt.system.format(
                    coach_domain=runtime_coach_domain, user_query=user_query
                )
                logger.info(f"[Planner] Using externalized planning prompt")
            except Exception as e:
                logger.warning(f"[Planner] Could not load planning prompt: {e}")
                # Fallback to hardcoded prompt
                planning_prompt = f"""
You are a {runtime_coach_domain} coaching planner. Break down this complex query into actionable steps.

Query: "{user_query}"

Create a plan with 3-5 specific steps that need to be executed to fully address this query.
Each step should be:
1. Specific and actionable
2. Focused on one aspect of the problem
3. Able to be executed independently

Format as:
STEP_1: [description]
STEP_2: [description]
STEP_3: [description]
...

Focus on {runtime_coach_domain} domain expertise and practical implementation.
"""

            rewoo_executor_runtime = SpecializedCoachReWOOExecutor(runtime_coach_domain)
            response = await rewoo_executor_runtime.llm.ainvoke(
                [HumanMessage(content=planning_prompt)]
            )
            plan_content = response.content

            # Parse plan steps
            plan_steps = []
            for line in plan_content.split("\n"):
                if line.strip().startswith("STEP_"):
                    step_text = (
                        line.split(":", 1)[1].strip() if ":" in line else line.strip()
                    )
                    plan_steps.append(step_text)

            execution_time = time.time() - start_time

            return {
                **state,
                "execution_path": "complex",
                "plan_steps": plan_steps,
                "plan_content": plan_content,
                "planner_execution_time": execution_time,
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"📋 Planner: Created {len(plan_steps)}-step plan for {runtime_coach_domain} coaching",
                            name=f"{runtime_coach_domain}_planner",
                        ),
                        AIMessage(
                            content=f"**Plan Steps:**\n{chr(10).join([f'{i+1}. {step}' for i, step in enumerate(plan_steps)])}",
                            name=f"{runtime_coach_domain}_planner",
                        ),
                    ],
                ),
            }

        except Exception as e:
            logger.error(f"[Planner] Error: {e}")
            return {
                **state,
                "execution_path": "complex",
                "plan_steps": [],
                "plan_content": f"Planning error: {str(e)}",
                "planner_execution_time": time.time() - start_time,
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"❌ Planning error for {runtime_coach_domain}: {str(e)}",
                            name=f"{runtime_coach_domain}_planner",
                        )
                    ],
                ),
            }

    async def executor_node(state: SpecializedCoachState) -> SpecializedCoachState:
        """Executor - Executes plan tasks using tools and domain knowledge."""

        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )
        logger.info(f"[Executor] Executing plan for {runtime_coach_domain}")
        start_time = time.time()

        plan_steps = state.get("plan_steps", [])
        user_query = state.get("user_query", "")

        if not plan_steps:
            logger.warning("[Executor] No plan steps found, using simple execution")
            return {
                **state,
                "executor_results": {},
                "executor_summary": "No plan available, proceeding with simple execution",
                "executor_execution_time": time.time() - start_time,
            }

        try:
            # Load executor prompts from external file
            prompt_loader = PromptLoader()
            try:
                executor_config = prompt_loader.load_prompt("executor_prompts")
                step_execution_template = executor_config.prompt.system[
                    "step_execution"
                ]
                execution_summary_template = executor_config.prompt.system[
                    "execution_summary"
                ]
                logger.info(f"[Executor] Using externalized executor prompts")
            except Exception as e:
                logger.warning(f"[Executor] Could not load executor prompts: {e}")
                # Fallback to hardcoded prompts
                step_execution_template = """You are a {coach_domain} coach executing this specific step of a larger plan.

Original Query: "{user_query}"
Current Step: {step}

Execute this step with your {coach_domain} expertise. Provide specific, actionable information for this step only.
Be concise but thorough."""

                execution_summary_template = """Summarize the execution of this {coach_domain} coaching plan:

Original Query: "{user_query}"

Execution Results:
{execution_results}

Provide a brief summary of what was accomplished and key findings."""

            executor_results = {}

            # Execute each plan step
            for i, step in enumerate(plan_steps):
                step_prompt = step_execution_template.format(
                    coach_domain=runtime_coach_domain, user_query=user_query, step=step
                )

                rewoo_executor_runtime = SpecializedCoachReWOOExecutor(
                    runtime_coach_domain
                )
                response = await rewoo_executor_runtime.llm.ainvoke(
                    [HumanMessage(content=step_prompt)]
                )
                executor_results[f"step_{i+1}"] = response.content

            # Create execution summary
            execution_results_text = chr(10).join(
                [
                    f"Step {i+1}: {result}"
                    for i, result in enumerate(executor_results.values())
                ]
            )
            summary_prompt = execution_summary_template.format(
                coach_domain=runtime_coach_domain,
                user_query=user_query,
                execution_results=execution_results_text,
            )

            response = await rewoo_executor_runtime.llm.ainvoke(
                [HumanMessage(content=summary_prompt)]
            )
            executor_summary = response.content

            execution_time = time.time() - start_time

            return {
                **state,
                "executor_results": executor_results,
                "executor_summary": executor_summary,
                "executor_execution_time": execution_time,
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"⚡ Executor: Completed {len(plan_steps)} steps for {runtime_coach_domain} plan",
                            name=f"{runtime_coach_domain}_executor",
                        ),
                        AIMessage(
                            content=f"**Execution Summary:**\n{executor_summary}",
                            name=f"{runtime_coach_domain}_executor",
                        ),
                    ],
                ),
            }

        except Exception as e:
            logger.error(f"[Executor] Error: {e}")
            return {
                **state,
                "executor_results": {},
                "executor_summary": f"Execution error: {str(e)}",
                "executor_execution_time": time.time() - start_time,
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"❌ Execution error for {runtime_coach_domain}: {str(e)}",
                            name=f"{runtime_coach_domain}_executor",
                        )
                    ],
                ),
            }

    async def reflection_node(state: SpecializedCoachState) -> SpecializedCoachState:
        """
        Enhanced Reflection - Quality assessment with intelligent looping capabilities.

        Based on coaching reflection research, this node:
        1. Assesses response quality across multiple dimensions
        2. Determines if improvement is needed
        3. Can trigger looping back to domain agent for refinement
        4. Tracks reflection iterations to prevent infinite loops
        """

        if not config.get("enable_reflection", True):
            return state

        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )
        logger.info(
            f"[Reflection] Applying quality reflection for {runtime_coach_domain}"
        )

        domain_response = state.get("domain_agent_response", "")
        user_query = state.get("user_query", "")
        reflection_iteration = state.get("reflection_iteration", 0)
        max_reflection_iterations = config.get("max_reflection_iterations", 2)

        # If no response to reflect on, skip
        if not domain_response:
            logger.warning("[Reflection] No domain agent response to reflect on")
            return {
                **state,
                "reflection_applied": True,
                "reflection_status": "skipped_no_response",
            }

        try:
            # Create comprehensive reflection assessment prompt using versioned JSON
            prompt_loader = PromptLoader()
            try:
                reflection_prompt_config = prompt_loader.load_prompt(
                    "reflection_assessment"
                )
                reflection_assessment_prompt = (
                    reflection_prompt_config.prompt.system.format(
                        coach_domain=runtime_coach_domain,
                        user_query=user_query,
                        coach_response=domain_response,
                    )
                )
                logger.info(
                    f"Using versioned reflection assessment prompt v{reflection_prompt_config.metadata.version}"
                )
            except Exception as e:
                logger.error(f"Error loading reflection assessment prompt: {e}")
                # Fallback to basic reflection prompt
                reflection_assessment_prompt = f"""
You are a reflective {runtime_coach_domain} coaching quality assessor. Evaluate this coaching response across multiple dimensions.

Original Query: "{user_query}"
Coach Response: "{domain_response}"

Assess the response on these critical dimensions (1-10 scale):

1. SAFETY & CONTRAINDICATIONS: Are safety considerations adequately addressed?
2. EVIDENCE-BASED: Is the advice grounded in evidence-based practice?
3. PERSONALIZATION: How well is it tailored to the user's context and needs?
4. CLARITY & ACTIONABILITY: How clear and actionable are the recommendations?
5. COMPLETENESS: Does it fully address the original query?
6. PROFESSIONAL STANDARDS: Does it meet professional coaching standards?

For each dimension, provide:
- Score (1-10)
- Brief reasoning
- Specific improvement needed (if any)

Then determine:
- OVERALL_QUALITY_SCORE: (1-10, average of dimensions)
- NEEDS_IMPROVEMENT: (true/false) - true if overall score < 7 or any critical dimension < 6
- IMPROVEMENT_PRIORITY: (high/medium/low) - urgency of improvement needed

Format your response as:
SAFETY_SCORE: [1-10]
SAFETY_REASONING: [brief explanation]
SAFETY_IMPROVEMENT: [specific improvement needed or "none"]

EVIDENCE_SCORE: [1-10]
EVIDENCE_REASONING: [brief explanation]
EVIDENCE_IMPROVEMENT: [specific improvement needed or "none"]

PERSONALIZATION_SCORE: [1-10]
PERSONALIZATION_REASONING: [brief explanation]
PERSONALIZATION_IMPROVEMENT: [specific improvement needed or "none"]

CLARITY_SCORE: [1-10]
CLARITY_REASONING: [brief explanation]
CLARITY_IMPROVEMENT: [specific improvement needed or "none"]

COMPLETENESS_SCORE: [1-10]
COMPLETENESS_REASONING: [brief explanation]
COMPLETENESS_IMPROVEMENT: [specific improvement needed or "none"]

PROFESSIONAL_SCORE: [1-10]
PROFESSIONAL_REASONING: [brief explanation]
PROFESSIONAL_IMPROVEMENT: [specific improvement needed or "none"]

OVERALL_QUALITY_SCORE: [1-10]
NEEDS_IMPROVEMENT: [true/false]
IMPROVEMENT_PRIORITY: [high/medium/low]
SPECIFIC_IMPROVEMENTS: [list key improvements needed]
"""

            # Get reflection assessment
            rewoo_executor_runtime = SpecializedCoachReWOOExecutor(runtime_coach_domain)
            reflection_response = await rewoo_executor_runtime.llm.ainvoke(
                [HumanMessage(content=reflection_assessment_prompt)]
            )

            # Parse reflection assessment
            reflection_assessment = _parse_reflection_assessment(
                reflection_response.content
            )

            logger.info(
                f"[Reflection] Quality assessment - Overall: {reflection_assessment.get('overall_score', 'N/A')}/10, "
                f"Needs improvement: {reflection_assessment.get('needs_improvement', False)}"
            )

            # Determine if we should loop back for improvement
            should_loop_back = (
                reflection_assessment.get("needs_improvement", False)
                and reflection_iteration < max_reflection_iterations
                and reflection_assessment.get("improvement_priority")
                in ["high", "medium"]
            )

            if should_loop_back:
                logger.info(
                    f"[Reflection] Quality insufficient, triggering improvement loop (iteration {reflection_iteration + 1})"
                )

                # Create improvement guidance for domain agent
                improvement_guidance = _create_improvement_guidance(
                    reflection_assessment, user_query, domain_response
                )

                return {
                    **state,
                    "reflection_iteration": reflection_iteration + 1,
                    "reflection_assessment": reflection_assessment,
                    "improvement_guidance": improvement_guidance,
                    "needs_improvement": True,
                    "reflection_loop_triggered": True,
                    "messages": add_messages(
                        state.get("messages", []),
                        [
                            AIMessage(
                                content=f"🔄 Reflection: Quality assessment indicates improvement needed (Score: {reflection_assessment.get('overall_score', 'N/A')}/10). Triggering refinement loop.",
                                name="reflection_agent",
                            )
                        ],
                    ),
                }

            else:
                # Quality is satisfactory or max iterations reached
                if reflection_iteration >= max_reflection_iterations:
                    logger.info(
                        f"[Reflection] Max iterations ({max_reflection_iterations}) reached, finalizing response"
                    )
                else:
                    logger.info(
                        f"[Reflection] Quality satisfactory (Score: {reflection_assessment.get('overall_score', 'N/A')}/10)"
                    )

                # Optionally apply final enhancement if enabled
                final_response = domain_response
                if (
                    config.get("apply_final_enhancement", False)
                    and reflection_assessment.get("overall_score", 10) < 9
                ):
                    final_response = await _apply_final_enhancement(
                        domain_response,
                        reflection_assessment,
                        user_query,
                        runtime_coach_domain,
                    )

                return {
                    **state,
                    "reflection_applied": True,
                    "reflection_assessment": reflection_assessment,
                    "reflection_final": True,
                    "reflection_iterations_completed": reflection_iteration,
                    "domain_agent_response": final_response,  # Use potentially enhanced response
                    "original_response": (
                        domain_response if final_response != domain_response else None
                    ),
                    "messages": add_messages(
                        state.get("messages", []),
                        [
                            AIMessage(
                                content=f"✅ Reflection: Quality assessment complete (Score: {reflection_assessment.get('overall_score', 'N/A')}/10). Response meets coaching standards.",
                                name="reflection_agent",
                            )
                        ],
                    ),
                }

        except Exception as e:
            logger.error(f"[Reflection] Error in quality assessment: {e}")
            # Fallback to basic reflection
            return {
                **state,
                "reflection_applied": True,
                "reflection_status": "error_fallback",
                "reflection_error": str(e),
                "reflection_improvements": [
                    "Applied basic quality verification (assessment failed)",
                    "Ensured coaching response was provided",
                ],
                "messages": add_messages(
                    state.get("messages", []),
                    [
                        AIMessage(
                            content=f"✅ Applied basic {runtime_coach_domain} coaching quality reflection (assessment error)",
                            name="reflection_agent",
                        )
                    ],
                ),
            }

    # Router functions
    def complexity_router(state: SpecializedCoachState) -> str:
        """Route based on complexity assessment."""
        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )
        complexity_level = state.get("complexity_level", "moderate")

        if complexity_level == "simple":
            logger.info(
                f"[Router] Routing {runtime_coach_domain} to simple path (complexity: {complexity_level})"
            )
            return "domain_agent_simple"
        else:
            logger.info(
                f"[Router] Routing {runtime_coach_domain} to complex path (complexity: {complexity_level})"
            )
            return "planner"

    def reflection_router(state: SpecializedCoachState) -> str:
        """Route from reflection - either loop back for improvement or finish."""

        # Check if reflection triggered a loop back
        if state.get("reflection_loop_triggered", False):
            execution_path = state.get("execution_path", "simple")

            if execution_path == "simple":
                logger.info(
                    "[Reflection Router] Looping back to simple domain agent for improvement"
                )
                return "domain_agent_simple"
            else:
                logger.info(
                    "[Reflection Router] Looping back to complex domain agent for improvement"
                )
                return "domain_agent_complex"
        else:
            logger.info("[Reflection Router] Quality satisfactory, proceeding to END")
            return "END"

    # Enhanced domain agent nodes that handle improvement guidance
    async def domain_agent_simple_node_with_improvement(
        state: SpecializedCoachState,
    ) -> SpecializedCoachState:
        """Domain Agent - Simple path execution with reflection improvement support."""

        # Check if this is an improvement iteration
        improvement_guidance = state.get("improvement_guidance")
        if improvement_guidance:
            logger.info(
                "[Domain Agent Simple] Processing improvement guidance from reflection"
            )

            # Enhance the user query with improvement guidance
            enhanced_state = {
                **state,
                "execution_path": "simple",
                "user_query_with_guidance": f"{state.get('user_query', '')}\n\nREFLECTION FEEDBACK:\n{improvement_guidance}",
                "is_improvement_iteration": True,
                "reflection_loop_triggered": False,  # Reset flag
            }
            return await domain_agent_node_with_improvement(enhanced_state)
        else:
            return await domain_agent_node({**state, "execution_path": "simple"})

    async def domain_agent_complex_node_with_improvement(
        state: SpecializedCoachState,
    ) -> SpecializedCoachState:
        """Domain Agent - Complex path execution with reflection improvement support."""

        # Check if this is an improvement iteration
        improvement_guidance = state.get("improvement_guidance")
        if improvement_guidance:
            logger.info(
                "[Domain Agent Complex] Processing improvement guidance from reflection"
            )

            # Enhance the state with improvement guidance
            enhanced_state = {
                **state,
                "execution_path": "complex",
                "user_query_with_guidance": f"{state.get('user_query', '')}\n\nREFLECTION FEEDBACK:\n{improvement_guidance}",
                "is_improvement_iteration": True,
                "reflection_loop_triggered": False,  # Reset flag
            }
            return await domain_agent_node_with_improvement(enhanced_state)
        else:
            return await domain_agent_node({**state, "execution_path": "complex"})

    async def domain_agent_node_with_improvement(
        state: SpecializedCoachState,
    ) -> SpecializedCoachState:
        """Enhanced Domain Agent that can process improvement guidance from reflection."""

        runtime_coach_domain = (
            state.get("coach_domain") or config.get("coach_domain") or coach_domain
        )

        is_improvement_iteration = state.get("is_improvement_iteration", False)
        improvement_guidance = state.get("improvement_guidance", "")

        if is_improvement_iteration:
            logger.info(
                f"[Domain Agent] Processing improvement iteration for {runtime_coach_domain}"
            )

            # Use the original user query but incorporate reflection guidance
            user_query = state.get("user_query", "")
            original_response = state.get("domain_agent_response", "")

            # Create improvement prompt
            improvement_prompt = f"""
You are a {runtime_coach_domain} coach improving your previous response based on quality reflection feedback.

Original Query: "{user_query}"
Your Previous Response: "{original_response}"

{improvement_guidance}

Provide an improved response that addresses the specific feedback while maintaining your coaching expertise and approach.
Focus on the identified improvements without losing the core value of your original response.
"""

            try:
                rewoo_executor_runtime = SpecializedCoachReWOOExecutor(
                    runtime_coach_domain
                )
                response_obj = await rewoo_executor_runtime.llm.ainvoke(
                    [HumanMessage(content=improvement_prompt)]
                )
                improved_response = response_obj.content

                return {
                    **state,
                    "domain_agent_response": improved_response,
                    "previous_response": original_response,
                    "improvement_applied": True,
                    "messages": add_messages(
                        state.get("messages", []),
                        [
                            AIMessage(
                                content=f"🔄 {runtime_coach_domain.title()} Coach (Improved): Revised response based on reflection feedback",
                                name=f"{runtime_coach_domain}_domain_agent",
                            ),
                            AIMessage(
                                content=improved_response,
                                name=f"{runtime_coach_domain}_coach",
                            ),
                        ],
                    ),
                }

            except Exception as e:
                logger.error(f"[Domain Agent] Improvement error: {e}")
                # Return original response if improvement fails
                return state

        else:
            # Normal execution - call the original domain agent
            return await domain_agent_node(state)

    # Build the graph
    builder = StateGraph(SpecializedCoachState)

    # Add nodes
    builder.add_node("complexity_assessment", complexity_assessment_node)
    builder.add_node("domain_agent_simple", domain_agent_simple_node_with_improvement)
    builder.add_node("planner", planner_node)
    builder.add_node("executor", executor_node)
    builder.add_node("domain_agent_complex", domain_agent_complex_node_with_improvement)
    builder.add_node("reflection", reflection_node)

    # Add edges
    builder.add_edge(START, "complexity_assessment")

    # Conditional routing based on complexity
    builder.add_conditional_edges(
        "complexity_assessment",
        complexity_router,
        {"domain_agent_simple": "domain_agent_simple", "planner": "planner"},
    )

    # Simple path: domain_agent_simple → reflection → [loop back OR end]
    builder.add_edge("domain_agent_simple", "reflection")

    # Complex path: planner → executor → domain_agent_complex → reflection → [loop back OR end]
    builder.add_edge("planner", "executor")
    builder.add_edge("executor", "domain_agent_complex")
    builder.add_edge("domain_agent_complex", "reflection")

    # Conditional routing from reflection - can loop back for improvement or end
    builder.add_conditional_edges(
        "reflection",
        reflection_router,
        {
            "domain_agent_simple": "domain_agent_simple",
            "domain_agent_complex": "domain_agent_complex",
            "END": END,
        },
    )

    # Compile with memory if enabled
    if config.get("enable_memory", True):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info(f"SpecializedCoachGraph compiled successfully for {coach_domain}")
    return graph


# Factory function for all domains
async def _experimental_create_all_specialized_coach_graphs(
    config: Optional[Dict[str, Any]] = None,
) -> Dict[str, CompiledStateGraph]:
    """Create SpecializedCoachGraphs for all coaching domains."""

    domains = ["strength", "nutrition", "cardio", "cycling", "recovery", "mental"]
    graphs = {}

    for domain in domains:
        try:
            graphs[domain] = await create_specialized_coach_graph(domain, config)
            logger.info(f"✅ Created SpecializedCoachGraph for {domain}")
        except Exception as e:
            logger.error(f"❌ Failed to create SpecializedCoachGraph for {domain}: {e}")

    return graphs


# LangGraph Studio wrapper functions (these are called from langgraph.json)
async def _experimental_create_specialized_coach_graph_from_config(
    config: Dict[str, Any],
) -> CompiledStateGraph:
    """
    Wrapper function for LangGraph Studio integration.

    Extracts coach_domain from config and creates the appropriate specialized coach graph.
    """
    # Debug logging to see what configuration is received
    logger.info(f"Received config in wrapper: {config}")

    coach_domain = config.get("coach_domain", "strength")
    logger.info(
        f"Creating SpecializedCoachGraph from config for domain: {coach_domain}"
    )

    # Ensure the coach_domain is explicitly passed in the config
    enhanced_config = {**config, "coach_domain": coach_domain}
    logger.info(f"Enhanced config with coach_domain: {enhanced_config}")

    # Create and return the graph directly without any method modifications
    graph = await create_specialized_coach_graph(coach_domain, enhanced_config)

    logger.info(f"Created SpecializedCoachGraph for domain: {coach_domain}")
    return graph
