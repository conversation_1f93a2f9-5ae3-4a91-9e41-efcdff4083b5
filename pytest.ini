[tool:pytest]
minversion = 6.0
addopts = 
    -ra 
    -q 
    --strict-markers
    --disable-warnings
    --asyncio-mode=auto
testpaths = 
    mcp_servers
    tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    mcp: marks tests as MCP-specific tests

# Environment variables for testing
env =
    PYTHONPATH = .
    MCP_TEST_MODE = true 