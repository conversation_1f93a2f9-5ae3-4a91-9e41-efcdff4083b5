"""
Tool Contract Manager

Manages and enforces contracts between agents and tools, ensuring proper
schema validation and access control.
"""

import logging
from typing import Any, Dict, List, Optional, Type

from pydantic import BaseModel, ValidationError

from ..schemas.nutrition_schemas import NUTRITION_TOOL_CONTRACTS
from ..schemas.strength_schemas import STRENGTH_TOOL_CONTRACTS

# from ..schemas.cardio_schemas import CARDIO_TOOL_CONTRACTS
# from ..schemas.recovery_schemas import RECOVERY_TOOL_CONTRACTS
# from ..schemas.mental_schemas import MENTAL_TOOL_CONTRACTS
from ..schemas.recovery_schemas import RECOVERY_TOOL_CONTRACTS
from ..schemas.mental_schemas import MENTAL_TOOL_CONTRACTS

# from ..schemas.cardio_schemas import CARDIO_TOOL_CONTRACTS
# from ..schemas.recovery_schemas import RECOVERY_TOOL_CONTRACTS
# from ..schemas.mental_schemas import MENTAL_TOOL_CONTRACTS

logger = logging.getLogger(__name__)


class ToolContractViolation(Exception):
    """Raised when a tool-agent contract is violated."""

    pass


class ToolContractManager:
    """
    Manages tool contracts and enforces validation between agents and tools.

    Key features:
    - Schema validation for all tool inputs/outputs
    - Domain-specific tool access control
    - Automatic validation of tool calls
    - Permission-based tool access
    """

    def __init__(self):
        self._contracts: Dict[str, Dict[str, Any]] = {}
        self._domain_permissions: Dict[str, List[str]] = {}
        self._initialize_contracts()

    def _initialize_contracts(self):
        """Initialize all tool contracts from domain schemas."""
        # Load strength domain contracts
        for tool_name, contract in STRENGTH_TOOL_CONTRACTS.items():
            full_tool_name = f"strength.{tool_name}"
            self._contracts[full_tool_name] = {
                "contract": contract,
                "domain": "strength_training",
                "input_schema": contract.input_schema,
                "output_schema": contract.output_schema,
                "required_permissions": contract.required_permissions,
            }

        # Load nutrition domain contracts
        for tool_name, contract in NUTRITION_TOOL_CONTRACTS.items():
            full_tool_name = f"nutrition.{tool_name}"
            self._contracts[full_tool_name] = {
                "contract": contract,
                "domain": "nutrition",
                "input_schema": contract.input_schema,
                "output_schema": contract.output_schema,
                "required_permissions": contract.required_permissions,
            }

        # Load cardio domain contracts
        # for tool_name, contract in CARDIO_TOOL_CONTRACTS.items():
        #     full_tool_name = f"cardio.{tool_name}"
        #     self._contracts[full_tool_name] = {
        #         "contract": contract,
        #         "domain": "cardiovascular_training",
        #         "input_schema": contract.input_schema,
        #         "output_schema": contract.output_schema,
        #         "required_permissions": contract.required_permissions,
        #     }

        # Load recovery domain contracts
        # for tool_name, contract in RECOVERY_TOOL_CONTRACTS.items():
        #     full_tool_name = f"recovery.{tool_name}"
        #     self._contracts[full_tool_name] = {
        #         "contract": contract,
        #         "domain": "recovery_regeneration",
        #         "input_schema": contract.input_schema,
        #         "output_schema": contract.output_schema,
        #         "required_permissions": contract.required_permissions,
        #     }

        # Load mental domain contracts
        # for tool_name, contract in MENTAL_TOOL_CONTRACTS.items():
        #     full_tool_name = f"mental.{tool_name}"
        #     self._contracts[full_tool_name] = {
        #         "contract": contract,
        #         "domain": "mental_training",
        #         "input_schema": contract.input_schema,
        #         "output_schema": contract.output_schema,
        #         "required_permissions": contract.required_permissions,
        #     }

        # Initialize domain permissions
        self._domain_permissions = {
            "strength_training": [
                "read_exercise_database",
                "search_exercises",
                "generate_progressions",
                "generate_programs",
                "access_user_profile",
            ],
            "nutrition": [
                "calculate_calories",
                "calculate_macros",
                "plan_meals",
                "access_food_database",
                "assess_nutrition",
                "provide_recommendations",
                "access_user_profile",
            ],
            # "cardiovascular_training": [
            #     "plan_routes",
            #     "calculate_zones",
            #     "assess_cardio",
            #     "access_maps",
            #     "access_user_profile",
            # ],
            # "recovery_regeneration": [
            #     "analyze_sleep",
            #     "track_recovery",
            #     "recommend_protocols",
            #     "access_user_profile",
            # ],
            # "mental_training": [
            #     "track_habits",
            #     "provide_motivation",
            #     "assess_mental_state",
            #     "access_user_profile",
            # ],
        }

        logger.info(
            f"✅ Initialized {len(self._contracts)} tool contracts across {len(self._domain_permissions)} domains"
        )

    def validate_agent_tool_access(
        self, agent_domain: str, tool_name: str, agent_permissions: List[str]
    ) -> bool:
        """
        Validate that an agent can access a specific tool.

        Args:
            agent_domain: Agent's primary domain
            tool_name: Name of the tool to access
            agent_permissions: Agent's current permissions

        Returns:
            bool: True if access is allowed

        Raises:
            ToolContractViolation: If access is denied
        """
        if tool_name not in self._contracts:
            raise ToolContractViolation(
                f"Tool '{tool_name}' not found in contracts registry"
            )

        contract_info = self._contracts[tool_name]
        tool_domain = contract_info["domain"]
        required_permissions = contract_info["required_permissions"]

        # Check domain match
        if agent_domain != tool_domain:
            raise ToolContractViolation(
                f"Agent domain '{agent_domain}' cannot access tool from domain '{tool_domain}'"
            )

        # Check permissions
        missing_permissions = set(required_permissions) - set(agent_permissions)
        if missing_permissions:
            raise ToolContractViolation(
                f"Agent missing required permissions: {list(missing_permissions)}"
            )

        return True

    def validate_tool_input(self, tool_name: str, input_data: Any) -> Any:
        """
        Validate tool input against contract schema.

        Args:
            tool_name: Name of the tool
            input_data: Input data to validate

        Returns:
            Validated input data

        Raises:
            ToolContractViolation: If validation fails
        """
        if tool_name not in self._contracts:
            raise ToolContractViolation(
                f"Tool '{tool_name}' not found in contracts registry"
            )

        contract_info = self._contracts[tool_name]
        input_schema = contract_info["input_schema"]

        try:
            # Validate using Pydantic schema
            if isinstance(input_data, dict):
                validated_input = input_schema(**input_data)
            else:
                validated_input = input_schema(input_data)

            logger.debug(f"✅ Input validation passed for tool '{tool_name}'")
            return validated_input

        except ValidationError as e:
            logger.error(f"❌ Input validation failed for tool '{tool_name}': {e}")
            raise ToolContractViolation(
                f"Input validation failed for '{tool_name}': {str(e)}"
            )

    def validate_tool_output(self, tool_name: str, output_data: Any) -> Any:
        """
        Validate tool output against contract schema.

        Args:
            tool_name: Name of the tool
            output_data: Output data to validate

        Returns:
            Validated output data

        Raises:
            ToolContractViolation: If validation fails
        """
        if tool_name not in self._contracts:
            raise ToolContractViolation(
                f"Tool '{tool_name}' not found in contracts registry"
            )

        contract_info = self._contracts[tool_name]
        output_schema = contract_info["output_schema"]

        try:
            # Validate using Pydantic schema
            if isinstance(output_data, dict):
                validated_output = output_schema(**output_data)
            else:
                validated_output = output_schema(output_data)

            logger.debug(f"✅ Output validation passed for tool '{tool_name}'")
            return validated_output

        except ValidationError as e:
            logger.error(f"❌ Output validation failed for tool '{tool_name}': {e}")
            raise ToolContractViolation(
                f"Output validation failed for '{tool_name}': {str(e)}"
            )

    def get_agent_allowed_tools(
        self, agent_domain: str, agent_permissions: List[str]
    ) -> List[str]:
        """
        Get list of tools an agent is allowed to access.

        Args:
            agent_domain: Agent's primary domain
            agent_permissions: Agent's current permissions

        Returns:
            List of allowed tool names
        """
        allowed_tools = []

        for tool_name, contract_info in self._contracts.items():
            tool_domain = contract_info["domain"]
            required_permissions = contract_info["required_permissions"]

            # Check domain match
            if agent_domain != tool_domain:
                continue

            # Check permissions
            if set(required_permissions).issubset(set(agent_permissions)):
                allowed_tools.append(tool_name)

        return allowed_tools

    def get_tool_contract_info(self, tool_name: str) -> Dict[str, Any]:
        """Get contract information for a specific tool."""
        if tool_name not in self._contracts:
            raise ToolContractViolation(
                f"Tool '{tool_name}' not found in contracts registry"
            )

        return self._contracts[tool_name].copy()

    def get_domain_tools(self, domain: str) -> List[str]:
        """Get all tools available for a specific domain."""
        domain_tools = []

        for tool_name, contract_info in self._contracts.items():
            if contract_info["domain"] == domain:
                domain_tools.append(tool_name)

        return domain_tools

    def register_custom_tool_contract(
        self,
        tool_name: str,
        domain: str,
        input_schema: Type[BaseModel],
        output_schema: Type[BaseModel],
        required_permissions: List[str],
    ):
        """
        Register a custom tool contract.

        Args:
            tool_name: Name of the tool
            domain: Tool domain
            input_schema: Pydantic schema for input validation
            output_schema: Pydantic schema for output validation
            required_permissions: List of required permissions
        """
        self._contracts[tool_name] = {
            "domain": domain,
            "input_schema": input_schema,
            "output_schema": output_schema,
            "required_permissions": required_permissions,
        }

        logger.info(
            f"📝 Registered custom tool contract for '{tool_name}' in domain '{domain}'"
        )

    def get_contract_stats(self) -> Dict[str, Any]:
        """Get statistics about registered contracts."""
        domain_counts = {}
        total_permissions = set()

        for contract_info in self._contracts.values():
            domain = contract_info["domain"]
            permissions = contract_info["required_permissions"]

            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            total_permissions.update(permissions)

        return {
            "total_contracts": len(self._contracts),
            "domains": list(domain_counts.keys()),
            "tools_per_domain": domain_counts,
            "unique_permissions": len(total_permissions),
            "all_permissions": sorted(list(total_permissions)),
        }


# Global contract manager instance
_contract_manager: Optional[ToolContractManager] = None


def get_contract_manager() -> ToolContractManager:
    """Get the global tool contract manager instance."""
    global _contract_manager
    if _contract_manager is None:
        _contract_manager = ToolContractManager()
    return _contract_manager
