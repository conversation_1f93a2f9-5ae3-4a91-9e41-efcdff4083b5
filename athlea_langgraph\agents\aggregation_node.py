"""
Production-Ready Aggregation Node

Enhanced aggregation node with robust error handling, streaming support,
and production-ready HITL implementation based on LangGraph best practices.
Uses versioned prompts from the prompts folder.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, AsyncIterator, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langgraph.errors import GraphInterrupt
from langgraph.types import Command, interrupt

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState
from athlea_langgraph.states.optimized_state import OptimizedCoachingState
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class InterruptReason(Enum):
    """Reasons for interrupting the graph execution."""

    REVIEW_RESPONSE = "review_response"
    ERROR_RECOVERY = "error_recovery"
    CLARIFICATION_NEEDED = "clarification_needed"
    APPROVAL_REQUIRED = "approval_required"


@dataclass
class InterruptContext:
    """Context information for interrupts."""

    reason: InterruptReason
    message: str
    metadata: Dict[str, Any]
    retry_count: int = 0
    max_retries: int = 3


class AggregationError(Exception):
    """Custom exception for aggregation-specific errors."""

    pass


async def stream_synthesis_with_retry(
    llm,
    messages: List[BaseMessage],
    max_retries: int = 3,
    initial_delay: float = 1.0,
    backoff_factor: float = 2.0,
) -> AsyncIterator[str]:
    """
    Stream LLM synthesis with exponential backoff retry logic.

    Args:
        llm: The language model instance
        messages: Messages to send to the LLM
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry

    Yields:
        Chunks of synthesized content
    """
    delay = initial_delay
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            async for chunk in llm.astream(messages):
                if chunk.content:
                    yield chunk.content
            return  # Success, exit the retry loop

        except Exception as e:
            last_error = e
            logger.warning(f"Synthesis attempt {attempt + 1} failed: {str(e)}")

            if attempt < max_retries:
                # Add jitter to prevent thundering herd
                jittered_delay = delay * (0.5 + 0.5 * asyncio.get_event_loop().time())
                logger.info(f"Retrying in {jittered_delay:.2f} seconds...")
                await asyncio.sleep(jittered_delay)
                delay *= backoff_factor
            else:
                logger.error(
                    f"All synthesis attempts failed. Last error: {str(last_error)}"
                )
                raise AggregationError(
                    f"Failed to synthesize responses after {max_retries} attempts"
                ) from last_error


async def aggregation_node(
    state: OptimizedCoachingState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Production-ready aggregation node with enhanced error handling and multi-message streaming.

    NEW FEATURE: Multi-message streaming - yields individual specialist responses as separate
    messages followed by the Athlea synthesis, enabling Level 3 implementation where users
    see separate chat bubbles for each specialist coach.

    Features:
    - Multi-message streaming with individual coach responses
    - Robust error handling with retries
    - Real-time streaming of synthesis
    - Timeout protection
    - Better interrupt handling
    - Performance monitoring
    - Graceful degradation
    - Uses new Athlea aggregator prompt for structured synthesis
    """
    start_time = time.time()
    logger.info("🔄 AGGREGATION: Starting multi-message aggregation node")

    # COMPREHENSIVE STATE LOGGING
    logger.info("=" * 80)
    logger.info("🔍 AGGREGATION: COMPREHENSIVE STATE ANALYSIS")
    logger.info("=" * 80)

    # Log state type and structure
    state_type = type(state).__name__
    logger.info(f"📊 AGGREGATION: State type: {state_type}")

    # CRITICAL: Log the EXACT state object for debugging
    logger.info(f"📊 AGGREGATION: Raw state object: {state}")

    # Log state memory address for tracking
    logger.info(f"📊 AGGREGATION: State memory address: {id(state)}")

    if isinstance(state, dict):
        state_keys = list(state.keys())
        logger.info(f"📊 AGGREGATION: State keys ({len(state_keys)}): {state_keys}")

        # Log key fields in detail
        coach_responses = state.get("coach_responses", {})
        execution_steps = state.get("execution_steps", [])
        current_node = state.get("current_node", "")
        routing_decision = state.get("routing_decision", "")
        required_coaches = state.get("required_coaches", [])
        primary_coach = state.get("primary_coach", "")

        logger.info(f"🎯 AGGREGATION: coach_responses type: {type(coach_responses)}")
        logger.info(
            f"🎯 AGGREGATION: coach_responses memory address: {id(coach_responses)}"
        )
        logger.info(
            f"🎯 AGGREGATION: coach_responses keys: {list(coach_responses.keys()) if coach_responses else 'None'}"
        )
        logger.info(
            f"🎯 AGGREGATION: coach_responses count: {len(coach_responses) if coach_responses else 0}"
        )

        # CRITICAL: Log the EXACT content of coach_responses
        if coach_responses:
            logger.info("🎯 AGGREGATION: DETAILED coach_responses content:")
            for coach_name, response in coach_responses.items():
                response_preview = (
                    response[:100] + "..." if len(response) > 100 else response
                )
                logger.info(
                    f"  📝 {coach_name} ({len(response)} chars): {response_preview}"
                )
        else:
            logger.warning("🎯 AGGREGATION: coach_responses is EMPTY or None!")

        logger.info(f"🔄 AGGREGATION: execution_steps: {execution_steps}")
        logger.info(f"🎯 AGGREGATION: current_node: {current_node}")
        logger.info(f"🎯 AGGREGATION: routing_decision: {routing_decision}")
        logger.info(f"🎯 AGGREGATION: required_coaches: {required_coaches}")
        logger.info(f"🎯 AGGREGATION: primary_coach: {primary_coach}")

        # CRITICAL: Check for any other response fields
        response_fields = [
            "final_response",
            "specialist_response",
            "aggregated_response",
        ]
        for field in response_fields:
            if field in state and state[field]:
                field_preview = (
                    state[field][:100] + "..."
                    if len(state[field]) > 100
                    else state[field]
                )
                logger.info(f"📄 AGGREGATION: Found {field}: {field_preview}")

        # CRITICAL: Log ALL state fields to see what's actually available
        logger.info("🔍 AGGREGATION: ALL STATE FIELDS:")
        for key, value in state.items():
            if isinstance(value, str) and len(value) > 50:
                value_preview = value[:50] + "..."
            elif isinstance(value, (list, dict)):
                value_preview = f"{type(value).__name__}({len(value)})"
            else:
                value_preview = str(value)
            logger.info(f"  🔑 {key}: {value_preview}")

    else:
        # Handle non-dict state objects
        logger.info(f"📊 AGGREGATION: State is not dict, type: {type(state)}")
        if hasattr(state, "__dict__"):
            state_attrs = list(state.__dict__.keys())
            logger.info(f"📊 AGGREGATION: State attributes: {state_attrs}")

            coach_responses = getattr(state, "coach_responses", {})
            logger.info(
                f"🎯 AGGREGATION: coach_responses from getattr: {coach_responses}"
            )

    logger.info("=" * 80)

    # CRITICAL: Add state reducer debugging
    logger.info("🔧 AGGREGATION: STATE REDUCER DEBUGGING")
    logger.info("=" * 80)

    # Check if this is an OptimizedCoachingState
    from athlea_langgraph.states.optimized_state import OptimizedCoachingState

    if isinstance(state, dict):
        logger.info("🔧 AGGREGATION: State is dict - checking reducer behavior")

        # Simulate what the reducer should have done
        from athlea_langgraph.states.optimized_state import (
            optimized_coach_responses_reducer,
        )

        # Test the reducer with empty left and our coach_responses
        test_left = {}
        test_right = state.get("coach_responses", {})

        logger.info(
            f"🔧 AGGREGATION: Testing reducer with left={test_left}, right={test_right}"
        )
        reducer_result = optimized_coach_responses_reducer(test_left, test_right)
        logger.info(f"🔧 AGGREGATION: Reducer result: {reducer_result}")

        # Check if the reducer is working correctly
        if reducer_result != test_right:
            logger.error("🔧 AGGREGATION: REDUCER MALFUNCTION DETECTED!")
            logger.error(f"  Expected: {test_right}")
            logger.error(f"  Got: {reducer_result}")

    logger.info("=" * 80)

    try:
        # Collect specialist responses with validation
        specialist_responses = collect_specialist_responses(state)

        logger.info(
            f"✅ AGGREGATION: Collected {len(specialist_responses)} specialist responses"
        )
        for i, resp in enumerate(specialist_responses):
            logger.info(f"  {i+1}. {resp['coach']}: {len(resp['response'])} chars")

        if not specialist_responses:
            logger.warning("❌ AGGREGATION: No specialist responses to aggregate")
            return handle_no_responses(state)

        # Log performance metrics
        logger.info(
            f"📊 AGGREGATION: Processing {len(specialist_responses)} specialist responses"
        )

        # Load the new Athlea aggregator prompt for structured synthesis
        user_query = state.get("user_query", "")
        reasoning_output = state.get("reasoning_output", "")

        # Format coach responses for the new structured prompt
        coach_responses_text = "\n\n".join(
            [
                f"**{resp['coach']}:**\n{resp['response']}"
                for resp in specialist_responses
            ]
        )

        logger.info(
            f"📝 AGGREGATION: Formatted coach responses ({len(coach_responses_text)} chars)"
        )

        # Initialize LLM with streaming
        llm = create_azure_chat_openai(temperature=0.7, streaming=True)

        # Try to load the new Athlea aggregator prompt
        try:
            with open("athlea_langgraph/prompts/athlea_aggregator_prompt.txt", "r") as f:
                athlea_prompt = f.read()

            # Create context for the new structured prompt
            context_prompt = f"""User Query: {user_query}

Specialist Coach Responses:
{coach_responses_text}

Please synthesize these responses using the required format."""

            messages = [
                SystemMessage(content=athlea_prompt),
                HumanMessage(content=context_prompt),
            ]
            logger.info("✅ AGGREGATION: Using new Athlea aggregator prompt for structured synthesis")

        except Exception as e:
            logger.warning(f"⚠️ AGGREGATION: Failed to load Athlea aggregator prompt: {e}, using fallback")

            # Fallback to the original head coach prompt
            fallback_prompt = create_fallback_synthesis_prompt(
                user_query, reasoning_output, specialist_responses
            )
            messages = [
                SystemMessage(
                    content="You are Athlea, the user's personal AI Head Coach synthesizing advice from specialist coaches."
                ),
                HumanMessage(content=fallback_prompt),
            ]

        # Stream synthesis with timeout protection
        logger.info("🔄 AGGREGATION: Starting synthesis streaming...")
        synthesized_content = ""

        try:
            # Set a reasonable timeout for synthesis
            async with asyncio.timeout(30):  # 30 second timeout
                async for chunk in stream_synthesis_with_retry(llm, messages):
                    synthesized_content += chunk
                    # Could emit streaming events here if needed

        except asyncio.TimeoutError:
            logger.error("⏰ AGGREGATION: Synthesis timed out after 30 seconds")
            synthesized_content = create_timeout_response(specialist_responses)

        synthesis_time = time.time() - start_time
        logger.info(
            f"✅ AGGREGATION: Synthesis complete: {len(synthesized_content)} chars in {synthesis_time:.2f}s"
        )

        # MULTI-MESSAGE STREAMING: Create individual specialist messages first
        messages_to_add = []

        # Create individual specialist coach messages
        coach_name_mapping = {
            "strength": "Strength Coach",
            "strength_coach": "Strength Coach",
            "cardio": "Cardio Coach",
            "cardio_coach": "Cardio Coach",
            "nutrition": "Nutrition Coach",
            "nutrition_coach": "Nutrition Coach",
            "recovery": "Recovery Coach",
            "recovery_coach": "Recovery Coach",
            "mental": "Mental Coach",
            "mental_coach": "Mental Coach"
        }

        for response_data in specialist_responses:
            coach_key = response_data.get("field", response_data["coach"].lower().replace(" ", "_"))
            display_name = coach_name_mapping.get(coach_key, response_data["coach"])

            specialist_message = AIMessage(
                content=response_data["response"],
                name=display_name,  # Use display name for frontend
                additional_kwargs={
                    "coach_type": coach_key,
                    "is_specialist_response": True,
                    "message_order": "specialist"
                },
            )
            messages_to_add.append(specialist_message)
            logger.info(f"✅ Created specialist message for {display_name}")

        # Create final Athlea synthesis message
        aggregated_message = AIMessage(
            content=synthesized_content,
            name="Athlea",  # Changed from "head_coach" to "Athlea" for consistency
            additional_kwargs={
                "synthesis_time": synthesis_time,
                "specialist_count": len(specialist_responses),
                "is_athlea_synthesis": True,
                "message_order": "final"
            },
        )
        messages_to_add.append(aggregated_message)

        logger.info(f"✅ Created Athlea synthesis message ({len(synthesized_content)} chars)")
        logger.info(f"🎯 MULTI-MESSAGE: Total messages to stream: {len(messages_to_add)}")

        # Prepare state update with all messages
        state_update = {
            "messages": messages_to_add,  # All specialist messages + Athlea synthesis
            "aggregated_response": synthesized_content,  # Maintain backward compatibility
            "final_response": synthesized_content,  # Maintain backward compatibility
            "aggregation_metadata": {
                "timestamp": time.time(),
                "duration": synthesis_time,
                "specialist_count": len(specialist_responses),
                "response_length": len(synthesized_content),
                "multi_message_count": len(messages_to_add),
                "streaming_mode": "multi_message"
            },
        }

        # Get enable_human_feedback from config first, then state as fallback
        enable_human_feedback = True  # Default value

        # Check config first (this is the primary source of truth)
        if config and "enable_human_feedback" in config:
            enable_human_feedback = config["enable_human_feedback"]
        # Fallback to state if config not available
        elif isinstance(state, dict):
            enable_human_feedback = state.get("enable_human_feedback", True)
        else:
            enable_human_feedback = getattr(state, "enable_human_feedback", True)

        logger.info(
            f"Human feedback setting: {enable_human_feedback} (source: {'config' if config and 'enable_human_feedback' in config else 'state/default'})"
        )

        # Create interrupt context for human review ONLY if human feedback is enabled
        if enable_human_feedback:
            interrupt_context = InterruptContext(
                reason=InterruptReason.REVIEW_RESPONSE,
                message=synthesized_content,
                metadata={
                    "specialists_consulted": [
                        resp["coach"] for resp in specialist_responses
                    ],
                    "synthesis_duration": synthesis_time,
                },
            )

            human_feedback = await handle_interrupt_with_recovery(
                interrupt_context, config
            )

            # Process human feedback
            if human_feedback:
                logger.info(f"Processing human feedback: {human_feedback[:100]}...")
                state_update["messages"].append(HumanMessage(content=human_feedback))
                state_update["human_feedback"] = human_feedback
        else:
            logger.info(
                "Skipping human feedback interrupt - disabled for streaming mode"
            )

        return state_update

    except GraphInterrupt:
        # GraphInterrupt is normal behavior for human-in-the-loop functionality
        # Let it bubble up to LangGraph for proper handling
        logger.info(
            "GraphInterrupt raised for human feedback - this is normal behavior"
        )
        raise

    except AggregationError as e:
        logger.error(f"Aggregation error: {str(e)}")
        return handle_aggregation_error(state, str(e))

    except Exception as e:
        logger.error(f"Unexpected error in aggregation: {str(e)}", exc_info=True)
        return handle_unexpected_error(state, str(e))


def collect_specialist_responses(state: OptimizedCoachingState) -> List[Dict[str, str]]:
    """
    Collect and validate specialist responses from state.

    Returns:
        List of specialist responses with coach names and content
    """
    try:
        if isinstance(state, dict):
            coach_responses = state.get("coach_responses", {})
            messages = state.get("messages", [])
        else:
            coach_responses = getattr(state, "coach_responses", {})
            messages = getattr(state, "messages", [])

        if not coach_responses:
            logger.warning(
                "⚠️ AGGREGATION: No coach responses found directly in state, checking messages..."
            )
            if not messages:
                logger.error("❌ AGGREGATION: No messages found in state either.")
                return {}

            # Fallback to extract from messages
            for message in reversed(messages):
                if hasattr(message, "additional_kwargs") and message.additional_kwargs:
                    if "coach_responses" in message.additional_kwargs:
                        coach_responses = message.additional_kwargs["coach_responses"]
                        logger.info(
                            f"✅ AGGREGATION: Found coach responses in message kwargs: {list(coach_responses.keys())}"
                        )
                        break
    except Exception as e:
        logger.error(f"❌ AGGREGATION: Error collecting specialist responses: {e}")
        return {}

    # DEBUG: Print ALL state keys to see what's available
    state_keys = list(state.keys()) if isinstance(state, dict) else []
    logger.info(f"🔍 AGGREGATION DEBUG: Available state keys: {state_keys}")

    # DEBUG: Print specific fields we're looking for
    coach_responses = state.get("coach_responses", {})
    final_response = state.get("final_response", "")
    specialist_response = state.get("specialist_response", "")
    current_node = state.get("current_node", "")
    execution_steps = state.get("execution_steps", [])

    # WORKAROUND: Extract coach responses from execution_steps if coach_responses is empty
    if not coach_responses or len(coach_responses) == 0:
        logger.warning(
            "🔧 WORKAROUND: coach_responses empty, extracting from execution_steps"
        )
        # Start with a fresh dictionary to collect extracted responses, but we'll merge
        extracted_coach_responses: Dict[str, str] = {}
        for step in execution_steps:
            if step.startswith("COACH_RESPONSE:"):
                try:
                    # Format: "COACH_RESPONSE:coach_name:base64_encoded_response"
                    parts = step.split(":", 2)  # Split into exactly 3 parts
                    if len(parts) == 3:
                        coach_name = parts[1]
                        encoded_response = parts[2]

                        # Decode the base64 encoded response
                        import base64

                        response_content = base64.b64decode(
                            encoded_response.encode("ascii")
                        ).decode("utf-8")

                        extracted_coach_responses[coach_name] = response_content
                        logger.info(
                            f"🔧 WORKAROUND: Extracted response for {coach_name}: {response_content[:50]}..."
                        )
                except Exception as e:
                    logger.warning(f"🔧 WORKAROUND: Failed to parse step '{step}': {e}")

        # Merge extracted responses with any existing ones (state may have been updated elsewhere)
        coach_responses = {**coach_responses, **extracted_coach_responses}

        logger.info(
            f"🔧 WORKAROUND: Extracted {len(extracted_coach_responses)} responses from execution_steps"
        )
        logger.info(
            f"🔧 WORKAROUND: Coach names found: {list(extracted_coach_responses.keys())}"
        )

        # CRITICAL FIX: Extract from base64 encoded backup in execution_steps
        if not coach_responses and execution_steps:
            logger.info(
                "🔧 CRITICAL_FIX: Attempting to extract from base64 backup encoding"
            )
            import base64

            for step in execution_steps:
                if step.startswith("COACH_RESPONSE:"):
                    try:
                        # Parse: COACH_RESPONSE:coach_name:base64_encoded_response
                        parts = step.split(":", 2)
                        if len(parts) == 3:
                            coach_name = parts[1]
                            encoded_response = parts[2]

                            # Decode the response
                            decoded_response = base64.b64decode(
                                encoded_response.encode("ascii")
                            ).decode("utf-8")
                            coach_responses[coach_name] = decoded_response

                            logger.info(
                                f"🔧 CRITICAL_FIX: Recovered {coach_name} response: {len(decoded_response)} chars"
                            )

                    except Exception as e:
                        logger.error(f"🔧 CRITICAL_FIX: Failed to decode {step}: {e}")

            logger.info(
                f"🔧 CRITICAL_FIX: Recovered {len(coach_responses)} responses from backup encoding"
            )
            logger.info(
                f"🔧 CRITICAL_FIX: Recovered coaches: {list(coach_responses.keys())}"
            )

    logger.info(f"🔍 AGGREGATION DEBUG: coach_responses: {coach_responses}")
    logger.info(f"🔍 AGGREGATION DEBUG: coach_responses type: {type(coach_responses)}")
    logger.info(
        f"🔍 AGGREGATION DEBUG: final_response: {final_response[:50] + '...' if final_response else ''}"
    )
    logger.info(
        f"🔍 AGGREGATION DEBUG: specialist_response: {specialist_response[:50] + '...' if specialist_response else ''}"
    )
    logger.info(f"🔍 AGGREGATION DEBUG: current_node: {current_node}")
    logger.info(f"🔍 AGGREGATION DEBUG: execution_steps count: {len(execution_steps)}")
    logger.info(
        f"🔍 AGGREGATION DEBUG: Found {len([s for s in execution_steps if 'coach' in s.lower()])} coach response steps"
    )
    logger.info(
        f"🔍 AGGREGATION DEBUG: Found data in messages: {[str(m)[:100] + '...' for m in messages]}"
    )

    # Collect specialist responses
    specialist_responses = []
    logger.info(
        f"Collecting specialist responses from coach_responses: {list(coach_responses.keys())}"
    )

    for coach_name, response in coach_responses.items():
        if response and response.strip():
            specialist_responses.append(
                {
                    "coach": coach_name,
                    "response": response,
                    "metadata": {"source": "coach_responses"},
                }
            )
            logger.info(f"✅ Added {coach_name} response: {len(response)} chars")

    # Check legacy fields if no responses found
    if not specialist_responses:
        logger.warning("No responses in coach_responses, checking legacy fields...")

        # Check for individual coach response fields
        legacy_fields = [
            "strength_response",
            "cardio_response",
            "nutrition_response",
            "recovery_response",
            "mental_response",
        ]
        for field in legacy_fields:
            if field in state and state[field]:
                coach_name = field.replace("_response", "")
                specialist_responses.append(
                    {
                        "coach": coach_name,
                        "response": state[field],
                        "metadata": {"source": "legacy_field"},
                    }
                )
                logger.info(
                    f"✅ Found legacy {coach_name} response: {len(state[field])} chars"
                )

    logger.info(f"Total responses collected: {len(specialist_responses)}")
    logger.info(
        f"✅ AGGREGATION: Collected {len(specialist_responses)} specialist responses"
    )

    # Show execution steps for debugging
    coach_response_steps = [
        step for step in execution_steps if step.startswith("COACH_RESPONSE:")
    ]
    logger.info(
        f"🔍 AGGREGATION DEBUG: Found {len(coach_response_steps)} coach response steps"
    )
    for step in coach_response_steps[:3]:  # Show first 3 for debugging
        logger.info(f"🔍 AGGREGATION DEBUG: Step: {step[:100]}...")

    # DEBUG: Check if specialist_response contains data
    if specialist_response:
        logger.info(
            f"🔍 AGGREGATION DEBUG: FOUND specialist_response data: {len(specialist_response)} chars"
        )

    # DEBUG: Print raw state content for all message fields
    if "messages" in state:
        messages = state["messages"]
        logger.info(
            f"🔍 AGGREGATION DEBUG: Found data in messages: {[str(msg)[:100] + '...' for msg in messages[-3:]]}"
        )

    # CRITICAL FIX: Check for any field containing response data
    response_fields = [
        "final_response",
        "specialist_response",
        "aggregated_response",
        "clarification_output",
    ]
    for field in response_fields:
        if field in state and state[field]:
            logger.info(
                f"🔍 AGGREGATION DEBUG: Found response in {field}: {state[field][:100]}"
            )

    # Check all state fields for potential response data
    for key, value in state.items():
        if (
            isinstance(value, str) and len(value) > 100 and "Used " in value
        ):  # Looks like tool output
            logger.info(
                f"🔍 AGGREGATION DEBUG: Found potential tool output in {key}: {value[:100]}..."
            )

    # CRITICAL FIX: At this point coach_responses contains all responses gathered either directly from state
    # or extracted from execution_steps. DO NOT overwrite with state.get() - that discards extracted data!

    # Map internal coach names to display names
    coach_display_names = {
        "strength_coach": "Strength Coach",
        "running_coach": "Running Coach",
        "cardio_coach": "Cardio Coach",
        "cycling_coach": "Cycling Coach",
        "nutrition_coach": "Nutrition Coach",
        "recovery_coach": "Recovery Coach",
        "mental_coach": "Mental Coach",
        "clarification": "Head Coach",  # Special case for clarification
    }

    responses = []

    # Collect responses from coach_responses dictionary
    for coach_name, response in coach_responses.items():
        if response and isinstance(response, str) and response.strip():
            display_name = coach_display_names.get(
                coach_name, coach_name.replace("_", " ").title()
            )
            responses.append(
                {
                    "coach": display_name,
                    "response": response.strip(),
                    "field": coach_name,
                }
            )
            logger.info(f"Found response from {display_name}: {len(response)} chars")
        elif response:
            logger.warning(
                f"Invalid response format for {coach_name}: {type(response)}"
            )

    # Fallback: Check for old-style individual fields if coach_responses is empty
    if not responses:
        logger.warning("No responses in coach_responses, checking legacy fields...")
        specialist_fields = [
            ("strength_response", "Strength Coach"),
            ("running_response", "Running Coach"),
            ("cardio_response", "Cardio Coach"),
            ("cycling_response", "Cycling Coach"),
            ("nutrition_response", "Nutrition Coach"),
            ("recovery_response", "Recovery Coach"),
            ("mental_response", "Mental Coach"),
        ]

        for field, coach_name in specialist_fields:
            response = state.get(field)
            if response and isinstance(response, str) and response.strip():
                responses.append(
                    {"coach": coach_name, "response": response.strip(), "field": field}
                )
                logger.info(
                    f"Found legacy response from {coach_name}: {len(response)} chars"
                )

    logger.info(f"Total responses collected: {len(responses)}")
    return responses


def create_fallback_synthesis_prompt(
    user_query: str, reasoning: str, specialist_responses: List[Dict[str, str]]
) -> str:
    """Create a fallback synthesis prompt when versioned prompt loading fails."""
    responses_text = "\n\n".join(
        [f"**{resp['coach']}:**\n{resp['response']}" for resp in specialist_responses]
    )

    return f"""Synthesize the following specialist coaching responses into a cohesive answer.

USER QUERY: {user_query}

REASONING ANALYSIS: {reasoning}

SPECIALIST RESPONSES:
{responses_text}

SYNTHESIS GUIDELINES:
1. Create a unified response that naturally integrates all specialist insights
2. Maintain an encouraging, supportive tone throughout
3. Provide specific, actionable recommendations
4. Acknowledge each specialist's contribution where relevant
5. Structure the response clearly with sections if needed
6. End with motivating next steps or encouragement
7. Keep the response conversational and accessible

Remember: You're the head coach bringing together expert advice into one coherent plan."""


async def handle_interrupt_with_recovery(
    context: InterruptContext, config: Optional[Dict[str, Any]] = None
) -> Optional[str]:
    """
    Handle interrupt with error recovery and retry logic.

    Returns:
        Human feedback if provided, None otherwise
    """
    retry_count = 0
    max_retries = context.max_retries

    while retry_count <= max_retries:
        try:
            logger.info(f"Interrupting for {context.reason.value}...")

            # Create interrupt message with context
            interrupt_message = {
                "content": context.message,
                "reason": context.reason.value,
                "metadata": context.metadata,
                "retry_count": retry_count,
            }

            # Perform the interrupt
            human_feedback = interrupt(interrupt_message)

            # Log successful interrupt
            logger.info(f"Successfully received human feedback after interrupt")
            return human_feedback

        except GraphInterrupt:
            # This is expected - the graph is pausing for human input
            raise

        except Exception as e:
            retry_count += 1
            logger.error(f"Interrupt attempt {retry_count} failed: {str(e)}")

            if retry_count <= max_retries:
                await asyncio.sleep(1.0 * retry_count)  # Linear backoff
            else:
                logger.error(
                    "Max interrupt retries exceeded, proceeding without human feedback"
                )
                return None

    return None


def handle_no_responses(state: OptimizedCoachingState) -> Dict[str, Any]:
    """Handle case where no specialist responses are available."""
    fallback_message = AIMessage(
        content="""I apologize, but I wasn't able to gather specific coaching advice for your request at this time. 

This might be because:
- Your question needs clarification
- Our specialist coaches need more context
- There was a temporary issue accessing the coaching expertise

Could you please rephrase your question or provide more details about what you're looking for? I'm here to help with any fitness, nutrition, or wellness questions you have!""",
        name="head_coach",
        additional_kwargs={"error_type": "no_responses"},
    )

    return {
        "messages": [fallback_message],
        "aggregated_response": fallback_message.content,
        "error_state": "no_specialist_responses",
    }


def handle_aggregation_error(
    state: OptimizedCoachingState, error_message: str
) -> Dict[str, Any]:
    """Handle aggregation-specific errors gracefully."""
    error_response = AIMessage(
        content=f"""I encountered an issue while preparing your personalized coaching response.

To ensure you get the best advice, could you please try:
1. Rephrasing your question
2. Being more specific about what aspect you'd like help with
3. Breaking down complex questions into simpler parts

I'm here to help with any fitness or wellness questions you have!""",
        name="head_coach",
        additional_kwargs={
            "error_type": "aggregation_error",
            "error_details": error_message,
        },
    )

    return {
        "messages": [error_response],
        "aggregated_response": error_response.content,
        "error_state": "aggregation_failed",
    }


def handle_unexpected_error(
    state: OptimizedCoachingState, error_message: str
) -> Dict[str, Any]:
    """Handle unexpected errors with graceful degradation."""
    logger.error(f"Unexpected error in aggregation: {error_message}")

    # Try to provide partial response if any specialist responses exist
    specialist_responses = collect_specialist_responses(state)

    if specialist_responses:
        # Provide raw specialist responses as fallback
        fallback_content = "Here's what our specialist coaches recommend:\n\n"
        for resp in specialist_responses:
            fallback_content += f"**{resp['coach']}:**\n{resp['response']}\n\n"
    else:
        fallback_content = """I apologize for the technical difficulty. Please try again or rephrase your question.
        
Our coaching team is here to help with:
- Strength training and technique
- Running and cardio programs  
- Nutrition guidance
- Recovery strategies
- Mental performance

What would you like to know more about?"""

    error_response = AIMessage(
        content=fallback_content,
        name="head_coach",
        additional_kwargs={"error_type": "unexpected_error", "fallback_mode": True},
    )

    return {
        "messages": [error_response],
        "aggregated_response": error_response.content,
        "error_state": "unexpected_error",
    }


def create_timeout_response(specialist_responses: List[Dict[str, str]]) -> str:
    """Create a response when synthesis times out."""
    response = "Based on our specialist coaches' input:\n\n"

    for resp in specialist_responses[:3]:  # Limit to first 3 for brevity
        response += f"• **{resp['coach']}** suggests: {resp['response'][:150]}...\n\n"

    response += "\nFor a more detailed synthesis of all recommendations, please feel free to ask follow-up questions!"

    return response
