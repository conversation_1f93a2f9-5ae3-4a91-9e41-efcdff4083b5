"""
Advanced Memory Manager - Integration of Phase 2 Components

Unified interface for all advanced memory features including:
- Domain-aware memory management
- Advanced retrieval with hybrid search
- Automatic summarization and compression
- Intelligent decay management
- Real-time analytics and monitoring
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .advanced_retrieval import (
    AdvancedRetrievalPipeline,
    RetrievalContext,
    SearchConfig,
)
from .analytics import AnalyticsConfig, MemoryAnalyticsEngine, MemoryOperationType
from .decay_manager import DecayConfig, MemoryDecayManager
from .domain_manager import DomainMemoryManager
from .mem0_adapter import Mem0Adapter, MemoryType
from .schemas.analytics_schemas import DateRange
from .schemas.domain_schemas import CoachingDomain
from .summarization_engine import MemorySummarizationEngine, SummarizationConfig

logger = logging.getLogger(__name__)


class AdvancedMemoryManager:
    """
    Unified advanced memory manager integrating all Phase 2 features.

    Features:
    - Domain-aware memory storage and retrieval
    - Advanced hybrid search with ranking
    - Automatic memory summarization
    - Intelligent memory decay and cleanup
    - Real-time analytics and monitoring
    - Optimized performance with caching
    """

    def __init__(
        self,
        environment: str = "development",
        enable_analytics: bool = True,
        enable_auto_maintenance: bool = True,
    ):
        """
        Initialize advanced memory manager.

        Args:
            environment: Environment name for mem0 configuration
            enable_analytics: Whether to enable analytics tracking
            enable_auto_maintenance: Whether to enable automatic maintenance
        """
        self.environment = environment
        self.enable_analytics = enable_analytics
        self.enable_auto_maintenance = enable_auto_maintenance

        # Initialize core adapter
        self.mem0_adapter = Mem0Adapter(environment=environment)

        # Initialize Phase 2 components
        self.domain_manager = DomainMemoryManager(self.mem0_adapter)
        self.retrieval_pipeline = AdvancedRetrievalPipeline(self.mem0_adapter)
        self.summarization_engine = MemorySummarizationEngine(self.mem0_adapter)
        self.decay_manager = MemoryDecayManager(self.mem0_adapter)

        # Analytics (optional)
        self.analytics_engine = (
            MemoryAnalyticsEngine(self.mem0_adapter) if enable_analytics else None
        )

        # Maintenance task
        self._maintenance_task = None
        if enable_auto_maintenance:
            self._start_maintenance_loop()

    async def store_memory(
        self,
        user_id: str,
        content: str,
        memory_type: MemoryType = MemoryType.CONVERSATION,
        domain: Optional[CoachingDomain] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Store memory with automatic domain classification and analytics tracking.

        Args:
            user_id: User identifier
            content: Memory content
            memory_type: Type of memory
            domain: Explicit domain (auto-classified if None)
            metadata: Additional metadata

        Returns:
            Memory ID
        """
        start_time = datetime.now()

        try:
            # Store with domain awareness
            memory_id = await self.domain_manager.store_domain_memory(
                user_id=user_id,
                content=content,
                domain=domain,
                memory_type=memory_type,
                metadata=metadata,
            )

            # Track analytics
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.ADD,
                    duration_ms=duration_ms,
                    success=True,
                    metadata={
                        "memory_type": memory_type.value,
                        "domain": domain.value if domain else "auto_classified",
                        "content_length": len(content),
                    },
                )

            logger.info(f"Stored memory {memory_id} for user {user_id}")
            return memory_id

        except Exception as e:
            # Track error
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.ADD,
                    duration_ms=duration_ms,
                    success=False,
                    error_message=str(e),
                )

            logger.error(f"Failed to store memory for user {user_id}: {e}")
            raise

    async def search_memories(
        self,
        user_id: str,
        query: str,
        domains: Optional[List[CoachingDomain]] = None,
        session_type: str = "coaching",
        limit: int = 10,
        use_advanced_ranking: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Search memories with advanced retrieval and ranking.

        Args:
            user_id: User identifier
            query: Search query
            domains: Specific domains to search (None for all)
            session_type: Type of session for context-aware retrieval
            limit: Maximum results
            use_advanced_ranking: Whether to use advanced ranking pipeline

        Returns:
            List of ranked and contextualized memories
        """
        start_time = datetime.now()

        try:
            if domains:
                # Domain-specific search
                memories = await self.domain_manager.search_domain_memories(
                    user_id=user_id,
                    query=query,
                    domains=domains,
                    limit=limit,
                )

                # Apply advanced ranking if requested
                if use_advanced_ranking:
                    context = RetrievalContext(
                        user_id=user_id,
                        session_type=session_type,
                        current_domains=domains,
                    )
                    ranked_memories = await self.retrieval_pipeline.rank_memories(
                        memories, context
                    )
                    memories = [
                        {
                            "id": rm.memory_id,
                            "content": rm.content,
                            "score": rm.final_score,
                            "metadata": rm.metadata,
                            "ranking_factors": rm.ranking_factors,
                        }
                        for rm in ranked_memories
                    ]
            else:
                # Use advanced contextual retrieval
                memories = await self.retrieval_pipeline.get_contextual_memories(
                    user_id=user_id,
                    session_type=session_type,
                    query=query,
                    limit=limit,
                )

            # Track analytics
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.SEARCH,
                    duration_ms=duration_ms,
                    success=True,
                    metadata={
                        "query_length": len(query),
                        "results_count": len(memories),
                        "domains": [d.value for d in domains] if domains else "all",
                        "session_type": session_type,
                        "relevance_score": (
                            memories[0].get("score", 0.0) if memories else 0.0
                        ),
                    },
                )

            logger.info(
                f"Found {len(memories)} memories for user {user_id} query: '{query}'"
            )
            return memories

        except Exception as e:
            # Track error
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.SEARCH,
                    duration_ms=duration_ms,
                    success=False,
                    error_message=str(e),
                )

            logger.error(f"Failed to search memories for user {user_id}: {e}")
            raise

    async def summarize_user_history(
        self,
        user_id: str,
        time_threshold: int = 30,
        config: Optional[SummarizationConfig] = None,
    ) -> Dict[str, Any]:
        """
        Summarize old conversation history for a user.

        Args:
            user_id: User identifier
            time_threshold: Age threshold in days for summarization
            config: Summarization configuration

        Returns:
            Summarization results
        """
        start_time = datetime.now()

        try:
            # Get old conversation memories
            cutoff_date = datetime.now() - timedelta(days=time_threshold)

            # Search for old conversations
            old_conversations = await self.mem0_adapter.search_memories(
                user_id=user_id,
                query="",  # Get all
                memory_types=[MemoryType.CONVERSATION],
                limit=1000,
            )

            # Filter by age
            eligible_conversations = []
            for memory in old_conversations:
                timestamp_str = memory.get("metadata", {}).get("timestamp")
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(
                            timestamp_str.replace("Z", "+00:00")
                        )
                        if timestamp.replace(tzinfo=None) < cutoff_date:
                            eligible_conversations.append(memory)
                    except (ValueError, AttributeError):
                        continue

            if not eligible_conversations:
                return {"message": "No old conversations found for summarization"}

            # Perform summarization
            result = await self.summarization_engine.summarize_conversation_batch(
                eligible_conversations, config or SummarizationConfig()
            )

            # Track analytics
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.SUMMARIZE,
                    duration_ms=duration_ms,
                    success=True,
                    metadata={
                        "conversations_processed": len(eligible_conversations),
                        "summary_length": len(result.get("summary", "")),
                        "compression_ratio": result.get("metadata", {}).get(
                            "compression_ratio", 0.0
                        ),
                    },
                )

            logger.info(
                f"Summarized {len(eligible_conversations)} conversations for user {user_id}"
            )
            return result

        except Exception as e:
            # Track error
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.SUMMARIZE,
                    duration_ms=duration_ms,
                    success=False,
                    error_message=str(e),
                )

            logger.error(f"Failed to summarize history for user {user_id}: {e}")
            raise

    async def run_memory_maintenance(
        self,
        user_id: str,
        config: Optional[DecayConfig] = None,
    ) -> Dict[str, Any]:
        """
        Run comprehensive memory maintenance for a user.

        Args:
            user_id: User identifier
            config: Decay configuration

        Returns:
            Maintenance results
        """
        start_time = datetime.now()

        try:
            # Run decay maintenance
            maintenance_results = await self.decay_manager.run_decay_maintenance(
                user_id=user_id,
                config=config or DecayConfig(),
            )

            # Track analytics
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.ARCHIVE,
                    duration_ms=duration_ms,
                    success=True,
                    metadata=maintenance_results,
                )

            logger.info(f"Completed memory maintenance for user {user_id}")
            return maintenance_results

        except Exception as e:
            # Track error
            if self.analytics_engine:
                duration_ms = (datetime.now() - start_time).total_seconds() * 1000
                await self.analytics_engine.track_event(
                    user_id=user_id,
                    operation_type=MemoryOperationType.ARCHIVE,
                    duration_ms=duration_ms,
                    success=False,
                    error_message=str(e),
                )

            logger.error(f"Failed memory maintenance for user {user_id}: {e}")
            raise

    async def get_analytics_report(
        self,
        days_back: int = 7,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate analytics report for system or user.

        Args:
            days_back: Number of days to include in report
            user_id: Optional user ID for user-specific report

        Returns:
            Analytics report
        """
        if not self.analytics_engine:
            return {"error": "Analytics not enabled"}

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        date_range = DateRange(start_date, end_date)

        try:
            report = await self.analytics_engine.generate_analytics_report(
                date_range=date_range,
                user_id=user_id,
            )

            return report.to_dict()

        except Exception as e:
            logger.error(f"Failed to generate analytics report: {e}")
            return {"error": str(e)}

    async def get_system_health(self) -> Dict[str, Any]:
        """Get current system health status."""

        if not self.analytics_engine:
            return {"error": "Analytics not enabled"}

        try:
            health_status = await self.analytics_engine.get_system_health()
            return health_status.to_dict()

        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {"error": str(e)}

    async def get_optimization_recommendations(
        self,
        user_id: Optional[str] = None,
    ) -> List[str]:
        """Get optimization recommendations."""

        recommendations = []

        try:
            # Get analytics recommendations
            if self.analytics_engine:
                analytics_recs = (
                    await self.analytics_engine.get_optimization_recommendations(
                        user_id
                    )
                )
                recommendations.extend(analytics_recs)

            # Get decay recommendations
            if user_id:
                decay_recs = await self.decay_manager.get_decay_recommendations(user_id)
                storage_analysis = decay_recs.get("storage_analysis", {})

                if storage_analysis.get("potential_savings_archival", 0) > 1000:
                    recommendations.append(
                        f"User {user_id} has {storage_analysis['potential_savings_archival']} bytes "
                        "that could be saved through archival."
                    )

                if len(decay_recs.get("deletion_candidates", [])) > 10:
                    recommendations.append(
                        f"User {user_id} has {len(decay_recs['deletion_candidates'])} memories "
                        "that could be safely deleted."
                    )

            return recommendations

        except Exception as e:
            logger.error(f"Failed to get recommendations: {e}")
            return [f"Error getting recommendations: {e}"]

    async def get_user_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive memory statistics for a user."""

        try:
            # Get domain stats
            domain_stats = await self.domain_manager.get_domain_stats(user_id)

            # Get basic stats
            basic_stats = await self.mem0_adapter.get_memory_stats(user_id)

            # Get decay recommendations
            decay_recs = await self.decay_manager.get_decay_recommendations(user_id)

            # Get user pattern analysis if analytics enabled
            user_patterns = {}
            if self.analytics_engine:
                user_patterns = await self.analytics_engine.analyze_user_patterns(
                    user_id
                )

            return {
                "user_id": user_id,
                "basic_stats": basic_stats,
                "domain_distribution": domain_stats,
                "decay_analysis": decay_recs,
                "usage_patterns": user_patterns,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Failed to get user stats for {user_id}: {e}")
            return {"error": str(e)}

    def _start_maintenance_loop(self) -> None:
        """Start background maintenance loop."""

        async def maintenance_loop():
            while True:
                try:
                    await asyncio.sleep(3600)  # Run every hour

                    # Process analytics events
                    if self.analytics_engine:
                        await self.analytics_engine._process_events_buffer()

                    logger.debug("Completed periodic maintenance")

                except Exception as e:
                    logger.error(f"Error in maintenance loop: {e}")

        # Start maintenance task
        self._maintenance_task = asyncio.create_task(maintenance_loop())

    async def shutdown(self) -> None:
        """Shutdown the memory manager and clean up resources."""

        if self._maintenance_task:
            self._maintenance_task.cancel()
            try:
                await self._maintenance_task
            except asyncio.CancelledError:
                pass

        # Process remaining analytics events
        if self.analytics_engine:
            await self.analytics_engine._process_events_buffer()

        logger.info("Advanced memory manager shutdown complete")


# Convenience function for creating a configured manager
async def create_advanced_memory_manager(
    environment: str = "development",
    enable_analytics: bool = True,
    enable_auto_maintenance: bool = True,
) -> AdvancedMemoryManager:
    """
    Create and initialize an advanced memory manager.

    Args:
        environment: Environment name for configuration
        enable_analytics: Whether to enable analytics
        enable_auto_maintenance: Whether to enable auto maintenance

    Returns:
        Configured AdvancedMemoryManager instance
    """
    manager = AdvancedMemoryManager(
        environment=environment,
        enable_analytics=enable_analytics,
        enable_auto_maintenance=enable_auto_maintenance,
    )

    logger.info(f"Created advanced memory manager for {environment} environment")
    return manager
