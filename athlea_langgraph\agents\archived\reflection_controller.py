"""
Reflection Loop Controller for Managing Generator-Reflector-Regenerator Cycles.

Controls the reflection workflow, determining when to reflect, regenerate, or finalize responses.
"""

import logging
from datetime import datetime
from typing import Any, Dict, Literal, Optional

from athlea_langgraph.states.reflection_state import ReflectionAgentState


class ReflectionLoopController:
    """
    Controls the reflection loop workflow for coaching responses.

    Implements decision logic for:
    - When to initiate reflection
    - When to regenerate responses
    - When to continue or stop reflection cycles
    - When to finalize responses
    """

    def __init__(self):
        """Initialize the reflection loop controller."""
        self.logger = logging.getLogger(__name__)

        # Thresholds for decision making
        self.safety_threshold = 0.7
        self.quality_threshold = 0.75
        self.improvement_threshold = 0.05

    def should_start_reflection(self, state: ReflectionAgentState) -> bool:
        """
        Determine if reflection should be initiated for the current response.

        Args:
            state: Current ReflectionAgentState

        Returns:
            Boolean indicating whether to start reflection
        """

        # Check if reflection is enabled
        if not state.get("reflection_enabled", False):
            self.logger.info("Reflection disabled, not starting reflection")
            return False

        # Check if we have a response to reflect on
        messages = state.get("messages", [])
        if not messages:
            self.logger.info("No messages to reflect on")
            return False

        # Check if we've already reached max reflections
        reflection_metadata = state.get("reflection_metadata", {})
        current_count = reflection_metadata.get("reflection_count", 0)
        max_reflections = reflection_metadata.get("max_reflections", 2)

        if current_count >= max_reflections:
            self.logger.info(
                f"Maximum reflections reached ({current_count}/{max_reflections})"
            )
            return False

        # Check if this is a coaching domain that benefits from reflection
        current_agent = state.get("current_reflection_agent", "")
        reflection_worthy_domains = [
            "strength",
            "nutrition",
            "cardio",
            "recovery",
            "mental",
            "cycling",
        ]

        domain = current_agent.replace("_coach", "").replace("_agent", "")
        if domain not in reflection_worthy_domains and domain != "general":
            self.logger.info(f"Domain {domain} not configured for reflection")
            return False

        # Always reflect on first response for safety-critical domains
        safety_critical_domains = ["strength", "nutrition", "recovery"]
        if domain in safety_critical_domains and current_count == 0:
            self.logger.info(f"Safety-critical domain {domain}, starting reflection")
            return True

        # Check user profile for high-risk factors
        user_profile = state.get("user_profile", {})
        has_limitations = bool(
            user_profile.get("limitations") or user_profile.get("medical_conditions")
        )

        if has_limitations and current_count == 0:
            self.logger.info(
                "User has limitations/medical conditions, starting reflection"
            )
            return True

        # Default: start reflection for first response
        if current_count == 0:
            self.logger.info("Starting initial reflection")
            return True

        return False

    def should_regenerate_response(self, state: ReflectionAgentState) -> bool:
        """
        Determine if the response should be regenerated based on reflection feedback.

        Args:
            state: Current ReflectionAgentState with reflection results

        Returns:
            Boolean indicating whether to regenerate the response
        """

        reflection_metadata = state.get("reflection_metadata", {})
        safety_validation = state.get("safety_validation", {})

        # Check safety score - always regenerate if safety is compromised
        injury_risk_score = safety_validation.get("injury_risk_score", 1.0)
        if injury_risk_score < self.safety_threshold:
            self.logger.info(
                f"Safety score too low ({injury_risk_score:.2f}), regenerating"
            )
            return True

        # Check for critical safety flags
        safety_flags = [
            not safety_validation.get("form_safety_validated", True),
            not safety_validation.get("contraindications_checked", True),
            len(safety_validation.get("safety_warnings", [])) > 0,
        ]

        if any(safety_flags):
            self.logger.info("Critical safety issues identified, regenerating")
            return True

        # Check quality score
        quality_score = reflection_metadata.get("quality_score", 1.0)
        if quality_score < self.quality_threshold:
            self.logger.info(
                f"Quality score too low ({quality_score:.2f}), regenerating"
            )
            return True

        # Check improvement areas for critical issues
        improvement_areas = reflection_metadata.get("improvement_areas", [])
        critical_areas = ["safety", "accuracy"]

        if any(area in improvement_areas for area in critical_areas):
            self.logger.info(
                f"Critical improvement areas identified: {improvement_areas}"
            )
            return True

        # Check if reflection explicitly recommends regeneration
        reflection_feedback = reflection_metadata.get("reflection_feedback", "")
        regeneration_indicators = [
            "regenerate",
            "rewrite",
            "start over",
            "critical issue",
            "unsafe",
            "incorrect",
            "dangerous",
        ]

        if any(
            indicator in reflection_feedback.lower()
            for indicator in regeneration_indicators
        ):
            self.logger.info("Reflection explicitly recommends regeneration")
            return True

        self.logger.info("No regeneration needed based on reflection analysis")
        return False

    def should_continue_reflection(self, state: ReflectionAgentState) -> bool:
        """
        Determine if another reflection cycle should be performed.

        Args:
            state: Current ReflectionAgentState after regeneration

        Returns:
            Boolean indicating whether to continue reflection
        """

        reflection_metadata = state.get("reflection_metadata", {})
        current_count = reflection_metadata.get("reflection_count", 0)
        max_reflections = reflection_metadata.get("max_reflections", 2)

        # Check if we've reached maximum reflections
        if current_count >= max_reflections:
            self.logger.info(
                f"Maximum reflections reached ({current_count}/{max_reflections})"
            )
            return False

        # Check if we have improvement assessment from regeneration
        improvement_assessment = state.get("improvement_assessment", {})
        improvement_score = improvement_assessment.get("improvement_score", 0.0)

        # If improvement was significant, we might be done
        if improvement_score > 0.9:
            self.logger.info(
                f"High improvement score ({improvement_score:.2f}), stopping reflection"
            )
            return False

        # Check if safety is now acceptable
        safety_validation = state.get("safety_validation", {})
        injury_risk_score = safety_validation.get("injury_risk_score", 1.0)

        if injury_risk_score >= self.safety_threshold:
            # Safety is good, check if quality improvement was minimal
            quality_before = state.get("response_quality_before", 0.0)
            quality_after = state.get("response_quality_after", 0.0)
            quality_improvement = quality_after - quality_before

            if quality_improvement < self.improvement_threshold:
                self.logger.info(
                    f"Minimal quality improvement ({quality_improvement:.3f}), stopping"
                )
                return False

        # Continue if we haven't reached max and there's still room for improvement
        if current_count < max_reflections and improvement_score < 0.85:
            self.logger.info(
                f"Continuing reflection (cycle {current_count + 1}/{max_reflections})"
            )
            return True

        return False

    def get_next_node(
        self, state: ReflectionAgentState
    ) -> Literal["reflect", "regenerate", "finalize"]:
        """
        Determine the next node in the reflection workflow.

        Args:
            state: Current ReflectionAgentState

        Returns:
            String indicating the next node to execute
        """

        reflection_metadata = state.get("reflection_metadata", {})

        # If no reflection has been done yet, start reflection
        if reflection_metadata.get("reflection_count", 0) == 0:
            if self.should_start_reflection(state):
                return "reflect"
            else:
                return "finalize"

        # If reflection has been done but no regeneration
        if not state.get("regeneration_metadata"):
            if self.should_regenerate_response(state):
                return "regenerate"
            else:
                return "finalize"

        # After regeneration, always finalize (no more loops)
        return "finalize"

    def create_reflection_summary(self, state: ReflectionAgentState) -> Dict[str, Any]:
        """
        Create a summary of the reflection process for logging and analytics.

        Args:
            state: Final ReflectionAgentState

        Returns:
            Dictionary containing reflection process summary
        """

        reflection_metadata = state.get("reflection_metadata", {})
        safety_validation = state.get("safety_validation", {})
        improvement_assessment = state.get("improvement_assessment", {})

        return {
            "total_reflections": reflection_metadata.get("reflection_count", 0),
            "max_reflections": reflection_metadata.get("max_reflections", 2),
            "final_safety_score": safety_validation.get("injury_risk_score", 1.0),
            "final_quality_score": reflection_metadata.get("quality_score", 1.0),
            "improvement_areas_addressed": improvement_assessment.get(
                "areas_likely_addressed", []
            ),
            "safety_warnings": len(safety_validation.get("safety_warnings", [])),
            "regeneration_performed": bool(state.get("regeneration_metadata")),
            "reflection_enabled": state.get("reflection_enabled", False),
            "domain": state.get("current_reflection_agent", "unknown"),
            "user_had_limitations": bool(
                state.get("user_profile", {}).get("limitations")
                or state.get("user_profile", {}).get("medical_conditions")
            ),
            "process_timestamp": datetime.utcnow().isoformat(),
        }


# Router functions for LangGraph integration
def reflection_router(state: ReflectionAgentState) -> str:
    """
    LangGraph router function for reflection workflow.

    Determines the next node in the reflection process based on
    current state and reflection controller logic.
    """

    logger = logging.getLogger(__name__)
    controller = ReflectionLoopController()

    try:
        next_node = controller.get_next_node(state)
        logger.info(f"Reflection router: directing to '{next_node}'")
        return next_node

    except Exception as e:
        logger.error(f"Error in reflection router: {e}")
        return "finalize"


def safety_gate_router(state: ReflectionAgentState) -> str:
    """
    Safety gate router that checks if response is safe enough to proceed.

    Acts as a safety checkpoint in the reflection workflow.
    """

    logger = logging.getLogger(__name__)

    try:
        safety_validation = state.get("safety_validation", {})
        injury_risk_score = safety_validation.get("injury_risk_score", 1.0)

        # Safety threshold check
        if injury_risk_score < 0.6:  # Lower threshold for safety gate
            logger.warning(
                f"Safety gate: blocking response with risk score {injury_risk_score:.2f}"
            )
            return "regenerate"

        # Check for critical safety flags
        critical_safety_issues = [
            not safety_validation.get("form_safety_validated", True),
            not safety_validation.get("contraindications_checked", True),
            len(safety_validation.get("safety_warnings", []))
            > 1,  # More than one warning
        ]

        if any(critical_safety_issues):
            logger.warning("Safety gate: critical safety issues detected")
            return "regenerate"

        logger.info("Safety gate: response approved for delivery")
        return "finalize"

    except Exception as e:
        logger.error(f"Error in safety gate router: {e}")
        # When in doubt, err on the side of caution
        return "regenerate"


# Finalization node
async def reflection_finalization_node(state: ReflectionAgentState) -> Dict[str, Any]:
    """
    Finalization node that completes the reflection process.

    Creates final summary and prepares the response for delivery.
    """

    logger = logging.getLogger(__name__)
    logger.info("--- Reflection Finalization Node ---")

    try:
        controller = ReflectionLoopController()

        # Create reflection summary
        reflection_summary = controller.create_reflection_summary(state)

        # Log reflection process completion
        logger.info(
            f"Reflection process completed: {reflection_summary['total_reflections']} cycles"
        )
        logger.info(
            f"Final safety score: {reflection_summary['final_safety_score']:.2f}"
        )
        logger.info(
            f"Final quality score: {reflection_summary['final_quality_score']:.2f}"
        )

        # Mark reflection as complete
        reflection_metadata = state.get("reflection_metadata", {})
        updated_metadata = {
            **reflection_metadata,
            "reflection_complete": True,
            "completion_timestamp": datetime.utcnow().isoformat(),
            "final_summary": reflection_summary,
        }

        return {
            "reflection_metadata": updated_metadata,
            "reflection_complete": True,
            "reflection_summary": reflection_summary,
        }

    except Exception as e:
        logger.error(f"Error in reflection finalization: {e}")
        return {
            "reflection_complete": True,
            "reflection_error": str(e),
            "reflection_metadata": {
                "reflection_complete": True,
                "error": True,
                "completion_timestamp": datetime.utcnow().isoformat(),
            },
        }
