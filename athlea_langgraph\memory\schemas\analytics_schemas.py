"""
Analytics schemas for memory system monitoring and performance tracking.

Defines data structures for analytics events, metrics, and reporting.
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum, IntEnum
from typing import Any, Dict, List, Optional


class MemoryOperationType(Enum):
    """Types of memory operations for tracking."""

    ADD = "add"
    SEARCH = "search"
    RETRIEVE = "retrieve"
    UPDATE = "update"
    DELETE = "delete"
    SUMMARIZE = "summarize"
    CLASSIFY = "classify"
    ARCHIVE = "archive"


class HealthStatusLevel(IntEnum):
    """Health status levels for system monitoring."""

    HEALTHY = 1
    WARNING = 2
    ERROR = 3
    CRITICAL = 4


@dataclass
class MemoryAnalyticsEvent:
    """Individual analytics event for memory operations."""

    event_id: str
    user_id: str
    operation_type: MemoryOperationType
    timestamp: datetime
    duration_ms: float
    success: bool
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "event_id": self.event_id,
            "user_id": self.user_id,
            "operation_type": self.operation_type.value,
            "timestamp": self.timestamp.isoformat(),
            "duration_ms": self.duration_ms,
            "success": self.success,
            "metadata": self.metadata,
            "error_message": self.error_message,
        }


@dataclass
class RetrievalMetrics:
    """Metrics for memory retrieval effectiveness."""

    total_queries: int
    average_latency_ms: float
    hit_rate: float  # Percentage of queries that returned relevant results
    average_relevance_score: float
    unique_users: int
    domain_distribution: Dict[str, int] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return {
            "total_queries": self.total_queries,
            "average_latency_ms": self.average_latency_ms,
            "hit_rate": self.hit_rate,
            "average_relevance_score": self.average_relevance_score,
            "unique_users": self.unique_users,
            "domain_distribution": self.domain_distribution,
        }


@dataclass
class StorageMetrics:
    """Metrics for memory storage usage."""

    total_memories: int
    memories_by_type: Dict[str, int] = field(default_factory=dict)
    memories_by_domain: Dict[str, int] = field(default_factory=dict)
    storage_size_mb: float = 0.0
    growth_rate_daily: float = 0.0
    summarized_memories: int = 0
    archived_memories: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return {
            "total_memories": self.total_memories,
            "memories_by_type": self.memories_by_type,
            "memories_by_domain": self.memories_by_domain,
            "storage_size_mb": self.storage_size_mb,
            "growth_rate_daily": self.growth_rate_daily,
            "summarized_memories": self.summarized_memories,
            "archived_memories": self.archived_memories,
        }


@dataclass
class PerformanceMetrics:
    """Performance metrics for memory system."""

    average_operation_latency: Dict[MemoryOperationType, float] = field(
        default_factory=dict
    )
    operations_per_second: float = 0.0
    error_rate: float = 0.0
    cache_hit_rate: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return {
            "average_operation_latency": {
                op.value: latency
                for op, latency in self.average_operation_latency.items()
            },
            "operations_per_second": self.operations_per_second,
            "error_rate": self.error_rate,
            "cache_hit_rate": self.cache_hit_rate,
        }


@dataclass
class AnalyticsReport:
    """Comprehensive analytics report for a time period."""

    start_time: datetime
    end_time: datetime
    retrieval_metrics: RetrievalMetrics
    storage_metrics: StorageMetrics
    performance_metrics: PerformanceMetrics
    user_engagement: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)

    @property
    def duration(self) -> timedelta:
        """Get the duration of this report period."""
        return self.end_time - self.start_time

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "duration_hours": self.duration.total_seconds() / 3600,
            "retrieval_metrics": self.retrieval_metrics.to_dict(),
            "storage_metrics": self.storage_metrics.to_dict(),
            "performance_metrics": self.performance_metrics.to_dict(),
            "user_engagement": self.user_engagement,
            "recommendations": self.recommendations,
        }


@dataclass
class HealthStatus:
    """Overall health status of the memory system."""

    status_level: HealthStatusLevel
    timestamp: datetime
    component_status: Dict[str, HealthStatusLevel] = field(default_factory=dict)
    active_alerts: List[str] = field(default_factory=list)
    metrics_summary: Dict[str, Any] = field(default_factory=dict)

    def add_alert(self, message: str) -> None:
        """Add an alert to the health status."""
        self.active_alerts.append(message)

        # Update overall status if needed
        if "critical" in message.lower() or "error" in message.lower():
            self.status_level = max(self.status_level, HealthStatusLevel.ERROR)
        elif "warning" in message.lower():
            self.status_level = max(self.status_level, HealthStatusLevel.WARNING)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for monitoring systems."""
        return {
            "status_level": self.status_level.name.lower(),
            "timestamp": self.timestamp.isoformat(),
            "component_status": {
                comp: status.name.lower()
                for comp, status in self.component_status.items()
            },
            "active_alerts": self.active_alerts,
            "metrics_summary": self.metrics_summary,
        }


@dataclass
class DateRange:
    """Date range for analytics queries."""

    start_date: datetime
    end_date: datetime

    def __post_init__(self):
        """Validate date range."""
        if self.start_date >= self.end_date:
            raise ValueError("Start date must be before end date")

    @property
    def duration_days(self) -> int:
        """Get duration in days."""
        return (self.end_date - self.start_date).days

    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary."""
        return {
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
        }
