#!/usr/bin/env python3
"""
Test Multi-Message Streaming Implementation

This test verifies that the aggregation node correctly creates individual
specialist coach messages followed by the Athlea synthesis message.
"""

import asyncio
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def test_aggregation_multi_message():
    """Test that aggregation node creates multiple messages correctly."""
    print("\n" + "=" * 60)
    print("🧪 TESTING MULTI-MESSAGE AGGREGATION")
    print("=" * 60)

    # Import the aggregation node
    from athlea_langgraph.agents.aggregation_node import aggregation_node

    # Create test state with multiple coach responses
    test_state = {
        "user_query": "I want to build muscle and improve my nutrition",
        "coach_responses": {
            "strength_coach": "Focus on compound movements like squats, deadlifts, and bench press. Train 3-4 times per week with progressive overload.",
            "nutrition_coach": "Eat in a slight caloric surplus with 1.6-2.2g protein per kg bodyweight. Include whole foods and time protein around workouts."
        },
        "reasoning_output": "User wants both strength training and nutrition advice",
        "enable_human_feedback": False  # Disable for testing
    }

    print(f"📊 Test state has {len(test_state['coach_responses'])} coach responses")
    
    try:
        # Call the aggregation node
        result = await aggregation_node(test_state)
        
        print(f"✅ Aggregation completed successfully")
        print(f"📊 Result keys: {list(result.keys())}")
        
        # Check that we have messages
        messages = result.get("messages", [])
        print(f"📨 Total messages created: {len(messages)}")
        
        # Verify message structure
        specialist_count = 0
        athlea_count = 0
        
        for i, msg in enumerate(messages):
            print(f"  Message {i+1}: name='{msg.name}', content_length={len(msg.content)}")
            
            if msg.name in ["Strength Coach", "Nutrition Coach", "Cardio Coach", "Recovery Coach", "Mental Coach"]:
                specialist_count += 1
                print(f"    ✅ Specialist message from {msg.name}")
            elif msg.name == "Athlea":
                athlea_count += 1
                print(f"    ✅ Athlea synthesis message")
            else:
                print(f"    ⚠️  Unknown message name: {msg.name}")
        
        print(f"\n📊 Message Summary:")
        print(f"  - Specialist messages: {specialist_count}")
        print(f"  - Athlea synthesis: {athlea_count}")
        
        # Verify expected structure
        expected_specialists = len(test_state['coach_responses'])
        if specialist_count == expected_specialists and athlea_count == 1:
            print(f"✅ PASS: Correct message structure ({specialist_count} specialists + 1 Athlea)")
        else:
            print(f"❌ FAIL: Expected {expected_specialists} specialists + 1 Athlea, got {specialist_count} + {athlea_count}")
        
        # Check backward compatibility fields
        if "aggregated_response" in result and "final_response" in result:
            print("✅ PASS: Backward compatibility fields present")
        else:
            print("❌ FAIL: Missing backward compatibility fields")
        
        # Check metadata
        metadata = result.get("aggregation_metadata", {})
        if metadata.get("streaming_mode") == "multi_message":
            print("✅ PASS: Multi-message streaming mode detected")
        else:
            print("❌ FAIL: Multi-message streaming mode not set")
            
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Aggregation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_empty_responses():
    """Test aggregation behavior with no coach responses."""
    print("\n" + "=" * 60)
    print("🧪 TESTING EMPTY RESPONSES HANDLING")
    print("=" * 60)

    from athlea_langgraph.agents.aggregation_node import aggregation_node

    # Create test state with no coach responses
    test_state = {
        "user_query": "Test query",
        "coach_responses": {},
        "enable_human_feedback": False
    }

    try:
        result = await aggregation_node(test_state)
        
        if "error_state" in result:
            print("✅ PASS: Correctly handled empty responses with error state")
            return True
        else:
            print("❌ FAIL: Should have returned error state for empty responses")
            return False
            
    except Exception as e:
        print(f"❌ FAIL: Unexpected error: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Multi-Message Streaming Tests")
    
    tests = [
        ("Multi-Message Aggregation", test_aggregation_multi_message),
        ("Empty Responses Handling", test_empty_responses),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Multi-message streaming is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
