"""
Strength Training Tools

This module provides comprehensive tools for strength training assessment,
exercise database management, and program design.
"""

import json

# LangChain tool integrations
from langchain_core.tools import tool

from ..base_tool import BaseDomainTool
from .exercise_database import StrengthExerciseDatabase
from .strength_assessment import (
    EquipmentInput,
    ExperienceInput,
    GoalsInput,
    MovementScreenInput,
    StrengthAssessmentInput,
    StrengthAssessmentOutput,
    StrengthAssessmentTool,
    StrengthTestingInput,
)


@tool("search_strength_exercises")
def search_strength_exercises(
    muscle_groups: str = "",
    equipment: str = "",
    exercise_type: str = "",
    difficulty: str = "",
    limit: int = 10,
) -> str:
    """Search for strength exercises based on criteria."""
    db = StrengthExerciseDatabase()
    results = db.search_exercises(
        muscle_groups=muscle_groups.split(",") if muscle_groups else [],
        equipment=equipment.split(",") if equipment else [],
        exercise_type=exercise_type,
        difficulty=difficulty,
        limit=limit,
    )
    return str(results)


@tool("get_exercise_progression")
async def get_exercise_progression(
    exercise_name: str, current_level: str = "beginner", user_goals: str = ""
) -> str:
    """Get progression suggestions for a specific exercise.

    Args:
        exercise_name: Name of the exercise (e.g., 'squat', 'push-up')
        current_level: Current skill level ('beginner', 'intermediate', 'advanced')
        user_goals: Comma-separated list of goals (e.g., 'build strength,increase reps')
    """
    try:
        from .exercise_database import StrengthExerciseDatabase
        from ..schemas.strength_schemas import ExerciseProgressionInput

        # Parse user goals if provided
        goals_list = (
            [goal.strip() for goal in user_goals.split(",") if goal.strip()]
            if user_goals
            else []
        )

        # Create proper input object
        progression_input = ExerciseProgressionInput(
            exercise_name=exercise_name,
            current_level=current_level,
            user_goals=goals_list,
        )

        db = StrengthExerciseDatabase()
        result = await db.get_exercise_progression(progression_input)

        # Return formatted result
        return json.dumps(
            {
                "exercise_name": result.exercise_name,
                "current_level": result.current_level,
                "progression_steps": result.progression_steps,
                "estimated_timeline": result.estimated_timeline,
                "key_milestones": result.key_milestones,
                "error": result.error,
            },
            indent=2,
        )

    except Exception as e:
        return json.dumps(
            {
                "exercise_name": exercise_name,
                "current_level": current_level,
                "error": f"Exercise progression failed: {str(e)}",
            },
            indent=2,
        )


@tool("comprehensive_strength_assessment")
async def comprehensive_strength_assessment(assessment_data: str) -> str:
    """
    Perform a comprehensive strength assessment including movement screening,
    strength testing, experience evaluation, and personalized recommendations.

    Args:
        assessment_data: JSON string containing complete assessment input data

    Returns:
        JSON string with detailed assessment results and recommendations
    """
    try:
        # Parse the input data
        data = json.loads(assessment_data)

        # Create the assessment input object
        assessment_input = StrengthAssessmentInput(**data)

        # Initialize the assessment tool
        assessment_tool = StrengthAssessmentTool()

        # Perform the assessment
        results = await assessment_tool.assess_strength(assessment_input)

        # Return results as JSON string
        return json.dumps(results.dict(), indent=2, default=str)

    except Exception as e:
        return json.dumps(
            {"error": f"Assessment failed: {str(e)}", "status": "failed"}, indent=2
        )


__all__ = [
    "StrengthExerciseDatabase",
    "StrengthAssessmentTool",
    "StrengthAssessmentInput",
    "StrengthAssessmentOutput",
    "MovementScreenInput",
    "StrengthTestingInput",
    "ExperienceInput",
    "EquipmentInput",
    "GoalsInput",
    "search_strength_exercises",
    "get_exercise_progression",
    "comprehensive_strength_assessment",
]
