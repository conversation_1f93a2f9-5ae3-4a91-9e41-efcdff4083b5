"""
Enhanced Coaching Graph with mem0 Integration

This builds on the existing coaching_graph.py but adds mem0 for:
1. Semantic memory search and retrieval
2. User profile management with structured data
3. Long-term memory with automatic summarization
4. Context-aware personalized responses
5. Multi-domain memory separation (workouts, nutrition, etc.)
"""

import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from .agents.head_coach import clarification_node, head_coach_node, head_coach_router
from .agents.planning_node import planning_node
from .agents.reasoning_node import reasoning_node
from .agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    running_coach_node,
    strength_coach_node,
)
from .memory.mem0_adapter import Mem0Adapter, MemoryType
from .memory.mongo_memory import MongoSaver  # Keep MongoDB for checkpointing
from .state import AgentState

# Import the new aggregation node
try:
    from .agents.aggregation_node_v2 import aggregation_node_v2

    use_v2_aggregation = True
except ImportError:
    from .agents.aggregation_node import aggregation_node

    use_v2_aggregation = False
    logging.warning(
        "Using v1 aggregation node. Consider upgrading to v2 for better production features."
    )

logger = logging.getLogger(__name__)


class Mem0EnhancedCoachingGraph:
    """
    A coaching graph enhanced with mem0 memory capabilities.

    Provides semantic memory search, user profiling, and personalized context injection.
    """

    def __init__(
        self,
        user_id: str,
        mongodb_uri: Optional[str] = None,
        environment: str = "development",
    ):
        self.user_id = user_id
        self.environment = environment

        # Initialize mem0 adapter for semantic memory
        self.memory_adapter = Mem0Adapter(environment=environment)

        # Initialize MongoDB checkpointer for session continuity
        if mongodb_uri:
            self.checkpointer = MongoSaver(
                connection_string=mongodb_uri,
                database="athlea_coaching",
                collection="checkpoints",
            )
        else:
            self.checkpointer = None
            logger.warning("No MongoDB URI provided, checkpointing disabled")

        self.graph = None

    async def initialize_user_profile(self, profile_data: Dict[str, Any]) -> bool:
        """
        Initialize user profile with structured data.

        Args:
            profile_data: Dictionary containing user profile information

        Returns:
            Success status
        """
        try:
            # Add individual profile components as memories
            profile_updates = {}

            if "goals" in profile_data:
                goals = profile_data["goals"]
                if isinstance(goals, list):
                    for goal in goals:
                        await self.memory_adapter.add_memory(
                            user_id=self.user_id,
                            content=f"Fitness goal: {goal}",
                            memory_type=MemoryType.GOAL,
                            metadata={"goal": goal},
                        )
                else:
                    await self.memory_adapter.add_memory(
                        user_id=self.user_id,
                        content=f"Fitness goal: {goals}",
                        memory_type=MemoryType.GOAL,
                        metadata={"goal": goals},
                    )

            if "preferences" in profile_data:
                prefs = profile_data["preferences"]
                if isinstance(prefs, list):
                    for pref in prefs:
                        await self.memory_adapter.add_memory(
                            user_id=self.user_id,
                            content=f"User preference: {pref}",
                            memory_type=MemoryType.PREFERENCE,
                            metadata={"preference": pref},
                        )
                else:
                    await self.memory_adapter.add_memory(
                        user_id=self.user_id,
                        content=f"User preference: {prefs}",
                        memory_type=MemoryType.PREFERENCE,
                        metadata={"preference": prefs},
                    )

            if "fitness_level" in profile_data:
                await self.memory_adapter.add_memory(
                    user_id=self.user_id,
                    content=f"Fitness level: {profile_data['fitness_level']}",
                    memory_type=MemoryType.USER_PROFILE,
                    metadata={"fitness_level": profile_data["fitness_level"]},
                )

            if "injuries" in profile_data:
                injuries = profile_data["injuries"]
                if isinstance(injuries, list):
                    for injury in injuries:
                        await self.memory_adapter.add_memory(
                            user_id=self.user_id,
                            content=f"Injury or limitation: {injury}",
                            memory_type=MemoryType.INJURY,
                            metadata={"injury": injury},
                        )
                else:
                    await self.memory_adapter.add_memory(
                        user_id=self.user_id,
                        content=f"Injury or limitation: {injuries}",
                        memory_type=MemoryType.INJURY,
                        metadata={"injury": injuries},
                    )

            logger.info(f"Initialized profile for user {self.user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize user profile for {self.user_id}: {e}")
            return False

    async def get_personalized_context(self, user_query: str) -> Dict[str, Any]:
        """
        Get personalized context for user query using mem0 semantic search.

        Args:
            user_query: Current user query/message

        Returns:
            Dictionary with relevant context and user profile
        """
        try:
            # Get relevant context from mem0
            context = await self.memory_adapter.get_relevant_context(
                user_id=self.user_id,
                query=user_query,
                max_tokens=1500,  # Conservative token limit for context
            )

            # Get recent activities for additional context
            recent_activities = await self.memory_adapter.get_recent_activities(
                user_id=self.user_id, limit=3
            )

            # Add recent activities to context
            context["recent_activities"] = recent_activities

            logger.info(f"Retrieved personalized context for user {self.user_id}")
            return context

        except Exception as e:
            logger.error(f"Failed to get personalized context for {self.user_id}: {e}")
            return {
                "user_profile": {},
                "relevant_memories": [],
                "context_summary": "Error retrieving context",
            }

    async def log_workout_session(self, workout_data: Dict[str, Any]) -> str:
        """
        Log a workout session to memory.

        Args:
            workout_data: Workout session information

        Returns:
            Memory ID of the logged workout
        """
        return await self.memory_adapter.log_workout(
            user_id=self.user_id, workout_data=workout_data
        )

    async def track_user_progress(self, progress_data: Dict[str, Any]) -> str:
        """
        Track user progress/achievements.

        Args:
            progress_data: Progress information

        Returns:
            Memory ID of the progress entry
        """
        return await self.memory_adapter.track_progress(
            user_id=self.user_id, progress_data=progress_data
        )

    async def memory_enhanced_reasoning_node(self, state: AgentState) -> Dict[str, Any]:
        """Enhanced reasoning node that incorporates user memories and profile."""
        logger.info("--- Mem0-Enhanced Reasoning Node ---")

        # Get personalized context
        user_query = state.get("user_query", "")
        context = await self.get_personalized_context(user_query)

        # Add memory context to state for reasoning
        memory_enhanced_state = {
            **state,
            "personalized_context": context,
            "user_profile": context.get("user_profile", {}),
            "relevant_memories": context.get("relevant_memories", []),
            "recent_activities": context.get("recent_activities", []),
        }

        # Run original reasoning with enhanced context
        result = await reasoning_node(memory_enhanced_state)

        # Preserve memory context in result
        result["personalized_context"] = context

        return result

    async def memory_enhanced_planning_node(self, state: AgentState) -> Dict[str, Any]:
        """Enhanced planning node that considers user profile and history."""
        logger.info("--- Mem0-Enhanced Planning Node ---")

        # Ensure we have personalized context
        if "personalized_context" not in state:
            context = await self.get_personalized_context(state.get("user_query", ""))
            state["personalized_context"] = context

        # Run original planning with memory context
        result = await planning_node(state)

        return result

    async def memory_enhanced_aggregation_node(
        self, state: AgentState
    ) -> Dict[str, Any]:
        """Enhanced aggregation node that saves interaction and provides personalized response."""
        logger.info("--- Mem0-Enhanced Aggregation Node ---")

        # Run original aggregation
        if use_v2_aggregation:
            result = await aggregation_node_v2(state)
        else:
            result = await aggregation_node(state)

        # Save interaction to mem0
        await self._save_interaction_to_mem0(state, result)

        # Enhance response with personalization
        await self._enhance_response_with_personalization(state, result)

        return result

    async def _save_interaction_to_mem0(
        self, state: AgentState, result: Dict[str, Any]
    ) -> None:
        """Save the interaction to mem0 memory."""
        try:
            user_message = state.get("user_query", "")
            assistant_response = result.get("aggregated_response", "")

            # Log conversation
            conversation_metadata = {
                "reasoning": state.get("reasoning_output", ""),
                "plan": state.get("plan", ""),
                "specialist_responses_count": len(
                    [
                        r
                        for r in [
                            state.get("strength_response"),
                            state.get("running_response"),
                            state.get("cardio_response"),
                            state.get("cycling_response"),
                            state.get("nutrition_response"),
                            state.get("recovery_response"),
                            state.get("mental_response"),
                        ]
                        if r
                    ]
                ),
                "session_type": "coaching_interaction",
            }

            memory_id = await self.memory_adapter.log_conversation(
                user_id=self.user_id,
                user_message=user_message,
                assistant_message=assistant_response,
                metadata=conversation_metadata,
            )

            logger.info(f"Saved conversation to mem0: {memory_id}")

            # Extract and save any insights about user preferences or goals
            await self._extract_and_save_insights(state, result)

        except Exception as e:
            logger.error(f"Failed to save interaction to mem0: {e}")

    async def _extract_and_save_insights(
        self, state: AgentState, result: Dict[str, Any]
    ) -> None:
        """Extract insights from interaction and save as memories."""
        try:
            user_query = state.get("user_query", "").lower()

            # Extract potential preferences
            if any(
                keyword in user_query
                for keyword in ["prefer", "like", "love", "hate", "dislike"]
            ):
                insight_content = (
                    f"User expressed preference: {state.get('user_query', '')}"
                )
                await self.memory_adapter.add_memory(
                    user_id=self.user_id,
                    content=insight_content,
                    memory_type=MemoryType.PREFERENCE,
                    metadata={
                        "source": "conversation_insight",
                        "extracted_from": user_query,
                    },
                )

            # Extract potential goals
            if any(
                keyword in user_query
                for keyword in ["goal", "want to", "trying to", "aim", "target"]
            ):
                insight_content = f"User mentioned goal: {state.get('user_query', '')}"
                await self.memory_adapter.add_memory(
                    user_id=self.user_id,
                    content=insight_content,
                    memory_type=MemoryType.GOAL,
                    metadata={
                        "source": "conversation_insight",
                        "extracted_from": user_query,
                    },
                )

            # Extract potential injury mentions
            if any(
                keyword in user_query
                for keyword in ["injury", "hurt", "pain", "problem", "can't"]
            ):
                insight_content = f"User mentioned potential limitation: {state.get('user_query', '')}"
                await self.memory_adapter.add_memory(
                    user_id=self.user_id,
                    content=insight_content,
                    memory_type=MemoryType.INJURY,
                    metadata={
                        "source": "conversation_insight",
                        "extracted_from": user_query,
                    },
                )

        except Exception as e:
            logger.error(f"Failed to extract insights: {e}")

    async def _enhance_response_with_personalization(
        self, state: AgentState, result: Dict[str, Any]
    ) -> None:
        """Enhance the response with personalized touches based on user profile."""
        try:
            context = state.get("personalized_context", {})
            user_profile = context.get("user_profile", {})

            # Add personalization note to response if we have profile info
            if user_profile.get("goals") or user_profile.get("preferences"):
                personalization_note = "\n\n*This response was personalized based on your profile and previous interactions.*"

                if "aggregated_response" in result:
                    result["aggregated_response"] += personalization_note

        except Exception as e:
            logger.error(f"Failed to enhance response with personalization: {e}")

    async def _build_graph(self, use_checkpointing: bool = True) -> None:
        """Build the LangGraph with mem0 enhancements."""
        logger.info("Building mem0-enhanced coaching graph...")

        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes with mem0 enhancements
        workflow.add_node("reasoning", self.memory_enhanced_reasoning_node)
        workflow.add_node("planning", self.memory_enhanced_planning_node)
        workflow.add_node("head_coach", head_coach_node)
        workflow.add_node("clarification", clarification_node)

        # Specialized coaches (can also be enhanced with memory if needed)
        workflow.add_node("strength_coach", strength_coach_node)
        workflow.add_node("running_coach", running_coach_node)
        workflow.add_node("cardio_coach", cardio_coach_node)
        workflow.add_node("cycling_coach", cycling_coach_node)
        workflow.add_node("nutrition_coach", nutrition_coach_node)
        workflow.add_node("recovery_coach", recovery_coach_node)
        workflow.add_node("mental_coach", mental_coach_node)

        # Enhanced aggregation node
        workflow.add_node("aggregation", self.memory_enhanced_aggregation_node)

        # Define the flow
        workflow.add_edge(START, "reasoning")
        workflow.add_edge("reasoning", "planning")
        workflow.add_edge("planning", "head_coach")

        # Head coach routing logic
        workflow.add_conditional_edges(
            "head_coach",
            head_coach_router,
            {
                "clarification": "clarification",
                "strength": "strength_coach",
                "running": "running_coach",
                "cardio": "cardio_coach",
                "cycling": "cycling_coach",
                "nutrition": "nutrition_coach",
                "recovery": "recovery_coach",
                "mental": "mental_coach",
                "aggregation": "aggregation",
            },
        )

        # Clarification loops back to head coach
        workflow.add_edge("clarification", "head_coach")

        # All specialists route to aggregation
        for specialist in [
            "strength_coach",
            "running_coach",
            "cardio_coach",
            "cycling_coach",
            "nutrition_coach",
            "recovery_coach",
            "mental_coach",
        ]:
            workflow.add_edge(specialist, "aggregation")

        workflow.add_edge("aggregation", END)

        # Compile the graph
        if use_checkpointing and self.checkpointer:
            self.graph = workflow.compile(checkpointer=self.checkpointer)
            logger.info("Graph compiled with MongoDB checkpointing")
        else:
            self.graph = workflow.compile()
            logger.info("Graph compiled without checkpointing")

    async def create_compiled_graph(
        self,
        enable_performance_monitoring: bool = True,
    ) -> CompiledStateGraph:
        """Create and return a compiled graph with mem0 enhancements."""
        await self._build_graph(use_checkpointing=bool(self.checkpointer))

        if enable_performance_monitoring:
            logger.info("Performance monitoring enabled for mem0-enhanced graph")

        return self.graph

    async def run_coaching_session(
        self,
        user_message: str,
        thread_id: str,
        user_profile: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Run a coaching session with mem0 memory integration.

        Args:
            user_message: User's input message
            thread_id: Session thread identifier
            user_profile: Optional user profile data to initialize

        Returns:
            Response with personalized coaching advice
        """
        try:
            # Initialize user profile if provided
            if user_profile:
                await self.initialize_user_profile(user_profile)

            # Build graph if not already built
            if not self.graph:
                await self._build_graph()

            # Prepare initial state
            initial_state = {
                "user_query": user_message,
                "messages": [HumanMessage(content=user_message)],
                "user_id": self.user_id,
                "thread_id": thread_id,
            }

            # Create config for checkpointing
            config = (
                {"configurable": {"thread_id": thread_id}} if self.checkpointer else {}
            )

            # Run the graph
            result = await self.graph.ainvoke(initial_state, config=config)

            # Add session metadata
            session_metadata = {
                "user_id": self.user_id,
                "thread_id": thread_id,
                "timestamp": datetime.now().isoformat(),
                "memory_enabled": True,
                "personalization_active": bool(result.get("personalized_context")),
            }

            result["session_metadata"] = session_metadata

            logger.info(
                f"Completed mem0-enhanced coaching session for user {self.user_id}"
            )
            return result

        except Exception as e:
            logger.error(f"Error in mem0-enhanced coaching session: {e}")
            return {
                "error": str(e),
                "aggregated_response": "I apologize, but I encountered an error while processing your request. Please try again.",
                "session_metadata": {
                    "user_id": self.user_id,
                    "thread_id": thread_id,
                    "timestamp": datetime.now().isoformat(),
                    "error": True,
                },
            }

    async def get_user_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics for the user."""
        return await self.memory_adapter.get_memory_stats(self.user_id)

    async def get_user_profile_summary(self) -> Dict[str, Any]:
        """Get a summary of the user's profile."""
        return await self.memory_adapter.get_user_profile(self.user_id)


async def get_mem0_enhanced_coaching_graph(
    user_id: str,
    mongodb_uri: Optional[str] = None,
    environment: str = "development",
    enable_performance_monitoring: bool = True,
) -> Mem0EnhancedCoachingGraph:
    """
    Factory function to create a mem0-enhanced coaching graph.

    Args:
        user_id: Unique user identifier
        mongodb_uri: MongoDB connection string (optional, for checkpointing)
        environment: Environment name (development, staging, production)
        enable_performance_monitoring: Whether to enable performance monitoring

    Returns:
        Configured Mem0EnhancedCoachingGraph instance
    """
    graph = Mem0EnhancedCoachingGraph(
        user_id=user_id, mongodb_uri=mongodb_uri, environment=environment
    )

    await graph.create_compiled_graph(
        enable_performance_monitoring=enable_performance_monitoring
    )

    logger.info(f"Created mem0-enhanced coaching graph for user {user_id}")
    return graph


__all__ = ["Mem0EnhancedCoachingGraph", "get_mem0_enhanced_coaching_graph"]
