"""
Mem0 Memory Service for Athlea Coaching System

This service provides intelligent memory capabilities using Mem0,
enabling personalized and context-aware coaching interactions.
"""

import os
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class Mem0MemoryService:
    """
    Memory service using Mem0 for intelligent memory management in coaching sessions.

    Based on Mem0's research showing:
    - 26% higher accuracy than OpenAI Memory
    - 91% lower latency
    - 90% token savings
    """

    def __init__(self, use_api: bool = False):
        """
        Initialize Mem0 Memory Service.

        Args:
            use_api: If True, use MemoryClient (API), else use Memory (local)
        """
        self.use_api = use_api
        self.memory = None
        self._initialized = False

    async def _initialize_memory(self):
        """Initialize Mem0 memory instance (async to handle blocking operations)."""
        if self._initialized:
            return

        try:
            # Import and initialization in a separate thread to avoid blocking
            def _blocking_init():
                import mem0

                if self.use_api:
                    return mem0.MemoryClient()
                else:
                    return mem0.Memory()

            # Run the blocking initialization in a thread pool
            self.memory = await asyncio.to_thread(_blocking_init)

            if self.use_api:
                logger.info("✅ Mem0 MemoryClient initialized (API-based)")
            else:
                logger.info("✅ Mem0 Memory initialized (local with env vars)")

            self._initialized = True

        except ImportError:
            logger.error("❌ Mem0 not installed. Install with: pip install mem0ai")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to initialize Mem0: {e}")
            raise

    async def add_coaching_memory(
        self,
        content: str,
        user_id: str,
        session_type: str = "coaching",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Add a coaching memory to Mem0.

        Args:
            content: The memory content to store
            user_id: User identifier
            session_type: Type of session (coaching, onboarding, etc.)
            metadata: Additional metadata to store

        Returns:
            Result from Mem0 add operation
        """
        try:
            await self._initialize_memory()
            # Prepare metadata
            mem_metadata = {
                "session_type": session_type,
                "timestamp": datetime.now().isoformat(),
                "domain": "fitness_coaching",
                **(metadata or {}),
            }

            # Add memory to Mem0
            result = await asyncio.to_thread(
                self.memory.add, content, user_id=user_id, metadata=mem_metadata
            )

            logger.info(
                f"📝 Added coaching memory for user {user_id}: {content[:50]}..."
            )
            return result

        except Exception as e:
            logger.error(f"❌ Failed to add coaching memory: {e}")
            return {"error": str(e)}

    async def search_coaching_memories(
        self, query: str, user_id: str, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant coaching memories.

        Args:
            query: Search query
            user_id: User identifier
            limit: Maximum number of results

        Returns:
            List of relevant memories
        """
        try:
            await self._initialize_memory()
            memories = await asyncio.to_thread(
                self.memory.search, query, user_id=user_id, limit=limit
            )

            logger.info(f"🔍 Found {len(memories)} memories for query: {query[:30]}...")
            return memories

        except Exception as e:
            logger.error(f"❌ Failed to search memories: {e}")
            return []

    async def get_user_coaching_context(
        self, user_id: str, query: Optional[str] = None, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get coaching context for a user as a list of memories.

        Args:
            user_id: User identifier
            query: Optional specific query for context
            limit: Maximum number of memories to return

        Returns:
            List of relevant memories
        """
        try:
            await self._initialize_memory()
            if query:
                memories = await self.search_coaching_memories(
                    query, user_id, limit=limit
                )
            else:
                # Get recent memories
                raw_memories = await asyncio.to_thread(
                    self.memory.get_all, user_id=user_id
                )

                # Handle different return formats from Mem0
                if isinstance(raw_memories, dict):
                    # New format: {'results': [...]}
                    all_memories = raw_memories.get("results", [])
                elif isinstance(raw_memories, list):
                    # Old format: direct list
                    all_memories = raw_memories
                else:
                    logger.warning(
                        f"Unexpected memory format in context: {type(raw_memories)}"
                    )
                    all_memories = []

                memories = all_memories[-limit:] if all_memories else []

            return memories

        except Exception as e:
            logger.error(f"❌ Failed to get coaching context: {e}")
            return []

    async def update_user_preferences(
        self, user_id: str, preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update user preferences in memory.

        Args:
            user_id: User identifier
            preferences: User preferences to store

        Returns:
            Result from memory operation
        """
        try:
            # Convert preferences to memory-friendly format
            pref_text = "User preferences: " + ", ".join(
                [f"{key}: {value}" for key, value in preferences.items()]
            )

            return await self.add_coaching_memory(
                pref_text,
                user_id,
                session_type="preferences",
                metadata={"type": "user_preferences", **preferences},
            )

        except Exception as e:
            logger.error(f"❌ Failed to update user preferences: {e}")
            return {"error": str(e)}

    async def store_coaching_session_summary(
        self,
        user_id: str,
        session_summary: str,
        coach_recommendations: List[str],
        user_feedback: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Store a complete coaching session summary.

        Args:
            user_id: User identifier
            session_summary: Summary of the session
            coach_recommendations: List of coach recommendations
            user_feedback: Optional user feedback

        Returns:
            Result from memory operation
        """
        try:
            # Create comprehensive session memory
            session_content = f"Coaching Session Summary: {session_summary}"

            if coach_recommendations:
                session_content += (
                    f"\nRecommendations: {'; '.join(coach_recommendations)}"
                )

            if user_feedback:
                session_content += f"\nUser Feedback: {user_feedback}"

            return await self.add_coaching_memory(
                session_content,
                user_id,
                session_type="session_summary",
                metadata={
                    "type": "session_summary",
                    "recommendations_count": len(coach_recommendations),
                    "has_feedback": bool(user_feedback),
                },
            )

        except Exception as e:
            logger.error(f"❌ Failed to store session summary: {e}")
            return {"error": str(e)}

    async def get_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a user.

        Args:
            user_id: User identifier

        Returns:
            Memory statistics
        """
        try:
            await self._initialize_memory()
            # Get memory stats
            stats = await asyncio.to_thread(self.memory.get_stat, user_id=user_id)
            return stats

        except Exception as e:
            logger.error(f"❌ Failed to get memory stats: {e}")
            return {"error": str(e)}

    async def delete_memory(self, memory_id: str) -> Dict[str, Any]:
        """
        Delete a memory from Mem0.

        Args:
            memory_id: ID of the memory to delete

        Returns:
            Result from memory operation
        """
        try:
            # Delete memory
            await asyncio.to_thread(self.memory.delete, memory_id=memory_id)
            logger.info(f"🗑️ Deleted memory with ID: {memory_id}")
            return {"status": "success"}

        except Exception as e:
            logger.error(f"❌ Failed to delete memory: {e}")
            return {"error": str(e)}


# Singleton instance for easy access
_mem0_service: Optional[Mem0MemoryService] = None


async def get_mem0_service(use_api: bool = False) -> Mem0MemoryService:
    """
    Get or create the Mem0 memory service instance.

    Args:
        use_api: Whether to use API-based or local memory

    Returns:
        Mem0MemoryService instance
    """
    global _mem0_service

    if _mem0_service is None:
        _mem0_service = Mem0MemoryService(use_api=use_api)
        # Initialize the memory service asynchronously
        await _mem0_service._initialize_memory()

    return _mem0_service
