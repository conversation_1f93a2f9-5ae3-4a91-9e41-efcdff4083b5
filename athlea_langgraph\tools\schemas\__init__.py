"""
Pydantic schemas for all hardened tools.
"""

from .azure_maps_schemas import (
    Address,
    AzureMapsError,
    AzureMapsInput,
    AzureMapsOutput,
    GeocodeResult,
    LocationResult,
    POIInfo,
    Position,
    WeatherMetric,
    WeatherResult,
)
from .cardio_schemas import (
    CalculatePaceZonesSchema,
    CalculateVo2MaxEstimateSchema,
    GenerateRunningRouteSchema,
    PlanIntervalWorkoutSchema,
)
from .elevation_schemas import (
    ElevationError,
    ElevationInput,
    ElevationOutput,
    ElevationPoint,
    ElevationResult,
)
from .external_schemas import (
    AirtableQuerySchema,
)
from .nutrition_schemas import (
    CalculateDailyCaloriesSchema,
    GenerateMealPlanSchema,
    TrackHydrationSchema,
)
from .recovery_schemas import (
    ActivityType,
    ExerciseTypeFilter,
    MobilityArea,
    MobilityExercise,
    MobilityProtocolInput,
    MobilityProtocolOutput,
    MovementScreenInput,
    MovementScreenOutput,
    RecoveryGoal,
    SleepAssessmentInput,
    SleepOptimizationOutput,
    WellnessAssessmentInput,
    WellnessTrackingOutput,
)
from .strength_schemas import (
    GenerateStrengthProgramSchema,
    SearchStrengthExercisesSchema,
)

__all__ = [
    # Elevation schemas
    "ElevationInput",
    "ElevationOutput",
    "ElevationPoint",
    "ElevationResult",
    "ElevationError",
    # Azure Maps schemas
    "AzureMapsInput",
    "AzureMapsOutput",
    "GeocodeResult",
    "WeatherResult",
    "LocationResult",
    "Position",
    "Address",
    "WeatherMetric",
    "POIInfo",
    "AzureMapsError",
    # Recovery schemas
    "MobilityProtocolInput",
    "MobilityProtocolOutput",
    "MobilityExercise",
    "SleepAssessmentInput",
    "SleepOptimizationOutput",
    "WellnessAssessmentInput",
    "WellnessTrackingOutput",
    "MovementScreenInput",
    "MovementScreenOutput",
    "ExerciseTypeFilter",
    "MobilityArea",
    "ActivityType",
    "RecoveryGoal",
    # Cardio schemas
    "CalculatePaceZonesSchema",
    "CalculateVo2MaxEstimateSchema",
    "GenerateRunningRouteSchema",
    "PlanIntervalWorkoutSchema",
    # Strength schemas
    "GenerateStrengthProgramSchema",
    "SearchStrengthExercisesSchema",
    # Nutrition schemas
    "CalculateDailyCaloriesSchema",
    "GenerateMealPlanSchema",
    "TrackHydrationSchema",
    # External schemas
    "AirtableQuerySchema",
]
