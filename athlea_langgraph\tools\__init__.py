"""
Hardened Tools Module

This module provides production-ready tools with:
- Schema validation using Pydantic
- Circuit breakers for external services
- Timeout protection and retries
- Structured error handling
- Comprehensive logging and metrics
"""

from .base_tool import HardenedTool, ToolError, ToolResponse
from .circuit_breaker import CircuitBreaker, CircuitState
from .external.azure_maps import AzureMapsTool
from .external.azure_search_retriever import AzureSearchRetrieverTool
from .external.google_maps_elevation import GoogleMapsElevationTool
from .external.session_generation import SessionGenerationTool

__all__ = [
    "HardenedTool",
    "ToolResponse",
    "ToolError",
    "CircuitBreaker",
    "CircuitState",
    "GoogleMapsElevationTool",
    "AzureMapsTool",
    "SessionGenerationTool",
    "AzureSearchRetrieverTool",
]
