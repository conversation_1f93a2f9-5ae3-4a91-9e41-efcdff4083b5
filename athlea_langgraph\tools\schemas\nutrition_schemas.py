"""
Nutrition Domain Schemas

Pydantic schemas for strict input/output validation of nutrition tools.
These schemas enforce tool-agent contracts and automatic validation.
"""

from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, field_validator, ConfigDict


class Gender(str, Enum):
    """Gender options for BMR calculations."""

    male = "male"
    female = "female"


class ActivityLevel(str, Enum):
    """Physical activity levels for TDEE calculation."""

    sedentary = "sedentary"
    lightly_active = "lightly_active"
    moderately_active = "moderately_active"
    very_active = "very_active"
    extremely_active = "extremely_active"


class NutritionGoal(str, Enum):
    """Nutrition and fitness goals."""

    weight_loss = "weight_loss"
    aggressive_weight_loss = "aggressive_weight_loss"
    weight_gain = "weight_gain"
    muscle_gain = "muscle_gain"
    maintenance = "maintenance"
    body_recomposition = "body_recomposition"
    performance = "performance"
    cutting = "cutting"
    bulking = "bulking"


class DietaryPreference(str, Enum):
    """Dietary preferences and restrictions."""

    high_protein = "high_protein"
    low_carb = "low_carb"
    keto = "keto"
    paleo = "paleo"
    vegetarian = "vegetarian"
    vegan = "vegan"
    mediterranean = "mediterranean"
    intermittent_fasting = "intermittent_fasting"
    balanced = "balanced"


class ProteinPreference(str, Enum):
    """Protein intake preferences."""

    low = "low"
    moderate = "moderate"
    high = "high"
    very_high = "very_high"


class MealType(str, Enum):
    """Types of meals for planning."""

    breakfast = "breakfast"
    lunch = "lunch"
    dinner = "dinner"
    snack = "snack"
    pre_workout = "pre_workout"
    post_workout = "post_workout"


# Input Schemas
class CalorieCalculationInput(BaseModel):
    """Input schema for calorie calculation."""

    age: int = Field(..., ge=13, le=100, description="Age in years")

    gender: Gender = Field(..., description="Biological gender for BMR calculation")

    weight_kg: float = Field(
        ..., gt=0, le=300, description="Current weight in kilograms"
    )

    height_cm: float = Field(..., gt=0, le=250, description="Height in centimeters")

    activity_level: ActivityLevel = Field(..., description="Physical activity level")

    goal: NutritionGoal = Field(..., description="Primary nutrition/fitness goal")

    body_fat_percentage: Optional[float] = Field(
        None, ge=3, le=50, description="Body fat percentage (if known)"
    )

    @field_validator("weight_kg")
    @classmethod
    def validate_weight(cls, v):
        if v < 30 or v > 300:
            raise ValueError("Weight must be between 30-300 kg")
        return v

    @field_validator("height_cm")
    @classmethod
    def validate_height(cls, v):
        if v < 100 or v > 250:
            raise ValueError("Height must be between 100-250 cm")
        return v


class MacroTargetsInput(BaseModel):
    """Input schema for macro target calculation."""

    target_calories: int = Field(
        ..., ge=800, le=5000, description="Target daily calories"
    )

    goal: NutritionGoal = Field(..., description="Primary nutrition goal")

    dietary_preferences: Optional[List[DietaryPreference]] = Field(
        None, description="Dietary preferences and restrictions"
    )

    protein_preference: Optional[ProteinPreference] = Field(
        None, description="Preferred protein intake level"
    )

    weight_kg: Optional[float] = Field(
        None, gt=0, description="Body weight for protein calculations"
    )

    training_days_per_week: Optional[int] = Field(
        None, ge=0, le=7, description="Number of training days per week"
    )

    @field_validator("dietary_preferences")
    @classmethod
    def validate_dietary_preferences(cls, v):
        if v and len(v) > 4:
            raise ValueError("Cannot specify more than 4 dietary preferences")
        return v

    @field_validator("target_calories")
    @classmethod
    def validate_calories(cls, v):
        if v < 800:
            raise ValueError("Target calories too low for safe nutrition")
        return v


class MealPlanningInput(BaseModel):
    """Input schema for meal planning."""

    daily_calories: int = Field(
        ..., ge=800, le=5000, description="Daily calorie target"
    )

    protein_grams: float = Field(
        ..., ge=20, le=400, description="Daily protein target in grams"
    )

    carb_grams: float = Field(
        ..., ge=0, le=800, description="Daily carbohydrate target in grams"
    )

    fat_grams: float = Field(
        ..., ge=10, le=300, description="Daily fat target in grams"
    )

    meals_per_day: int = Field(3, ge=1, le=8, description="Number of meals per day")

    dietary_restrictions: Optional[List[str]] = Field(
        None, description="Food allergies and restrictions"
    )

    preferred_foods: Optional[List[str]] = Field(
        None, description="Preferred foods to include"
    )

    cooking_time_preference: Optional[str] = Field(
        "moderate", description="Preferred cooking time (quick/moderate/elaborate)"
    )

    @field_validator("meals_per_day")
    @classmethod
    def validate_meals(cls, v):
        if v < 1 or v > 8:
            raise ValueError("Meals per day must be between 1-8")
        return v


class NutritionAssessmentInput(BaseModel):
    """Input schema for nutrition assessment."""

    current_diet_description: str = Field(
        ..., min_length=10, description="Description of current eating habits"
    )

    health_goals: List[str] = Field(..., description="Health and fitness goals")

    dietary_restrictions: Optional[List[str]] = Field(
        None, description="Allergies, intolerances, ethical restrictions"
    )

    budget_preference: Optional[str] = Field(
        None, description="Budget level (low/moderate/high)"
    )

    cooking_skill_level: Optional[str] = Field(
        "beginner", description="Cooking skill level"
    )

    time_availability: Optional[str] = Field(
        None, description="Available time for meal prep"
    )

    @field_validator("health_goals")
    @classmethod
    def validate_goals(cls, v):
        if len(v) < 1:
            raise ValueError("Must specify at least one health goal")
        if len(v) > 5:
            raise ValueError("Cannot specify more than 5 health goals")
        return v


# Output Schemas
class CalorieCalculationOutput(BaseModel):
    """Output schema for calorie calculation results."""

    bmr: int = Field(..., description="Basal Metabolic Rate in calories")
    tdee: int = Field(..., description="Total Daily Energy Expenditure")
    target_calories: int = Field(..., description="Recommended daily calories for goal")
    formula_used: str = Field(..., description="BMR formula used")
    activity_multiplier: float = Field(..., description="Activity level multiplier")
    goal_adjustment: float = Field(..., description="Calorie adjustment for goal")
    recommendations: List[str] = Field(
        ..., description="Calorie-related recommendations"
    )
    error: Optional[str] = Field(
        None, description="Error message if calculation failed"
    )


class MacroTargetsOutput(BaseModel):
    """Output schema for macro target results."""

    target_calories: int = Field(..., description="Daily calorie target")
    protein_grams: float = Field(..., description="Daily protein target in grams")
    carb_grams: float = Field(..., description="Daily carbohydrate target in grams")
    fat_grams: float = Field(..., description="Daily fat target in grams")
    protein_percentage: float = Field(
        ..., description="Protein as percentage of calories"
    )
    carb_percentage: float = Field(..., description="Carbs as percentage of calories")
    fat_percentage: float = Field(..., description="Fat as percentage of calories")
    distribution_rationale: str = Field(..., description="Explanation of macro split")
    meal_distribution: Dict[str, Any] = Field(
        ..., description="How to distribute across meals"
    )
    error: Optional[str] = Field(
        None, description="Error message if calculation failed"
    )


class FoodItem(BaseModel):
    """Individual food item with nutritional info."""

    name: str = Field(..., description="Food item name")
    quantity: str = Field(..., description="Serving size")
    calories: float = Field(..., description="Calories per serving")
    protein: float = Field(..., description="Protein grams per serving")
    carbs: float = Field(..., description="Carb grams per serving")
    fat: float = Field(..., description="Fat grams per serving")
    fiber: Optional[float] = Field(None, description="Fiber grams per serving")
    preparation_notes: Optional[str] = Field(
        None, description="Preparation instructions"
    )


class MealPlan(BaseModel):
    """Individual meal plan."""

    meal_type: str = Field(..., description="Type of meal")
    foods: List[FoodItem] = Field(..., description="Foods in this meal")
    total_calories: float = Field(..., description="Total calories in meal")
    total_protein: float = Field(..., description="Total protein in meal")
    total_carbs: float = Field(..., description="Total carbs in meal")
    total_fat: float = Field(..., description="Total fat in meal")
    preparation_time: Optional[str] = Field(None, description="Estimated prep time")
    cooking_instructions: Optional[List[str]] = Field(None, description="Cooking steps")


class MealPlanningOutput(BaseModel):
    """Output schema for meal planning results."""

    daily_meal_plan: List[MealPlan] = Field(..., description="Complete daily meal plan")
    daily_totals: Dict[str, float] = Field(..., description="Daily nutritional totals")
    target_adherence: Dict[str, float] = Field(
        ..., description="How well plan meets targets"
    )
    shopping_list: List[str] = Field(..., description="Required ingredients")
    meal_prep_tips: List[str] = Field(
        ..., description="Meal preparation recommendations"
    )
    error: Optional[str] = Field(None, description="Error message if planning failed")


class NutritionAssessmentOutput(BaseModel):
    """Output schema for nutrition assessment results."""

    assessment_summary: str = Field(..., description="Overall diet assessment")
    strengths: List[str] = Field(..., description="Current diet strengths")
    areas_for_improvement: List[str] = Field(
        ..., description="Areas needing improvement"
    )
    recommended_changes: List[str] = Field(
        ..., description="Specific recommended changes"
    )
    priority_actions: List[str] = Field(
        ..., description="Most important actions to take"
    )
    estimated_timeline: str = Field(
        ..., description="Timeline for implementing changes"
    )
    success_metrics: List[str] = Field(..., description="How to track progress")
    error: Optional[str] = Field(None, description="Error message if assessment failed")


# Tool Contract Enforcement
class NutritionToolContract(BaseModel):
    """Enforces tool contracts for nutrition domain."""

    tool_name: str = Field(..., description="Name of the tool")
    input_schema: type = Field(..., description="Required input schema")
    output_schema: type = Field(..., description="Expected output schema")
    required_permissions: List[str] = Field(
        ..., description="Required agent permissions"
    )
    domain: str = Field("nutrition", description="Tool domain")

    model_config = ConfigDict(arbitrary_types_allowed=True)


# Available tool contracts for nutrition domain
NUTRITION_TOOL_CONTRACTS = {
    "calorie_calculator": NutritionToolContract(
        tool_name="calorie_calculator",
        input_schema=CalorieCalculationInput,
        output_schema=CalorieCalculationOutput,
        required_permissions=["calculate_calories", "access_user_profile"],
    ),
    "macro_targets": NutritionToolContract(
        tool_name="macro_targets",
        input_schema=MacroTargetsInput,
        output_schema=MacroTargetsOutput,
        required_permissions=["calculate_macros", "access_user_profile"],
    ),
    "meal_planner": NutritionToolContract(
        tool_name="meal_planner",
        input_schema=MealPlanningInput,
        output_schema=MealPlanningOutput,
        required_permissions=["plan_meals", "access_food_database"],
    ),
    "nutrition_assessment": NutritionToolContract(
        tool_name="nutrition_assessment",
        input_schema=NutritionAssessmentInput,
        output_schema=NutritionAssessmentOutput,
        required_permissions=["assess_nutrition", "provide_recommendations"],
    ),
}


class CalculateDailyCaloriesSchema(BaseModel):
    """Schema for daily calorie calculation tool."""

    age: int = Field(..., ge=13, le=100, description="Age in years")
    gender: str = Field(..., description="Gender (male, female, other)")
    weight_kg: float = Field(..., gt=0, le=300, description="Body weight in kilograms")
    height_cm: float = Field(..., gt=0, le=250, description="Height in centimeters")
    activity_level: str = Field(
        ...,
        description="Activity level (sedentary, lightly_active, moderately_active, very_active, extremely_active)",
    )
    goal: str = Field(
        ...,
        description="Fitness goal (maintain, lose_weight, gain_weight, muscle_gain)",
    )
    body_fat_percentage: Optional[float] = Field(
        None, ge=3, le=50, description="Body fat percentage if known"
    )
    training_days_per_week: Optional[int] = Field(
        None, ge=0, le=7, description="Number of training days per week"
    )


class GenerateMealPlanSchema(BaseModel):
    """Schema for meal plan generation tool."""

    calorie_target: int = Field(
        ..., ge=800, le=6000, description="Daily calorie target"
    )
    dietary_restrictions: Optional[List[str]] = Field(
        None,
        description="List of dietary restrictions (vegetarian, vegan, gluten-free, etc.)",
    )
    preferences: Optional[List[str]] = Field(
        None, description="List of food preferences"
    )
    meals_per_day: Optional[int] = Field(
        3, ge=1, le=6, description="Number of meals per day"
    )
    days: Optional[int] = Field(
        1, ge=1, le=14, description="Number of days to plan for"
    )


class TrackHydrationSchema(BaseModel):
    """Schema for hydration tracking tool."""

    amount_ml: int = Field(
        ..., gt=0, description="Amount of water consumed in milliliters"
    )
    time_of_day: str = Field(
        ..., description="Time of day (morning, afternoon, evening, night)"
    )
    beverage_type: Optional[str] = Field(
        "water", description="Type of beverage (water, sports_drink, tea, coffee, etc.)"
    )
