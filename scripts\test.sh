#!/bin/bash
# test.sh - Comprehensive testing for python-langgraph

echo "🧪 Running Python test suite..."

# Ensure we are in the script's directory's parent (project root)
# cd "$(dirname "$0")" || exit

# Activate virtual environment if present and not already active
# This depends on your specific venv setup.
# If you typically run `poetry shell` first, this might not be needed.
# if [ -d "venv" ] && [ -z "$VIRTUAL_ENV" ]; then
#     echo "🚀 Activating virtual environment..."
#     source venv/bin/activate
# fi

# Option 1: Run pytest directly using Poetry
# This respects pyproject.toml configuration and markers.
# The -v flag increases verbosity.
# --tb=short provides a shorter traceback format.
echo "Running pytest with Poetry..."
poetry run pytest -v --tb=short

# Option 2: Use your advanced Phase 3 runner for a specific suite (e.g., 'all' or 'fast')
# Uncomment the one you prefer as a default, or add logic to select.
# echo "Running Phase 3 'fast' tests via runner script..."
# poetry run python tests/runners/run_phase3_tests.py fast

# echo "Running Phase 3 'all' tests via runner script..."
# poetry run python tests/runners/run_phase3_tests.py all

# Option 3: Run for coverage (can be slower)
# echo "Running tests with coverage report..."
# poetry run python tests/runners/run_phase3_tests.py coverage
# echo "Coverage report usually generated in htmlcov/"

TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ All executed tests passed!"
else
    echo "❌ Some tests failed. Review the output above."
fi

exit $TEST_EXIT_CODE 