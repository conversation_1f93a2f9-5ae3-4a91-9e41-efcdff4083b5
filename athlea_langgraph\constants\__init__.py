"""
Constants module for Athlea LangGraph.
Provides centralized access to constants, enums, defaults, and message templates.
"""

from .enums import (
    IntensityLevel,
    TimeUnit,
    HTTPContentType,
    LogLevel,
    OperationStatus,
    QueryType,
    SortDirection,
    ValidationMode,
)

from .defaults import (
    DEFAULT_TIMEOUT_SECONDS,
    DEFAULT_API_TIMEOUT_SECONDS,
    DEFAULT_SYNTHESIS_TIMEOUT_SECONDS,
    DEFAULT_TASK_TIMEOUT_SECONDS,
    DEFAULT_SESSION_DURATION_MINUTES,
    DEFAULT_RECOVERY_DURATION_MINUTES,
    DEFAULT_MOBILITY_DURATION_SECONDS,
    DEFAULT_REST_PERIOD_SECONDS,
    DEFAULT_MAX_RETRIES,
    DEFAULT_BATCH_SIZE,
    DEFAULT_PAGE_SIZE,
    DEFAULT_MAX_RESULTS,
    DEFAULT_SETS,
    DEFAULT_REPS,
    DEFAULT_INTENSITY,
    DEFAULT_SKILL_CATEGORY,
    DEFAULT_MEALS_PER_DAY,
    DEFAULT_CALORIE_RANGE_MIN,
    DEFAULT_CALORIE_RANGE_MAX,
    DEFAULT_CHUNK_DELAY_MS,
    DEFAULT_NODE_TRANSITION_DELAY_MS,
    DEFAULT_BUFFER_SIZE,
    DEFAULT_FLUSH_INTERVAL_MS,
    DEFAULT_CACHE_TTL_SECONDS,
    DEFAULT_MEMORY_RETENTION_DAYS,
    DEFAULT_BATCH_PROCESSING_SIZE,
    DEFAULT_HEALTH_CHECK_INTERVAL_MINUTES,
    DEFAULT_ERROR_RATE_THRESHOLD,
    DEFAULT_LATENCY_THRESHOLD_MS,
    DEFAULT_ENCODING,
    DEFAULT_DATE_FORMAT,
    DEFAULT_DATETIME_FORMAT,
    DEFAULT_CURRENT_5K_TIME,
    DEFAULT_SLEEP_HOURS,
    DEFAULT_MICRO_HABIT_DURATION,
    DEFAULT_HOLD_DURATION,
    DEFAULT_HOLD_DURATION_RANGE,
    DEFAULT_REST_PERIOD_RANGE,
    DEFAULT_EXTENDED_REST_PERIOD,
    DEFAULT_LONG_REST_PERIOD,
    DEFAULT_EXERCISE_LIMIT,
    DEFAULT_MAX_PARALLEL_WORKERS,
    DEFAULT_MAX_DISTANCE_KM,
    DEFAULT_SEARCH_RADIUS_METERS,
)

from .messages import (
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    COACHING_RESPONSES,
    THINKING_MESSAGES,
    PROGRESS_MESSAGES,
    FALLBACK_MESSAGES,
    VALIDATION_MESSAGES,
    SPECIALIST_EMOJIS,
    DEFAULT_RECOMMENDATIONS,
)

__all__ = [
    # Enums
    "IntensityLevel",
    "TimeUnit",
    "HTTPContentType",
    "LogLevel",
    "OperationStatus",
    "QueryType",
    "SortDirection",
    "ValidationMode",
    # Timeout defaults
    "DEFAULT_TIMEOUT_SECONDS",
    "DEFAULT_API_TIMEOUT_SECONDS",
    "DEFAULT_SYNTHESIS_TIMEOUT_SECONDS",
    "DEFAULT_TASK_TIMEOUT_SECONDS",
    # Duration defaults
    "DEFAULT_SESSION_DURATION_MINUTES",
    "DEFAULT_RECOVERY_DURATION_MINUTES",
    "DEFAULT_MOBILITY_DURATION_SECONDS",
    "DEFAULT_REST_PERIOD_SECONDS",
    # API operation defaults
    "DEFAULT_MAX_RETRIES",
    "DEFAULT_BATCH_SIZE",
    "DEFAULT_PAGE_SIZE",
    "DEFAULT_MAX_RESULTS",
    # Training defaults
    "DEFAULT_SETS",
    "DEFAULT_REPS",
    "DEFAULT_INTENSITY",
    "DEFAULT_SKILL_CATEGORY",
    # Nutrition defaults
    "DEFAULT_MEALS_PER_DAY",
    "DEFAULT_CALORIE_RANGE_MIN",
    "DEFAULT_CALORIE_RANGE_MAX",
    # Streaming defaults
    "DEFAULT_CHUNK_DELAY_MS",
    "DEFAULT_NODE_TRANSITION_DELAY_MS",
    "DEFAULT_BUFFER_SIZE",
    "DEFAULT_FLUSH_INTERVAL_MS",
    # Cache and memory defaults
    "DEFAULT_CACHE_TTL_SECONDS",
    "DEFAULT_MEMORY_RETENTION_DAYS",
    "DEFAULT_BATCH_PROCESSING_SIZE",
    # Health check defaults
    "DEFAULT_HEALTH_CHECK_INTERVAL_MINUTES",
    "DEFAULT_ERROR_RATE_THRESHOLD",
    "DEFAULT_LATENCY_THRESHOLD_MS",
    # Format defaults
    "DEFAULT_ENCODING",
    "DEFAULT_DATE_FORMAT",
    "DEFAULT_DATETIME_FORMAT",
    # Domain-specific defaults
    "DEFAULT_CURRENT_5K_TIME",
    "DEFAULT_SLEEP_HOURS",
    "DEFAULT_MICRO_HABIT_DURATION",
    "DEFAULT_HOLD_DURATION",
    "DEFAULT_HOLD_DURATION_RANGE",
    "DEFAULT_REST_PERIOD_RANGE",
    "DEFAULT_EXTENDED_REST_PERIOD",
    "DEFAULT_LONG_REST_PERIOD",
    # System limits
    "DEFAULT_EXERCISE_LIMIT",
    "DEFAULT_MAX_PARALLEL_WORKERS",
    "DEFAULT_MAX_DISTANCE_KM",
    "DEFAULT_SEARCH_RADIUS_METERS",
    # Message templates
    "ERROR_MESSAGES",
    "SUCCESS_MESSAGES",
    "COACHING_RESPONSES",
    "THINKING_MESSAGES",
    "PROGRESS_MESSAGES",
    "FALLBACK_MESSAGES",
    "VALIDATION_MESSAGES",
    "SPECIALIST_EMOJIS",
    "DEFAULT_RECOMMENDATIONS",
]
