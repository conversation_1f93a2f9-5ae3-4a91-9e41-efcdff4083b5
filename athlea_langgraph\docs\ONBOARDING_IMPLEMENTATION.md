# Onboarding Graph Implementation

## Overview

The onboarding graph implementation provides a comprehensive user onboarding flow that guides users through the process of gathering their fitness information and generating a personalized plan. This implementation mirrors the TypeScript frontend onboarding flow while adapting it for the Python LangGraph backend.

## Architecture

### Key Components

1. **OnboardingState**: TypedDict-based state schema that manages all onboarding data
2. **Four Core Nodes**: Information gathering, completion checking, input handling, and plan generation
3. **Graph Factory**: Centralized management for multiple graph types
4. **Structured Output**: JSON schema-based plan generation

### Graph Flow

```
START → gatherInfo → checkCompletion → [generatePlan | needInput] → END
                                         ↑                    ↓
                                   (enough info)        (HITL interrupt)
```

## Implementation Details

### 1. State Management (`schemas/onboarding_state.py`)

The onboarding state includes:

- **Core Fields**: messages, user_id, onboarding_stage
- **User Profile**: goal, experience_level, time_commitment, equipment
- **Tracking Data**: sidebar_data with goals, summary_items, sport_suggestions
- **Control Flags**: has_enough_info, needs_input, requires_input
- **Generated Content**: generated_plan, sport_suggestions

Key Features:
- Pydantic models for structured data validation
- Sport suggestions with predefined options
- Comprehensive plan structure with phases and example sessions
- State reducers for message handling

### 2. Information Gatherer Node (`agents/onboarding/information_gatherer_node.py`)

**Primary Function**: Manages the main conversation flow and information extraction.

**Key Features**:
- **Dynamic System Prompts**: Adapts prompts based on conversation stage and selected sports
- **Goal Extraction**: LLM-based extraction of user fitness goals
- **Summary Generation**: Structured extraction of user information into categorized summary items
- **Sport Selection**: Handles sport selection from predefined suggestions
- **Multi-stage Conversation**: Manages both initial greeting and ongoing information gathering

**Implementation Pattern**:
```python
class InformationGathererNode:
    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        # 1. Handle sport selection
        # 2. Extract goals from conversation
        # 3. Extract structured summary
        # 4. Generate conversational response
        # 5. Update state
```

### 3. Check Completion Node (`agents/onboarding/check_completion_node.py`)

**Primary Function**: Determines if enough information has been gathered to proceed with plan generation.

**Validation Criteria**:
1. Specific fitness goals mentioned
2. Experience level stated
3. Time commitment details provided
4. Equipment access mentioned
5. Priorities/connections/seasonality information
6. User readiness confirmed in last message

**Implementation**:
- Low temperature (0.0) for deterministic checking
- Strict prompt requiring explicit user information
- Boolean response validation

### 4. Need Input Node (`agents/onboarding/need_input_node.py`)

**Primary Function**: Handles human-in-the-loop interrupts when more information is needed.

**Features**:
- Sets appropriate flags for UI handling
- Uses LangGraph's `interrupt()` function for proper HITL
- Provides clear messaging about what information is needed

### 5. Generate Plan Node (`agents/onboarding/generate_plan_node.py`)

**Primary Function**: Creates a structured fitness plan based on gathered information.

**Features**:
- **Structured Output**: Uses JSON schema for consistent plan format
- **Plan Components**: Includes phases, example sessions, rationale
- **Error Handling**: Graceful degradation with error states
- **Comprehensive Plans**: Generates UUID, disciplines, levels, durations

**Plan Structure**:
```python
class PlanDetails(BaseModel):
    plan_id: str  # UUID
    name: str     # Catchy plan name
    description: str
    duration: str
    level: str    # Beginner/Intermediate/Advanced
    plan_type: str
    disciplines: List[str]
    rationale: str
    phases: List[PlanPhase]
    example_sessions: List[ExampleSession]
```

### 6. Onboarding Graph (`graphs/onboarding_graph.py`)

**Primary Function**: Orchestrates the onboarding flow with proper edge routing.

**Graph Structure**:
- **Nodes**: gatherInfo, checkCompletion, needInput, generatePlan
- **Edges**: Linear flow with conditional routing after completion check
- **HITL Support**: Proper interrupt handling for user input
- **State Persistence**: Checkpoint support for conversation continuity

### 7. Graph Factory (`graph_factory.py`)

**Primary Function**: Centralized management of multiple graph types.

**Features**:
- **Multiple Graph Types**: Supports coaching and onboarding graphs
- **Checkpointer Management**: Memory and MongoDB checkpointer support
- **Caching**: Efficient graph instance caching
- **Type Safety**: Enum-based graph type management

## Usage Examples

### Basic Onboarding Flow

```python
from athlea_langgraph import (
    create_onboarding_graph_factory,
    create_initial_onboarding_state
)
from langchain_core.messages import HumanMessage

# Create graph and initial state
graph = create_onboarding_graph_factory(checkpointer_type="memory")
state = create_initial_onboarding_state("user_123")

# Add user message
state["messages"] = [HumanMessage(content="Hi, I want to start running!")]
state["user_input"] = "Hi, I want to start running!"

# Execute graph
config = {"configurable": {"thread_id": "thread_123"}}
result = await graph.ainvoke(state, config)
```

### Using Graph Factory

```python
from athlea_langgraph import get_graph_by_type

# Get different graph types
onboarding_graph = get_graph_by_type("onboarding", checkpointer_type="memory")
coaching_graph = get_graph_by_type("coaching", checkpointer_type="mongo",
                                 connection_string="mongodb://localhost:27017")
```

### Sport Selection Flow

```python
# Initial greeting
state = create_initial_onboarding_state("user_123")
state["messages"] = [HumanMessage(content="Hi!")]
result1 = await graph.ainvoke(state, config)

# Sport selection from suggestions
state["messages"].append(HumanMessage(content="Running"))
state["user_input"] = "Running"
result2 = await graph.ainvoke(state, config)
```

## Key Design Decisions

### 1. State Architecture
- **TypedDict over Pydantic**: Maintains compatibility with LangGraph while providing type safety
- **Nested Pydantic Models**: Complex data structures use Pydantic for validation
- **Sidebar Data Pattern**: Mirrors frontend state management for UI consistency

### 2. LLM Integration
- **Multiple LLM Calls**: Separate calls for goal extraction, summary generation, and conversation
- **Structured Output**: JSON schema enforcement for plan generation
- **Temperature Control**: Different temperatures for creative vs. deterministic tasks

### 3. Error Handling
- **Graceful Degradation**: Continues operation even if individual components fail
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **State Recovery**: Proper error states for UI handling

### 4. Human-in-the-Loop
- **LangGraph Interrupts**: Native interrupt support for proper HITL implementation
- **Resume Capability**: State persistence allows conversation resumption
- **Clear Messaging**: Explicit flags and prompts for UI guidance

## Integration with Frontend

### State Synchronization
The Python implementation maintains compatibility with the TypeScript frontend:

- **Sidebar Data**: Mirrors frontend sidebar state structure
- **Sport Suggestions**: Same format and options as frontend
- **Plan Structure**: Compatible JSON format for plan details
- **Progress Tracking**: Same stage names and progression logic

### API Compatibility
- **Message Format**: Compatible with LangChain message types
- **State Updates**: Incremental updates that can be streamed to frontend
- **Error Handling**: Consistent error states and messaging

## Testing

### Test Coverage
The implementation includes comprehensive tests:

1. **State Creation**: Validation of initial state structure
2. **Basic Flow**: End-to-end onboarding process
3. **Sport Selection**: Handling of sport choice logic
4. **Graph Factory**: Multi-graph management
5. **Error Cases**: Graceful handling of failures

### Running Tests

```bash
cd /Users/<USER>/python-langgraph
python test_onboarding_graph.py
```

## Performance Considerations

### Optimization Features
- **Graph Caching**: Factory-based caching of compiled graphs
- **Checkpointer Reuse**: Shared checkpointer instances
- **Lazy Loading**: Graphs compiled only when needed
- **Parallel Processing**: Multiple LLM calls can be optimized for concurrency

### Monitoring
- **Comprehensive Logging**: Detailed execution logs
- **Performance Metrics**: Timing and success rate tracking
- **Error Tracking**: Detailed error logging and reporting

## Future Enhancements

### Planned Features
1. **Memory Integration**: Long-term user profile storage
2. **Advanced Planning**: More sophisticated plan generation algorithms
3. **Multi-modal Support**: Image and voice input handling
4. **Personalization**: ML-based recommendation improvements

### Extension Points
- **Custom Validators**: Additional completion criteria
- **Plugin Architecture**: Custom node implementations
- **External Integrations**: Fitness API connections
- **Advanced Analytics**: User behavior tracking and optimization

## Conclusion

The onboarding graph implementation provides a robust, scalable foundation for user onboarding in the Athlea LangGraph system. It successfully mirrors the frontend functionality while providing additional capabilities like structured output, comprehensive error handling, and multi-graph management. The implementation follows LangGraph best practices and maintains compatibility with the existing coaching system architecture. 