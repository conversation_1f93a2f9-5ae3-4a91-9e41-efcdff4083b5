#!/usr/bin/env python3
"""
Test script for the prompt management system.
Tests prompt creation, loading, and rendering.
"""

import logging
import sys
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)


def test_prompt_system():
    """Test the complete prompt management system."""

    try:
        logger.info("🧪 Starting prompt system tests...")

        # Test 1: Import prompt modules directly
        logger.info("📦 Test 1: Testing imports...")
        from athlea_langgraph.utils.prompt_loader import PromptLoader
        from athlea_langgraph.utils.prompt_migration import PromptMigrator
        from athlea_langgraph.utils.prompt_models import (
            PromptConfig,
            PromptContent,
            PromptMetadata,
            PromptType,
        )

        print("✅ All prompt modules imported successfully!")

        # Test 2: Create prompt loader
        logger.info("🔧 Test 2: Creating PromptLoader...")
        prompts_dir = Path("athlea_langgraph/prompts")
        loader = PromptLoader(str(prompts_dir))
        loader.initialize()
        print("✅ PromptLoader created and initialized!")

        # Test 3: Create a sample prompt directly
        logger.info("📝 Test 3: Creating sample prompt...")
        migrator = PromptMigrator(prompts_dir)

        sample_prompt_content = """You are a Strength Training Coach specializing in resistance training, powerlifting, bodybuilding, and functional strength development.

Your expertise includes:
- Exercise selection and progression
- Program design for different goals (strength, hypertrophy, power)
- Proper form and technique
- Equipment recommendations
- Injury prevention and modification
- Periodization and programming

You have access to specialized tools for exercise databases and program generation.
Always provide evidence-based advice and consider the user's experience level, goals, and any limitations they may have."""

        prompt_config = migrator.create_prompt_config(
            "STRENGTH_COACH_PROMPT",
            sample_prompt_content,
            "test_script",
            PromptType.COACH,
        )

        # Save the prompt
        output_path = loader.save_prompt(prompt_config, "strength_coach")
        print(f"✅ Sample prompt created at: {output_path}")

        # Test 4: Load the prompt
        logger.info("📥 Test 4: Loading prompt...")
        loaded_config = loader.load_prompt("strength_coach")
        print(
            f"✅ Loaded prompt: {loaded_config.metadata.name} v{loaded_config.metadata.version}"
        )
        print(f"   - Type: {loaded_config.metadata.prompt_type.value}")
        print(f"   - Tags: {', '.join(loaded_config.metadata.tags)}")

        # Test 5: Validate prompt
        logger.info("🔍 Test 5: Validating prompt...")
        is_valid = loader.validate_prompt(loaded_config)
        print(f"✅ Prompt validation: {'PASSED' if is_valid else 'FAILED'}")

        # Test 6: Render prompt
        logger.info("🎨 Test 6: Rendering prompt...")
        rendered = loader.render_prompt("strength_coach")
        print(f"✅ Rendered prompt length: {len(rendered)} characters")

        # Test 7: Render with context
        logger.info("🎨 Test 7: Rendering with context...")
        context = {
            "user_profile": "Beginner athlete looking to build strength",
            "goals": "Build muscle mass and improve functional strength",
        }
        rendered_context = loader.render_prompt("strength_coach", context)
        print(f"✅ Context rendered prompt length: {len(rendered_context)} characters")

        # Test 8: List prompts
        logger.info("📋 Test 8: Listing prompts...")
        available_prompts = loader.list_prompts()
        print(f"✅ Available prompts: {', '.join(available_prompts)}")

        # Test 9: Cache stats
        logger.info("📊 Test 9: Cache statistics...")
        cache_stats = loader.get_cache_stats()
        print(
            f"✅ Cache stats: {cache_stats['cached_prompts']} cached, {cache_stats['tracked_files']} files tracked"
        )

        # Test 10: Display sample
        print("\n" + "=" * 60)
        print("📋 SAMPLE RENDERED PROMPT (first 200 characters):")
        print("=" * 60)
        print(rendered[:200] + "...")

        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Prompt system is working correctly.")
        print("=" * 60)

        return True

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def verify_directory_structure():
    """Verify the prompt directory structure was created correctly."""

    prompts_dir = Path("athlea_langgraph/prompts")

    print("\n📁 DIRECTORY STRUCTURE VERIFICATION:")
    print("=" * 40)

    if not prompts_dir.exists():
        print(f"❌ Prompts directory not found: {prompts_dir}")
        return False

    expected_subdirs = ["coaches", "reasoning", "onboarding", "system"]

    for subdir in expected_subdirs:
        subdir_path = prompts_dir / subdir
        status = "✅" if subdir_path.exists() else "❌"
        print(
            f"{status} {subdir}/ {'(exists)' if subdir_path.exists() else '(missing)'}"
        )

    # Check for any created prompt files
    json_files = list(prompts_dir.rglob("*.json"))
    print(f"\n📄 JSON prompt files found: {len(json_files)}")
    for json_file in json_files:
        rel_path = json_file.relative_to(prompts_dir)
        print(f"   - {rel_path}")

    return True


if __name__ == "__main__":
    print("🚀 PROMPT SYSTEM TESTING")
    print("=" * 50)

    # Verify directory structure first
    verify_directory_structure()

    # Run the main tests
    success = test_prompt_system()

    if success:
        print("\n🏆 Testing complete! Ready for Phase 2.")
    else:
        print("\n💥 Testing failed! Need to fix issues before proceeding.")
        sys.exit(1)
