"""
Hybrid Agent Graphs - Integration of Existing Agents with MCP Tools

This module integrates the existing agent files (mental_agent.py, nutrition_agent.py, etc.)
with MCP tools for a complete hybrid approach.
"""

import logging
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from ...agents.cardio_agent import cardio_agent
from ...agents.mental_agent import mental_agent
from ...agents.nutrition_agent import nutrition_agent
from ...tools.schemas.cardio_schemas import (
    CalculatePaceZonesSchema,
    CalculateVo2MaxEstimateSchema,
    GenerateRunningRouteSchema,
    PlanIntervalWorkoutSchema,
)

# Import existing agents
from ...agents.strength_agent import strength_agent
from ...states.state import AgentState
from ...utils.mcp_agent_integration import get_agent_tools

logger = logging.getLogger(__name__)


class HybridAgentState(AgentState):
    """Enhanced state for hybrid agent testing with MCP integration."""

    current_agent: Optional[str] = None
    agent_response: Optional[str] = None
    tools_used: List[str] = []
    mcp_tools_available: List[str] = []
    direct_tools_available: List[str] = []
    debug_info: Dict[str, Any] = {}


async def create_hybrid_strength_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create hybrid strength agent graph using existing agent + MCP tools."""

    if config is None:
        config = {}

    logger.info("Creating hybrid strength agent graph with MCP integration")

    async def hybrid_strength_node(state: HybridAgentState) -> Dict[str, Any]:
        """Hybrid strength agent node using existing agent with MCP tools."""

        try:
            logger.info("🏋️ Running hybrid strength agent with MCP tools...")

            # Get hybrid tools (direct + MCP)
            tools = await get_agent_tools("strength", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            # Update the existing agent with new tools
            await strength_agent.get_domain_tools()  # Load direct tools

            # Add MCP tools to the agent
            if tools:
                strength_agent.tools.extend(
                    [t for t in tools if t not in strength_agent.tools]
                )

                # Update the ReAct executor if it exists - CRITICAL: Update both tools and tool_map
                if (
                    hasattr(strength_agent, "react_executor")
                    and strength_agent.react_executor
                ):
                    strength_agent.react_executor.tools = strength_agent.tools
                    # Update the tool_map which is what the executor actually uses for lookups
                    strength_agent.react_executor.tool_map = {
                        tool.name: tool for tool in strength_agent.tools
                    }

            logger.info(
                f"🔧 Strength agent loaded {len(strength_agent.tools)} total tools"
            )

            # Process the request using the existing agent
            result = await strength_agent.process(state, config)

            # Extract response information
            response = result.get("response", "No response generated")

            # Track tools used (this would need to be implemented in the agent)
            tools_used = result.get("tools_used", [])

            # Create response message
            ai_message = AIMessage(content=response, name="strength_agent")

            messages = list(state.get("messages", []))

            return {
                "messages": messages + [ai_message],
                "current_agent": "strength_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "direct_tools_available": [t.name for t in strength_agent.tools],
                "debug_info": {
                    "agent": "strength_agent",
                    "tools_count": len(strength_agent.tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "mcp_enabled": True,
                    "agent_class": "StrengthAgent",
                    "hybrid_mode": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in hybrid strength agent: {e}", exc_info=True)
            error_msg = f"Hybrid strength agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="strength_agent")],
                "current_agent": "strength_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "direct_tools_available": [],
                "debug_info": {
                    "agent": "strength_agent",
                    "error": str(e),
                    "hybrid_mode": True,
                },
            }

    # Build graph
    builder = StateGraph(HybridAgentState)
    builder.add_node("strength_agent", hybrid_strength_node)
    builder.add_edge(START, "strength_agent")
    builder.add_edge("strength_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Hybrid strength agent graph compiled successfully")
    return graph


async def create_hybrid_nutrition_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create hybrid nutrition agent graph using existing agent + MCP tools."""

    if config is None:
        config = {}

    logger.info("Creating hybrid nutrition agent graph with MCP integration")

    async def hybrid_nutrition_node(state: HybridAgentState) -> Dict[str, Any]:
        """Hybrid nutrition agent node using existing agent with MCP tools."""

        try:
            logger.info("🥗 Running hybrid nutrition agent with MCP tools...")

            # Get hybrid tools (direct + MCP)
            tools = await get_agent_tools("nutrition", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            # Update the existing agent with new tools
            await nutrition_agent.get_domain_tools()  # Load direct tools

            # Add MCP tools to the agent
            if tools:
                nutrition_agent.tools.extend(
                    [t for t in tools if t not in nutrition_agent.tools]
                )

                # Update the ReAct executor if it exists - CRITICAL: Update both tools and tool_map
                if (
                    hasattr(nutrition_agent, "react_executor")
                    and nutrition_agent.react_executor
                ):
                    nutrition_agent.react_executor.tools = nutrition_agent.tools
                    # Update the tool_map which is what the executor actually uses for lookups
                    nutrition_agent.react_executor.tool_map = {
                        tool.name: tool for tool in nutrition_agent.tools
                    }

            logger.info(
                f"🔧 Nutrition agent loaded {len(nutrition_agent.tools)} total tools"
            )

            # Process the request using the existing agent
            result = await nutrition_agent.process(state, config)

            # Extract response information
            response = result.get("response", "No response generated")
            tools_used = result.get("tools_used", [])

            # Create response message
            ai_message = AIMessage(content=response, name="nutrition_agent")

            messages = list(state.get("messages", []))

            return {
                "messages": messages + [ai_message],
                "current_agent": "nutrition_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "direct_tools_available": [t.name for t in nutrition_agent.tools],
                "debug_info": {
                    "agent": "nutrition_agent",
                    "tools_count": len(nutrition_agent.tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "mcp_enabled": True,
                    "agent_class": "NutritionAgent",
                    "hybrid_mode": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in hybrid nutrition agent: {e}", exc_info=True)
            error_msg = f"Hybrid nutrition agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="nutrition_agent")],
                "current_agent": "nutrition_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "direct_tools_available": [],
                "debug_info": {
                    "agent": "nutrition_agent",
                    "error": str(e),
                    "hybrid_mode": True,
                },
            }

    # Build graph
    builder = StateGraph(HybridAgentState)
    builder.add_node("nutrition_agent", hybrid_nutrition_node)
    builder.add_edge(START, "nutrition_agent")
    builder.add_edge("nutrition_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Hybrid nutrition agent graph compiled successfully")
    return graph


async def create_hybrid_cardio_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create hybrid cardio agent graph using existing agent + MCP tools."""

    if config is None:
        config = {}

    logger.info("Creating hybrid cardio agent graph with MCP integration")

    async def hybrid_cardio_node(state: HybridAgentState) -> Dict[str, Any]:
        """Hybrid cardio agent node using existing agent with MCP tools."""

        try:
            logger.info("🏃 Running hybrid cardio agent with MCP tools...")

            # Get hybrid tools (direct + MCP)
            tools = await get_agent_tools("cardio", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            # Update the existing agent with new tools
            await cardio_agent.get_domain_tools()  # Load direct tools

            # Add MCP tools to the agent
            if tools:
                cardio_agent.tools.extend(
                    [t for t in tools if t not in cardio_agent.tools]
                )

                # Update the ReAct executor if it exists - CRITICAL: Update both tools and tool_map
                if (
                    hasattr(cardio_agent, "react_executor")
                    and cardio_agent.react_executor
                ):
                    cardio_agent.react_executor.tools = cardio_agent.tools
                    # Update the tool_map which is what the executor actually uses for lookups
                    cardio_agent.react_executor.tool_map = {
                        tool.name: tool for tool in cardio_agent.tools
                    }

            logger.info(f"🔧 Cardio agent loaded {len(cardio_agent.tools)} total tools")

            # Process the request using the existing agent
            result = await cardio_agent.process(state, config)

            # Extract response information
            response = result.get("response", "No response generated")
            tools_used = result.get("tools_used", [])

            # Create response message
            ai_message = AIMessage(content=response, name="cardio_agent")

            messages = list(state.get("messages", []))

            return {
                "messages": messages + [ai_message],
                "current_agent": "cardio_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "direct_tools_available": [t.name for t in cardio_agent.tools],
                "debug_info": {
                    "agent": "cardio_agent",
                    "tools_count": len(cardio_agent.tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "mcp_enabled": True,
                    "agent_class": "CardioAgent",
                    "hybrid_mode": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in hybrid cardio agent: {e}", exc_info=True)
            error_msg = f"Hybrid cardio agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="cardio_agent")],
                "current_agent": "cardio_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "direct_tools_available": [],
                "debug_info": {
                    "agent": "cardio_agent",
                    "error": str(e),
                    "hybrid_mode": True,
                },
            }

    # Build graph
    builder = StateGraph(HybridAgentState)
    builder.add_node("cardio_agent", hybrid_cardio_node)
    builder.add_edge(START, "cardio_agent")
    builder.add_edge("cardio_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Hybrid cardio agent graph compiled successfully")
    return graph


async def create_hybrid_mental_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create hybrid mental agent graph using existing agent + MCP tools."""

    if config is None:
        config = {}

    logger.info("Creating hybrid mental agent graph with MCP integration")

    async def hybrid_mental_node(state: HybridAgentState) -> Dict[str, Any]:
        """Hybrid mental agent node using existing agent with MCP tools."""

        try:
            logger.info("🧠 Running hybrid mental agent with MCP tools...")

            # Get hybrid tools (direct + MCP)
            tools = await get_agent_tools("mental", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            # Update the existing agent with new tools
            await mental_agent.get_domain_tools()  # Load direct tools

            # Add MCP tools to the agent
            if tools:
                mental_agent.tools.extend(
                    [t for t in tools if t not in mental_agent.tools]
                )

                # Update the ReAct executor if it exists - CRITICAL: Update both tools and tool_map
                if (
                    hasattr(mental_agent, "react_executor")
                    and mental_agent.react_executor
                ):
                    mental_agent.react_executor.tools = mental_agent.tools
                    # Update the tool_map which is what the executor actually uses for lookups
                    mental_agent.react_executor.tool_map = {
                        tool.name: tool for tool in mental_agent.tools
                    }

            logger.info(f"🔧 Mental agent loaded {len(mental_agent.tools)} total tools")

            # Process the request using the existing agent
            result = await mental_agent.process(state, config)

            # Extract response information
            response = result.get("response", "No response generated")
            tools_used = result.get("tools_used", [])

            # Create response message
            ai_message = AIMessage(content=response, name="mental_agent")

            messages = list(state.get("messages", []))

            return {
                "messages": messages + [ai_message],
                "current_agent": "mental_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "direct_tools_available": [t.name for t in mental_agent.tools],
                "debug_info": {
                    "agent": "mental_agent",
                    "tools_count": len(mental_agent.tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "mcp_enabled": True,
                    "agent_class": "MentalAgent",
                    "hybrid_mode": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in hybrid mental agent: {e}", exc_info=True)
            error_msg = f"Hybrid mental agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="mental_agent")],
                "current_agent": "mental_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "direct_tools_available": [],
                "debug_info": {
                    "agent": "mental_agent",
                    "error": str(e),
                    "hybrid_mode": True,
                },
            }

    # Build graph
    builder = StateGraph(HybridAgentState)
    builder.add_node("mental_agent", hybrid_mental_node)
    builder.add_edge(START, "mental_agent")
    builder.add_edge("mental_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Hybrid mental agent graph compiled successfully")
    return graph
