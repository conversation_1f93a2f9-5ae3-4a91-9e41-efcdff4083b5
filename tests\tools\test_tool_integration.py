#!/usr/bin/env python3
"""
Test script to verify tool integration in the optimized coaching graph.
This script will help us understand:
1. Whether domain agents are properly connected to the graph
2. Which tools are being used by the graph
3. Whether tool calls are being tracked
"""

import asyncio
import logging
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "."))

from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_coaching_graph,
)
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
    ComprehensiveCoachingConfig,
    create_domain_integrated_react_coaches,
)
from athlea_langgraph.agents.strength_agent import strength_agent
from athlea_langgraph.agents.mental_agent import mental_agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_domain_agent_tools():
    """Test that domain agents have their tools properly loaded."""
    print("=== Testing Domain Agent Tools ===")

    # Test strength agent
    strength_tools = await strength_agent.get_domain_tools()
    print(f"Strength Agent Tools: {[t.name for t in strength_tools]}")
    print(f"Strength Agent Tool Count: {len(strength_tools)}")

    # Test mental agent
    mental_tools = await mental_agent.get_domain_tools()
    print(f"Mental Agent Tools: {[t.name for t in mental_tools]}")
    print(f"Mental Agent Tool Count: {len(mental_tools)}")

    return strength_tools, mental_tools


async def test_optimized_graph_integration():
    """Test what tools the optimized graph coaches have with domain integration."""
    print("\n=== Testing OPTIMIZED Graph with Domain Integration ===")

    # Create optimized graph
    config = ComprehensiveCoachingConfig(
        user_id="test_user", enable_memory=False, use_react_agents=True
    )

    graph = await create_optimized_coaching_graph(config)
    print(f"Optimized Graph created with {len(graph.nodes)} nodes")

    # Try to access the react coaches from the graph creation process
    from athlea_langgraph.agents.specialized_coaches import get_tools_manager

    tools_manager = await get_tools_manager()
    domain_react_coaches = await create_domain_integrated_react_coaches(
        tools_manager, config
    )

    print(
        f"Domain-Integrated React Coaches Created: {list(domain_react_coaches.keys())}"
    )

    # Check tools for each coach
    for coach_name, executor in domain_react_coaches.items():
        print(f"{coach_name} Tools: {[t.name for t in executor.tools]}")
        print(f"{coach_name} Tool Count: {len(executor.tools)}")
        print(
            f"{coach_name} Has Tool Call History: {hasattr(executor, 'tool_call_history')}"
        )
        if hasattr(executor, "tool_call_history"):
            print(f"{coach_name} Tool Call History: {executor.tool_call_history}")

    return domain_react_coaches


async def test_tool_call_tracking_in_graph():
    """Test tool call tracking in the actual optimized graph execution."""
    print("\n=== Testing Tool Call Tracking in Optimized Graph ===")

    # Create optimized graph
    config = ComprehensiveCoachingConfig(
        user_id="test_user", enable_memory=False, use_react_agents=True
    )

    graph = await create_optimized_coaching_graph(config)

    # Create a test state that should trigger tools
    from athlea_langgraph.states.optimized_state import OptimizedCoachingState

    test_state = OptimizedCoachingState(
        user_query="I want to build muscle mass. Can you assess my strength and recommend specific exercises for powerlifting?",
        messages=[],
        user_profile={
            "experience_level": "intermediate",
            "goals": ["muscle_gain", "powerlifting"],
        },
    )

    # Execute the graph
    try:
        config_for_graph = {"configurable": {"thread_id": "test_thread"}}
        result = await graph.ainvoke(test_state, config=config_for_graph)

        print(f"Graph execution result:")
        print(
            f"  - Final response: {result.get('final_response', 'No response')[:100]}..."
        )
        print(f"  - Current node: {result.get('current_node', 'Unknown')}")
        print(f"  - Execution steps: {result.get('execution_steps', [])}")
        print(f"  - Coach responses: {list(result.get('coach_responses', {}).keys())}")

        # Check if we can access tool call history from the state
        debug_info = result.get("debug_info", {})
        print(f"  - Debug info: {debug_info}")

        return result

    except Exception as e:
        print(f"Error testing graph execution: {e}")
        import traceback

        traceback.print_exc()
        return None


async def test_add_tool_call_flags():
    """Add tool call tracking flags to domain agents."""
    print("\n=== Adding Tool Call Tracking Flags ===")

    # Test adding a simple tool call counter to strength agent
    if not hasattr(strength_agent, "tool_calls_count"):
        strength_agent.tool_calls_count = 0
        print("✅ Added tool_calls_count to strength_agent")

    if not hasattr(strength_agent, "last_tools_called"):
        strength_agent.last_tools_called = []
        print("✅ Added last_tools_called to strength_agent")

    # Do the same for mental agent
    if not hasattr(mental_agent, "tool_calls_count"):
        mental_agent.tool_calls_count = 0
        print("✅ Added tool_calls_count to mental_agent")

    if not hasattr(mental_agent, "last_tools_called"):
        mental_agent.last_tools_called = []
        print("✅ Added last_tools_called to mental_agent")

    print("🔧 Tool call tracking flags added to domain agents")


async def main():
    """Main test function."""
    print("Starting OPTIMIZED Graph Tool Integration Verification Tests")
    print("=" * 60)

    try:
        # Test 1: Domain agent tools
        domain_tools = await test_domain_agent_tools()

        # Test 2: Optimized graph with domain integration
        graph_coaches = await test_optimized_graph_integration()

        # Test 3: Add tool call tracking flags
        await test_add_tool_call_flags()

        # Test 4: Tool call tracking in graph execution
        graph_result = await test_tool_call_tracking_in_graph()

        print("\n" + "=" * 60)
        print("SUMMARY:")
        print("1. Domain agents have tools properly loaded ✓")
        if graph_coaches:
            # Check if the first coach has domain-specific tools
            first_coach = list(graph_coaches.values())[0]
            if (
                len(first_coach.tools) > 4
            ):  # Domain agents have 3-4 specific tools vs 4 generic tools
                print("2. Optimized graph now uses domain agent tools ✓")
            else:
                print("2. Optimized graph still using generic tools ❌")
        print("3. Tool call tracking flags added ✓")
        if graph_result:
            print("4. Graph execution completed ✓")
        else:
            print("4. Graph execution failed ❌")

        print("\nNext steps:")
        print("- Verify tool calls are actually being made during execution")
        print("- Add comprehensive integration tests")
        print("- Monitor tool call performance in production scenarios")

    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
