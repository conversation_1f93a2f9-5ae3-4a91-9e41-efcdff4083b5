import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import json

import pytest
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from mcp.server import NotificationOptions, Server
from mcp.server.session import ServerSession
from mcp.server.stdio import stdio_server

# Import actual MCP types - these should be available since mcp==1.9.1 is in requirements
from mcp.types import (
    CallToolRequest,
    InitializeRequest,
    ListToolsRequest,
    TextContent,
    Tool,
)

from athlea_langgraph.tools.schemas import (
    GenerateStrengthProgramSchema,
    SearchStrengthExercisesSchema,
)

# Imports from the strength MCP server and related schemas
from mcp_servers.strength_mcp.server import app as strength_mcp_app
from mcp_servers.strength_mcp.server import call_tool as strength_call_tool_handler
from mcp_servers.strength_mcp.server import exercise_db, assessment_tool
from mcp_servers.strength_mcp.server import list_tools as strength_list_tools_handler


@pytest.fixture
def mock_request():
    """Provides a mock ListToolsRequest object."""
    return ListToolsRequest(method="tools/list")


@pytest.fixture
def mock_call_tool_request():
    """Provides a mock CallToolRequest object factory."""

    def _create_request(name: str, arguments: dict):
        return CallToolRequest(
            method="tools/call", params={"name": name, "arguments": arguments}
        )

    return _create_request


@pytest.mark.asyncio
async def test_list_tools_strength_mcp(mock_request):
    """Tests the list_tools handler of the Strength MCP server."""
    # The actual list_tools function doesn't take parameters
    listed_tools = await strength_list_tools_handler()

    # Check that we get the expected number of tools (3 tools from the server)
    assert len(listed_tools) == 3

    tool_names = [t.name for t in listed_tools]
    assert "search_strength_exercises" in tool_names
    assert "assess_strength_level" in tool_names
    assert "get_exercise_progression" in tool_names

    # Verify tool structure matches MCP Tool specification
    for tool in listed_tools:
        assert isinstance(tool, Tool)
        assert hasattr(tool, "name")
        assert hasattr(tool, "description")
        assert hasattr(tool, "inputSchema")


@pytest.mark.asyncio
async def test_call_tool_search_strength_exercises_valid_input(mock_call_tool_request):
    """Tests call_tool with search_strength_exercises and valid input."""
    # Mock the underlying tool method
    exercise_db.search_exercises = AsyncMock(
        return_value={"exercises": [{"name": "Bench Press", "reps": 10}]}
    )

    # Use correct parameter names matching server implementation
    tool_call_input = {
        "muscle_groups": ["chest", "triceps"],
        "equipment": ["barbell"],
        "exercise_type": "compound",
        "difficulty": "intermediate",
        "limit": 5,
    }

    result = await strength_call_tool_handler(
        "search_strength_exercises", tool_call_input
    )

    exercise_db.search_exercises.assert_called_once()

    # Verify result is properly formatted for MCP
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Bench Press" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_assess_strength_level_valid_input(mock_call_tool_request):
    """Tests call_tool with assess_strength_level and valid input."""
    assessment_tool.assess_strength = AsyncMock(
        return_value={
            "strength_level": "intermediate",
            "recommendations": ["increase frequency"],
        }
    )

    # Create a complete valid assessment data structure
    assessment_data = {
        "user_id": "test_user_123",
        "age": 25,
        "gender": "male",
        "movement_screen": {
            "overhead_squat_quality": 3,
            "single_leg_balance": 4,
            "shoulder_mobility": 3,
            "hip_mobility": 4,
            "core_stability": 3,
            "pain_areas": [],
            "previous_injuries": [],
        },
        "strength_testing": {
            "bodyweight_kg": 70.0,
            "squat_1rm": 100.0,
            "bench_press_1rm": 80.0,
            "deadlift_1rm": 120.0,
            "overhead_press_1rm": 50.0,
            "pull_up_max": 10,
            "push_up_max": 25,
            "plank_max_seconds": 60,
        },
        "experience": {
            "training_years": 2.0,
            "training_frequency": 3,
            "previous_programs": ["Starting Strength"],
            "competition_experience": False,
            "coaching_history": False,
            "technique_confidence": 3,
        },
        "equipment": {
            "gym_access": True,
            "home_equipment": ["dumbbells", "pull-up bar"],
            "preferred_setting": "gym",
            "budget_constraints": False,
        },
        "goals": {
            "primary_goals": ["strength", "muscle building"],
            "secondary_goals": ["conditioning"],
            "timeline": "6 months",
            "motivation_level": 4,
            "time_availability": 60,
        },
    }

    tool_call_input = {"assessment_data": json.dumps(assessment_data)}

    result = await strength_call_tool_handler("assess_strength_level", tool_call_input)

    assessment_tool.assess_strength.assert_called_once()

    # Verify result is properly formatted for MCP
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "strength_level" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_invalid_tool_name_strength_mcp(mock_call_tool_request):
    """Tests call_tool with an invalid tool name for Strength MCP."""

    result = await strength_call_tool_handler("non_existent_strength_tool", {})

    # Check that result contains error message
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Unknown tool" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_search_strength_exercises_invalid_input(
    mock_call_tool_request,
):
    """Tests call_tool with search_strength_exercises and empty input (should work with defaults)."""
    # Mock the underlying tool method
    exercise_db.search_exercises = AsyncMock(
        return_value={"exercises": [{"name": "Default Exercise"}]}
    )

    tool_call_input = {}  # Empty input - server should handle with defaults

    result = await strength_call_tool_handler(
        "search_strength_exercises", tool_call_input
    )

    # Should succeed with defaults, not error
    exercise_db.search_exercises.assert_called_once()
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    # Should contain valid JSON response
    assert "exercises" in result.content[0].text


@pytest.mark.asyncio
async def test_mcp_server_integration():
    """Integration test for the full MCP server workflow."""
    # Test server initialization
    assert isinstance(strength_mcp_app, Server)

    # Test that the server can handle the basic operations
    with patch("mcp_servers.strength_mcp.server.exercise_db") as mock_exercise_tool:

        mock_exercise_tool.search_exercises = AsyncMock(
            return_value={"exercises": [{"name": "Push-up", "difficulty": "medium"}]}
        )

        # Simulate listing tools
        tools = await strength_list_tools_handler()

        # Verify tools are returned
        assert len(tools) == 3
        assert any(tool.name == "search_strength_exercises" for tool in tools)

        # Simulate calling a tool
        result = await strength_call_tool_handler(
            "search_strength_exercises",
            {
                "muscle_groups": ["chest"],
                "exercise_type": "compound",
                "equipment": ["bodyweight"],
            },
        )

        # Verify result format
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "Push-up" in result.content[0].text


@pytest.mark.asyncio
async def test_mcp_server_stdio_communication():
    """Test the stdio server communication pattern."""
    # This test simulates how the MCP server would be used in practice

    async def mock_main():
        """Mock the server's main function to test stdio communication setup."""
        try:
            # This would normally start the stdio server
            # We're just testing that it can be set up without errors
            server_instance = strength_mcp_app
            assert server_instance is not None

            # Test that we can call the handler functions directly
            tools = await strength_list_tools_handler()
            assert len(tools) > 0

            return True
        except Exception as e:
            pytest.fail(f"Server setup failed: {e}")

    result = await mock_main()
    assert result is True


# Performance test
@pytest.mark.asyncio
async def test_tool_call_performance():
    """Test that tool calls complete within reasonable time."""
    import time

    with patch("mcp_servers.strength_mcp.server.exercise_db") as mock_tool:
        mock_tool.search_exercises = AsyncMock(
            return_value={"exercises": [{"name": "Squat"}]}
        )

        start_time = time.time()
        result = await strength_call_tool_handler(
            "search_strength_exercises",
            {
                "muscle_groups": ["legs"],
                "exercise_type": "compound",
                "equipment": ["barbell"],
            },
        )
        end_time = time.time()

        # Tool call should complete within 1 second
        assert (end_time - start_time) < 1.0
        assert len(result.content) == 1


# Error handling test
@pytest.mark.asyncio
async def test_tool_exception_handling():
    """Test that tool exceptions are properly handled and formatted for MCP."""
    with patch("mcp_servers.strength_mcp.server.exercise_db") as mock_tool:
        mock_tool.search_exercises = AsyncMock(
            side_effect=Exception("Database connection failed")
        )

        result = await strength_call_tool_handler(
            "search_strength_exercises",
            {"muscle_groups": ["chest"], "equipment": ["barbell"]},
        )

        # Should return MCP-formatted error response instead of raising exception
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "Error" in result.content[0].text
        assert "Database connection failed" in result.content[0].text
