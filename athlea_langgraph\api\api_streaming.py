"""
FastAPI application for streaming memory-enhanced coaching responses.

This module provides SSE endpoints that are compatible with the Next.js
frontend chat interface, including proper error handling and CORS support.
"""

import asyncio
import json
import logging
import os
from ..config import config
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from fastapi import Body, FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from athlea_langgraph.config import config
from athlea_langgraph.graph_factory import GraphType, get_graph_by_type
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    create_initial_onboarding_state,
)

from .streaming import (
    StreamingEventType,
    create_coaching_streamer,
    stream_coaching_response,
    stream_specialized_coach,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic here
    logger.info("Initializing coaching agents...")
    # Add any initialization code that was in your startup event
    mongodb_uri = config.database.mongodb_uri
    if not mongodb_uri:
        logger.error("MONGODB_URI environment variable not set")
        # This will prevent the app from starting if MONGODB_URI is missing
        raise ValueError("MONGODB_URI environment variable is required for startup")
    logger.info("MONGODB_URI configured, proceeding with startup.")
    yield


app = FastAPI(
    title="Athlea Coaching API",
    description="Memory-enhanced coaching agents with streaming support",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class CoachingRequest(BaseModel):
    """Request model for coaching endpoints."""

    message: str
    threadId: Optional[str] = None
    userId: str
    userProfile: Optional[Dict[str, Any]] = None


class SpecializedCoachRequest(BaseModel):
    """Request model for specialized coach endpoints."""

    message: str
    coachType: str
    threadId: Optional[str] = None
    userId: str


class OnboardingRequest(BaseModel):
    """Request model for onboarding sessions."""

    message: str
    user_id: str
    thread_id: Optional[str] = None
    user_profile: Optional[Dict[str, Any]] = None
    conversation_history: Optional[List[Union[Dict[str, Any], BaseMessage]]] = Field(
        default_factory=list,
        description="Previous conversation messages - can be dicts with 'role' and 'content' or BaseMessage objects",
    )
    resume_from_interrupt: Optional[bool] = False


async def handle_streaming_errors(generator):
    """Wrapper to handle errors in streaming generators."""
    try:
        async for chunk in generator:
            yield chunk
    except Exception as e:
        logger.error(f"Streaming error: {e}")
        error_event = f'event: {StreamingEventType.ERROR}\ndata: {{"message": "An error occurred during processing. Please try again."}}\n\n'
        yield error_event


@app.get("/api/coaching")
async def stream_coaching_get(
    message: str = Query(..., description="User message"),
    threadId: str = Query(..., description="Thread ID"),
    userId: str = Query(..., description="User ID"),
    singleCoach: Optional[str] = Query(None, description="Specific coach type"),
):
    """
    GET endpoint for streaming coaching responses (Next.js compatibility).

    This endpoint matches the frontend's expectation for GET requests with query parameters.
    """
    mongodb_uri = config.database.mongodb_uri
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        if singleCoach:
            # Route to specific coach
            generator = stream_specialized_coach(
                user_message=message,
                coach_type=singleCoach,
                thread_id=threadId,
                user_id=userId,
                mongodb_uri=mongodb_uri,
            )
        else:
            # General coaching
            generator = stream_coaching_response(
                user_message=message,
                thread_id=threadId,
                user_id=userId,
                mongodb_uri=mongodb_uri,
            )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
            },
        )

    except Exception as e:
        logger.error(f"Error in coaching endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/coaching")
async def stream_coaching_post(request: CoachingRequest):
    """
    POST endpoint for streaming coaching responses.

    Alternative endpoint that accepts JSON payloads.
    """
    mongodb_uri = config.database.mongodb_uri
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    thread_id = request.threadId or f"thread_{request.userId}_{hash(request.message)}"

    try:
        generator = stream_coaching_response(
            user_message=request.message,
            thread_id=thread_id,
            user_id=request.userId,
            mongodb_uri=mongodb_uri,
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
            },
        )

    except Exception as e:
        logger.error(f"Error in coaching POST endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/coaching/specialized")
async def stream_specialized_coaching(
    message: str = Query(..., description="User message"),
    coachType: str = Query(..., description="Coach type"),
    threadId: str = Query(..., description="Thread ID"),
    userId: str = Query(..., description="User ID"),
):
    """
    GET endpoint for streaming specialized coach responses.
    """
    mongodb_uri = config.database.mongodb_uri
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    valid_coaches = [
        "strength",
        "running",
        "cardio",
        "cycling",
        "nutrition",
        "recovery",
        "mental",
    ]

    if coachType not in valid_coaches:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid coach type. Must be one of: {', '.join(valid_coaches)}",
        )

    try:
        generator = stream_specialized_coach(
            user_message=message,
            coach_type=coachType,
            thread_id=threadId,
            user_id=userId,
            mongodb_uri=mongodb_uri,
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
            },
        )

    except Exception as e:
        logger.error(f"Error in specialized coaching endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/coaching/specialized")
async def stream_specialized_coaching_post(request: SpecializedCoachRequest):
    """
    POST endpoint for streaming specialized coach responses.
    """
    mongodb_uri = config.database.mongodb_uri
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    thread_id = request.threadId or f"thread_{request.userId}_{hash(request.message)}"

    try:
        generator = stream_specialized_coach(
            user_message=request.message,
            coach_type=request.coachType,
            thread_id=thread_id,
            user_id=request.userId,
            mongodb_uri=mongodb_uri,
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
            },
        )

    except Exception as e:
        logger.error(f"Error in specialized coaching POST endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    mongodb_uri = config.database.mongodb_uri
    return {
        "status": "healthy",
        "mongodb_configured": bool(mongodb_uri),
        "timestamp": "2024-01-15T10:30:00Z",
    }


@app.get("/api/coaches")
async def get_available_coaches():
    """Get list of available specialized coaches."""
    return {
        "coaches": [
            {
                "id": "strength",
                "name": "Strength Coach",
                "description": "Strength training and muscle building",
            },
            {
                "id": "running",
                "name": "Running Coach",
                "description": "Running technique and endurance",
            },
            {
                "id": "cardio",
                "name": "Cardio Coach",
                "description": "Cardiovascular fitness and conditioning",
            },
            {
                "id": "cycling",
                "name": "Cycling Coach",
                "description": "Cycling performance and technique",
            },
            {
                "id": "nutrition",
                "name": "Nutrition Coach",
                "description": "Diet and nutrition guidance",
            },
            {
                "id": "recovery",
                "name": "Recovery Coach",
                "description": "Recovery and rehabilitation",
            },
            {
                "id": "mental",
                "name": "Mental Coach",
                "description": "Mental training and mindset",
            },
        ]
    }


@app.post("/api/domain-coach")
async def domain_coach_endpoint(request: Request):
    """
    Simple domain coach endpoint that matches the Next.js frontend expectations.

    Expected format:
    - data: {content}
    - data: [DONE]
    """
    try:
        body = await request.json()
        messages = body.get("messages", [])

        if not messages:
            raise HTTPException(status_code=400, detail="No messages provided")

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # Generate thread ID
        thread_id = f"simple_coach_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        user_id = "simple_coach_user"  # Default user for simple coach

        async def generate_simple_stream():
            """Generate simple streaming response compatible with frontend."""
            try:
                # Stream the coaching response
                async for event in stream_coaching_response(
                    user_message=user_message,
                    thread_id=thread_id,
                    user_id=user_id,
                    mongodb_uri=config.database.mongodb_uri,
                ):
                    # Parse SSE event to extract content
                    if "data:" in event:
                        lines = event.strip().split("\n")
                        for line in lines:
                            if line.startswith("data:"):
                                data_str = line.split(":", 1)[1].strip()
                                try:
                                    data = json.loads(data_str)
                                    # Extract content from various event types
                                    content = ""
                                    if isinstance(data, dict):
                                        if "content" in data:
                                            content = data["content"]
                                        elif "token" in data:
                                            content = data["token"]
                                        elif (
                                            "message" in data
                                            and isinstance(data["message"], dict)
                                            and "content" in data["message"]
                                        ):
                                            content = data["message"]["content"]
                                    elif isinstance(data, str):
                                        content = data

                                    if content:
                                        yield f"data: {content}\n\n"

                                except json.JSONDecodeError:
                                    # If it's not JSON, treat as plain text
                                    if data_str and data_str != "":
                                        yield f"data: {data_str}\n\n"

                    # Check for completion
                    if (
                        StreamingEventType.COMPLETE in event
                        or StreamingEventType.ERROR in event
                    ):
                        break

                # Send completion signal
                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"Error in simple streaming: {e}")
                yield f"data: Sorry, there was an error processing your request.\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_simple_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        logger.error(f"Error in domain coach endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def stream_onboarding_response(
    request: OnboardingRequest,
) -> AsyncGenerator[str, None]:
    """Stream onboarding response with real-time updates."""
    try:
        # Get onboarding graph
        graph = get_graph_by_type(
            GraphType.ONBOARDING.value, checkpointer_type="memory"
        )

        # Prepare configuration
        thread_id = request.thread_id or f"onboarding_{request.user_id}"
        config = {"configurable": {"thread_id": thread_id}}

        # Send initial status
        yield f'event: status\ndata: {{"type": "session_started", "message": "Starting onboarding session...", "user_id": "{request.user_id}"}}\n\n'

        # Handle resume from interrupt or new conversation
        if request.resume_from_interrupt:
            yield f'event: status\ndata: {{"type": "resuming", "message": "Resuming onboarding session..."}}\n\n'

            # Resume from interrupt with user input
            result = await graph.ainvoke(
                Command(resume=HumanMessage(content=request.message)), config
            )
        else:
            # Create initial state or get existing state
            try:
                # Try to get existing state first
                snapshot = await graph.aget_state(config)

                if snapshot.values:
                    # Existing conversation - add new message
                    state = snapshot.values
                    logger.info("Continuing existing onboarding conversation")
                else:
                    # New conversation - create initial state
                    state = create_initial_onboarding_state(request.user_id)
                    logger.info("Created new onboarding conversation")

                # Add the new user message
                state["user_input"] = request.message
                state["messages"] = state.get("messages", []) + [
                    HumanMessage(content=request.message)
                ]

                # Add user profile if provided
                if request.user_profile:
                    state["user_profile"] = request.user_profile

                yield f'event: status\ndata: {{"type": "processing", "message": "Processing your message..."}}\n\n'

                # Execute the graph
                result = await graph.ainvoke(state, config)

            except Exception as e:
                logger.error(f"Error during graph execution: {e}")
                yield f'event: error\ndata: {{"type": "execution_error", "message": "An error occurred during processing", "error": "{str(e)}"}}\n\n'
                return

        # Stream the result
        async for chunk in stream_onboarding_result(result):
            yield chunk

    except Exception as e:
        logger.error(f"Error in stream_onboarding_response: {e}")
        yield f'event: error\ndata: {{"type": "stream_error", "message": "An error occurred during streaming", "error": "{str(e)}"}}\n\n'


async def stream_onboarding_result(result: Dict[str, Any]) -> AsyncGenerator[str, None]:
    """Stream the onboarding graph execution result."""
    try:
        # Stream onboarding stage update
        if "onboarding_stage" in result:
            yield f'event: stage_update\ndata: {{"stage": "{result["onboarding_stage"]}", "message": "Moved to {result["onboarding_stage"]} stage"}}\n\n'

        # Stream sidebar data updates
        if "sidebar_data" in result and result["sidebar_data"]:
            # Convert Pydantic model to dict if needed
            sidebar_data = result["sidebar_data"]
            if hasattr(sidebar_data, "model_dump"):
                sidebar_data = sidebar_data.model_dump()
            elif hasattr(sidebar_data, "dict"):
                sidebar_data = sidebar_data.dict()

            sidebar_json = json.dumps(sidebar_data)
            yield f'event: sidebar_update\ndata: {{"data": {sidebar_json}}}\n\n'

        # Stream messages if available
        if "messages" in result and result["messages"]:
            last_message = result["messages"][-1]

            if hasattr(last_message, "content"):
                response_content = last_message.content

                # Send response start
                yield f'event: response_start\ndata: {{"total_length": {len(response_content)}}}\n\n'

                # Stream response in chunks
                chunk_size = 50  # Characters per chunk
                for i in range(0, len(response_content), chunk_size):
                    chunk = response_content[i : i + chunk_size]
                    chunk_data = {
                        "chunk": chunk,
                        "position": i,
                        "is_final": i + chunk_size >= len(response_content),
                    }
                    yield f"event: response_chunk\ndata: {json.dumps(chunk_data)}\n\n"
                    # Small delay for streaming effect
                    await asyncio.sleep(0.05)

        # Stream plan if generated
        if "generated_plan" in result and result["generated_plan"]:
            # Convert Pydantic model to dict if needed
            plan_data = result["generated_plan"]
            if hasattr(plan_data, "model_dump"):
                plan_data = plan_data.model_dump()
            elif hasattr(plan_data, "dict"):
                plan_data = plan_data.dict()

            plan_json = json.dumps(plan_data)
            yield f'event: plan_generated\ndata: {{"plan": {plan_json}, "message": "Your personalized plan has been generated!"}}\n\n'

        # Handle interrupts (need more input)
        if result.get("needs_input") or result.get("requires_input"):
            interrupt_data = {
                "type": "interrupt",
                "message": "I need more information to continue",
                "stage": result.get("onboarding_stage", "unknown"),
            }
            yield f"event: needs_input\ndata: {json.dumps(interrupt_data)}\n\n"

        # Send completion status
        completion_message = "Processing completed"
        if result.get("onboarding_stage") == "complete":
            completion_message = "Onboarding completed successfully!"
        elif result.get("has_enough_info"):
            completion_message = "Information gathering completed"

        completion_data = {
            "type": "completed",
            "message": completion_message,
            "stage": result.get("onboarding_stage", "unknown"),
        }
        yield f"event: status\ndata: {json.dumps(completion_data)}\n\n"

    except Exception as e:
        logger.error(f"Error streaming onboarding result: {e}")
        error_data = {
            "type": "result_stream_error",
            "message": "Error streaming result",
            "error": str(e),
        }
        yield f"event: error\ndata: {json.dumps(error_data)}\n\n"


@app.post("/api/onboarding")
async def stream_onboarding_session(request: OnboardingRequest):
    """
    Stream an onboarding session with real-time updates.

    Supports:
    - New onboarding conversations
    - Resume from interrupts (HITL)
    - Real-time streaming of responses
    - Sidebar data updates
    - Plan generation
    """
    try:
        # Validate request
        if not request.user_id:
            raise HTTPException(status_code=400, detail="user_id is required")

        if not request.message:
            raise HTTPException(status_code=400, detail="message is required")

        # Return streaming response
        return StreamingResponse(
            handle_streaming_errors(stream_onboarding_response(request)),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in stream_onboarding_session: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error during onboarding"
        )


@app.get("/api/onboarding/status/{user_id}")
async def get_onboarding_status(user_id: str, thread_id: Optional[str] = None):
    """
    Get the current onboarding status for a user.

    Returns:
    - Current onboarding stage
    - Whether onboarding is complete
    - Last conversation state
    """
    try:
        # Get graph
        graph = get_graph_by_type(
            GraphType.ONBOARDING.value, checkpointer_type="memory"
        )

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Get current state
        snapshot = await graph.aget_state(config)

        if not snapshot.values:
            return {
                "status": "not_started",
                "stage": "initial",
                "has_enough_info": False,
                "needs_input": True,
                "message": "Onboarding not started",
            }

        state = snapshot.values
        return {
            "status": (
                "in_progress"
                if not state.get("onboarding_stage") == "complete"
                else "completed"
            ),
            "stage": state.get("onboarding_stage", "unknown"),
            "has_enough_info": state.get("has_enough_info", False),
            "needs_input": state.get("needs_input", True),
            "requires_input": state.get("requires_input", False),
            "sidebar_data": state.get("sidebar_data"),
            "generated_plan": state.get("generated_plan"),
            "user_id": user_id,
            "thread_id": config_thread_id,
        }

    except Exception as e:
        logger.error(f"Error getting onboarding status: {e}")
        raise HTTPException(
            status_code=500, detail="Error retrieving onboarding status"
        )


@app.post("/api/onboarding/reset/{user_id}")
async def reset_onboarding(user_id: str, thread_id: Optional[str] = None):
    """
    Reset onboarding for a user (clear conversation state).
    """
    try:
        # Get graph
        graph = get_graph_by_type(
            GraphType.ONBOARDING.value, checkpointer_type="memory"
        )

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Clear state by creating a new initial state
        initial_state = create_initial_onboarding_state(user_id)

        # Update the state to reset
        await graph.aupdate_state(config, initial_state)

        return {
            "status": "reset",
            "message": "Onboarding has been reset",
            "user_id": user_id,
            "thread_id": config_thread_id,
        }

    except Exception as e:
        logger.error(f"Error resetting onboarding: {e}")
        raise HTTPException(status_code=500, detail="Error resetting onboarding")


if __name__ == "__main__":
    import uvicorn

    port = int(os.environ.get("PORT", 8000))  # Keep as os.environ for server startup
    host = os.environ.get("HOST", "0.0.0.0")  # Keep as os.environ for server startup

    logger.info(f"Starting Athlea Coaching API on {host}:{port}")

    uvicorn.run(
        "athlea_langgraph.api_streaming:app",
        host=host,
        port=port,
        reload=True,
        log_level="info",
    )
