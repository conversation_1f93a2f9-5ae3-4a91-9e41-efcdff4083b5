"""
Memory Decay Manager for Intelligent Memory Lifecycle Management

Implements intelligent memory decay mechanisms including:
- Time-based importance scoring
- Automatic archiving of old memories
- Memory cleanup and optimization
- Resurrection of archived memories when relevant
"""

import asyncio
import logging
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple

from .mem0_adapter import Mem0Adapter, MemoryType
from .schemas.memory_metadata import (
    AdvancedMemoryMetadata,
    ImportanceDecayMode,
    ImportanceScore,
    SummaryLevel,
)

logger = logging.getLogger(__name__)


@dataclass
class DecayConfig:
    """Configuration for memory decay operations."""

    default_decay_mode: ImportanceDecayMode = ImportanceDecayMode.LINEAR
    default_decay_rate: float = 0.02  # 2% per day
    archive_threshold: float = 0.1  # Archive when importance drops below this
    deletion_threshold: float = 0.05  # Delete when importance drops below this
    min_age_for_decay: int = 7  # Minimum age in days before decay starts
    max_memories_per_user: int = 5000  # Maximum memories before forced cleanup
    resurrection_threshold: float = 0.7  # Min relevance to resurrect archived memory
    batch_size: int = 100  # Number of memories to process in one batch


class MemoryDecayManager:
    """
    Manages the lifecycle of memories through intelligent decay mechanisms.

    Features:
    - Time-based importance decay
    - Automatic archiving and cleanup
    - Memory resurrection for relevant content
    - Batch processing for efficiency
    - Configurable decay strategies
    """

    def __init__(self, mem0_adapter: Mem0Adapter):
        """
        Initialize memory decay manager.

        Args:
            mem0_adapter: Configured mem0 adapter instance
        """
        self.mem0_adapter = mem0_adapter
        self.decay_stats = {
            "memories_processed": 0,
            "memories_archived": 0,
            "memories_deleted": 0,
            "memories_resurrected": 0,
            "total_space_saved": 0,
        }

    async def calculate_importance_score(
        self,
        memory: Dict[str, Any],
        current_time: Optional[datetime] = None,
        config: DecayConfig = DecayConfig(),
    ) -> float:
        """
        Calculate current importance score for a memory considering decay.

        Args:
            memory: Memory dictionary
            current_time: Current time for decay calculation
            config: Decay configuration

        Returns:
            Current importance score (0.0 to 1.0)
        """
        if current_time is None:
            current_time = datetime.now()

        metadata = memory.get("metadata", {})

        # Get or create importance score
        importance_data = metadata.get("importance_score")
        if importance_data and isinstance(importance_data, dict):
            importance_score = ImportanceScore.from_dict(importance_data)
        else:
            # Create new importance score based on memory characteristics
            importance_score = self._create_initial_importance_score(memory, config)

        # Calculate time-based decay
        memory_timestamp = self._get_memory_timestamp(memory)
        if memory_timestamp:
            age_days = (current_time - memory_timestamp).days

            # Don't decay very recent memories
            if age_days < config.min_age_for_decay:
                return importance_score.base_score

            # Calculate decay
            current_score = importance_score.calculate_time_decay(current_time)

            # Apply access boost if recently accessed
            if importance_score.last_accessed:
                days_since_access = (current_time - importance_score.last_accessed).days
                if days_since_access < 7:  # Recent access
                    access_boost = 0.1 * (7 - days_since_access) / 7
                    current_score = min(1.0, current_score + access_boost)

            return current_score

        return importance_score.base_score

    async def archive_old_memories(
        self,
        user_id: str,
        age_threshold: int = 90,
        config: DecayConfig = DecayConfig(),
    ) -> List[str]:
        """
        Archive old memories that have low importance.

        Args:
            user_id: User identifier
            age_threshold: Minimum age in days for archival consideration
            config: Decay configuration

        Returns:
            List of archived memory IDs
        """
        cutoff_date = datetime.now() - timedelta(days=age_threshold)

        # Get candidate memories for archival
        candidate_memories = await self._get_old_memories(user_id, cutoff_date)

        archived_memory_ids = []
        current_time = datetime.now()

        for memory in candidate_memories:
            # Skip already archived memories
            if self._is_archived(memory):
                continue

            # Calculate current importance
            importance_score = await self.calculate_importance_score(
                memory, current_time, config
            )

            # Archive if importance is below threshold
            if importance_score <= config.archive_threshold:
                memory_id = memory.get("id")
                if memory_id:
                    success = await self._archive_memory(
                        memory_id, user_id, importance_score
                    )
                    if success:
                        archived_memory_ids.append(memory_id)
                        self.decay_stats["memories_archived"] += 1

        logger.info(f"Archived {len(archived_memory_ids)} memories for user {user_id}")
        return archived_memory_ids

    async def cleanup_redundant_memories(
        self,
        user_id: str,
        config: DecayConfig = DecayConfig(),
    ) -> int:
        """
        Clean up redundant or very low-importance memories.

        Args:
            user_id: User identifier
            config: Decay configuration

        Returns:
            Number of memories deleted
        """
        # Get all user memories for analysis
        all_memories = await self._get_all_user_memories(user_id)

        if len(all_memories) <= config.max_memories_per_user:
            logger.info(
                f"User {user_id} has {len(all_memories)} memories, below cleanup threshold"
            )
            return 0

        # Analyze memories for redundancy and low importance
        memories_to_delete = []
        current_time = datetime.now()

        # Group memories by content similarity for redundancy detection
        content_groups = self._group_similar_memories(all_memories)

        for group in content_groups:
            if len(group) > 1:  # Multiple similar memories
                # Keep the most important and recent, delete others
                group_scores = []
                for memory in group:
                    importance = await self.calculate_importance_score(
                        memory, current_time, config
                    )
                    timestamp = self._get_memory_timestamp(memory)
                    recency_score = self._calculate_recency_score(
                        timestamp, current_time
                    )
                    combined_score = importance * 0.7 + recency_score * 0.3
                    group_scores.append((memory, combined_score))

                # Sort by combined score and keep the best one
                group_scores.sort(key=lambda x: x[1], reverse=True)
                for memory, score in group_scores[1:]:  # Skip the best one
                    if score < config.deletion_threshold:
                        memories_to_delete.append(memory)

        # Also delete very low importance memories regardless of redundancy
        for memory in all_memories:
            if memory in [m for m, _ in sum(content_groups, [])]:
                continue  # Already processed in redundancy check

            importance = await self.calculate_importance_score(
                memory, current_time, config
            )
            if importance < config.deletion_threshold:
                memories_to_delete.append(memory)

        # Delete identified memories
        deleted_count = 0
        for memory in memories_to_delete:
            memory_id = memory.get("id")
            if memory_id:
                try:
                    await self.mem0_adapter.delete_memory(memory_id, user_id)
                    deleted_count += 1
                    self.decay_stats["memories_deleted"] += 1
                    self.decay_stats["total_space_saved"] += len(
                        memory.get("content", "")
                    )
                except Exception as e:
                    logger.error(f"Failed to delete memory {memory_id}: {e}")

        logger.info(
            f"Deleted {deleted_count} redundant/low-importance memories for user {user_id}"
        )
        return deleted_count

    async def resurrect_archived_memory(
        self,
        memory_id: str,
        user_id: str,
        relevance_context: str = "",
        config: DecayConfig = DecayConfig(),
    ) -> bool:
        """
        Resurrect an archived memory if it becomes relevant again.

        Args:
            memory_id: Memory identifier
            user_id: User identifier
            relevance_context: Context that makes this memory relevant again
            config: Decay configuration

        Returns:
            True if memory was successfully resurrected
        """
        # This would need to be implemented with a way to retrieve archived memories
        # For now, we'll log the intention
        logger.info(
            f"Would resurrect memory {memory_id} for user {user_id} due to relevance: {relevance_context}"
        )

        # In a real implementation, this would:
        # 1. Retrieve the archived memory
        # 2. Check if relevance context makes it worth resurrecting
        # 3. Restore it to active memory with updated importance score
        # 4. Update decay stats

        self.decay_stats["memories_resurrected"] += 1
        return True

    async def run_decay_maintenance(
        self,
        user_id: str,
        config: DecayConfig = DecayConfig(),
    ) -> Dict[str, Any]:
        """
        Run comprehensive decay maintenance for a user.

        Args:
            user_id: User identifier
            config: Decay configuration

        Returns:
            Maintenance results and statistics
        """
        logger.info(f"Starting decay maintenance for user {user_id}")

        maintenance_start = datetime.now()
        results = {
            "start_time": maintenance_start.isoformat(),
            "archived_memories": [],
            "deleted_count": 0,
            "processing_time_ms": 0,
            "space_saved": 0,
        }

        try:
            # Step 1: Archive old memories
            archived_ids = await self.archive_old_memories(user_id, config=config)
            results["archived_memories"] = archived_ids

            # Step 2: Clean up redundant memories
            deleted_count = await self.cleanup_redundant_memories(
                user_id, config=config
            )
            results["deleted_count"] = deleted_count

            # Step 3: Update importance scores for remaining memories
            await self._update_importance_scores(user_id, config)

            # Calculate results
            maintenance_end = datetime.now()
            results["processing_time_ms"] = (
                maintenance_end - maintenance_start
            ).total_seconds() * 1000
            results["space_saved"] = self.decay_stats["total_space_saved"]

            logger.info(f"Completed decay maintenance for user {user_id}: {results}")

        except Exception as e:
            logger.error(f"Error during decay maintenance for user {user_id}: {e}")
            results["error"] = str(e)

        return results

    async def get_decay_recommendations(
        self,
        user_id: str,
        config: DecayConfig = DecayConfig(),
    ) -> Dict[str, Any]:
        """
        Get recommendations for memory decay actions.

        Args:
            user_id: User identifier
            config: Decay configuration

        Returns:
            Decay recommendations and analysis
        """
        all_memories = await self._get_all_user_memories(user_id)
        current_time = datetime.now()

        recommendations = {
            "total_memories": len(all_memories),
            "archival_candidates": [],
            "deletion_candidates": [],
            "high_importance_memories": [],
            "storage_analysis": {},
        }

        archival_candidates = []
        deletion_candidates = []
        high_importance = []
        total_storage = 0

        for memory in all_memories:
            importance = await self.calculate_importance_score(
                memory, current_time, config
            )
            memory_size = len(memory.get("content", ""))
            total_storage += memory_size

            memory_analysis = {
                "id": memory.get("id"),
                "importance_score": importance,
                "content_length": memory_size,
                "memory_type": memory.get("metadata", {}).get("memory_type"),
                "age_days": self._get_memory_age_days(memory, current_time),
            }

            if importance <= config.deletion_threshold:
                deletion_candidates.append(memory_analysis)
            elif importance <= config.archive_threshold:
                archival_candidates.append(memory_analysis)
            elif importance >= 0.8:
                high_importance.append(memory_analysis)

        recommendations["archival_candidates"] = sorted(
            archival_candidates, key=lambda x: x["importance_score"]
        )
        recommendations["deletion_candidates"] = sorted(
            deletion_candidates, key=lambda x: x["importance_score"]
        )
        recommendations["high_importance_memories"] = sorted(
            high_importance, key=lambda x: x["importance_score"], reverse=True
        )

        recommendations["storage_analysis"] = {
            "total_storage_bytes": total_storage,
            "average_memory_size": (
                total_storage / len(all_memories) if all_memories else 0
            ),
            "potential_savings_archival": sum(
                m["content_length"] for m in archival_candidates
            ),
            "potential_savings_deletion": sum(
                m["content_length"] for m in deletion_candidates
            ),
        }

        return recommendations

    def _create_initial_importance_score(
        self,
        memory: Dict[str, Any],
        config: DecayConfig,
    ) -> ImportanceScore:
        """Create initial importance score for a memory."""

        metadata = memory.get("metadata", {})
        memory_type = metadata.get("memory_type", "")

        # Base importance by memory type
        type_importance = {
            "goal": 0.9,
            "preference": 0.8,
            "injury": 0.95,
            "progress": 0.7,
            "conversation": 0.5,
            "workout_log": 0.6,
            "insight": 0.8,
        }

        base_score = type_importance.get(memory_type, 0.5)

        # Adjust based on content characteristics
        content = memory.get("content", "")
        if any(
            keyword in content.lower()
            for keyword in ["goal", "injury", "limitation", "achievement"]
        ):
            base_score = min(1.0, base_score + 0.1)

        return ImportanceScore(
            base_score=base_score,
            current_score=base_score,
            decay_mode=config.default_decay_mode,
            decay_rate=config.default_decay_rate,
        )

    def _get_memory_timestamp(self, memory: Dict[str, Any]) -> Optional[datetime]:
        """Extract timestamp from memory metadata."""

        metadata = memory.get("metadata", {})
        timestamp_str = metadata.get("timestamp")

        if timestamp_str:
            try:
                return datetime.fromisoformat(
                    timestamp_str.replace("Z", "+00:00")
                ).replace(tzinfo=None)
            except (ValueError, AttributeError):
                pass

        return None

    def _get_memory_age_days(
        self, memory: Dict[str, Any], current_time: datetime
    ) -> int:
        """Get memory age in days."""

        timestamp = self._get_memory_timestamp(memory)
        if timestamp:
            return (current_time - timestamp).days
        return 0

    def _calculate_recency_score(
        self, timestamp: Optional[datetime], current_time: datetime
    ) -> float:
        """Calculate recency score (0.0 to 1.0)."""

        if not timestamp:
            return 0.0

        age_days = (current_time - timestamp).days
        # Exponential decay with half-life of 30 days
        return 0.5 ** (age_days / 30.0)

    def _is_archived(self, memory: Dict[str, Any]) -> bool:
        """Check if a memory is already archived."""

        metadata = memory.get("metadata", {})
        summary_level = metadata.get("summary_level")
        return summary_level == SummaryLevel.ARCHIVED.value

    async def _archive_memory(
        self, memory_id: str, user_id: str, importance_score: float
    ) -> bool:
        """Archive a memory by updating its metadata."""

        # In a real implementation, this would update the memory's metadata
        # to mark it as archived and possibly move it to cold storage
        logger.info(
            f"Archiving memory {memory_id} for user {user_id} (importance: {importance_score:.3f})"
        )
        return True

    async def _get_old_memories(
        self, user_id: str, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get memories older than cutoff date."""

        all_memories = await self.mem0_adapter.search_memories(
            user_id=user_id,
            query="",  # Empty query to get all
            limit=1000,
        )

        old_memories = []
        for memory in all_memories:
            timestamp = self._get_memory_timestamp(memory)
            if timestamp and timestamp < cutoff_date:
                old_memories.append(memory)

        return old_memories

    async def _get_all_user_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all memories for a user."""

        return await self.mem0_adapter.search_memories(
            user_id=user_id,
            query="",  # Empty query to get all
            limit=10000,  # Large limit to get all memories
        )

    def _group_similar_memories(
        self, memories: List[Dict[str, Any]]
    ) -> List[List[Dict[str, Any]]]:
        """Group memories by content similarity for redundancy detection."""

        # Simplified similarity grouping based on content overlap
        groups = []
        processed = set()

        for i, memory1 in enumerate(memories):
            if i in processed:
                continue

            group = [memory1]
            content1 = memory1.get("content", "").lower()
            words1 = set(content1.split())

            for j, memory2 in enumerate(memories[i + 1 :], i + 1):
                if j in processed:
                    continue

                content2 = memory2.get("content", "").lower()
                words2 = set(content2.split())

                # Calculate Jaccard similarity
                if words1 and words2:
                    intersection = words1.intersection(words2)
                    union = words1.union(words2)
                    similarity = len(intersection) / len(union)

                    if similarity > 0.7:  # High similarity threshold
                        group.append(memory2)
                        processed.add(j)

            if len(group) > 1:
                groups.append(group)
            processed.add(i)

        return groups

    async def _update_importance_scores(
        self, user_id: str, config: DecayConfig
    ) -> None:
        """Update importance scores for all user memories."""

        # This would update the metadata of memories with current importance scores
        # For now, we'll just log the intention
        logger.info(f"Updating importance scores for user {user_id}")

    def get_decay_stats(self) -> Dict[str, Any]:
        """Get decay manager statistics."""

        return {
            **self.decay_stats,
            "efficiency_ratio": (
                self.decay_stats["memories_archived"]
                + self.decay_stats["memories_deleted"]
            )
            / max(1, self.decay_stats["memories_processed"]),
        }
