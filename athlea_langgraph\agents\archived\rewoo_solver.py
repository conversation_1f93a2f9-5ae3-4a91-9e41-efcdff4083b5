"""
ReWOO Solver Agent for Athlea Coaching System.

Implements the "Solver" phase of the ReWOO pattern, synthesizing outputs
from multiple parallel worker agents into coherent, comprehensive coaching responses.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.rewoo_state import ReWOOConfig, ReWOOState, WorkerResult

logger = logging.getLogger(__name__)


class ReWOOSolver:
    """
    ReWOO Solver Agent for intelligent response synthesis.

    Combines outputs from parallel worker agents into comprehensive,
    coherent coaching responses that address all aspects of user queries.
    """

    def __init__(self, config: Optional[ReWOOConfig] = None):
        """
        Initialize the ReWOO Solver.

        Args:
            config: ReWOO configuration settings
        """
        self.config = config or {}
        self.llm = create_azure_chat_openai()

    async def solve(self, state: ReWOOState) -> ReWOOState:
        """
        Synthesize worker results into a comprehensive coaching response.

        Args:
            state: Current ReWOO state with worker results

        Returns:
            Updated state with synthesized response
        """

        synthesis_start_time = time.time()
        logger.info("🧩 ReWOO Solver: Starting response synthesis")

        if not state.get("worker_results"):
            logger.warning("⚠️ No worker results found for synthesis")
            return await self._create_fallback_response(state)

        try:
            worker_results = state["worker_results"]
            execution_plan = state.get("execution_plan", {})

            # Filter successful results for synthesis
            successful_results = {
                task_id: result
                for task_id, result in worker_results.items()
                if result["success"]
                and result["confidence_score"]
                >= self.config.get("quality_threshold", 0.7)
            }

            if not successful_results:
                logger.warning(
                    "⚠️ No successful worker results meeting quality threshold"
                )
                return await self._create_error_synthesis(
                    state, "No high-quality worker results available"
                )

            # Generate synthesis reasoning
            synthesis_reasoning = await self._generate_synthesis_reasoning(
                state["user_query"],
                successful_results,
                execution_plan,
                state.get("user_profile"),
            )

            # Synthesize the final response
            final_response = await self._synthesize_response(
                state["user_query"],
                successful_results,
                synthesis_reasoning,
                execution_plan.get("execution_strategy", "parallel"),
            )

            # Check if reflection should be applied
            if state.get("reflection_enabled", False):
                final_response = await self._apply_reflection_if_enabled(
                    final_response, state
                )

            synthesis_time = time.time() - synthesis_start_time
            logger.info(
                f"✅ ReWOO Solver: Synthesis completed in {synthesis_time:.2f}s"
            )

            # Create completion message
            domains_addressed = list(
                set(
                    result["worker_agent"].replace("_coach", "")
                    for result in successful_results.values()
                )
            )
            completion_message = f"🎯 Comprehensive coaching response synthesized from {len(successful_results)} specialist inputs across {', '.join(domains_addressed)} domains"

            return {
                **state,
                "synthesis_reasoning": synthesis_reasoning,
                "final_response": final_response,
                "synthesis_start_time": synthesis_start_time,
                "total_execution_time": time.time()
                - state.get("planning_start_time", synthesis_start_time),
                "messages": state["messages"]
                + [
                    AIMessage(content=completion_message),
                    AIMessage(content=final_response),
                ],
            }

        except Exception as e:
            logger.error(f"❌ ReWOO Solver error: {str(e)}")
            return await self._create_error_synthesis(state, str(e))

    async def _generate_synthesis_reasoning(
        self,
        user_query: str,
        worker_results: Dict[str, WorkerResult],
        execution_plan: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]],
    ) -> str:
        """
        Generate reasoning for how to synthesize worker results.

        Args:
            user_query: Original user query
            worker_results: Successful worker results
            execution_plan: Original execution plan
            user_profile: User profile

        Returns:
            Synthesis reasoning explanation
        """

        # Create summary of worker contributions
        worker_summary = []
        for task_id, result in worker_results.items():
            domain = result["worker_agent"].replace("_coach", "")
            confidence = result["confidence_score"]
            tools_used = len(result["tools_used"])

            worker_summary.append(
                f"- {domain.title()} Coach: {confidence:.1f} confidence, {tools_used} tools used"
            )

        reasoning_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content="""You are synthesizing multiple coaching specialist responses. Your job is to create a reasoning plan for how to combine these responses effectively.

Consider:
1. How each specialist's input addresses different aspects of the query
2. Where there might be overlap or complementary information
3. What order to present information for maximum clarity
4. How to create a coherent narrative that flows naturally
5. Which specialist's advice should take priority for different aspects

Provide a clear synthesis strategy that will result in a comprehensive, well-structured response."""
                ),
                HumanMessage(
                    content=f"""Original Query: {user_query}

Execution Plan Strategy: {execution_plan.get('execution_strategy', 'parallel')}

Worker Contributions:
{chr(10).join(worker_summary)}

User Profile: {user_profile or 'No profile provided'}

Generate synthesis reasoning:"""
                ),
            ]
        )

        response = await self.llm.ainvoke(reasoning_prompt.format_messages())
        return response.content

    async def _synthesize_response(
        self,
        user_query: str,
        worker_results: Dict[str, WorkerResult],
        synthesis_reasoning: str,
        execution_strategy: str,
    ) -> str:
        """
        Synthesize worker results into final coaching response.

        Args:
            user_query: Original user query
            worker_results: Successful worker results
            synthesis_reasoning: Synthesis reasoning
            execution_strategy: Execution strategy used

        Returns:
            Final synthesized coaching response
        """

        # Prepare worker contributions for synthesis
        worker_contributions = []
        for task_id, result in worker_results.items():
            domain = result["worker_agent"].replace("_coach", "")
            contribution = f"""
**{domain.title()} Specialist Input:**
{result['result']}

*Confidence: {result['confidence_score']:.1f}, Tools used: {', '.join(result['tools_used']) if result['tools_used'] else 'None'}*
"""
            worker_contributions.append(contribution)

        synthesis_strategy = self.config.get("synthesis_strategy", "comprehensive")

        synthesis_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content=f"""You are an expert fitness coaching coordinator synthesizing responses from multiple specialists.

Your task is to create a comprehensive, coherent coaching response that:
1. Directly addresses the user's original query
2. Integrates insights from all relevant specialists
3. Maintains a natural, conversational flow
4. Prioritizes safety and evidence-based recommendations
5. Provides actionable, practical advice

Synthesis Strategy: {synthesis_strategy}
- comprehensive: Include all relevant specialist input with full integration
- prioritized: Focus on highest-priority/confidence inputs first
- domain_weighted: Weight responses based on domain relevance to query

Guidelines:
- Start with a brief acknowledgment of the user's query
- Present information in logical order (often: assessment → recommendations → implementation)
- Highlight any important safety considerations or warnings
- End with clear next steps or action items
- Maintain Athlea's supportive, expert coaching tone"""
                ),
                HumanMessage(
                    content=f"""Original User Query: {user_query}

Synthesis Reasoning:
{synthesis_reasoning}

Worker Specialist Contributions:
{chr(10).join(worker_contributions)}

Create a comprehensive, well-structured coaching response:"""
                ),
            ]
        )

        response = await self.llm.ainvoke(synthesis_prompt.format_messages())
        return response.content

    async def _apply_reflection_if_enabled(
        self, response: str, state: ReWOOState
    ) -> str:
        """
        Apply Phase 1 reflection to the synthesized response if enabled.

        Args:
            response: Synthesized response
            state: Current state

        Returns:
            Potentially improved response after reflection
        """

        if not state.get("reflection_enabled", False):
            return response

        logger.info("🔄 Applying Phase 1 reflection to ReWOO synthesis")

        try:
            # Import reflection components (lazy import to avoid circular dependencies)
            from athlea_langgraph.agents.reflection_agent import reflection_node
            from athlea_langgraph.agents.response_regenerator import (
                response_regenerator_node,
            )
            from athlea_langgraph.states.reflection_state import (
                create_reflection_config,
                enhance_state_with_reflection,
            )

            # Convert ReWOO state to reflection state
            reflection_state = enhance_state_with_reflection(
                dict(state),
                create_reflection_config(reflection_enabled=True, safety_threshold=0.8),
            )

            # Add the synthesized response as the latest message
            reflection_state["messages"] = state["messages"] + [
                AIMessage(content=response)
            ]

            # Apply reflection
            reflected_state = await reflection_node(reflection_state)

            # If reflection suggests improvements, regenerate
            if (
                reflected_state.get("reflection_metadata", {}).get(
                    "needs_improvement", False
                )
                and reflected_state.get("reflection_metadata", {}).get(
                    "safety_score", 1.0
                )
                >= 0.7
            ):

                improved_state = await response_regenerator_node(reflected_state)

                # Extract improved response
                if improved_state["messages"]:
                    for msg in reversed(improved_state["messages"]):
                        if isinstance(msg, AIMessage) and msg.content != response:
                            logger.info("✨ Response improved through reflection")
                            return msg.content

            # Mark that reflection was applied
            state["post_rewoo_reflection"] = True

        except Exception as e:
            logger.warning(
                f"⚠️ Reflection application failed: {str(e)}, using original response"
            )

        return response

    async def _create_fallback_response(self, state: ReWOOState) -> ReWOOState:
        """
        Create a fallback response when no worker results are available.

        Args:
            state: Current state

        Returns:
            State with fallback response
        """

        logger.warning("🔄 Creating fallback response due to no worker results")

        fallback_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content="""You are an Athlea fitness coach providing a helpful response when our specialized systems are unavailable.

Provide a general but helpful coaching response that:
1. Acknowledges the user's query
2. Provides basic, safe guidance
3. Encourages the user to try again or contact support
4. Maintains a supportive, professional tone"""
                ),
                HumanMessage(
                    content=f"User Query: {state.get('user_query', 'No query provided')}\n\nUser Profile: {state.get('user_profile', {})}\n\nProvide helpful fallback response:"
                ),
            ]
        )

        try:
            response = await self.llm.ainvoke(fallback_prompt.format_messages())
            fallback_response = response.content
        except Exception as e:
            logger.error(f"❌ Fallback response generation failed: {str(e)}")
            fallback_response = """I apologize, but I'm experiencing technical difficulties right now. 

For your fitness and nutrition questions, I recommend:
1. Consulting with a qualified fitness professional
2. Starting with basic, proven approaches appropriate for your fitness level
3. Prioritizing safety and gradual progression
4. Trying your request again in a few moments

Thank you for your patience, and I'm here to help when systems are fully operational!"""

        return {
            **state,
            "final_response": fallback_response,
            "synthesis_reasoning": "Fallback response due to no worker results",
            "total_execution_time": time.time()
            - state.get("planning_start_time", time.time()),
            "messages": state["messages"]
            + [
                AIMessage(content="⚠️ Using fallback response approach"),
                AIMessage(content=fallback_response),
            ],
        }

    async def _create_error_synthesis(
        self, state: ReWOOState, error_message: str
    ) -> ReWOOState:
        """
        Create an error response when synthesis fails.

        Args:
            state: Current state
            error_message: Error that occurred

        Returns:
            State with error response
        """

        logger.error(f"🚨 Synthesis error: {error_message}")

        # Try to salvage any partial worker results
        worker_results = state.get("worker_results", {})
        partial_responses = []

        for task_id, result in worker_results.items():
            if result.get("success", False):
                domain = result["worker_agent"].replace("_coach", "")
                partial_responses.append(
                    f"**{domain.title()}:** {result['result'][:200]}..."
                )

        if partial_responses:
            error_response = f"""I encountered an issue while coordinating responses from our coaching specialists, but I was able to gather some initial insights:

{chr(10).join(partial_responses)}

For a complete response, please try your request again. If the issue persists, our coaching team is available to help directly."""
        else:
            error_response = f"""I apologize, but I encountered a technical issue while processing your coaching request: {error_message}

Please try again, and if the problem continues, our support team can assist you directly. Your fitness journey is important to us!"""

        return {
            **state,
            "final_response": error_response,
            "synthesis_reasoning": f"Error synthesis due to: {error_message}",
            "total_execution_time": time.time()
            - state.get("planning_start_time", time.time()),
            "messages": state["messages"]
            + [
                AIMessage(content=f"❌ Synthesis error: {error_message}"),
                AIMessage(content=error_response),
            ],
        }

    def _calculate_synthesis_quality(
        self, worker_results: Dict[str, WorkerResult]
    ) -> float:
        """
        Calculate overall quality of the synthesis based on worker results.

        Args:
            worker_results: Worker results to evaluate

        Returns:
            Quality score (0.0-1.0)
        """

        if not worker_results:
            return 0.0

        # Calculate average confidence of successful results
        successful_results = [r for r in worker_results.values() if r["success"]]
        if not successful_results:
            return 0.0

        avg_confidence = sum(r["confidence_score"] for r in successful_results) / len(
            successful_results
        )

        # Adjust for coverage (more domains = better coverage)
        domains_covered = len(set(r["worker_agent"] for r in successful_results))
        coverage_bonus = min(
            domains_covered * 0.1, 0.3
        )  # Up to 0.3 bonus for good coverage

        # Adjust for tool usage (more tools = more thorough analysis)
        avg_tools = sum(len(r["tools_used"]) for r in successful_results) / len(
            successful_results
        )
        tool_bonus = min(avg_tools * 0.05, 0.1)  # Up to 0.1 bonus for tool usage

        quality = avg_confidence + coverage_bonus + tool_bonus
        return min(1.0, quality)


async def rewoo_solver_node(state: ReWOOState) -> ReWOOState:
    """
    ReWOO Solver node for LangGraph integration.

    Args:
        state: Current ReWOO state

    Returns:
        Updated state with synthesized response
    """

    config = state.get("coordination_metadata", {}).get("config")
    solver = ReWOOSolver(config)

    return await solver.solve(state)
