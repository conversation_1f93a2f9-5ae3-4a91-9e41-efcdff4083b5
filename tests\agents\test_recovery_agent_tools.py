"""
Test suite for Recovery Agent direct tool integration.

This test suite verifies that the recovery agent can properly load and use
its domain-specific tools (generate_mobility_protocol, optimize_sleep, assess_wellness)
without relying on a central tools manager.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from athlea_langgraph.states import AgentState
from athlea_langgraph.agents.recovery_agent import recovery_agent


class TestRecoveryAgentToolsIntegration:
    """Test recovery agent's direct tool integration capabilities."""

    @pytest.mark.asyncio
    async def test_recovery_agent_initialization(self):
        """Test that recovery agent initializes properly."""
        assert recovery_agent is not None
        assert recovery_agent.name == "recovery_agent"
        assert recovery_agent.domain == "recovery_wellness"
        assert hasattr(recovery_agent, "get_domain_tools")

    @pytest.mark.asyncio
    async def test_recovery_agent_tool_loading_efficiency(self):
        """Test that tools are loaded efficiently with caching."""
        # First call should load tools
        tools_first = await recovery_agent.get_domain_tools()

        # Second call should use cached tools
        tools_second = await recovery_agent.get_domain_tools()

        assert tools_first == tools_second
        assert (
            len(tools_first) == 4
        )  # generate_mobility_protocol, optimize_sleep, assess_wellness
        assert recovery_agent._tools_loaded is True

    @pytest.mark.asyncio
    async def test_recovery_agent_has_expected_tools(self):
        """Test that recovery agent has the expected domain-specific tools."""
        tools = await recovery_agent.get_domain_tools()
        tool_names = [tool.name for tool in tools]

        expected_tools = [
            "generate_mobility_protocol",
            "optimize_sleep",
            "assess_wellness",
            "graphrag_search",
        ]

        for expected_tool in expected_tools:
            assert (
                expected_tool in tool_names
            ), f"Missing expected tool: {expected_tool}"

    @pytest.mark.asyncio
    async def test_recovery_agent_tool_integration(self):
        """Test that recovery agent can integrate with its tools."""
        # Mock the tools to avoid actual API calls
        with (
            patch(
                "athlea_langgraph.tools.recovery.generate_mobility_protocol"
            ) as mock_mobility,
            patch("athlea_langgraph.tools.recovery.optimize_sleep") as mock_sleep,
            patch("athlea_langgraph.tools.recovery.assess_wellness") as mock_wellness,
        ):

            # Configure mocks
            mock_mobility.name = "generate_mobility_protocol"
            mock_sleep.name = "optimize_sleep"
            mock_wellness.name = "assess_wellness"

            # Reset tools to force reload with mocks
            recovery_agent._tools_loaded = False
            recovery_agent.tools = []

            tools = await recovery_agent.get_domain_tools()

            assert len(tools) == 4
            tool_names = [tool.name for tool in tools]
            assert "generate_mobility_protocol" in tool_names
            assert "optimize_sleep" in tool_names
            assert "assess_wellness" in tool_names

    @pytest.mark.asyncio
    async def test_recovery_agent_prompt_loading(self):
        """Test that recovery agent loads its system prompt."""
        await recovery_agent._load_system_prompt()
        prompt = recovery_agent.system_prompt

        assert prompt is not None
        assert len(prompt) > 0
        assert "Recovery Coach" in prompt or "recovery" in prompt.lower()
        assert recovery_agent._prompt_loaded is True

    @pytest.mark.asyncio
    @patch("athlea_langgraph.tools.recovery.assess_wellness")
    async def test_recovery_agent_process_method(self, mock_assess_wellness: AsyncMock):
        """Test that recovery agent has the wellness assessment tool available."""
        mock_assess_wellness.return_value = '{"recovery_score": 85}'

        # Test state
        test_state = AgentState(
            user_query="I'm feeling tired and sore",
            messages=[],
            user_profile={"sport": "running"},
        )

        result = await recovery_agent.process(test_state, {})

        # Verify the agent has tools available
        tools = await recovery_agent.get_domain_tools()
        tool_names = [tool.name for tool in tools]
        assert "assess_wellness" in tool_names

        # Verify the result is processed correctly
        assert result is not None
        assert "response" in result
        assert len(result["response"]) > 0

    @pytest.mark.asyncio
    async def test_recovery_agent_error_handling(self):
        """Test recovery agent handles errors gracefully."""
        # Test with invalid state
        with patch.object(recovery_agent, "get_domain_tools") as mock_get_tools:
            mock_get_tools.side_effect = Exception("Tool loading failed")

            # Reset loaded state
            recovery_agent._tools_loaded = False
            recovery_agent.tools = []

            with pytest.raises(Exception, match="Tool loading failed"):
                await recovery_agent.get_domain_tools()

    @pytest.mark.asyncio
    async def test_recovery_agent_wellness_assessment_integration(self):
        """Test recovery agent's integration with wellness assessment tool."""
        tools = await recovery_agent.get_domain_tools()
        wellness_tool = None

        for tool in tools:
            if tool.name == "assess_wellness":
                wellness_tool = tool
                break

        assert wellness_tool is not None, "assess_wellness tool not found"

    @pytest.mark.asyncio
    async def test_recovery_agent_sleep_optimization_integration(self):
        """Test recovery agent's integration with sleep optimization tool."""
        tools = await recovery_agent.get_domain_tools()
        sleep_tool = None

        for tool in tools:
            if tool.name == "optimize_sleep":
                sleep_tool = tool
                break

        assert sleep_tool is not None, "optimize_sleep tool not found"

    @pytest.mark.asyncio
    async def test_recovery_agent_mobility_protocol_integration(self):
        """Test recovery agent's integration with mobility protocol tool."""
        tools = await recovery_agent.get_domain_tools()
        mobility_tool = None

        for tool in tools:
            if tool.name == "generate_mobility_protocol":
                mobility_tool = tool
                break

        assert mobility_tool is not None, "generate_mobility_protocol tool not found"

    @pytest.mark.asyncio
    async def test_recovery_agent_concurrent_tool_access(self):
        """Test that recovery agent handles concurrent tool access properly."""
        # Reset tool loading state
        recovery_agent.tools = []
        recovery_agent._tools_loaded = False
        # Simulate concurrent access
        tasks = [
            recovery_agent.get_domain_tools(),
            recovery_agent.get_domain_tools(),
            recovery_agent.get_domain_tools(),
        ]

        results = await asyncio.gather(*tasks)

        # All results should be identical (cached)
        assert all(len(result) == 4 for result in results)
        assert all(result == results[0] for result in results)

    @pytest.mark.asyncio
    async def test_recovery_agent_performance_metrics(self):
        """Test recovery agent performance characteristics."""
        import time

        # Test tool loading performance
        recovery_agent.tools = []
        recovery_agent._tools_loaded = False
        start_time = time.time()
        tools = await recovery_agent.get_domain_tools()
        load_time = time.time() - start_time

        assert len(tools) == 4
        assert load_time < 1.0, f"Tool loading took too long: {load_time}s"

        # Test cached access performance
        start_time = time.time()
        cached_tools = await recovery_agent.get_domain_tools()
        cached_time = time.time() - start_time

        assert cached_tools == tools
        assert cached_time < 0.1, f"Cached access took too long: {cached_time}s"

    def test_recovery_agent_tool_names_consistency(self):
        """Test that recovery agent tool names are consistent."""
        expected_tools = {
            "generate_mobility_protocol",
            "optimize_sleep",
            "assess_wellness",
            "graphrag_search",
        }

        # This test verifies the expected tool names match what we import
        # It's a static test to catch naming inconsistencies
        assert len(expected_tools) == 4
        assert "generate_mobility_protocol" in expected_tools
        assert "optimize_sleep" in expected_tools
        assert "assess_wellness" in expected_tools

    @pytest.mark.asyncio
    async def test_recovery_agent_domain_specialization(self):
        """Test that recovery agent is properly specialized for its domain."""
        assert recovery_agent.domain == "recovery_wellness"
        assert "recovery" in recovery_agent.permissions[0]
        assert "sleep" in recovery_agent.permissions[1]
        assert "mobility" in recovery_agent.permissions[2]

        # Test that agent has appropriate configuration
        assert recovery_agent.max_iterations == 10
        assert recovery_agent.temperature == 0.7
