# Agent-Tool Integration Documentation

This document describes the hybrid tool integration approach implemented for the Athlea coaching system, combining direct tool integration with MCP (Model Context Protocol) servers.

## Overview

The tool integration system provides each coaching agent with domain-specific tools while leveraging MCP servers for external service integration. This hybrid approach optimizes for performance (direct tools) while maintaining standardization for external APIs (MCP).

## Architecture

### Hybrid Integration Model

```mermaid
graph TB
    A[Coaching Agent] --> B[Modular Tools Manager]
    B --> C[Direct Tools]
    B --> D[MCP Client Manager]
    D --> E[External MCP Server]
    E --> F[Azure Maps]
    E --> G[Airtable]
    E --> H[Google Search]
    E --> I[Weather API]
    
    C --> J[Strength Tools]
    C --> K[Nutrition Tools]
    C --> L[Cardio Tools]
    C --> M[Recovery Tools]
    C --> N[Mental Tools]
```

### Tool Distribution Strategy

**🔧 Direct Integration (High Performance)**
- Domain-specific calculation tools
- Assessment and evaluation tools
- Internal business logic
- Fast local operations

**🔌 MCP Integration (Standardized External APIs)**
- Location and mapping services (Azure Maps)
- Data retrieval services (Airtable, Search)
- External research APIs
- Weather and environmental data

## Implementation Components

### 1. MCP Client Manager (`mcp_client_manager.py`)

Manages connections to MCP servers and provides categorized tool access:

```python
from athlea_langgraph.tools.mcp_client_manager import get_mcp_client_manager

# Initialize MCP client
mcp_manager = await get_mcp_client_manager()

# Get location tools for route planning
location_tools = await mcp_manager.get_location_tools()

# Get data tools for research
data_tools = await mcp_manager.get_data_tools()
```

**Key Features:**
- Automatic server discovery and connection
- Tool categorization (location, data, external)
- Graceful fallback when MCP unavailable
- Caching for performance

### 2. Enhanced Modular Tools Manager

Updated to integrate MCP tools with direct tools:

```python
from athlea_langgraph.tools.modular_agent_tools import get_modular_tools_manager

tools_manager = await get_modular_tools_manager()

# Each agent gets hybrid tool set
strength_tools = tools_manager.get_strength_agent_tools()  # Direct + MCP location tools
cardio_tools = tools_manager.get_cardio_agent_tools()      # Direct + All location tools
nutrition_tools = tools_manager.get_nutrition_agent_tools() # Direct + MCP data tools
```

## Agent Tool Mapping

### 🏋️ Strength Agent
**Direct Tools:**
- `search_strength_exercises` - Exercise database queries
- `get_exercise_progression` - Progression calculations
- `strength_assessment` - Fitness evaluations

**MCP Tools:**
- `search_locations` - Find gyms and fitness centers
- `find_nearby_facilities` - Locate equipment and spaces

### 🥗 Nutrition Agent
**Direct Tools:**
- `calculate_daily_calories` - Metabolic calculations
- `calculate_macro_targets` - Macro distribution
- `macro_calculator` - Nutritional analysis

**MCP Tools:**
- `query_airtable` - Recipe and food databases
- `search_web` - Nutrition research and data

### 🏃 Cardio Agent
**Direct Tools:**
- `cardio_assessment` - Fitness evaluations
- `google_maps_elevation` - Route elevation analysis
- `azure_maps` - Basic route planning

**MCP Tools (Comprehensive Location Suite):**
- `search_locations` - Find running routes and tracks
- `get_route_directions` - Detailed route planning
- `find_nearby_facilities` - Locate trails and venues
- `calculate_travel_carbon` - Environmental impact
- `get_weather_data` - Weather conditions for outdoor training

### 🧘 Recovery Agent
**Direct Tools:**
- `recovery_assessment` - Recovery evaluations
- `session_generation` - Recovery protocol creation

**MCP Tools:**
- `search_web` - Recovery technique research
- `search_wikipedia` - Educational resources

### 🧠 Mental Agent
**Direct Tools:**
- `mental_assessment` - Psychological evaluations

**MCP Tools:**
- `search_web` - Mental health resources
- `search_wikipedia` - Research and educational content

## Configuration

### MCP Server Configuration

The system automatically connects to the external MCP server:

```python
# Default configuration in mcp_client_manager.py
server_config = {
    "external": {
        "url": "http://localhost:8006/mcp",
        "transport": "streamable_http",
    }
}
```

### Tool Prompts Integration

Coach prompts reference the available tools:

```json
{
  "system": "You have access to specialized tools:\n- Location tools for finding safe running/walking routes\n- Session generation tools for creating cardio workouts\n- Heart rate zone calculators and fitness assessments"
}
```

## Usage Examples

### Basic Agent Tool Usage

```python
# Initialize the tools manager
tools_manager = await get_modular_tools_manager()

# Create a cardio agent with hybrid tools
cardio_tools = tools_manager.get_cardio_agent_tools()

# The agent now has both direct and MCP tools
for tool in cardio_tools:
    print(f"Tool: {tool.name} - {tool.description}")
```

### MCP Tool Usage

```python
# Direct MCP client usage
mcp_manager = await get_mcp_client_manager()

# Get tools for a specific domain
agent_tools = await mcp_manager.get_tools_for_agent("cardio")

# Use location tools for route planning
location_tools = await mcp_manager.get_location_tools()
```

### Agent Integration

```python
from langgraph.prebuilt import create_react_agent

# Get tools for the specific agent
tools = tools_manager.get_cardio_agent_tools()

# Create agent with hybrid tool set
agent = create_react_agent(model, tools)

# Agent can now use both direct and MCP tools seamlessly
response = await agent.ainvoke({
    "messages": ["Plan a 5km running route in Central Park with elevation data"]
})
```

## Testing

### Test Tool Integration

```bash
# Run the tool integration test suite
cd /Users/<USER>/python-langgraph
python scripts/test_tool_integration.py
```

### Test MCP Servers

```bash
# Run MCP server tests
python run_mcp_tests.py
```

## Benefits of Hybrid Approach

### ✅ Performance Optimization
- **Direct tools**: Fast execution for internal calculations
- **MCP tools**: Standardized for external APIs
- **Selective loading**: Agents get only relevant tools

### ✅ Maintainability
- **Clear separation**: Internal vs external tool concerns
- **Standardized interfaces**: MCP protocol compliance
- **Modular architecture**: Easy to add/remove tools

### ✅ Scalability
- **MCP expansion**: Easy to add new external services
- **Agent specialization**: Domain-specific tool distribution
- **Contract enforcement**: Consistent tool behavior

### ✅ Fallback Resilience
- **Graceful degradation**: Works without MCP servers
- **Error isolation**: MCP failures don't break direct tools
- **Development flexibility**: Can develop without external dependencies

## Troubleshooting

### MCP Connection Issues

```bash
# Check if external MCP server is running
curl http://localhost:8006/mcp

# Check MCP server logs
cd mcp_servers/external_mcp
python server.py
```

### Tool Discovery Problems

```python
# Debug tool loading
tools_manager = await get_modular_tools_manager()
all_tools = tools_manager.get_all_tools_with_mcp()
print(all_tools)
```

### Missing Dependencies

```bash
# Install MCP dependencies
pip install -r requirements-mcp.txt

# Or install individually
pip install langchain-mcp-adapters mcp
```

## Future Enhancements

### Planned Improvements
1. **Dynamic MCP Discovery**: Auto-discover available MCP servers
2. **Tool Caching**: Cache MCP tool responses for performance
3. **Health Monitoring**: Monitor MCP server health and availability
4. **Load Balancing**: Distribute MCP requests across multiple servers

### Expansion Opportunities
1. **Additional MCP Servers**: Add domain-specific MCP servers
2. **Tool Composition**: Combine multiple tools for complex operations
3. **Agent Collaboration**: Cross-agent tool sharing
4. **Performance Metrics**: Tool usage analytics and optimization

This hybrid integration provides the best of both worlds: high-performance direct tools for core functionality and standardized MCP integration for external services. 