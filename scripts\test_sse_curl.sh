#!/bin/bash

# Test SSE connection with curl
# This helps diagnose connection issues at a lower level

echo "🧪 SSE Connection Test with curl"
echo "================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Backend URL
BACKEND_URL="http://localhost:8000"

# Test 1: Health check
echo -e "${YELLOW}Test 1: Health Check${NC}"
echo "Testing: $BACKEND_URL/api/health"
curl -s "$BACKEND_URL/api/health" | jq . || echo -e "${RED}Failed to connect to backend${NC}"
echo ""

# Test 2: Simple SSE stream
echo -e "${YELLOW}Test 2: Simple SSE Stream${NC}"
echo "Testing: Simple coaching query"
echo "Press Ctrl+C to stop after seeing some output"
echo ""

# URL encode the message
MESSAGE="What's a good beginner workout?"
THREAD_ID="test-thread-$(date +%s)"
USER_ID="test-user-123"

# Build the URL with parameters
URL="$BACKEND_URL/api/coaching?message=$(echo -n "$MESSAGE" | jq -sRr @uri)&threadId=$THREAD_ID&userId=$USER_ID"

echo "URL: $URL"
echo ""
echo "Starting SSE stream..."
echo "========================"

# Use curl with SSE headers and show raw output
curl -N -H "Accept: text/event-stream" \
     -H "Cache-Control: no-cache" \
     "$URL" 2>&1 | while IFS= read -r line; do
    # Add timestamp to each line
    echo "[$(date '+%H:%M:%S')] $line"
    
    # Check for specific patterns
    if [[ "$line" == *"ERR_INCOMPLETE_CHUNKED_ENCODING"* ]]; then
        echo -e "${RED}ERROR: Incomplete chunked encoding detected!${NC}"
    elif [[ "$line" == *"ERR_CONNECTION_REFUSED"* ]]; then
        echo -e "${RED}ERROR: Connection refused!${NC}"
    elif [[ "$line" == *"event: error"* ]]; then
        echo -e "${RED}ERROR: Error event received${NC}"
    elif [[ "$line" == *"event: complete"* ]]; then
        echo -e "${GREEN}SUCCESS: Stream completed${NC}"
        break
    fi
done

echo ""
echo "Test completed. Check output above for errors." 