"""
Base Agent Interface for Modular ReAct Coaching Architecture

This module provides the foundation for all coaching agents following multi-agent best practices
and ReAct (Reasoning and Acting) patterns:
- Single responsibility per agent
- Schema validation contracts
- Permission management
- ReAct tool execution pattern
- Standardized error handling

Reference: Multi-Agent Best Practices Guide + ReAct Pattern
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field, field_validator

from athlea_langgraph.states import AgentState

# ReAct executor removed - using direct tool calling

logger = logging.getLogger(__name__)


def convert_to_base_messages(
    conversation_history: List[Union[Dict[str, Any], BaseMessage]],
) -> List[BaseMessage]:
    """
    Convert conversation history from mixed format to proper BaseMessage objects.

    Args:
        conversation_history: List of messages in dict or BaseMessage format

    Returns:
        List of BaseMessage objects
    """
    messages = []
    for msg in conversation_history:
        if isinstance(msg, BaseMessage):
            # Already a BaseMessage, keep as is
            messages.append(msg)
        elif isinstance(msg, dict):
            # Convert dict to BaseMessage
            content = msg.get("content", "")
            role = msg.get("role", "user")

            if role == "user" or role == "human":
                messages.append(HumanMessage(content=content))
            elif role == "assistant" or role == "ai":
                messages.append(AIMessage(content=content))
            elif role == "system":
                messages.append(SystemMessage(content=content))
            else:
                # Default to HumanMessage for unknown roles
                logger.warning(f"Unknown role '{role}', treating as user message")
                messages.append(HumanMessage(content=content))
        else:
            logger.warning(f"Unknown message format: {type(msg)}, skipping")

    return messages


class AgentInputSchema(BaseModel):
    """Base input schema for all coaching agents."""

    user_query: str = Field(..., description="User's request or question")
    user_profile: Dict[str, Any] = Field(
        default_factory=dict, description="User profile information"
    )
    conversation_history: List[Union[Dict[str, Any], BaseMessage]] = Field(
        default_factory=list,
        description="Conversation history - can be dicts with 'role' and 'content' or BaseMessage objects",
    )

    @field_validator("conversation_history")
    @classmethod
    def validate_conversation_history(cls, v):
        """Convert conversation history to BaseMessage objects."""
        return convert_to_base_messages(v)


class AgentOutputSchema(BaseModel):
    """Base output schema for all coaching agents."""

    response: str = Field(..., description="Agent's response to the user")
    specialist_completed: bool = Field(
        default=True, description="Whether the specialist has completed their work"
    )
    error: Optional[str] = Field(default=None, description="Error message if any")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class BaseReActAgent(ABC):
    """
    Base class for ReAct-based coaching agents following multi-agent best practices.

    Each agent should:
    1. Focus on a single coaching domain (strength, nutrition, etc.)
    2. Use ReAct pattern for tool execution and reasoning
    3. Validate inputs/outputs with schemas
    4. Only access tools relevant to their domain
    5. Handle errors gracefully
    6. Respect permission boundaries
    """

    def __init__(
        self,
        name: str,
        domain: str,
        system_prompt: str,
        tools: List[BaseTool],
        permissions: List[str],
        max_iterations: int = 10,
        temperature: float = 0.7,
        input_schema: Type[BaseModel] = AgentInputSchema,
        output_schema: Type[BaseModel] = AgentOutputSchema,
    ):
        self.name = name
        self.domain = domain
        self.system_prompt = system_prompt
        self.tools = tools
        self.permissions = permissions
        self.max_iterations = max_iterations
        self.temperature = temperature
        self.input_schema = input_schema
        self.output_schema = output_schema

        # Tools and prompt are ready for direct tool calling
        logger.info(
            f"Initialized {self.name} agent for {self.domain} domain with {len(tools)} tools (direct calling)"
        )

    async def process(
        self, state: AgentState, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Main entry point for ReAct agent processing.

        Args:
            state: Current agent state
            config: Optional configuration

        Returns:
            Dictionary with agent response following output schema
        """
        try:
            # 1. Validate input
            input_data = self._validate_input(state)

            # 2. Check permissions
            if not self._check_permissions(state):
                return self._create_error_response(
                    "Insufficient permissions for this agent"
                )

            # 3. Execute direct tool calling
            result = await self._execute_direct_tool_calling(input_data, config)

            # 4. Validate output
            validated_result = self._validate_output(result)

            # 5. Add agent metadata
            validated_result.update(
                {
                    "agent_name": self.name,
                    "agent_domain": self.domain,
                    "tools_available": [tool.name for tool in self.tools],
                    "tools_called": result.get("metadata", {}).get("tools_called", 0),
                }
            )

            return validated_result

        except Exception as e:
            logger.error(f"Error in {self.name} agent: {e}")
            return self._create_error_response(f"Agent error: {str(e)}")

    async def _execute_direct_tool_calling(
        self, validated_input: AgentInputSchema, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute direct tool calling without ReAct wrapper.

        Args:
            validated_input: Validated input data
            config: Optional configuration

        Returns:
            Dictionary with response, metadata, and full message history
        """
        from ..services.azure_openai_service import create_azure_chat_openai
        from langchain_core.messages import SystemMessage, HumanMessage

        try:
            # Create LLM with direct tool calling
            llm = create_azure_chat_openai(temperature=self.temperature, streaming=True)

            # Bind tools to LLM
            llm_with_tools = llm.bind_tools(self.tools)

            # Create conversation messages
            conversation_messages = [
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=validated_input.user_query),
            ]

            # Add conversation history if available
            if validated_input.conversation_history:
                conversation_messages = (
                    [SystemMessage(content=self.system_prompt)]
                    + list(validated_input.conversation_history)
                    + [HumanMessage(content=validated_input.user_query)]
                )

            # Call LLM with direct tool calling
            response = await llm_with_tools.ainvoke(conversation_messages)

            # Execute tool calls if any and synthesize results
            tool_results = []
            if response.tool_calls:
                logger.info(
                    f"🔧 {self.name}: Executing {len(response.tool_calls)} tool calls"
                )

                for tool_call in response.tool_calls:
                    tool_name = tool_call["name"]
                    tool_args = tool_call["args"]

                    logger.info(
                        f"🔧 {self.name}: Calling tool {tool_name} with args: {tool_args}"
                    )

                    # Find and execute the tool
                    tool = next((t for t in self.tools if t.name == tool_name), None)
                    if tool:
                        try:
                            tool_result = (
                                await tool.ainvoke(tool_args)
                                if hasattr(tool, "ainvoke")
                                else tool.invoke(tool_args)
                            )
                            tool_results.append(
                                {
                                    "tool_name": tool_name,
                                    "tool_args": tool_args,
                                    "tool_result": tool_result,
                                }
                            )
                            logger.info(
                                f"✅ {self.name}: Tool {tool_name} executed successfully"
                            )
                        except Exception as tool_error:
                            logger.error(
                                f"❌ {self.name}: Tool {tool_name} failed: {tool_error}"
                            )
                            tool_results.append(
                                {
                                    "tool_name": tool_name,
                                    "tool_args": tool_args,
                                    "tool_result": f"Error: {tool_error}",
                                }
                            )
                    else:
                        logger.error(f"❌ {self.name}: Tool {tool_name} not found")
                        tool_results.append(
                            {
                                "tool_name": tool_name,
                                "tool_args": tool_args,
                                "tool_result": f"Tool {tool_name} not found",
                            }
                        )

            # CRITICAL FIX: Synthesize tool results into proper coaching response
            if tool_results:
                logger.info(
                    f"🎯 {self.name}: Synthesizing {len(tool_results)} tool results into coaching response"
                )

                # Start building the messages for the synthesis call
                synthesis_messages = conversation_messages + [response]

                # CRITICAL FIX: Add tool results back to the conversation history
                # for the synthesis step. This is required by the tool-calling model.
                from langchain_core.messages import ToolMessage

                for tool_call, result in zip(response.tool_calls, tool_results):
                    synthesis_messages.append(
                        ToolMessage(
                            content=str(result["tool_result"]),
                            tool_call_id=tool_call["id"],
                        )
                    )

                # Add the final user-facing prompt
                synthesis_messages.append(
                    HumanMessage(
                        content="Based on the tool results, please provide a comprehensive, helpful, and human-readable coaching response. Synthesize this information into actionable advice, formatted as a professional fitness coach would. Do not mention the tools or include raw data."
                    )
                )

                # Get synthesized response
                try:
                    synthesis_response = await llm.ainvoke(synthesis_messages)
                    final_response = synthesis_response.content
                    logger.info(
                        f"✅ {self.name}: Successfully synthesized tool results into coaching response"
                    )
                except Exception as e:
                    logger.error(
                        f"❌ {self.name}: Synthesis failed, using fallback: {e}"
                    )
                    # Fallback: use original response + simple formatting
                    final_response = response.content
                    if tool_results:
                        final_response += f"\n\nBased on my analysis:\n"
                        for result in tool_results:
                            if "error" not in str(result["tool_result"]).lower():
                                final_response += f"• {result['tool_result']}\n"
            else:
                # No tools called, use original response
                final_response = response.content

            return {
                "response": final_response,
                "specialist_completed": True,
                "error": None,
                "metadata": {
                    "tools_called": (
                        len(response.tool_calls) if response.tool_calls else 0
                    ),
                    "agent_name": self.name,
                    "agent_domain": self.domain,
                },
                "messages": conversation_messages + [response],
            }

        except Exception as e:
            logger.error(f"Error in direct tool calling for {self.name}: {e}")
            return {
                "response": f"I encountered an error: {str(e)}",
                "specialist_completed": False,
                "error": str(e),
                "metadata": {
                    "agent_name": self.name,
                    "agent_domain": self.domain,
                },
                "messages": validated_input.conversation_history,
            }

    def _validate_input(self, state: AgentState) -> AgentInputSchema:
        """Validate input state against the Pydantic schema."""
        try:
            # The schema will automatically convert the conversation history
            return self.input_schema(
                user_query=state.get("user_query", ""),
                user_profile=state.get("user_profile", {}),
                conversation_history=state.get("messages", []),
            )
        except Exception as e:
            raise ValueError(f"Input validation failed: {e}")

    def _validate_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate output against agent's output schema."""
        try:
            validated = self.output_schema(**result)
            return validated.model_dump()
        except Exception as e:
            logger.warning(f"Output validation failed for {self.name}: {e}")
            # Return result anyway but log the validation issue
            return result

    def _check_permissions(self, state: AgentState) -> bool:
        """Check if agent has permission to access required data."""
        # Domain-specific permission checking can be implemented here
        # For now, return True but this can be enhanced with proper ACL
        return True

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "response": f"I'm sorry, I encountered an issue: {error_message}",
            "specialist_completed": False,
            "error": error_message,
            "agent_name": self.name,
            "agent_domain": self.domain,
            "metadata": {"error_type": "agent_error"},
        }

    # Abstract methods to be implemented by each coaching agent
    @abstractmethod
    def get_domain_tools(self) -> List[BaseTool]:
        """Get domain-specific tools for this agent."""
        pass

    @abstractmethod
    def get_domain_prompt(self) -> str:
        """Get domain-specific system prompt."""
        pass

    # Concrete methods for agent information
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        return self.system_prompt

    def get_domain(self) -> str:
        """Get the coaching domain this agent handles."""
        return self.domain

    def get_permissions(self) -> List[str]:
        """Get the data permissions this agent requires."""
        return self.permissions

    def get_tools(self) -> List[BaseTool]:
        """Get all tools available to this agent."""
        return self.tools

    def __str__(self) -> str:
        return f"{self.name} ({self.domain} domain, direct tool calling)"

    def __repr__(self) -> str:
        return f"BaseDirectToolAgent(name='{self.name}', domain='{self.domain}', tools={len(self.tools)})"


# Factory function to create agent node functions for LangGraph
async def create_agent_node_function(agent: BaseReActAgent):
    """
    Create a node function for LangGraph from a BaseReActAgent.

    Args:
        agent: The ReAct agent instance

    Returns:
        Async function suitable for use as a LangGraph node
    """

    async def agent_node(
        state: AgentState, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Auto-generated node function for direct tool calling agent."""
        return await agent.process(state, config)

    # Set function name for better debugging
    agent_node.__name__ = f"{agent.name}_node"
    return agent_node


__all__ = [
    "AgentInputSchema",
    "AgentOutputSchema",
    "BaseReActAgent",  # Now using direct tool calling
    "create_agent_node_function",
    "convert_to_base_messages",
]
