#!/usr/bin/env python3
"""
Detailed test to analyze streaming behavior and timing.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import List, Dict, Any

import aiohttp


class DetailedStreamingAnalyzer:
    """Analyze streaming behavior in detail."""

    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url

    async def analyze_streaming(
        self,
        message: str,
        thread_id: str,
        user_id: str,
    ) -> Dict[str, Any]:
        """Analyze streaming response timing and chunking."""
        print(f"\n🔬 ANALYZING: {message[:50]}...")

        url = f"{self.backend_url}/api/coaching"
        params = {
            "message": message,
            "threadId": thread_id,
            "userId": user_id,
        }

        events: List[Dict[str, Any]] = []
        start_time = time.time()
        first_token_time = None
        last_event_time = start_time

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    params=params,
                    headers={"Accept": "text/event-stream"},
                ) as response:
                    if response.status != 200:
                        return {"error": f"HTTP {response.status}"}

                    async for line in response.content:
                        current_time = time.time()
                        line = line.decode("utf-8").strip()

                        if line.startswith("event:"):
                            event_type = line[6:].strip()
                        elif line.startswith("data:") and line != "data:":
                            try:
                                data = json.loads(line[5:].strip())

                                event_info = {
                                    "type": event_type,
                                    "time": current_time - start_time,
                                    "delta": current_time - last_event_time,
                                    "data": data,
                                }

                                if event_type == "token" and first_token_time is None:
                                    first_token_time = current_time
                                    event_info["first_token"] = True

                                events.append(event_info)
                                last_event_time = current_time

                                if event_type == "complete":
                                    break

                            except json.JSONDecodeError:
                                pass

        except Exception as e:
            return {"error": str(e)}

        # Analyze the results
        total_time = last_event_time - start_time
        time_to_first_token = (
            (first_token_time - start_time) if first_token_time else None
        )

        # Group tokens by agent
        tokens_by_agent: Dict[str, List[Dict[str, Any]]] = {}
        for event in events:
            if event["type"] == "token":
                agent = event["data"].get("agent", "unknown")
                if agent not in tokens_by_agent:
                    tokens_by_agent[agent] = []
                tokens_by_agent[agent].append(
                    {
                        "content": event["data"].get("content", ""),
                        "time": event["time"],
                        "delta": event["delta"],
                    }
                )

        # Calculate statistics
        analysis = {
            "total_time": total_time,
            "time_to_first_token": time_to_first_token,
            "total_events": len(events),
            "event_types": {},
            "agents": {},
        }

        # Count event types
        for event in events:
            event_type = event["type"]
            if event_type not in analysis["event_types"]:
                analysis["event_types"][event_type] = 0
            analysis["event_types"][event_type] += 1

        # Analyze each agent's streaming
        for agent, tokens in tokens_by_agent.items():
            total_content = "".join(t["content"] for t in tokens)
            analysis["agents"][agent] = {
                "token_count": len(tokens),
                "total_chars": len(total_content),
                "avg_chars_per_token": (
                    len(total_content) / len(tokens) if tokens else 0
                ),
                "time_between_tokens": [t["delta"] for t in tokens[1:]],
                "content_preview": (
                    total_content[:100] + "..."
                    if len(total_content) > 100
                    else total_content
                ),
            }

        return analysis


async def main():
    """Run detailed streaming analysis."""
    analyzer = DetailedStreamingAnalyzer()

    test_messages = [
        "How can I start cycling training?",
        "What's a good warm-up routine?",
        "I need a detailed 12-week marathon training plan with nutrition advice",
    ]

    print("🔬 Detailed Streaming Analysis")
    print("=" * 80)

    for i, message in enumerate(test_messages, 1):
        print(f"\n📊 Test {i}/{len(test_messages)}")
        print(f"Message: {message}")
        print("-" * 40)

        analysis = await analyzer.analyze_streaming(
            message=message, thread_id=f"test-{i}", user_id="test-user"
        )

        if "error" in analysis:
            print(f"❌ Error: {analysis['error']}")
            continue

        print(f"\n⏱️  Timing:")
        print(f"  - Total time: {analysis['total_time']:.2f}s")
        print(
            f"  - Time to first token: {analysis['time_to_first_token']:.2f}s"
            if analysis["time_to_first_token"]
            else "  - No tokens received"
        )

        print(f"\n📈 Events:")
        for event_type, count in analysis["event_types"].items():
            print(f"  - {event_type}: {count}")

        print(f"\n🤖 Agent Analysis:")
        for agent, stats in analysis["agents"].items():
            print(f"\n  {agent}:")
            print(f"    - Tokens: {stats['token_count']}")
            print(f"    - Total chars: {stats['total_chars']}")
            print(f"    - Avg chars/token: {stats['avg_chars_per_token']:.0f}")
            if stats["time_between_tokens"]:
                avg_delta = sum(stats["time_between_tokens"]) / len(
                    stats["time_between_tokens"]
                )
                print(f"    - Avg time between tokens: {avg_delta:.3f}s")
            print(f"    - Content: {stats['content_preview']}")

        if i < len(test_messages):
            await asyncio.sleep(1)

    print("\n" + "=" * 80)
    print("✅ Analysis complete!")

    # Provide recommendations
    print("\n💡 Streaming Behavior Analysis:")
    print("\nCURRENT BEHAVIOR:")
    print("- The backend is sending complete messages as single tokens")
    print("- This causes the frontend to display the entire response at once")
    print("- No progressive/streaming display effect")

    print("\nRECOMMENDED FIX:")
    print("1. Modify the LLM calls to use streaming=True")
    print("2. Stream tokens as they arrive from the LLM")
    print("3. Each token should be sent as a separate SSE event")
    print("4. This will create the progressive typing effect in the frontend")


if __name__ == "__main__":
    asyncio.run(main())
