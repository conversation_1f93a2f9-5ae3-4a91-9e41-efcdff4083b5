"""
A/B Testing Framework for prompt experimentation with traffic splitting and analytics.
"""

import hashlib
import json
import random
import statistics
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .prompt_models import PromptConfig
from .version_manager import VersionManager


class TestStatus(Enum):
    """A/B Test status."""

    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    STOPPED = "stopped"


@dataclass
class TestMetrics:
    """Metrics for A/B test evaluation."""

    total_requests: int = 0
    success_rate: float = 0.0
    average_response_time: float = 0.0
    error_count: int = 0
    user_satisfaction: float = 0.0
    conversion_rate: float = 0.0
    custom_metrics: Dict[str, float] = None

    def __post_init__(self):
        if self.custom_metrics is None:
            self.custom_metrics = {}

    def to_dict(self) -> dict:
        return {
            "total_requests": self.total_requests,
            "success_rate": self.success_rate,
            "average_response_time": self.average_response_time,
            "error_count": self.error_count,
            "user_satisfaction": self.user_satisfaction,
            "conversion_rate": self.conversion_rate,
            "custom_metrics": self.custom_metrics,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "TestMetrics":
        return cls(
            total_requests=data.get("total_requests", 0),
            success_rate=data.get("success_rate", 0.0),
            average_response_time=data.get("average_response_time", 0.0),
            error_count=data.get("error_count", 0),
            user_satisfaction=data.get("user_satisfaction", 0.0),
            conversion_rate=data.get("conversion_rate", 0.0),
            custom_metrics=data.get("custom_metrics", {}),
        )


@dataclass
class TestVariant:
    """A single variant in an A/B test."""

    name: str
    prompt_name: str
    version: str
    traffic_percentage: float
    metrics: TestMetrics
    description: str = ""

    def to_dict(self) -> dict:
        return {
            "name": self.name,
            "prompt_name": self.prompt_name,
            "version": self.version,
            "traffic_percentage": self.traffic_percentage,
            "metrics": self.metrics.to_dict(),
            "description": self.description,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "TestVariant":
        return cls(
            name=data["name"],
            prompt_name=data["prompt_name"],
            version=data["version"],
            traffic_percentage=data["traffic_percentage"],
            metrics=TestMetrics.from_dict(data["metrics"]),
            description=data.get("description", ""),
        )


@dataclass
class ABTest:
    """A/B Test configuration and state."""

    test_id: str
    name: str
    description: str
    prompt_name: str
    variants: List[TestVariant]
    status: TestStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    target_sample_size: int = 1000
    confidence_level: float = 0.95
    minimum_effect_size: float = 0.05
    created_by: str = "System"
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

    def to_dict(self) -> dict:
        return {
            "test_id": self.test_id,
            "name": self.name,
            "description": self.description,
            "prompt_name": self.prompt_name,
            "variants": [v.to_dict() for v in self.variants],
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "ended_at": self.ended_at.isoformat() if self.ended_at else None,
            "target_sample_size": self.target_sample_size,
            "confidence_level": self.confidence_level,
            "minimum_effect_size": self.minimum_effect_size,
            "created_by": self.created_by,
            "tags": self.tags,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ABTest":
        return cls(
            test_id=data["test_id"],
            name=data["name"],
            description=data["description"],
            prompt_name=data["prompt_name"],
            variants=[TestVariant.from_dict(v) for v in data["variants"]],
            status=TestStatus(data["status"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            started_at=(
                datetime.fromisoformat(data["started_at"])
                if data.get("started_at")
                else None
            ),
            ended_at=(
                datetime.fromisoformat(data["ended_at"])
                if data.get("ended_at")
                else None
            ),
            target_sample_size=data.get("target_sample_size", 1000),
            confidence_level=data.get("confidence_level", 0.95),
            minimum_effect_size=data.get("minimum_effect_size", 0.05),
            created_by=data.get("created_by", "System"),
            tags=data.get("tags", []),
        )


@dataclass
class TestResults:
    """Statistical results of an A/B test."""

    test_id: str
    winning_variant: Optional[str]
    confidence_score: float
    p_value: float
    effect_size: float
    is_statistically_significant: bool
    sample_size_achieved: bool
    duration_days: float
    recommendations: List[str]
    detailed_analysis: Dict[str, Any]

    def to_dict(self) -> dict:
        return {
            "test_id": self.test_id,
            "winning_variant": self.winning_variant,
            "confidence_score": self.confidence_score,
            "p_value": self.p_value,
            "effect_size": self.effect_size,
            "is_statistically_significant": self.is_statistically_significant,
            "sample_size_achieved": self.sample_size_achieved,
            "duration_days": self.duration_days,
            "recommendations": self.recommendations,
            "detailed_analysis": self.detailed_analysis,
        }


class ABTestManager:
    """Manager for A/B testing operations."""

    def __init__(self, prompts_dir: str):
        self.prompts_dir = Path(prompts_dir)
        self.ab_tests_dir = self.prompts_dir / ".ab_tests"
        self.test_logs_dir = self.ab_tests_dir / "logs"

        # Create directories
        self.ab_tests_dir.mkdir(exist_ok=True)
        self.test_logs_dir.mkdir(exist_ok=True)

        self.version_manager = VersionManager(prompts_dir)

    def create_test(
        self,
        name: str,
        description: str,
        prompt_name: str,
        variant_configs: List[Dict[str, Any]],
        target_sample_size: int = 1000,
        confidence_level: float = 0.95,
        minimum_effect_size: float = 0.05,
        created_by: str = "System",
        tags: List[str] = None,
    ) -> ABTest:
        """Create a new A/B test."""
        # Generate test ID
        test_id = f"test_{prompt_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Validate traffic split
        total_traffic = sum(config["traffic_percentage"] for config in variant_configs)
        if abs(total_traffic - 100.0) > 0.01:
            raise ValueError(
                f"Traffic percentages must sum to 100, got {total_traffic}"
            )

        # Create variants
        variants = []
        for config in variant_configs:
            # Verify version exists
            versions = self.version_manager.list_versions(prompt_name)
            if config["version"] not in versions:
                raise ValueError(
                    f"Version {config['version']} not found for prompt {prompt_name}"
                )

            variant = TestVariant(
                name=config["name"],
                prompt_name=prompt_name,
                version=config["version"],
                traffic_percentage=config["traffic_percentage"],
                metrics=TestMetrics(),
                description=config.get("description", ""),
            )
            variants.append(variant)

        # Create test
        test = ABTest(
            test_id=test_id,
            name=name,
            description=description,
            prompt_name=prompt_name,
            variants=variants,
            status=TestStatus.DRAFT,
            created_at=datetime.now(),
            target_sample_size=target_sample_size,
            confidence_level=confidence_level,
            minimum_effect_size=minimum_effect_size,
            created_by=created_by,
            tags=tags or [],
        )

        # Save test
        self._save_test(test)

        return test

    def start_test(self, test_id: str) -> ABTest:
        """Start an A/B test."""
        test = self.load_test(test_id)

        if test.status != TestStatus.DRAFT:
            raise ValueError(
                f"Test {test_id} cannot be started from status {test.status.value}"
            )

        test.status = TestStatus.ACTIVE
        test.started_at = datetime.now()

        self._save_test(test)
        self._log_test_event(test_id, "Test started")

        return test

    def stop_test(self, test_id: str, reason: str = "Manual stop") -> ABTest:
        """Stop an A/B test."""
        test = self.load_test(test_id)

        if test.status not in [TestStatus.ACTIVE, TestStatus.PAUSED]:
            raise ValueError(
                f"Test {test_id} cannot be stopped from status {test.status.value}"
            )

        test.status = TestStatus.STOPPED
        test.ended_at = datetime.now()

        self._save_test(test)
        self._log_test_event(test_id, f"Test stopped: {reason}")

        return test

    def pause_test(self, test_id: str) -> ABTest:
        """Pause an A/B test."""
        test = self.load_test(test_id)

        if test.status != TestStatus.ACTIVE:
            raise ValueError(
                f"Test {test_id} cannot be paused from status {test.status.value}"
            )

        test.status = TestStatus.PAUSED

        self._save_test(test)
        self._log_test_event(test_id, "Test paused")

        return test

    def resume_test(self, test_id: str) -> ABTest:
        """Resume a paused A/B test."""
        test = self.load_test(test_id)

        if test.status != TestStatus.PAUSED:
            raise ValueError(
                f"Test {test_id} cannot be resumed from status {test.status.value}"
            )

        test.status = TestStatus.ACTIVE

        self._save_test(test)
        self._log_test_event(test_id, "Test resumed")

        return test

    def get_variant_for_user(self, test_id: str, user_id: str) -> Optional[TestVariant]:
        """Get the variant a user should see (deterministic traffic splitting)."""
        test = self.load_test(test_id)

        if test.status != TestStatus.ACTIVE:
            return None

        # Create deterministic hash for user
        hash_input = f"{test_id}:{user_id}"
        user_hash = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
        bucket = (user_hash % 10000) / 100.0  # 0-99.99%

        # Find variant based on traffic split
        cumulative_traffic = 0.0
        for variant in test.variants:
            cumulative_traffic += variant.traffic_percentage
            if bucket < cumulative_traffic:
                return variant

        # Fallback to first variant
        return test.variants[0] if test.variants else None

    def record_metrics(
        self,
        test_id: str,
        variant_name: str,
        metrics_update: Dict[str, Any],
    ):
        """Record metrics for a test variant."""
        test = self.load_test(test_id)

        # Find variant
        variant = next((v for v in test.variants if v.name == variant_name), None)
        if not variant:
            raise ValueError(f"Variant {variant_name} not found in test {test_id}")

        # Update metrics
        for key, value in metrics_update.items():
            if hasattr(variant.metrics, key):
                if key == "custom_metrics" and isinstance(value, dict):
                    variant.metrics.custom_metrics.update(value)
                else:
                    setattr(variant.metrics, key, value)

        self._save_test(test)
        self._log_test_event(test_id, f"Metrics updated for variant {variant_name}")

    def get_test_results(self, test_id: str) -> TestResults:
        """Analyze test results and provide statistical significance."""
        test = self.load_test(test_id)

        if len(test.variants) < 2:
            raise ValueError("Test must have at least 2 variants for analysis")

        # Get primary metric (using success_rate as default)
        primary_metric = "success_rate"
        variant_results = []

        for variant in test.variants:
            metric_value = getattr(variant.metrics, primary_metric, 0.0)
            sample_size = variant.metrics.total_requests
            variant_results.append((variant.name, metric_value, sample_size))

        # Simple statistical analysis (in production, use proper statistical tests)
        variant_results.sort(key=lambda x: x[1], reverse=True)  # Sort by metric value

        winning_variant = variant_results[0][0]
        best_metric = variant_results[0][1]
        runner_up_metric = variant_results[1][1] if len(variant_results) > 1 else 0

        # Calculate effect size
        effect_size = abs(best_metric - runner_up_metric) / max(best_metric, 0.01)

        # Simple significance check (placeholder - use proper statistical tests)
        total_requests = sum(v.metrics.total_requests for v in test.variants)
        sample_size_achieved = total_requests >= test.target_sample_size
        is_significant = (
            effect_size >= test.minimum_effect_size and sample_size_achieved
        )

        # Calculate duration
        duration_days = 0.0
        if test.started_at:
            end_time = test.ended_at or datetime.now()
            duration_days = (end_time - test.started_at).total_seconds() / 86400

        # Generate recommendations
        recommendations = []
        if is_significant:
            recommendations.append(f"Promote variant '{winning_variant}' as the winner")
        else:
            recommendations.append("Continue running the test for more data")
            if not sample_size_achieved:
                recommendations.append(
                    f"Need {test.target_sample_size - total_requests} more samples"
                )

        results = TestResults(
            test_id=test_id,
            winning_variant=winning_variant if is_significant else None,
            confidence_score=0.95 if is_significant else 0.5,  # Placeholder
            p_value=0.03 if is_significant else 0.15,  # Placeholder
            effect_size=effect_size,
            is_statistically_significant=is_significant,
            sample_size_achieved=sample_size_achieved,
            duration_days=duration_days,
            recommendations=recommendations,
            detailed_analysis={
                "variant_results": [
                    {
                        "name": name,
                        "metric_value": metric,
                        "sample_size": size,
                        "conversion_rate": getattr(
                            next(v for v in test.variants if v.name == name).metrics,
                            "conversion_rate",
                            0.0,
                        ),
                    }
                    for name, metric, size in variant_results
                ],
                "total_sample_size": total_requests,
                "test_duration_days": duration_days,
            },
        )

        return results

    def promote_winner(self, test_id: str, winning_variant: str) -> str:
        """Promote the winning variant to production."""
        test = self.load_test(test_id)

        # Find winning variant
        winner = next((v for v in test.variants if v.name == winning_variant), None)
        if not winner:
            raise ValueError(f"Variant {winning_variant} not found in test {test_id}")

        # Mark test as completed
        test.status = TestStatus.COMPLETED
        test.ended_at = datetime.now()

        self._save_test(test)
        self._log_test_event(
            test_id, f"Winner promoted: {winning_variant} (v{winner.version})"
        )

        return winner.version

    def list_tests(
        self,
        status: Optional[TestStatus] = None,
        prompt_name: Optional[str] = None,
    ) -> List[ABTest]:
        """List A/B tests with optional filtering."""
        tests = []

        for test_file in self.ab_tests_dir.glob("test_*.json"):
            try:
                test = self._load_test_from_file(test_file)

                # Apply filters
                if status and test.status != status:
                    continue
                if prompt_name and test.prompt_name != prompt_name:
                    continue

                tests.append(test)
            except Exception:
                continue  # Skip corrupted test files

        # Sort by creation date (newest first)
        tests.sort(key=lambda t: t.created_at, reverse=True)

        return tests

    def load_test(self, test_id: str) -> ABTest:
        """Load a test by ID."""
        test_file = self.ab_tests_dir / f"{test_id}.json"

        if not test_file.exists():
            raise FileNotFoundError(f"Test {test_id} not found")

        return self._load_test_from_file(test_file)

    def delete_test(self, test_id: str):
        """Delete a test (only if not active)."""
        test = self.load_test(test_id)

        if test.status == TestStatus.ACTIVE:
            raise ValueError(f"Cannot delete active test {test_id}")

        test_file = self.ab_tests_dir / f"{test_id}.json"
        if test_file.exists():
            test_file.unlink()

        self._log_test_event(test_id, "Test deleted")

    def _save_test(self, test: ABTest):
        """Save test to disk."""
        test_file = self.ab_tests_dir / f"{test.test_id}.json"

        with open(test_file, "w") as f:
            json.dump(test.to_dict(), f, indent=2, default=str)

    def _load_test_from_file(self, test_file: Path) -> ABTest:
        """Load test from file."""
        with open(test_file, "r") as f:
            data = json.load(f)

        return ABTest.from_dict(data)

    def _log_test_event(self, test_id: str, event: str):
        """Log test events for audit trail."""
        log_file = self.test_logs_dir / f"{test_id}.log"

        timestamp = datetime.now().isoformat()
        log_entry = f"{timestamp}: {event}\n"

        with open(log_file, "a") as f:
            f.write(log_entry)
