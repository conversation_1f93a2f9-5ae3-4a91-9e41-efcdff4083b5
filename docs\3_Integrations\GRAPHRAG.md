# GraphRAG Integration

## Overview

This document outlines the comprehensive GraphRAG integration that connects the Athlea LangGraph backend to the existing GraphRAG accelerator infrastructure. The integration provides evidence-based coaching through a combination of Azure Cognitive Search (textual evidence) and Cosmos DB Gremlin Graph (structured relationships).

## Architecture

The GraphRAG integration consists of several key components:

```
┌─────────────────────────────────────────────────────────────┐
│                    LangGraph Agent Nodes                   │
├─────────────────────────────────────────────────────────────┤
│  graphrag_agent_node  │  Specialized Coach Nodes            │
└─────────────────────────────────────────────────────────────┘
                             │
┌─────────────────────────────────────────────────────────────┐
│                   GraphRAG Service Layer                   │
├─────────────────────────────────────────────────────────────┤
│  GraphRAG Service     │  Gremlin Service  │  ACS Integration │
└─────────────────────────────────────────────────────────────┘
                             │
┌─────────────────────────────────────────────────────────────┐
│                External Infrastructure                      │
├─────────────────────────────────────────────────────────────┤
│  Azure Cognitive     │  Cosmos DB        │  GraphRAG        │
│  Search (ACS)        │  Gremlin Graph    │  Accelerator     │
│  (textual evidence)  │  (relationships)  │  (APIs)          │
└─────────────────────────────────────────────────────────────┘
```

## Key Components

### 1. Configuration (`athlea_langgraph/config/graphrag_config.py`)

Centralized configuration for all GraphRAG components:

```python
from athlea_langgraph.config.graphrag_config import get_graphrag_config

config = get_graphrag_config()
print(f"Cosmos endpoint: {config.cosmos_endpoint}")
print(f"ACS index: {config.acs_index_name}")
```

**Environment Variables Required:**
- `AZURE_SEARCH_SERVICE_NAME`
- `AZURE_SEARCH_API_KEY`
- `GRAPHRAG_API_BASE_URL` (optional)
- `GRAPHRAG_API_KEY` (optional)

### 2. Gremlin Service (`athlea_langgraph/services/gremlin_service.py`)

Direct connection to Cosmos DB Gremlin Graph:

```python
from athlea_langgraph.services.gremlin_service import get_gremlin_service

gremlin_service = get_gremlin_service()

# Find document by DOI
result = await gremlin_service.find_document_by_doi("10.3390/healthcare12141387")

# Find entity relationships
result = await gremlin_service.find_entity_relationships("FIFA 11+")

# Custom Gremlin query
result = await gremlin_service.execute_query({
    "query": "g.V().hasLabel('Paper').has('DOI', '10.3390/healthcare12141387')"
})
```

### 3. Unified GraphRAG Service (`athlea_langgraph/services/graphrag_service.py`)

Orchestrates both ACS and Gremlin queries:

```python
from athlea_langgraph.services.graphrag_service import get_graphrag_service, GraphRAGQueryInput

graphrag_service = get_graphrag_service()

# Comprehensive query
result = await graphrag_service.execute_graphrag_query(GraphRAGQueryInput(
    query="How effective is FIFA 11+ for injury prevention?",
    entities=["FIFA 11+", "FUTSAL"],
    include_acs=True,
    include_graph=True,
    top_k=10
))

print(f"Found {result.acs_chunk_count} text chunks")
print(f"Found {result.graph_entity_count} graph entities") 
print(f"Extracted {len(result.causalities)} causal relationships")
```

### 4. GraphRAG Tools (`athlea_langgraph/tools/external/graphrag_tool.py`)

LangChain-compatible tools for use in agent workflows:

```python
from athlea_langgraph.tools.external.graphrag_tool import create_graphrag_tool

# Create tool
tool = create_graphrag_tool()

# Use in LangChain workflows
result = await tool._arun(
    query="Does Pilates improve flexibility?",
    include_causalities=True
)
```

### 5. GraphRAG Agent Node (`athlea_langgraph/agents/graphrag_node.py`)

Specialized LangGraph node for evidence-based responses:

```python
from athlea_langgraph.agents.graphrag_node import graphrag_agent_node
from athlea_langgraph.states import AgentState
from langchain_core.messages import HumanMessage

# Create state with user query
state = AgentState({
    "messages": [
        HumanMessage(content="Tell me about HIIT for improving VO2 max")
    ]
})

# Process with GraphRAG
result_state = await graphrag_agent_node(state)
ai_response = result_state["messages"][-1]
print(ai_response.content)
```

## Data Fusion Process

The GraphRAG integration implements a sophisticated data fusion process:

### 1. Query Analysis
- Determines if query is research-relevant
- Extracts entities and parameters
- Classifies query type (injury prevention, training protocol, etc.)

### 2. Parallel Data Retrieval
- **ACS Query**: Retrieves relevant text chunks with semantic/hybrid search
- **Gremlin Query**: Finds structured relationships and entities
- Executes both queries in parallel for efficiency

### 3. Evidence Synthesis
- Combines textual evidence with structured relationships
- Extracts causal relationships (PREVENTS, IMPROVES, ENHANCES, CAUSES)
- Generates actionable recommendations with specific protocols

### 4. Response Generation
- Uses specialized system prompts based on query type
- Includes confidence scores and evidence quality
- Provides specific dosages, conditions, and populations

## Testing the Integration

Use the provided test script to verify all components:

```bash
python test_graphrag_integration.py
```

The test script validates:
- Configuration setup
- Azure Cognitive Search connectivity
- Gremlin Graph connectivity  
- Unified GraphRAG service functionality
- Tool and agent node integration
- Specific causality extraction

## Example Queries and Expected Outputs

### 1. Injury Prevention Query

**Input:** "How can I prevent ankle injuries in football?"

**Expected Output:**
```
## Research Summary
Found 12 relevant research chunks from 8 documents. Found structured relationships from 3 graph queries.

## Textual Evidence (12 research chunks found)
**Evidence 1** (Score: 0.85, Source: DOI:10.1234/example):
Proprioceptive training significantly reduces ankle injury rates in football players...

## ⚡ Causal Relationships
**Proprioceptive Training PREVENTS Ankle Injuries** (Confidence: 0.9)
  - Condition: when performed 3x per week for 6+ weeks
  - Dosage: 15-20 minutes per session

## 🎯 Actionable Recommendations
1. Implement proprioceptive training 3x per week during pre-season
2. Continue balance exercises throughout competitive season
3. Focus on single-leg stability and reactive balance tasks
```

### 2. Training Protocol Query

**Input:** "What's the optimal HIIT protocol for improving VO2 max?"

**Expected Output:**
```
## Research Summary
Based on meta-analysis of 23 studies involving 1,240 participants, HIIT significantly improves VO2 max across various populations.

## Specific Protocol
- **Frequency**: 3 times per week
- **Duration**: 4-6 weeks minimum for adaptations
- **Intensity**: 85-95% HRmax during work intervals
- **Work:Rest Ratio**: 1:1 to 2:1 depending on fitness level
- **Population**: Effective in both trained and untrained individuals

## ⚡ Causal Relationships
**HIIT IMPROVES VO2_Max** (Confidence: 0.92)
  - Condition: when intensity >85% HRmax
  - Dosage: 3x per week, 4-6 weeks minimum
```

## Integration with Existing Agents

The GraphRAG functionality can be integrated into existing specialized coaches:

### 1. Enhanced Strength Coach

```python
from athlea_langgraph.agents.graphrag_node import query_training_protocol

async def enhanced_strength_coach_node(state: AgentState) -> AgentState:
    # Existing strength coach logic...
    
    # Add GraphRAG research lookup for strength protocols
    if "strength training" in user_query.lower():
        research_result = await query_training_protocol(
            exercise="strength training",
            goal="muscle hypertrophy"
        )
        # Incorporate research into response...
    
    return state
```

### 2. Enhanced Injury Prevention

```python
from athlea_langgraph.agents.graphrag_node import query_injury_prevention

async def enhanced_injury_prevention_node(state: AgentState) -> AgentState:
    # Extract injury type from query
    injury_type = extract_injury_type(user_query)
    
    # Get research-backed prevention protocols
    research_result = await query_injury_prevention(
        injury_type=injury_type,
        sport=user_sport
    )
    
    # Generate evidence-based recommendations...
    return state
```

## Causality Extraction

The system extracts specific causal relationships based on your research:

### Known Causalities from Futsal Study:

1. **FIFA 11+ → Injury Reduction**
   - Protocol: 20 minutes, 2x/week, 10+ weeks
   - Population: Amateur and youth players
   - Confidence: High (multiple RCTs)

2. **Pilates → Flexibility Improvement**
   - Protocol: 25 minutes, 3x/week, 4 weeks
   - Effect: Immediate improvements
   - Warning: Gains diminish after 15 days without maintenance

3. **Proprioceptive Training → Ankle Injury Prevention**
   - Protocol: Season-long implementation
   - Population: Elite players
   - Effect: Persistent after program ends

4. **HIIT → Game Performance Enhancement**
   - Protocol: 30-40 minutes, 1x/week, 4+ weeks
   - Bonus Effects: Improved movement quality, reduced knee valgus

## Troubleshooting

### Common Issues:

1. **Configuration Errors**
   ```
   Error: Missing required GraphRAG configuration: Azure Search API key
   ```
   **Solution:** Set `AZURE_SEARCH_API_KEY` environment variable

2. **Gremlin Connection Issues**
   ```
   Error: Cannot connect to Gremlin graph: Connection timeout
   ```
   **Solution:** Check Cosmos DB credentials and network connectivity

3. **No Results Found**
   ```
   Warning: Document not found by DOI (might not be in graph yet)
   ```
   **Solution:** Verify data has been populated in the knowledge graph

4. **Azure Search Errors**
   ```
   Error: ACS query failed: Invalid filter expression
   ```
   **Solution:** Check filter syntax and field names in ACS index

### Performance Optimization:

1. **Parallel Queries**: ACS and Gremlin queries execute in parallel
2. **Circuit Breakers**: Prevent cascading failures
3. **Caching**: Consider implementing Redis caching for frequent queries
4. **Batch Processing**: Group multiple entity lookups when possible

## Future Enhancements

1. **Entity Extraction**: Automatic entity detection from user queries
2. **Confidence Calibration**: Improved confidence scoring algorithms  
3. **Multi-language Support**: Support for non-English research papers
4. **Real-time Updates**: Live updates when new research is added
5. **User Feedback**: Learning from user interactions to improve relevance

## Conclusion

The GraphRAG integration provides Athlea coaches with evidence-based, research-backed responses that include:

- **Specific protocols** with dosages and conditions
- **Causal relationships** with confidence scores
- **Textual evidence** from peer-reviewed research
- **Structured knowledge** from the knowledge graph
- **Actionable recommendations** for immediate implementation

This transforms the AI coaches from general advisors to evidence-based research assistants that can provide the same level of detail and specificity as described in your causality examples. 