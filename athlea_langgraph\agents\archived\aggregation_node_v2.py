"""
Production-Ready Aggregation Node V2

Enhanced aggregation node with robust error handling, streaming support,
and production-ready HITL implementation based on LangGraph best practices.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, AsyncIterator, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langgraph.errors import GraphInterrupt
from langgraph.types import Command, interrupt

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState

logger = logging.getLogger(__name__)


class InterruptReason(Enum):
    """Reasons for interrupting the graph execution."""

    REVIEW_RESPONSE = "review_response"
    ERROR_RECOVERY = "error_recovery"
    CLARIFICATION_NEEDED = "clarification_needed"
    APPROVAL_REQUIRED = "approval_required"


@dataclass
class InterruptContext:
    """Context information for interrupts."""

    reason: InterruptReason
    message: str
    metadata: Dict[str, Any]
    retry_count: int = 0
    max_retries: int = 3


class AggregationError(Exception):
    """Custom exception for aggregation-specific errors."""

    pass


async def stream_synthesis_with_retry(
    llm,
    messages: List[BaseMessage],
    max_retries: int = 3,
    initial_delay: float = 1.0,
    backoff_factor: float = 2.0,
) -> AsyncIterator[str]:
    """
    Stream LLM synthesis with exponential backoff retry logic.

    Args:
        llm: The language model instance
        messages: Messages to send to the LLM
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each retry

    Yields:
        Chunks of synthesized content
    """
    delay = initial_delay
    last_error = None

    for attempt in range(max_retries + 1):
        try:
            async for chunk in llm.astream(messages):
                if chunk.content:
                    yield chunk.content
            return  # Success, exit the retry loop

        except Exception as e:
            last_error = e
            logger.warning(f"Synthesis attempt {attempt + 1} failed: {str(e)}")

            if attempt < max_retries:
                # Add jitter to prevent thundering herd
                jittered_delay = delay * (0.5 + 0.5 * asyncio.get_event_loop().time())
                logger.info(f"Retrying in {jittered_delay:.2f} seconds...")
                await asyncio.sleep(jittered_delay)
                delay *= backoff_factor
            else:
                logger.error(
                    f"All synthesis attempts failed. Last error: {str(last_error)}"
                )
                raise AggregationError(
                    f"Failed to synthesize responses after {max_retries} attempts"
                ) from last_error


async def aggregation_node_v2(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Production-ready aggregation node with enhanced error handling and streaming.

    Features:
    - Robust error handling with retries
    - Real-time streaming of synthesis
    - Timeout protection
    - Better interrupt handling
    - Performance monitoring
    - Graceful degradation
    """
    start_time = time.time()
    logger.info("--- Aggregation Node V2 ---")

    try:
        # Collect specialist responses with validation
        specialist_responses = collect_specialist_responses(state)

        if not specialist_responses:
            logger.warning("No specialist responses to aggregate")
            return handle_no_responses(state)

        # Log performance metrics
        logger.info(f"Collected {len(specialist_responses)} specialist responses")

        # Create synthesis prompt
        synthesis_prompt = create_synthesis_prompt(
            state.get("user_query", ""),
            state.get("reasoning_output", ""),
            specialist_responses,
        )

        # Initialize LLM with streaming
        llm = create_azure_chat_openai(temperature=0.7, streaming=True)

        messages = [
            SystemMessage(
                content="""You are a head fitness coach synthesizing advice from specialist coaches.
Your role is to create a cohesive, actionable response that integrates all specialist insights."""
            ),
            HumanMessage(content=synthesis_prompt),
        ]

        # Stream synthesis with timeout protection
        logger.info("Starting synthesis streaming...")
        synthesized_content = ""

        try:
            # Set a reasonable timeout for synthesis
            async with asyncio.timeout(30):  # 30 second timeout
                async for chunk in stream_synthesis_with_retry(llm, messages):
                    synthesized_content += chunk
                    # Could emit streaming events here if needed

        except asyncio.TimeoutError:
            logger.error("Synthesis timed out after 30 seconds")
            synthesized_content = create_timeout_response(specialist_responses)

        logger.info(
            f"Synthesis complete: {len(synthesized_content)} chars in {time.time() - start_time:.2f}s"
        )

        # Create aggregated message
        aggregated_message = AIMessage(
            content=synthesized_content,
            name="head_coach",
            additional_kwargs={
                "synthesis_time": time.time() - start_time,
                "specialist_count": len(specialist_responses),
            },
        )

        # Prepare state update
        state_update = {
            "messages": [aggregated_message],
            "aggregated_response": synthesized_content,
            "aggregation_metadata": {
                "timestamp": time.time(),
                "duration": time.time() - start_time,
                "specialist_count": len(specialist_responses),
                "response_length": len(synthesized_content),
            },
        }

        # Get enable_human_feedback from config first, then state as fallback
        enable_human_feedback = True  # Default value

        # Check config first (this is the primary source of truth)
        if config and "enable_human_feedback" in config:
            enable_human_feedback = config["enable_human_feedback"]
        # Fallback to state if config not available
        elif isinstance(state, dict):
            enable_human_feedback = state.get("enable_human_feedback", True)
        else:
            enable_human_feedback = getattr(state, "enable_human_feedback", True)

        logger.info(
            f"Human feedback setting: {enable_human_feedback} (source: {'config' if config and 'enable_human_feedback' in config else 'state/default'})"
        )

        # Create interrupt context for human review ONLY if human feedback is enabled
        if enable_human_feedback:
            interrupt_context = InterruptContext(
                reason=InterruptReason.REVIEW_RESPONSE,
                message=synthesized_content,
                metadata={
                    "specialists_consulted": [
                        resp["coach"] for resp in specialist_responses
                    ],
                    "synthesis_duration": time.time() - start_time,
                },
            )

            human_feedback = await handle_interrupt_with_recovery(
                interrupt_context, config
            )

            # Process human feedback
            if human_feedback:
                logger.info(f"Processing human feedback: {human_feedback[:100]}...")
                state_update["messages"].append(HumanMessage(content=human_feedback))
                state_update["human_feedback"] = human_feedback
        else:
            logger.info(
                "Skipping human feedback interrupt - disabled for streaming mode"
            )

        return state_update

    except GraphInterrupt:
        # GraphInterrupt is normal behavior for human-in-the-loop functionality
        # Let it bubble up to LangGraph for proper handling
        logger.info(
            "GraphInterrupt raised for human feedback - this is normal behavior"
        )
        raise

    except AggregationError as e:
        logger.error(f"Aggregation error: {str(e)}")
        return handle_aggregation_error(state, str(e))

    except Exception as e:
        logger.error(f"Unexpected error in aggregation: {str(e)}", exc_info=True)
        return handle_unexpected_error(state, str(e))


def collect_specialist_responses(state: AgentState) -> List[Dict[str, str]]:
    """
    Collect and validate specialist responses from state.

    Returns:
        List of specialist responses with coach names and content
    """
    # Get coach_responses from state - this is where coaches store their responses
    coach_responses = state.get("coach_responses", {})

    logger.info(
        f"Collecting specialist responses from coach_responses: {list(coach_responses.keys())}"
    )

    # Map internal coach names to display names
    coach_display_names = {
        "strength_coach": "Strength Coach",
        "running_coach": "Running Coach",
        "cardio_coach": "Cardio Coach",
        "cycling_coach": "Cycling Coach",
        "nutrition_coach": "Nutrition Coach",
        "recovery_coach": "Recovery Coach",
        "mental_coach": "Mental Coach",
        "clarification": "Head Coach",  # Special case for clarification
    }

    responses = []

    # Collect responses from coach_responses dictionary
    for coach_name, response in coach_responses.items():
        if response and isinstance(response, str) and response.strip():
            display_name = coach_display_names.get(
                coach_name, coach_name.replace("_", " ").title()
            )
            responses.append(
                {
                    "coach": display_name,
                    "response": response.strip(),
                    "field": coach_name,
                }
            )
            logger.info(f"Found response from {display_name}: {len(response)} chars")
        elif response:
            logger.warning(
                f"Invalid response format for {coach_name}: {type(response)}"
            )

    # Fallback: Check for old-style individual fields if coach_responses is empty
    if not responses:
        logger.warning("No responses in coach_responses, checking legacy fields...")
        specialist_fields = [
            ("strength_response", "Strength Coach"),
            ("running_response", "Running Coach"),
            ("cardio_response", "Cardio Coach"),
            ("cycling_response", "Cycling Coach"),
            ("nutrition_response", "Nutrition Coach"),
            ("recovery_response", "Recovery Coach"),
            ("mental_response", "Mental Coach"),
        ]

        for field, coach_name in specialist_fields:
            response = state.get(field)
            if response and isinstance(response, str) and response.strip():
                responses.append(
                    {"coach": coach_name, "response": response.strip(), "field": field}
                )
                logger.info(
                    f"Found legacy response from {coach_name}: {len(response)} chars"
                )

    logger.info(f"Total responses collected: {len(responses)}")
    return responses


def create_synthesis_prompt(
    user_query: str, reasoning: str, specialist_responses: List[Dict[str, str]]
) -> str:
    """Create an optimized synthesis prompt."""
    responses_text = "\n\n".join(
        [f"**{resp['coach']}:**\n{resp['response']}" for resp in specialist_responses]
    )

    return f"""Synthesize the following specialist coaching responses into a cohesive answer.

USER QUERY: {user_query}

REASONING ANALYSIS: {reasoning}

SPECIALIST RESPONSES:
{responses_text}

SYNTHESIS GUIDELINES:
1. Create a unified response that naturally integrates all specialist insights
2. Maintain an encouraging, supportive tone throughout
3. Provide specific, actionable recommendations
4. Acknowledge each specialist's contribution where relevant
5. Structure the response clearly with sections if needed
6. End with motivating next steps or encouragement
7. Keep the response conversational and accessible

Remember: You're the head coach bringing together expert advice into one coherent plan."""


async def handle_interrupt_with_recovery(
    context: InterruptContext, config: Optional[Dict[str, Any]] = None
) -> Optional[str]:
    """
    Handle interrupt with error recovery and retry logic.

    Returns:
        Human feedback if provided, None otherwise
    """
    retry_count = 0
    max_retries = context.max_retries

    while retry_count <= max_retries:
        try:
            logger.info(f"Interrupting for {context.reason.value}...")

            # Create interrupt message with context
            interrupt_message = {
                "content": context.message,
                "reason": context.reason.value,
                "metadata": context.metadata,
                "retry_count": retry_count,
            }

            # Perform the interrupt
            human_feedback = interrupt(interrupt_message)

            # Log successful interrupt
            logger.info(f"Successfully received human feedback after interrupt")
            return human_feedback

        except GraphInterrupt:
            # This is expected - the graph is pausing for human input
            raise

        except Exception as e:
            retry_count += 1
            logger.error(f"Interrupt attempt {retry_count} failed: {str(e)}")

            if retry_count <= max_retries:
                await asyncio.sleep(1.0 * retry_count)  # Linear backoff
            else:
                logger.error(
                    "Max interrupt retries exceeded, proceeding without human feedback"
                )
                return None

    return None


def handle_no_responses(state: AgentState) -> Dict[str, Any]:
    """Handle case where no specialist responses are available."""
    fallback_message = AIMessage(
        content="""I apologize, but I wasn't able to gather specific coaching advice for your request at this time. 

This might be because:
- Your question needs clarification
- Our specialist coaches need more context
- There was a temporary issue accessing the coaching expertise

Could you please rephrase your question or provide more details about what you're looking for? I'm here to help with any fitness, nutrition, or wellness questions you have!""",
        name="head_coach",
        additional_kwargs={"error_type": "no_responses"},
    )

    return {
        "messages": [fallback_message],
        "aggregated_response": fallback_message.content,
        "error_state": "no_specialist_responses",
    }


def handle_aggregation_error(state: AgentState, error_message: str) -> Dict[str, Any]:
    """Handle aggregation-specific errors gracefully."""
    error_response = AIMessage(
        content=f"""I encountered an issue while preparing your personalized coaching response.

To ensure you get the best advice, could you please try:
1. Rephrasing your question
2. Being more specific about what aspect you'd like help with
3. Breaking down complex questions into simpler parts

I'm here to help with any fitness or wellness questions you have!""",
        name="head_coach",
        additional_kwargs={
            "error_type": "aggregation_error",
            "error_details": error_message,
        },
    )

    return {
        "messages": [error_response],
        "aggregated_response": error_response.content,
        "error_state": "aggregation_failed",
    }


def handle_unexpected_error(state: AgentState, error_message: str) -> Dict[str, Any]:
    """Handle unexpected errors with graceful degradation."""
    logger.error(f"Unexpected error in aggregation: {error_message}")

    # Try to provide partial response if any specialist responses exist
    specialist_responses = collect_specialist_responses(state)

    if specialist_responses:
        # Provide raw specialist responses as fallback
        fallback_content = "Here's what our specialist coaches recommend:\n\n"
        for resp in specialist_responses:
            fallback_content += f"**{resp['coach']}:**\n{resp['response']}\n\n"
    else:
        fallback_content = """I apologize for the technical difficulty. Please try again or rephrase your question.
        
Our coaching team is here to help with:
- Strength training and technique
- Running and cardio programs  
- Nutrition guidance
- Recovery strategies
- Mental performance

What would you like to know more about?"""

    error_response = AIMessage(
        content=fallback_content,
        name="head_coach",
        additional_kwargs={"error_type": "unexpected_error", "fallback_mode": True},
    )

    return {
        "messages": [error_response],
        "aggregated_response": error_response.content,
        "error_state": "unexpected_error",
    }


def create_timeout_response(specialist_responses: List[Dict[str, str]]) -> str:
    """Create a response when synthesis times out."""
    response = "Based on our specialist coaches' input:\n\n"

    for resp in specialist_responses[:3]:  # Limit to first 3 for brevity
        response += f"• **{resp['coach']}** suggests: {resp['response'][:150]}...\n\n"

    response += "\nFor a more detailed synthesis of all recommendations, please feel free to ask follow-up questions!"

    return response
