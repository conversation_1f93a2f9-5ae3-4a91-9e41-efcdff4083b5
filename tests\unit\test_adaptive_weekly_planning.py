"""
Tests for Adaptive Weekly Planning System

Comprehensive test suite for the new multi-agent adaptive weekly planning system
that implements batch coach coordination with event-driven adaptations.

Based on research patterns from:
- Multi-agent shared persistent state (event sourcing)
- LlamaIndex multi-agent framework patterns
- AWS multi-agent coordination research
"""

import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from athlea_langgraph.engines.session_state_models import (
    FitnessProfile,
    TrainingSession,
    SessionFeedback,
    WeeklyPlan,
    CoachMemory,
    UserControlState,
    AdaptationData,
    EnhancedOnboardingState,
    create_fitness_profile_from_onboarding,
)
from athlea_langgraph.engines.coach_intelligence import (
    DomainCoach,
    RunningCoach,
    StrengthCoach,
    CyclingCoach,
)
from athlea_langgraph.engines.session_generation_engine import (
    SessionGenerationEngine,
    CoachCoordinationSystem,
    PeriodizationModel,
)
from athlea_langgraph.agents.session_generation_nodes import (
    session_generation_node,
    coach_coordination_node,
    user_control_node,
    session_adaptation_node,
    session_flow_router,
)
from athlea_langgraph.graphs.onboarding_graph import OnboardingGraph
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    PlanDetails,
    create_initial_onboarding_state,
)


class TestAdaptiveWeeklyPlanningCore:
    """Test core adaptive weekly planning functionality."""

    def test_fitness_profile_creation(self):
        """Test creating a comprehensive fitness profile."""
        profile = FitnessProfile(
            user_id="test_user_123",
            aerobic_threshold=150,
            anaerobic_threshold=175,
            vo2_max=55.0,
            resting_heart_rate=65,
            max_heart_rate=185,
            equipment_available=["treadmill", "dumbbells", "resistance_bands"],
            training_constraints={
                "max_weekly_hours": 8,
                "preferred_days": ["monday", "wednesday", "friday", "saturday"],
                "time_slots": ["morning", "evening"],
            },
        )

        assert profile.user_id == "test_user_123"
        assert profile.aerobic_threshold == 150
        assert profile.vo2_max == 55.0
        assert "treadmill" in profile.equipment_available
        assert profile.training_constraints["max_weekly_hours"] == 8

    def test_fitness_profile_from_onboarding(self):
        """Test creating fitness profile from onboarding state."""
        onboarding_state = create_initial_onboarding_state("test_user")
        onboarding_state["goal"] = "lose weight and build strength"
        onboarding_state["experience_level"] = "beginner"
        onboarding_state["time_commitment"] = "3-4 hours per week"
        onboarding_state["equipment"] = "home gym with dumbbells"

        profile = create_fitness_profile_from_onboarding(onboarding_state)

        assert profile.user_id == "test_user"
        assert profile.experience_level == "beginner"
        assert "dumbbells" in profile.equipment_available

    def test_training_session_enhancement(self):
        """Test enhanced training session with coach intelligence."""
        session = TrainingSession(
            session_id="enhanced_session_123",
            date=datetime.now(),
            domain="running",
            session_type="tempo_run",
            duration_minutes=45,
            difficulty_level=7,
            coach_rationale="Building lactate threshold capacity during base phase",
            adaptation_triggers=["pace_feedback", "heart_rate_monitoring"],
            weekly_context="Week 3 of base building phase",
        )

        assert session.coach_rationale is not None
        assert "lactate threshold" in session.coach_rationale
        assert len(session.adaptation_triggers) == 2
        assert session.weekly_context == "Week 3 of base building phase"

    def test_weekly_plan_structure(self):
        """Test weekly plan structure with coordination notes."""
        weekly_plan = WeeklyPlan(
            week_number=1,
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=7),
            sessions={
                "day_1": [MagicMock(domain="running")],
                "day_3": [MagicMock(domain="strength")],
                "day_5": [MagicMock(domain="running")],
            },
            weekly_focus="base_building",
            coordination_notes={
                "running_coach": "Focus on aerobic development",
                "strength_coach": "Support running with core strength",
                "coordination": "Allow 24h recovery between sessions",
            },
            adaptation_triggers={
                "day_1": ["pace_feedback"],
                "day_3": ["strength_feedback"],
                "day_5": ["endurance_feedback"],
            },
        )

        assert weekly_plan.week_number == 1
        assert len(weekly_plan.sessions) == 3
        assert "coordination" in weekly_plan.coordination_notes
        assert weekly_plan.weekly_focus == "base_building"


class TestCoachIntelligenceSystem:
    """Test coach intelligence and memory systems."""

    @pytest.fixture
    def sample_fitness_profile(self):
        """Create sample fitness profile for testing."""
        return FitnessProfile(
            user_id="test_user",
            aerobic_threshold=160,
            vo2_max=50.0,
            equipment_available=["treadmill", "dumbbells"],
            training_constraints={"max_weekly_hours": 6},
        )

    @pytest.fixture
    def running_coach(self):
        """Create running coach instance."""
        return RunningCoach()

    @pytest.fixture
    def strength_coach(self):
        """Create strength coach instance."""
        return StrengthCoach()

    def test_coach_memory_initialization(self, running_coach):
        """Test coach memory initialization and structure."""
        memory = running_coach.memory

        assert isinstance(memory, CoachMemory)
        assert memory.coach_id == running_coach.coach_id
        assert memory.domain == "running"
        assert memory.sessions_created == []
        assert memory.successful_patterns == []
        assert memory.user_preferences == {}

    @pytest.mark.asyncio
    async def test_coach_session_generation_with_context(
        self, strength_coach, sample_fitness_profile
    ):
        """Test coach session generation within weekly context."""
        # Mock a daily allocation within weekly framework
        daily_allocation = MagicMock()
        daily_allocation.target_duration = 60
        daily_allocation.intensity_focus = "moderate"
        daily_allocation.primary_goals = ["strength_building"]

        weekly_framework = MagicMock()
        weekly_framework.weekly_theme = "base_building"
        weekly_framework.week_number = 1

        with patch.object(
            strength_coach, "generate_session_with_rationale"
        ) as mock_generate:
            mock_session = TrainingSession(
                session_id="test_session",
                date=datetime.now(),
                domain="strength",
                duration_minutes=60,
                coach_rationale="Focused on compound movements for base strength",
            )
            mock_generate.return_value = mock_session

            session = await strength_coach.generate_session_within_framework(
                allocation=daily_allocation,
                user_profile=sample_fitness_profile,
                existing_sessions=[],
                weekly_context=weekly_framework,
            )

            assert isinstance(session, TrainingSession)
            assert session.domain == "strength"
            assert session.duration_minutes == 60
            assert session.coach_rationale is not None

    def test_coach_memory_learning(self, running_coach):
        """Test coach memory learning from feedback."""
        feedback = SessionFeedback(
            session_id="test_session_123",
            user_id="test_user",
            completed=True,
            difficulty_rating=7,
            enjoyment_rating=8,
            perceived_exertion=6,
            notes="Felt great, could do more",
        )

        # Coach learns from feedback
        learning_update = running_coach.learn_from_feedback(feedback)

        assert learning_update is not None
        # Check that positive feedback is recorded in preferences
        assert running_coach.memory.user_preferences.get("preferred_difficulty") >= 7


class TestBatchCoordinationSystem:
    """Test batch coach coordination and consensus mechanisms."""

    @pytest.fixture
    def coordination_system(self):
        """Create coordination system instance."""
        return CoachCoordinationSystem()

    @pytest.fixture
    def mock_coaches(self):
        """Create mock coaches for testing."""
        running_coach = MagicMock()
        running_coach.domain = "running"
        running_coach.coach_id = "running_coach_001"

        strength_coach = MagicMock()
        strength_coach.domain = "strength"
        strength_coach.coach_id = "strength_coach_001"

        return {"running": running_coach, "strength": strength_coach}

    @pytest.mark.asyncio
    async def test_batch_weekly_coordination(self, coordination_system, mock_coaches):
        """Test batch coordination of coaches for weekly planning."""
        user_profile = FitnessProfile(
            user_id="test_user", training_constraints={"max_weekly_hours": 6}
        )

        weekly_focus = MagicMock()
        weekly_focus.theme = "base_building"
        weekly_focus.week_number = 1

        # Mock coach proposals
        for coach in mock_coaches.values():
            coach.propose_weekly_contribution = AsyncMock()
            coach.propose_weekly_contribution.return_value = MagicMock(
                domain=coach.domain,
                recommended_sessions=2,
                total_weekly_load=180,  # minutes
                load_distribution={"low": 60, "moderate": 90, "high": 30},
            )

        coordination_result = await coordination_system.batch_coordinate_weekly_plan(
            coaches=mock_coaches,
            user_profile=user_profile,
            weekly_focus=weekly_focus,
            previous_feedback=[],
        )

        assert coordination_result is not None
        assert len(coordination_result.daily_frameworks) == 7  # 7 days
        assert coordination_result.coordination_notes is not None

    @pytest.mark.asyncio
    async def test_coach_conflict_resolution(self, coordination_system):
        """Test resolution of conflicts between coach proposals."""
        # Create conflicting proposals (both want same time slots)
        coach_proposals = {
            "running": MagicMock(
                preferred_days=[1, 3, 5],  # Monday, Wednesday, Friday
                session_duration=60,
                intensity_requirements="moderate",
            ),
            "strength": MagicMock(
                preferred_days=[1, 3, 5],  # Same days - conflict!
                session_duration=60,
                intensity_requirements="high",
            ),
        }

        weekly_focus = MagicMock()
        user_constraints = MagicMock()
        user_constraints.max_sessions_per_day = 1

        coordination_result = await coordination_system._resolve_weekly_coordination(
            coach_proposals=coach_proposals,
            weekly_focus=weekly_focus,
            user_constraints=user_constraints,
        )

        # Should resolve conflict by distributing across different days
        assert coordination_result is not None
        assert len(coordination_result.conflict_resolutions) > 0


class TestSessionGenerationEngine:
    """Test the main session generation engine with adaptive weekly planning."""

    @pytest.fixture
    def generation_engine(self):
        """Create session generation engine."""
        return SessionGenerationEngine()

    @pytest.fixture
    def sample_plan_details(self):
        """Create sample plan details."""
        return PlanDetails(
            name="Beginner Fitness Plan",
            description="A comprehensive fitness plan for beginners",
            duration="12 weeks",
            level="beginner",
            plan_type="general_fitness",
            disciplines=["running", "strength"],
            rationale="Balanced approach for fitness building",
            phases=[],
            example_sessions=[],
        )

    @pytest.mark.asyncio
    async def test_adaptive_weekly_generation(
        self, generation_engine, sample_plan_details
    ):
        """Test adaptive weekly session generation with full coordination."""
        user_profile = FitnessProfile(
            user_id="test_user",
            training_constraints={"max_weekly_hours": 6},
            equipment_available=["treadmill", "dumbbells"],
        )

        # Mock the coordination system
        with patch.object(
            generation_engine.coordination_system, "batch_coordinate_weekly_plan"
        ) as mock_coord:
            mock_coord.return_value = MagicMock(
                daily_frameworks={
                    f"day_{i}": MagicMock(
                        day=i,
                        coach_allocations={
                            "running": MagicMock(should_generate_session=i in [1, 3, 5])
                        },
                    )
                    for i in range(1, 8)
                },
                coordination_notes={"general": "Week 1 focus on establishing routine"},
                load_distribution={"running": 180, "strength": 120},
            )

            weekly_plan = await generation_engine.generate_weekly_sessions(
                user_profile=user_profile,
                plan_details=sample_plan_details,
                week_number=1,
                previous_performance=[],
            )

        assert isinstance(weekly_plan, WeeklyPlan)
        assert weekly_plan.week_number == 1
        assert weekly_plan.coordination_notes is not None
        assert weekly_plan.adaptation_triggers is not None


class TestAdaptiveOnboardingGraph:
    """Test the enhanced onboarding graph with adaptive weekly planning."""

    @pytest.fixture
    def onboarding_graph(self):
        """Create onboarding graph instance."""
        return OnboardingGraph()

    @pytest.fixture
    def sample_onboarding_state(self):
        """Create sample onboarding state."""
        state = create_initial_onboarding_state("test_user_123")
        state["generated_plan"] = PlanDetails(
            name="Test Fitness Plan",
            description="A test plan",
            duration="8 weeks",
            level="intermediate",
            plan_type="strength_endurance",
            disciplines=["running", "strength"],
            rationale="Test rationale",
            phases=[],
            example_sessions=[],
        )
        return state

    @pytest.mark.asyncio
    async def test_session_generation_node(self, sample_onboarding_state):
        """Test the enhanced session generation node."""
        # Mock the session generation engine
        with patch(
            "athlea_langgraph.agents.session_generation_nodes.SessionGenerationEngine"
        ) as mock_engine_class:
            mock_engine = MagicMock()
            mock_engine_class.return_value = mock_engine

            mock_weekly_plan = WeeklyPlan(
                week_number=1,
                start_date=datetime.now(),
                end_date=datetime.now() + timedelta(days=7),
                sessions={"day_1": [MagicMock()]},
                weekly_focus="base_building",
                coordination_notes={"general": "Initial week coordination"},
                adaptation_triggers={"day_1": ["feedback_trigger"]},
            )
            mock_engine.generate_weekly_sessions.return_value = mock_weekly_plan

            result_state = await session_generation_node(sample_onboarding_state)

            assert result_state["generated_sessions"] is not None
            assert result_state["current_week"] == 1

    def test_session_flow_router(self, sample_onboarding_state):
        """Test session flow routing logic."""
        # Test different routing scenarios

        # 1. Complete (no pending actions)
        sample_onboarding_state["generated_sessions"] = {"week_1": MagicMock()}
        route = session_flow_router(sample_onboarding_state)
        assert route == "complete"

        # 2. User control needed (if paused state exists)
        sample_onboarding_state["paused_at"] = datetime.now()
        route = session_flow_router(sample_onboarding_state)
        assert route == "user_control"


class TestEventDrivenStateManagement:
    """Test event-driven state management patterns based on research."""

    def test_shared_persistent_state_structure(self):
        """Test shared persistent state structure for multi-agent coordination."""
        # Create enhanced onboarding state
        base_state = create_initial_onboarding_state("test_user")

        # Add adaptive weekly planning fields
        base_state["fitness_profile"] = FitnessProfile(user_id="test_user")
        base_state["generated_weekly_plans"] = {}
        base_state["coach_memories"] = {}
        base_state["adaptation_data"] = {
            "user_preferences": {"difficulty": "moderate"},
            "performance_trends": {"endurance": "improving"},
            "adaptation_velocity": 0.8,
        }

        assert base_state["user_id"] == "test_user"
        assert isinstance(base_state["fitness_profile"], FitnessProfile)
        assert base_state["adaptation_data"]["adaptation_velocity"] == 0.8

    def test_event_sourcing_pattern(self):
        """Test event sourcing pattern for coach state changes."""
        # Simulate event log for coach learning
        coach_events = [
            {
                "event_type": "session_generated",
                "timestamp": datetime.now(),
                "coach_id": "running_coach",
                "session_data": {"type": "easy_run", "duration": 30},
                "context": {"week": 1, "day": 1},
            },
            {
                "event_type": "feedback_received",
                "timestamp": datetime.now(),
                "coach_id": "running_coach",
                "feedback_data": {"rating": 8, "notes": "felt great"},
                "learning_update": {"user_prefers_longer_runs": True},
            },
            {
                "event_type": "adaptation_applied",
                "timestamp": datetime.now(),
                "coach_id": "running_coach",
                "adaptation": {"increase_duration": 5},
            },
        ]

        # Replay events to reconstruct coach state
        coach_memory = CoachMemory(coach_id="running_coach", domain="running")

        for event in coach_events:
            if event["event_type"] == "session_generated":
                coach_memory.sessions_created.append(event["session_data"])
            elif event["event_type"] == "feedback_received":
                coach_memory.user_preferences.update(event["learning_update"])

        # Verify state reconstruction
        assert len(coach_memory.sessions_created) == 1
        assert coach_memory.user_preferences["user_prefers_longer_runs"] is True

    def test_context_passing_between_agents(self):
        """Test context passing mechanism between coaches."""
        # Create shared context object
        shared_context = {
            "user_profile": FitnessProfile(user_id="test_user"),
            "weekly_theme": "endurance_building",
            "previous_sessions": [
                TrainingSession(
                    session_id="prev_1",
                    date=datetime.now() - timedelta(days=1),
                    domain="running",
                    session_type="easy_run",
                    duration_minutes=30,
                )
            ],
            "coordination_constraints": {
                "max_daily_load": 90,
                "recovery_requirements": ["post_run_recovery"],
            },
        }

        # Test context access by different coaches
        running_coach = RunningCoach()
        strength_coach = StrengthCoach()

        # Both coaches can access shared context
        assert shared_context["user_profile"].user_id == "test_user"
        assert shared_context["weekly_theme"] == "endurance_building"
        assert len(shared_context["previous_sessions"]) == 1


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
