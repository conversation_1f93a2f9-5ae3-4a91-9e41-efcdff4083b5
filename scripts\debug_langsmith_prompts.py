#!/usr/bin/env python3
"""
Debug LangSmith Prompts Structure

Detailed script to inspect the structure of prompts returned by LangSmith API.
"""

import os
import sys

try:
    from langsmith import Client
except ImportError:
    print("❌ Error: langsmith package not found.")
    print("Install with: pip install langsmith")
    sys.exit(1)


def main():
    api_key = os.getenv("LANGSMITH_API_KEY")
    if not api_key:
        print("❌ Error: LANGSMITH_API_KEY environment variable not set.")
        sys.exit(1)

    try:
        client = Client()
        print("✅ Connected to LangSmith")

        # Debug the list_prompts response structure
        print("\n🔍 Calling client.list_prompts()...")
        list_result = client.list_prompts()

        print(f"Type of list_prompts() result: {type(list_result)}")
        print(f"Result: {repr(list_result)}")

        # Try to understand what list_prompts returns
        if hasattr(list_result, "__iter__"):
            print("\n🔍 Iterating over list_prompts()...")
            all_items = []

            for i, item in enumerate(list_result):
                print(f"\n--- Item {i+1} ---")
                print(f"Type: {type(item)}")
                print(f"Value: {repr(item)}")

                # If it's a tuple, examine its parts
                if isinstance(item, tuple):
                    print(f"Tuple length: {len(item)}")
                    for j, part in enumerate(item):
                        print(f"  Part {j}: {type(part)} -> {repr(part)[:100]}...")

                        # If this part looks like a list of prompts
                        if hasattr(part, "__iter__") and not isinstance(part, str):
                            try:
                                part_list = list(part)
                                print(f"    Contains {len(part_list)} items")
                                if part_list:
                                    first_item = part_list[0]
                                    print(f"    First item type: {type(first_item)}")
                                    if hasattr(first_item, "repo_handle"):
                                        print(
                                            f"    First prompt name: {first_item.repo_handle}"
                                        )
                            except Exception as e:
                                print(f"    Error examining part: {e}")

                all_items.append(item)

                # Stop after a few items to avoid spam
                if i >= 2:
                    print(f"\n... (stopped after {i+1} items)")
                    break

            print(f"\n📊 Total items processed: {len(all_items)}")

        else:
            print("❌ list_prompts() result is not iterable")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
