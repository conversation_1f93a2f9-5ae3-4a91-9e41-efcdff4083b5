# 3. Integrations

This section provides documentation for the various external services and platforms that the Athlea coaching system integrates with. These integrations enhance the capabilities of our coaching agents, enabling them to access real-world data, perform complex automation, and provide evidence-based insights.

## Available Integration Guides

-   **`N8N.md`**: A comprehensive guide to our integration with the **n8n workflow automation platform**. This covers how we use a FastAPI wrapper to bridge our Python-based LangGraph agents with n8n's JavaScript environment, enabling complex follow-up actions like sending emails or calendar invites.

-   **`GRAPHRAG.md`**: Explains the integration with **GraphRAG**, our system for evidence-based coaching. This document details how we use Azure Cognitive Search and Cosmos DB Gremlin Graphs to provide research-backed, factual responses.

-   **`LANGSMITH.md`**: Details our integration with **LangSmith**, the platform we use for debugging, monitoring, and analyzing our LangGraph agents. It explains how traces are automatically captured and what metrics to monitor.

-   **`STREAMING_API.md`**: A guide for frontend developers on how to connect to our **streaming API**. This API uses Server-Sent Events (SSE) to provide real-time, token-by-token responses from our coaching agents. 