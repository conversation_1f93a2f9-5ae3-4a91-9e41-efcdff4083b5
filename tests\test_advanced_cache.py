#!/usr/bin/env python3
"""
Advanced Cache Performance Testing Suite

This script tests the new Redis-based caching system for user data
to verify ultra-fast performance and proper cache behavior.
"""

import asyncio
import os
import sys
import time
import statistics
from typing import List, Dict, Any
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.insert(0, ".")

from athlea_langgraph.utils.advanced_user_data_cache import (
    AdvancedUserDataCache,
    CacheConfig,
    CacheStrategy,
    fetch_user_data_ultra_fast,
    get_advanced_cache,
)

# Load environment variables
load_dotenv()


class CacheTestSuite:
    """Comprehensive test suite for the advanced caching system."""

    def __init__(self):
        self.mongodb_uri = os.getenv("MONGODB_URI")
        self.test_user_id = os.getenv("TEST_USER_ID", "test_user_123")
        self.cache: AdvancedUserDataCache = None
        self.results: Dict[str, Any] = {}

    async def setup(self):
        """Initialize the cache system for testing."""
        print("🔧 Setting up Advanced Cache Test Suite...")

        if not self.mongodb_uri:
            raise ValueError("MONGODB_URI environment variable not set")

        # Create cache with test configuration
        config = CacheConfig(
            host="athlea-redis-cache.redis.cache.windows.net",
            port=6380,
            password="aw0Pq65cGC5FA7lccDbjI7ab1sRSKM8S2AzCaCcRnr0=",
            ssl=True,
            profile_ttl=3600,  # 1 hour for testing
            plan_ttl=1800,  # 30 minutes for testing
        )

        self.cache = AdvancedUserDataCache(
            mongodb_uri=self.mongodb_uri,
            config=config,
            strategy=CacheStrategy.CACHE_ASIDE,
        )

        await self.cache.initialize()
        print("✅ Cache system initialized successfully")

    async def test_connection_health(self) -> Dict[str, Any]:
        """Test Redis and MongoDB connection health."""
        print("\n🏥 Testing Connection Health...")

        health_status = await self.cache.health_check()

        print(f"   Redis Status: {health_status['redis']['status']}")
        if "latency_ms" in health_status["redis"]:
            print(f"   Redis Latency: {health_status['redis']['latency_ms']:.2f}ms")

        print(f"   MongoDB Status: {health_status['mongodb']['status']}")
        if "latency_ms" in health_status["mongodb"]:
            print(f"   MongoDB Latency: {health_status['mongodb']['latency_ms']:.2f}ms")

        print(f"   Overall Status: {health_status['overall']}")

        return health_status

    async def test_cache_performance(
        self, iterations: int = 50, test_user_id: str = None
    ) -> Dict[str, Any]:
        """Test cache performance with multiple iterations."""
        print(f"\n⚡ Testing Cache Performance ({iterations} iterations)...")

        user_id = test_user_id or self.test_user_id
        response_times = []
        cache_sources = []

        # Clear cache first to ensure we test both cache miss and hit scenarios
        await self.cache.invalidate_user_cache(user_id)

        for i in range(iterations):
            start_time = time.time()

            # Fetch user data
            user_data = await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)

            elapsed = (time.time() - start_time) * 1000  # Convert to milliseconds
            response_times.append(elapsed)
            cache_sources.append(user_data.get("cache_source", "unknown"))

            if i == 0:
                print(f"   First request (cache miss): {elapsed:.2f}ms")
            elif i == 1:
                print(f"   Second request (cache hit): {elapsed:.2f}ms")

        # Calculate statistics
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        min_time = min(response_times)
        max_time = max(response_times)

        # Analyze cache behavior
        from collections import Counter

        source_counts = Counter(cache_sources)
        cache_hits = source_counts.get("advanced_redis_cache", 0)
        cache_efficiency = cache_hits / iterations

        results = {
            "iterations": iterations,
            "user_id": user_id,
            "performance": {
                "avg_response_time_ms": round(avg_time, 2),
                "median_response_time_ms": round(median_time, 2),
                "min_response_time_ms": round(min_time, 2),
                "max_response_time_ms": round(max_time, 2),
                "std_dev_ms": (
                    round(statistics.stdev(response_times), 2)
                    if len(response_times) > 1
                    else 0
                ),
            },
            "cache_behavior": {
                "cache_efficiency": round(cache_efficiency, 2),
                "cache_hits": cache_hits,
                "source_distribution": dict(source_counts),
                "expected_speedup": (
                    round(max_time / avg_time, 2) if avg_time > 0 else 0
                ),
            },
        }

        print(f"   Average Response Time: {avg_time:.2f}ms")
        print(f"   Cache Efficiency: {cache_efficiency:.1%}")
        print(f"   Speed Improvement: {results['cache_behavior']['expected_speedup']}x")

        return results

    async def test_cache_warming(self, user_ids: List[str]) -> Dict[str, Any]:
        """Test cache warming functionality."""
        print(f"\n🔥 Testing Cache Warming ({len(user_ids)} users)...")

        start_time = time.time()
        results = await self.cache.warm_cache_for_users(user_ids)
        elapsed = time.time() - start_time

        successful = sum(results.values())
        total = len(user_ids)

        warming_results = {
            "total_users": total,
            "successful": successful,
            "failed": total - successful,
            "success_rate": successful / total if total > 0 else 0,
            "warming_time_seconds": round(elapsed, 2),
            "users_per_second": round(total / elapsed, 2) if elapsed > 0 else 0,
            "individual_results": results,
        }

        print(f"   Warmed {successful}/{total} users in {elapsed:.2f}s")
        print(f"   Success Rate: {warming_results['success_rate']:.1%}")
        print(f"   Throughput: {warming_results['users_per_second']:.1f} users/second")

        return warming_results

    async def test_cache_invalidation(self, user_id: str) -> Dict[str, Any]:
        """Test cache invalidation functionality."""
        print(f"\n🗑️  Testing Cache Invalidation for user: {user_id}...")

        # First, ensure data is cached
        await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)

        # Test invalidation
        start_time = time.time()
        success = await self.cache.invalidate_user_cache(user_id)
        elapsed = (time.time() - start_time) * 1000

        # Verify invalidation by checking if next request hits MongoDB
        next_request_start = time.time()
        user_data = await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)
        next_request_time = (time.time() - next_request_start) * 1000

        invalidation_results = {
            "invalidation_successful": success,
            "invalidation_time_ms": round(elapsed, 2),
            "next_request_time_ms": round(next_request_time, 2),
            "cache_actually_cleared": user_data.get("cache_source")
            != "advanced_redis_cache",
        }

        print(f"   Invalidation Successful: {success}")
        print(f"   Invalidation Time: {elapsed:.2f}ms")
        print(
            f"   Cache Actually Cleared: {invalidation_results['cache_actually_cleared']}"
        )

        return invalidation_results

    async def test_memory_vs_redis_performance(self, user_id: str) -> Dict[str, Any]:
        """Test performance difference between memory cache and Redis cache."""
        print(f"\n🧠 Testing Memory vs Redis Cache Performance...")

        # Clear all caches
        await self.cache.invalidate_user_cache(user_id)
        self.cache.memory_cache.clear()

        # Test cold start (MongoDB)
        start_time = time.time()
        await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)
        cold_start_time = (time.time() - start_time) * 1000

        # Clear memory cache but keep Redis cache
        self.cache.memory_cache.clear()

        # Test Redis cache hit
        start_time = time.time()
        await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)
        redis_hit_time = (time.time() - start_time) * 1000

        # Test memory cache hit
        start_time = time.time()
        await fetch_user_data_ultra_fast(user_id, self.mongodb_uri)
        memory_hit_time = (time.time() - start_time) * 1000

        performance_comparison = {
            "cold_start_ms": round(cold_start_time, 2),
            "redis_hit_ms": round(redis_hit_time, 2),
            "memory_hit_ms": round(memory_hit_time, 2),
            "speedup_factors": {
                "redis_vs_cold": (
                    round(cold_start_time / redis_hit_time, 2)
                    if redis_hit_time > 0
                    else 0
                ),
                "memory_vs_cold": (
                    round(cold_start_time / memory_hit_time, 2)
                    if memory_hit_time > 0
                    else 0
                ),
                "memory_vs_redis": (
                    round(redis_hit_time / memory_hit_time, 2)
                    if memory_hit_time > 0
                    else 0
                ),
            },
        }

        print(f"   MongoDB (cold): {cold_start_time:.2f}ms")
        print(
            f"   Redis Cache: {redis_hit_time:.2f}ms ({performance_comparison['speedup_factors']['redis_vs_cold']}x faster)"
        )
        print(
            f"   Memory Cache: {memory_hit_time:.2f}ms ({performance_comparison['speedup_factors']['memory_vs_cold']}x faster)"
        )

        return performance_comparison

    async def test_cache_statistics(self) -> Dict[str, Any]:
        """Test cache statistics and metrics."""
        print(f"\n📊 Testing Cache Statistics...")

        stats = await self.cache.get_cache_stats()

        print(f"   Hit Rate: {stats['application_metrics']['hit_rate']:.1%}")
        print(f"   Total Requests: {stats['application_metrics']['total_requests']}")
        print(
            f"   Average Response Time: {stats['application_metrics']['avg_response_time_ms']:.2f}ms"
        )
        print(
            f"   Memory Cache Size: {stats['memory_cache']['size']}/{stats['memory_cache']['max_size']}"
        )
        print(
            f"   Memory Cache Utilization: {stats['memory_cache']['utilization']:.1%}"
        )

        return stats

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        print("🚀 Starting Comprehensive Advanced Cache Test Suite")
        print("=" * 60)

        try:
            await self.setup()

            # Test 1: Connection Health
            health_results = await self.test_connection_health()

            # Test 2: Cache Performance
            performance_results = await self.test_cache_performance(
                50, self.test_user_id
            )

            # Test 3: Cache Warming
            test_users = [f"test_user_{i}" for i in range(1, 6)]  # 5 test users
            warming_results = await self.test_cache_warming(test_users)

            # Test 4: Cache Invalidation
            invalidation_results = await self.test_cache_invalidation(self.test_user_id)

            # Test 5: Memory vs Redis Performance
            comparison_results = await self.test_memory_vs_redis_performance(
                self.test_user_id
            )

            # Test 6: Cache Statistics
            stats_results = await self.test_cache_statistics()

            # Compile final results
            final_results = {
                "test_summary": {
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "mongodb_uri_configured": bool(self.mongodb_uri),
                    "test_user_id": self.test_user_id,
                    "overall_status": "SUCCESS",
                },
                "health_check": health_results,
                "performance_test": performance_results,
                "cache_warming": warming_results,
                "cache_invalidation": invalidation_results,
                "performance_comparison": comparison_results,
                "cache_statistics": stats_results,
            }

            print("\n" + "=" * 60)
            print("🎉 All Tests Completed Successfully!")
            print(
                f"   Overall Cache Efficiency: {performance_results['cache_behavior']['cache_efficiency']:.1%}"
            )
            print(
                f"   Average Response Time: {performance_results['performance']['avg_response_time_ms']:.2f}ms"
            )
            print(
                f"   Memory Cache Speedup: {comparison_results['speedup_factors']['memory_vs_cold']}x"
            )
            print(
                f"   Redis Cache Speedup: {comparison_results['speedup_factors']['redis_vs_cold']}x"
            )

            return final_results

        except Exception as e:
            print(f"\n❌ Test Suite Failed: {e}")
            return {
                "test_summary": {
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "overall_status": "FAILED",
                    "error": str(e),
                }
            }

        finally:
            if self.cache:
                await self.cache.close()

    async def cleanup(self):
        """Clean up resources."""
        if self.cache:
            await self.cache.close()


async def main():
    """Run the advanced cache test suite."""
    test_suite = CacheTestSuite()

    try:
        results = await test_suite.run_comprehensive_test()

        # Optionally save results to file
        import json

        with open("cache_test_results.json", "w") as f:
            json.dump(results, f, indent=2)

        print(f"\n📄 Test results saved to: cache_test_results.json")

        # Return exit code based on results
        if results.get("test_summary", {}).get("overall_status") == "SUCCESS":
            return 0
        else:
            return 1

    except Exception as e:
        print(f"❌ Critical test error: {e}")
        return 1

    finally:
        await test_suite.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
