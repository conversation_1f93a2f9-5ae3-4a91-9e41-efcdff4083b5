"""
Domain Manager for Multi-Domain Memory Separation

Provides domain-specific memory organization, classification, and retrieval
for coaching domains (strength, cardio, nutrition, etc.).
"""

import logging
import re
from collections import Counter
from typing import Any, Dict, List, Optional, Tuple

from .mem0_adapter import Mem0Adapter, MemoryType
from .schemas.domain_schemas import (
    DOMAIN_KEYWORDS,
    CoachingDomain,
    DomainClassification,
    MemoryDomainMetadata,
)

logger = logging.getLogger(__name__)


class DomainMemoryManager:
    """
    Manages domain-specific memory organization and retrieval.

    Features:
    - Automatic domain classification based on content
    - Domain-specific memory storage and retrieval
    - Cross-domain context when relevant
    - Domain-aware memory organization
    """

    def __init__(self, mem0_adapter: Mem0Adapter):
        """
        Initialize domain memory manager.

        Args:
            mem0_adapter: Configured mem0 adapter instance
        """
        self.mem0_adapter = mem0_adapter
        self.classification_cache = {}  # Cache for repeated classifications

    async def classify_memory_domain(
        self, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> DomainClassification:
        """
        Classify memory content into coaching domains.

        Args:
            content: Memory content to classify
            metadata: Additional metadata that might help classification

        Returns:
            Domain classification with primary and secondary domains
        """
        # Check cache first
        content_hash = hash(content.lower()[:500])  # Use first 500 chars for cache key
        if content_hash in self.classification_cache:
            return self.classification_cache[content_hash]

        # Clean and prepare content for analysis
        content_lower = content.lower()
        words = re.findall(r"\b\w+\b", content_lower)

        # Calculate domain scores based on keyword matching
        domain_scores = {}
        for domain, keywords in DOMAIN_KEYWORDS.items():
            score = 0.0
            matched_keywords = []

            for keyword in keywords:
                # Count exact matches and partial matches
                exact_matches = content_lower.count(keyword.lower())
                if exact_matches > 0:
                    score += exact_matches * 2  # Exact matches get double weight
                    matched_keywords.append(keyword)

                # Check for partial matches in compound words
                partial_matches = sum(1 for word in words if keyword.lower() in word)
                score += partial_matches * 0.5

            # Normalize score by total keywords for this domain
            normalized_score = score / len(keywords) if keywords else 0.0
            domain_scores[domain] = normalized_score

            logger.debug(
                f"Domain {domain.value}: score={normalized_score:.3f}, keywords={matched_keywords}"
            )

        # Use metadata for additional context
        if metadata:
            memory_type = metadata.get("memory_type", "")

            # Boost certain domains based on memory type
            if memory_type == "workout_log":
                domain_scores[CoachingDomain.STRENGTH] *= 1.2
                domain_scores[CoachingDomain.CARDIO] *= 1.2
            elif memory_type == "goal" and "weight" in content_lower:
                domain_scores[CoachingDomain.NUTRITION] *= 1.3
            elif memory_type == "injury":
                domain_scores[CoachingDomain.RECOVERY] *= 1.5

        # Determine primary and secondary domains
        sorted_domains = sorted(domain_scores.items(), key=lambda x: x[1], reverse=True)

        # Primary domain is the highest scoring (minimum threshold 0.1)
        primary_domain = CoachingDomain.GENERAL
        confidence_scores = {}

        if sorted_domains and sorted_domains[0][1] > 0.1:
            primary_domain = sorted_domains[0][0]

        # Secondary domains are those scoring > 50% of primary and > 0.05
        secondary_domains = []
        primary_score = sorted_domains[0][1] if sorted_domains else 0.0

        for domain, score in sorted_domains[1:]:
            confidence_scores[domain] = score
            if score > primary_score * 0.5 and score > 0.05:
                secondary_domains.append(domain)
            elif len(secondary_domains) >= 3:  # Limit secondary domains
                break

        confidence_scores[primary_domain] = primary_score

        classification = DomainClassification(
            primary_domain=primary_domain,
            secondary_domains=secondary_domains,
            confidence_scores=confidence_scores,
        )

        # Cache the result
        self.classification_cache[content_hash] = classification

        logger.info(
            f"Classified content as {primary_domain.value} (confidence: {primary_score:.3f})"
        )
        return classification

    async def store_domain_memory(
        self,
        user_id: str,
        content: str,
        domain: Optional[CoachingDomain] = None,
        memory_type: MemoryType = MemoryType.CONVERSATION,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Store memory with domain classification.

        Args:
            user_id: User identifier
            content: Memory content
            domain: Explicit domain (if None, will auto-classify)
            memory_type: Type of memory
            metadata: Additional metadata

        Returns:
            Memory ID
        """
        if metadata is None:
            metadata = {}

        # Auto-classify domain if not provided
        if domain is None:
            classification = await self.classify_memory_domain(content, metadata)
        else:
            # Create classification with provided domain
            classification = DomainClassification(
                primary_domain=domain,
                confidence_scores={domain: 1.0},
            )

        # Create domain metadata
        domain_metadata = MemoryDomainMetadata(
            domain_classification=classification,
            domain_specific_tags=self._extract_domain_tags(
                content, classification.primary_domain
            ),
        )

        # Enhance metadata with domain information
        enhanced_metadata = {
            **metadata,
            "domain_metadata": domain_metadata.to_dict(),
            "primary_domain": classification.primary_domain.value,
            "secondary_domains": [d.value for d in classification.secondary_domains],
        }

        # Store memory with domain-enhanced metadata
        memory_id = await self.mem0_adapter.add_memory(
            user_id=user_id,
            content=content,
            memory_type=memory_type,
            metadata=enhanced_metadata,
        )

        logger.info(
            f"Stored domain memory {memory_id} for user {user_id} in domain {classification.primary_domain.value}"
        )
        return memory_id

    async def search_domain_memories(
        self,
        user_id: str,
        query: str,
        domains: List[CoachingDomain],
        limit: int = 10,
        cross_domain_context: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Search for memories within specific domains.

        Args:
            user_id: User identifier
            query: Search query
            domains: List of domains to search in
            limit: Maximum results
            cross_domain_context: Whether to include cross-domain context

        Returns:
            List of relevant memories with domain information
        """
        # First, get all memories matching the query
        all_memories = await self.mem0_adapter.search_memories(
            user_id=user_id,
            query=query,
            limit=limit * 2,  # Get extra to filter by domain
        )

        # Filter by specified domains
        domain_values = [d.value for d in domains]
        filtered_memories = []

        for memory in all_memories:
            memory_metadata = memory.get("metadata", {})
            primary_domain = memory_metadata.get("primary_domain")
            secondary_domains = memory_metadata.get("secondary_domains", [])

            # Check if memory belongs to any of the specified domains
            if primary_domain in domain_values or any(
                d in domain_values for d in secondary_domains
            ):
                # Add domain relevance score
                relevance_score = 1.0
                if primary_domain in domain_values:
                    relevance_score += 0.5  # Boost for primary domain match

                memory["domain_relevance"] = relevance_score
                memory["matching_domains"] = [
                    d
                    for d in domain_values
                    if d == primary_domain or d in secondary_domains
                ]
                filtered_memories.append(memory)

            if len(filtered_memories) >= limit:
                break

        # Add cross-domain context if requested and we have few results
        if cross_domain_context and len(filtered_memories) < limit // 2:
            context_memories = await self._get_cross_domain_context(
                user_id, query, domains, limit - len(filtered_memories)
            )
            filtered_memories.extend(context_memories)

        # Sort by combined score (original relevance + domain relevance)
        filtered_memories.sort(
            key=lambda m: m.get("score", 0.0) * m.get("domain_relevance", 1.0),
            reverse=True,
        )

        logger.info(
            f"Found {len(filtered_memories)} domain-specific memories for user {user_id}"
        )
        return filtered_memories[:limit]

    async def get_cross_domain_context(
        self,
        user_id: str,
        primary_domain: CoachingDomain,
        query: str = "",
        limit: int = 5,
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get relevant context from other domains that might be relevant.

        Args:
            user_id: User identifier
            primary_domain: The primary domain for context
            query: Additional query context
            limit: Maximum memories per domain

        Returns:
            Dictionary mapping domain names to relevant memories
        """
        # Define domain relationships for cross-context
        domain_relationships = {
            CoachingDomain.STRENGTH: [
                CoachingDomain.NUTRITION,
                CoachingDomain.RECOVERY,
            ],
            CoachingDomain.CARDIO: [
                CoachingDomain.NUTRITION,
                CoachingDomain.RECOVERY,
                CoachingDomain.MENTAL,
            ],
            CoachingDomain.RUNNING: [
                CoachingDomain.CARDIO,
                CoachingDomain.NUTRITION,
                CoachingDomain.RECOVERY,
            ],
            CoachingDomain.CYCLING: [
                CoachingDomain.CARDIO,
                CoachingDomain.NUTRITION,
                CoachingDomain.RECOVERY,
            ],
            CoachingDomain.NUTRITION: [CoachingDomain.STRENGTH, CoachingDomain.CARDIO],
            CoachingDomain.RECOVERY: [
                CoachingDomain.STRENGTH,
                CoachingDomain.CARDIO,
                CoachingDomain.MENTAL,
            ],
            CoachingDomain.MENTAL: [CoachingDomain.RECOVERY, CoachingDomain.CARDIO],
        }

        related_domains = domain_relationships.get(primary_domain, [])
        cross_domain_context = {}

        for domain in related_domains:
            memories = await self.search_domain_memories(
                user_id=user_id,
                query=query,
                domains=[domain],
                limit=limit,
                cross_domain_context=False,  # Prevent recursive calls
            )

            if memories:
                cross_domain_context[domain.value] = memories

        logger.info(
            f"Retrieved cross-domain context for {primary_domain.value}: {list(cross_domain_context.keys())}"
        )
        return cross_domain_context

    async def _get_cross_domain_context(
        self,
        user_id: str,
        query: str,
        excluded_domains: List[CoachingDomain],
        limit: int,
    ) -> List[Dict[str, Any]]:
        """Get context from domains not in the excluded list."""
        all_domains = list(CoachingDomain)
        context_domains = [d for d in all_domains if d not in excluded_domains]

        context_memories = []
        per_domain_limit = (
            max(1, limit // len(context_domains)) if context_domains else 0
        )

        for domain in context_domains[:3]:  # Limit to top 3 context domains
            memories = await self.search_domain_memories(
                user_id=user_id,
                query=query,
                domains=[domain],
                limit=per_domain_limit,
                cross_domain_context=False,
            )

            # Mark as cross-domain context
            for memory in memories:
                memory["is_cross_domain_context"] = True
                memory["context_domain"] = domain.value

            context_memories.extend(memories)

        return context_memories[:limit]

    def _extract_domain_tags(self, content: str, domain: CoachingDomain) -> List[str]:
        """Extract domain-specific tags from content."""
        content_lower = content.lower()
        domain_keywords = DOMAIN_KEYWORDS.get(domain, [])

        # Find keywords that appear in the content
        found_tags = []
        for keyword in domain_keywords:
            if keyword.lower() in content_lower:
                found_tags.append(keyword)

        # Add some domain-specific extraction rules
        if domain == CoachingDomain.STRENGTH:
            # Extract exercise names, weights, reps
            exercise_patterns = [
                r"\b\d+\s*lbs?\b",
                r"\b\d+\s*kg\b",
                r"\b\d+\s*reps?\b",
                r"\b\d+\s*sets?\b",
            ]
            for pattern in exercise_patterns:
                matches = re.findall(pattern, content_lower)
                found_tags.extend(matches[:3])  # Limit to first 3 matches

        elif domain == CoachingDomain.RUNNING:
            # Extract distances, times, paces
            running_patterns = [
                r"\b\d+\s*miles?\b",
                r"\b\d+\s*km\b",
                r"\b\d+:\d+\s*pace\b",
            ]
            for pattern in running_patterns:
                matches = re.findall(pattern, content_lower)
                found_tags.extend(matches[:3])

        return found_tags[:10]  # Limit total tags

    async def get_domain_stats(self, user_id: str) -> Dict[str, Any]:
        """Get statistics about user's memory distribution across domains."""
        # Get all user memories
        all_memories = await self.mem0_adapter.search_memories(
            user_id=user_id,
            query="",  # Empty query to get all
            limit=1000,  # Large limit to get comprehensive stats
        )

        # Count memories by domain
        domain_counts = Counter()
        memory_types_by_domain = {}

        for memory in all_memories:
            metadata = memory.get("metadata", {})
            primary_domain = metadata.get("primary_domain", "general")
            memory_type = metadata.get("memory_type", "unknown")

            domain_counts[primary_domain] += 1

            if primary_domain not in memory_types_by_domain:
                memory_types_by_domain[primary_domain] = Counter()
            memory_types_by_domain[primary_domain][memory_type] += 1

        return {
            "total_memories": len(all_memories),
            "domain_distribution": dict(domain_counts),
            "memory_types_by_domain": {
                domain: dict(types) for domain, types in memory_types_by_domain.items()
            },
            "most_active_domain": (
                domain_counts.most_common(1)[0] if domain_counts else None
            ),
        }
