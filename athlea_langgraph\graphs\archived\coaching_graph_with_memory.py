"""
Enhanced Coaching Graph with Memory Integration.

This module extends the basic coaching graph with memory capabilities,
including user profile management and conversation history.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from ...agents.head_coach import clarification_node, head_coach_node, head_coach_router
from ...agents.planning_node import planning_node
from ...agents.reasoning_node import reasoning_node
from ...agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    running_coach_node,
    strength_coach_node,
)
from ...memory.mongo_memory import MongoMemoryStore, MongoSaver
from ...states.state import AgentState

# Import the new aggregation node
try:
    from ...agents.aggregation_node_v2 import aggregation_node_v2

    use_v2_aggregation = True
except ImportError:
    from ...agents.aggregation_node import aggregation_node

    use_v2_aggregation = False
    logging.warning(
        "Using v1 aggregation node. Consider upgrading to v2 for better production features."
    )

logger = logging.getLogger(__name__)


class MemoryEnhancedCoachingGraph:
    """
    A coaching graph enhanced with MongoDB memory capabilities.

    Provides both short-term (checkpointing) and long-term memory management.
    """

    def __init__(self, mongodb_uri: str, user_id: str):
        self.mongodb_uri = mongodb_uri
        self.user_id = user_id

        # Initialize memory components
        self.checkpointer = MongoSaver(
            connection_string=mongodb_uri,
            database="athlea_coaching",
            collection="checkpoints",
        )

        self.memory_store = MongoMemoryStore(
            connection_string=mongodb_uri,
            database="athlea_coaching",
            collection="memories",
        )

        self.graph = None

    async def load_user_memories(
        self, query: str = None, limit: int = 10
    ) -> List[Dict]:
        """Load relevant memories for the user."""
        namespace = f"user:{self.user_id}"

        if query:
            # Semantic search for relevant memories
            return await self.memory_store.search(namespace, query, limit)
        else:
            # Get user profile
            profile = await self.memory_store.get(namespace, "profile")
            return [profile] if profile else []

    async def save_user_memory(self, key: str, memory_data: Dict[str, Any]) -> None:
        """Save a memory for the user."""
        namespace = f"user:{self.user_id}"

        # Add timestamp to memory
        memory_data["timestamp"] = datetime.now().isoformat()
        memory_data["user_id"] = self.user_id

        await self.memory_store.put(namespace, key, memory_data)

    async def update_user_profile(self, profile_updates: Dict[str, Any]) -> None:
        """Update user profile with new information."""
        namespace = f"user:{self.user_id}"

        # Get existing profile
        existing_profile = await self.memory_store.get(namespace, "profile") or {}

        # Merge updates
        existing_profile.update(profile_updates)
        existing_profile["last_updated"] = datetime.now().isoformat()

        # Save updated profile
        await self.memory_store.put(namespace, "profile", existing_profile)

    async def memory_enhanced_reasoning_node(self, state: AgentState) -> Dict[str, Any]:
        """Enhanced reasoning node that incorporates user memories."""
        logger.info("--- Memory-Enhanced Reasoning Node ---")

        # Load relevant memories based on user query
        user_query = state.get("user_query", "")
        relevant_memories = await self.load_user_memories(user_query, limit=5)

        # Add memory context to state
        memory_context = {
            "relevant_memories": relevant_memories,
            "memory_count": len(relevant_memories),
        }

        # Run original reasoning with memory context
        enhanced_state = {**state, "memory_context": memory_context}
        result = await reasoning_node(enhanced_state)

        # Add memory context to result
        result["memory_context"] = memory_context

        return result

    async def memory_enhanced_aggregation_node(
        self, state: AgentState
    ) -> Dict[str, Any]:
        """Enhanced aggregation node that saves interaction to long-term memory."""
        logger.info("--- Memory-Enhanced Aggregation Node ---")

        # Run original aggregation
        if use_v2_aggregation:
            result = await aggregation_node_v2(state)
        else:
            result = await aggregation_node(state)

        # Save interaction to long-term memory
        await self._save_interaction_memory(state, result)

        return result

    async def _save_interaction_memory(
        self, state: AgentState, result: Dict[str, Any]
    ) -> None:
        """Save the interaction to long-term memory."""
        try:
            interaction_data = {
                "content": f"User asked: {state.get('user_query', '')}. Response: {result.get('aggregated_response', '')}",
                "user_query": state.get("user_query", ""),
                "response": result.get("aggregated_response", ""),
                "specialist_responses": {
                    "strength": state.get("strength_response"),
                    "running": state.get("running_response"),
                    "cardio": state.get("cardio_response"),
                    "cycling": state.get("cycling_response"),
                    "nutrition": state.get("nutrition_response"),
                    "recovery": state.get("recovery_response"),
                    "mental": state.get("mental_response"),
                },
                "reasoning": state.get("reasoning_output"),
                "plan": state.get("plan"),
                "type": "coaching_interaction",
            }

            # Generate unique key for this interaction
            interaction_key = f"interaction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            await self.save_user_memory(interaction_key, interaction_data)
            logger.info(f"Saved interaction memory: {interaction_key}")

        except Exception as e:
            logger.error(f"Failed to save interaction memory: {e}")

    async def _build_graph(self, use_checkpointing: bool = True) -> None:
        """Build the LangGraph with memory enhancements."""
        logger.info("Building memory-enhanced coaching graph...")

        # Import all the required components
        from ...agents.aggregation_node_v2 import aggregation_node_v2
        from ...agents.head_coach import head_coach_node, head_coach_router
        from ...agents.planning_node import planning_node
        from ...agents.reasoning_node import reasoning_node
        from ...agents.specialized_coaches import (
            cardio_coach_node,
            cycling_coach_node,
            mental_coach_node,
            nutrition_coach_node,
            recovery_coach_node,
            running_coach_node,
            strength_coach_node,
        )

        # Create the graph builder
        builder = StateGraph(AgentState)

        # Add all nodes
        builder.add_node("reasoning", self.memory_enhanced_reasoning_node)
        builder.add_node("planning", planning_node)
        builder.add_node("head_coach", head_coach_node)
        builder.add_node("strength_coach", strength_coach_node)
        builder.add_node("running_coach", running_coach_node)
        builder.add_node("cardio_coach", cardio_coach_node)
        builder.add_node("cycling_coach", cycling_coach_node)
        builder.add_node("nutrition_coach", nutrition_coach_node)
        builder.add_node("recovery_coach", recovery_coach_node)
        builder.add_node("mental_coach", mental_coach_node)
        builder.add_node("aggregate_responses", self.memory_enhanced_aggregation_node)
        builder.add_node("clarification", clarification_node)

        # Add edges (same as before)
        builder.add_edge("reasoning", "planning")
        builder.add_edge("planning", "head_coach")
        builder.add_edge("strength_coach", "head_coach")
        builder.add_edge("running_coach", "head_coach")
        builder.add_edge("cardio_coach", "head_coach")
        builder.add_edge("cycling_coach", "head_coach")
        builder.add_edge("nutrition_coach", "head_coach")
        builder.add_edge("recovery_coach", "head_coach")
        builder.add_edge("mental_coach", "head_coach")
        builder.add_edge("clarification", "head_coach")
        builder.add_edge("aggregate_responses", END)

        # Add conditional edges
        builder.add_conditional_edges("head_coach", head_coach_router)

        # Set entry point
        builder.add_edge(START, "reasoning")

        # Compile with or without checkpointing
        if use_checkpointing:
            self.graph = builder.compile(checkpointer=self.checkpointer)
        else:
            self.graph = builder.compile()

        logger.info("Memory-enhanced coaching graph built successfully.")

    async def create_compiled_graph(
        self,
        enable_performance_monitoring: bool = True,
    ) -> CompiledStateGraph:
        """
        Create and compile the memory-enhanced coaching graph.

        Returns:
            Compiled state graph with MongoDB memory integration
        """
        logger.info("Creating memory-enhanced coaching graph...")

        # Configure logging
        if enable_performance_monitoring:
            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            )

        # Create the graph
        await self._build_graph()

        logger.info("Memory-enhanced coaching graph compiled successfully.")
        return self.graph

    async def run_coaching_session(
        self,
        user_message: str,
        thread_id: str,
        user_profile: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Run a coaching session with memory integration.

        Args:
            user_message: The user's message/query
            thread_id: Unique thread ID for session continuity
            user_profile: Optional user profile data

        Returns:
            The coaching response with memory context
        """
        if not self.graph:
            await self.create_compiled_graph()

        # Load or create user profile
        if not user_profile:
            profile_memories = await self.load_user_memories()
            user_profile = profile_memories[0] if profile_memories else {}

        # Prepare initial state
        initial_state = {
            "messages": [HumanMessage(content=user_message)],
            "user_query": user_message,
            "user_profile": user_profile,
            "routing_decision": None,
            "pending_agents": None,
            "plan": None,
            "current_step": None,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            "current_plan": None,
            "is_onboarding": False,
            # Initialize specialist responses
            "strength_response": None,
            "running_response": None,
            "cardio_response": None,
            "cycling_response": None,
            "nutrition_response": None,
            "recovery_response": None,
            "mental_response": None,
            # Initialize other outputs
            "reasoning_output": None,
            "clarification_output": None,
            "aggregated_response": None,
        }

        # Run the graph with checkpointing
        config = {"configurable": {"thread_id": thread_id}}

        try:
            result = await self.graph.ainvoke(initial_state, config)

            # Update user profile if needed
            if result.get("user_profile") and result["user_profile"] != user_profile:
                await self.update_user_profile(result["user_profile"])

            return result

        except Exception as e:
            logger.error(f"Error in coaching session: {e}")
            raise


# Convenience function for backward compatibility
async def get_memory_enhanced_coaching_graph(
    mongodb_uri: str,
    user_id: str,
    enable_performance_monitoring: bool = True,
) -> MemoryEnhancedCoachingGraph:
    """
    Create a memory-enhanced coaching graph instance.

    Args:
        mongodb_uri: MongoDB connection string
        user_id: Unique user identifier
        enable_performance_monitoring: Enable performance metrics

    Returns:
        MemoryEnhancedCoachingGraph instance
    """
    graph_manager = MemoryEnhancedCoachingGraph(mongodb_uri, user_id)
    await graph_manager.create_compiled_graph(enable_performance_monitoring)
    return graph_manager
