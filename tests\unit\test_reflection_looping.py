#!/usr/bin/env python3
"""
Test script for enhanced reflection system with looping capabilities
"""

import asyncio
import logging
from athlea_langgraph.graphs.specialized_coach_graph import (
    create_specialized_coach_graph_from_config,
)

# Set up logging
logging.basicConfig(level=logging.INFO)


async def test_reflection_system():
    """Test the enhanced reflection system with both quality cases."""

    # Config with reflection enabled and multiple iterations allowed
    config = {
        "coach_domain": "strength",
        "user_id": "test_user",
        "enable_memory": False,
        "enable_reflection": True,
        "max_reflection_iterations": 2,  # Allow up to 2 improvement loops
        "apply_final_enhancement": True,  # Enable final enhancement
    }

    print("=== TESTING ENHANCED REFLECTION SYSTEM ===")
    print(f"Config: {config}")

    try:
        # Create the graph
        graph = await create_specialized_coach_graph_from_config(config)
        print("✅ Graph created successfully!")

        # Test 1: Basic query - should have good quality, no looping
        print("\n--- Test 1: High Quality Response (No Looping Expected) ---")
        simple_input = {
            "messages": [
                {"type": "human", "content": "What's the proper form for a squat?"}
            ],
            "coach_domain": "strength",
        }

        result1 = await graph.ainvoke(simple_input)
        print(f"✅ Test 1 completed!")
        print(f"Complexity: {result1.get('complexity_level')}")
        print(
            f"Reflection iterations: {result1.get('reflection_iterations_completed', 0)}"
        )
        print(f"Reflection final: {result1.get('reflection_final', False)}")
        print(f"Improvement applied: {result1.get('improvement_applied', False)}")
        if result1.get("reflection_assessment"):
            print(
                f"Quality score: {result1['reflection_assessment'].get('overall_score', 'N/A')}/10"
            )

        # Test 2: Potentially lower quality query - might trigger improvement
        print("\n--- Test 2: Potentially Lower Quality Response (Looping Possible) ---")
        vague_input = {
            "messages": [{"type": "human", "content": "How do I get strong?"}],
            "coach_domain": "strength",
        }

        result2 = await graph.ainvoke(vague_input)
        print(f"✅ Test 2 completed!")
        print(f"Complexity: {result2.get('complexity_level')}")
        print(
            f"Reflection iterations: {result2.get('reflection_iterations_completed', 0)}"
        )
        print(f"Reflection final: {result2.get('reflection_final', False)}")
        print(f"Improvement applied: {result2.get('improvement_applied', False)}")
        print(f"Loop triggered: {result2.get('reflection_loop_triggered', False)}")
        if result2.get("reflection_assessment"):
            print(
                f"Quality score: {result2['reflection_assessment'].get('overall_score', 'N/A')}/10"
            )

        # Show improvement guidance if any
        if result2.get("improvement_guidance"):
            print(
                f"Improvement guidance provided: Yes ({len(result2['improvement_guidance'])} chars)"
            )

        print("\n=== REFLECTION SYSTEM TEST COMPLETE ===")
        print("The reflection system can now:")
        print("1. ✅ Assess response quality across 6 dimensions")
        print("2. ✅ Determine if improvement is needed")
        print("3. ✅ Loop back to domain agent with specific guidance")
        print("4. ✅ Track iterations to prevent infinite loops")
        print("5. ✅ Apply final enhancements if needed")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_reflection_system())
