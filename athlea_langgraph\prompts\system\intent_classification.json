{"metadata": {"name": "intent_classification", "version": "1.0.0", "description": "Early intent classification for routing user queries to appropriate handlers", "author": "System", "created_at": "2025-06-06T16:55:00.000Z", "updated_at": "2025-06-06T16:55:00.000Z", "prompt_type": "system", "tags": ["classification", "intent", "routing", "early_detection"], "changelog": [{"version": "1.0.0", "date": "2025-06-06T16:55:00.000Z", "changes": "Initial creation from hardcoded intent classification prompt", "author": "System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "Classify user fitness queries quickly into these categories:\n\nSIMPLE_GREETING: Basic greetings like \"hello\", \"hi\", \"hey\"\nUNCLEAR_SHORT: Vague requests like \"help\", \"advice\", \"guidance\"\nCOMPLEX_QUERY: Specific fitness questions requiring detailed coaching\n\nRespond with ONLY the category name.", "context_template": null, "user_template": "Query: {user_query}", "examples": [{"input": "hi there", "output": "SIMPLE_GREETING"}, {"input": "hello", "output": "SIMPLE_GREETING"}, {"input": "help", "output": "UNCLEAR_SHORT"}, {"input": "advice", "output": "UNCLEAR_SHORT"}, {"input": "I need help with my squat form", "output": "COMPLEX_QUERY"}, {"input": "What's the best workout for building muscle?", "output": "COMPLEX_QUERY"}], "instructions": "Analyze the user query and classify it into one of three categories based on complexity and specificity. Simple greetings should go to SIMPLE_GREETING, vague or very short requests to UNCLEAR_SHORT, and specific fitness questions to COMPLEX_QUERY.", "constraints": ["Respond with ONLY the category name", "No additional explanation needed", "Fast classification for routing purposes"]}, "variables": {"temperature": 0.1, "max_tokens": 50, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["user_query"], "max_length": 500, "min_length": 5, "required_fields": ["user_query"], "allowed_variables": ["user_query"]}}