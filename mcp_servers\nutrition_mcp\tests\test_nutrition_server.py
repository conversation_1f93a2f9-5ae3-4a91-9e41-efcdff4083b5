import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import json

import pytest
from fastapi import HTTP<PERSON>x<PERSON>
from mcp.server import NotificationOptions, Server
from mcp.server.session import ServerSession
from mcp.server.stdio import stdio_server

# Import actual MCP types - these should be available since mcp==1.9.1 is in requirements
from mcp.types import (
    CallToolRequest,
    InitializeRequest,
    ListToolsRequest,
    TextContent,
    Tool,
)

from athlea_langgraph.tools.schemas import (
    CalculateDailyCaloriesSchema,
    GenerateMealPlanSchema,
    TrackHydrationSchema,
)

# Imports from the nutrition MCP server and related schemas
from mcp_servers.nutrition_mcp.server import app as nutrition_mcp_app
from mcp_servers.nutrition_mcp.server import call_tool as nutrition_call_tool_handler
from mcp_servers.nutrition_mcp.server import list_tools as nutrition_list_tools_handler
from mcp_servers.nutrition_mcp.server import (
    calorie_calc,
    meal_plan_tool,
    nutrition_assessment,
    recipe_tool,
)


@pytest.fixture
def mock_request():
    """Provides a mock ListToolsRequest object."""
    return ListToolsRequest(method="tools/list")


@pytest.fixture
def mock_call_tool_request():
    """Provides a mock CallToolRequest object factory."""

    def _create_request(name: str, arguments: dict):
        return CallToolRequest(
            method="tools/call", params={"name": name, "arguments": arguments}
        )

    return _create_request


@pytest.mark.asyncio
async def test_list_tools_nutrition_mcp(mock_request):
    """Tests the list_tools handler of the Nutrition MCP server."""
    # The actual list_tools function doesn't take parameters
    listed_tools = await nutrition_list_tools_handler()

    # Check that we get the expected number of tools
    assert len(listed_tools) >= 4

    tool_names = [t.name for t in listed_tools]
    assert "calculate_daily_calories" in tool_names
    assert "generate_comprehensive_meal_plan" in tool_names
    assert "search_recipes" in tool_names
    assert "track_hydration" in tool_names

    # Verify tool structure matches MCP Tool specification
    for tool in listed_tools:
        assert isinstance(tool, Tool)
        assert hasattr(tool, "name")
        assert hasattr(tool, "description")
        assert hasattr(tool, "inputSchema")


@pytest.mark.asyncio
async def test_call_tool_calculate_daily_calories_valid_input(mock_call_tool_request):
    """Tests call_tool with calculate_daily_calories and valid input."""
    # Mock the underlying tool method - use correct method name from calculator
    with patch.object(
        calorie_calc, "calculate_daily_calories", new_callable=AsyncMock
    ) as mock_calc:
        mock_calc.return_value = {"calories": 2500}

        tool_call_input = {
            "age": 30,
            "sex": "male",
            "height": 180,
            "weight": 75,
            "activity_level": "sedentary",
        }

        result = await nutrition_call_tool_handler(
            "calculate_daily_calories", tool_call_input
        )

        mock_calc.assert_called_once()
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "2500" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_generate_meal_plan_valid_input(mock_call_tool_request):
    """Tests call_tool with generate_meal_plan and valid input."""
    # Mock the underlying tool method
    with patch.object(
        meal_plan_tool, "generate_meal_plan", new_callable=AsyncMock
    ) as mock_plan:
        # Create a mock return object with dict() method
        mock_result = MagicMock()
        mock_result.dict.return_value = {
            "plan": "Breakfast: Oats, Lunch: Salad, Dinner: Chicken"
        }
        mock_plan.return_value = mock_result

        tool_call_input = {
            "date": "2024-01-15",  # Required field
            "calorie_target": 2000,
            "diet_type": "balanced",
        }

        result = await nutrition_call_tool_handler(
            "generate_comprehensive_meal_plan", tool_call_input
        )

        mock_plan.assert_called_once()
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "Breakfast" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_track_hydration_valid_input(mock_call_tool_request):
    """Tests call_tool with track_hydration and valid input."""
    tool_call_input = {
        "weight_kg": 70,
        "activity_duration_minutes": 60,
        "activity_intensity": "moderate",
        "current_intake_ml": 500,
    }

    result = await nutrition_call_tool_handler("track_hydration", tool_call_input)

    # Check that result is properly formatted for MCP
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    # Verify the hydration calculation logic is working
    response_data = json.loads(result.content[0].text)
    assert "daily_hydration_goal_ml" in response_data
    assert "remaining_ml" in response_data


@pytest.mark.asyncio
async def test_call_tool_invalid_tool_name_nutrition_mcp(mock_call_tool_request):
    """Tests call_tool with an invalid tool name for Nutrition MCP."""
    result = await nutrition_call_tool_handler("non_existent_nutrition_tool", {})

    # Check that result contains error message
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Error" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_calculate_daily_calories_invalid_input(mock_call_tool_request):
    """Tests call_tool with calculate_daily_calories and invalid input (missing required field)."""
    with patch.object(
        calorie_calc, "calculate_daily_calories", new_callable=AsyncMock
    ) as mock_calc:
        mock_calc.side_effect = Exception("Missing required field")

        tool_call_input = {
            "sex": "female",
            "height": 160,
        }  # Missing age, weight, activity_level

        result = await nutrition_call_tool_handler(
            "calculate_daily_calories", tool_call_input
        )

        # Should return MCP-formatted error response
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "Error" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_generate_meal_plan_invalid_input_type(mock_call_tool_request):
    """Tests call_tool with generate_meal_plan and invalid input type."""
    tool_call_input = {
        "daily_calories": "two thousand",  # Should be int
        "dietary_restrictions": "vegetarian",  # Should be list
    }

    result = await nutrition_call_tool_handler(
        "generate_comprehensive_meal_plan", tool_call_input
    )

    # Should return MCP-formatted error response
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Error" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_track_hydration_invalid_input_value(mock_call_tool_request):
    """Tests call_tool with track_hydration and invalid input value (negative weight)."""
    tool_call_input = {
        "weight_kg": -70,  # Should be positive
        "activity_duration_minutes": 60,
        "activity_intensity": "moderate",
    }

    result = await nutrition_call_tool_handler("track_hydration", tool_call_input)

    # Check that result contains error message or handles gracefully
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    # Either should be an error or calculated result (depending on validation)
    response_text = result.content[0].text
    assert response_text is not None
