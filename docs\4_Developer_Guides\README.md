# 4. Developer Guides

This section provides practical, "how-to" guides for developers working on the Athlea coaching system. These documents cover common development, debugging, and testing tasks.

## Available Guides

-   **`1_AGENT_AND_TOOL_DEV.md`**: This is the primary guide for extending the capabilities of our agents. It explains how to:
    -   Create new, specialized tools for our coaches.
    -   Set up a **Model Context Protocol (MCP)** server to expose those tools.
    -   Integrate the new tools with the appropriate specialized coaches.

-   **`2_LANGGRAPH_STUDIO.md`**: A comprehensive guide to using **LangGraph Studio**, our primary tool for visual debugging. It covers setup, basic and advanced features, and common testing scenarios. If you need to understand or debug the flow of an agent, start here.

-   **`3_TESTING_GUIDE.md`**: Explains the testing strategy for the project, focusing on how to test the **specialized coaches**. It details the available test scripts, what they cover (tool integration, memory, specialist routing), and how to interpret their output. 