"""
Response Regeneration Agent for Coaching Response Improvement.

Takes reflection feedback and regenerates improved coaching responses
that address safety concerns and quality issues identified during reflection.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.reflection_state import ReflectionAgentState


class CoachingResponseRegenerator:
    """
    Regenerates coaching responses based on reflection feedback.

    Implements the improvement side of the Generator-Reflector loop,
    taking critique and safety concerns to produce better responses.
    """

    def __init__(self, model_name: str = "gpt-4"):
        """Initialize the response regenerator with specified model."""
        self.llm = create_azure_chat_openai(model=model_name)
        self.logger = logging.getLogger(__name__)

        # Domain-specific improvement prompts
        self.improvement_prompts = self._initialize_improvement_prompts()

    def _initialize_improvement_prompts(self) -> Dict[str, str]:
        """Initialize domain-specific improvement prompt templates."""
        return {
            "strength": """
            You are an expert strength training coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Address all safety concerns identified in the reflection
            2. Ensure proper form cues and technique descriptions
            3. Include appropriate progression guidelines
            4. Add safety disclaimers where needed
            5. Consider user limitations and contraindications
            
            Focus on creating a response that is both effective and maximally safe.
            """,
            "nutrition": """
            You are an expert sports nutrition coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Address allergen concerns and dietary restrictions
            2. Ensure supplement recommendations are safe and evidence-based
            3. Include appropriate portion and timing guidelines
            4. Add medical disclaimers where appropriate
            5. Consider individual metabolic and health factors
            
            Focus on nutritional safety while maintaining coaching effectiveness.
            """,
            "cardio": """
            You are an expert cardiovascular training coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Ensure training intensities are appropriate for fitness level
            2. Include proper warm-up and cool-down protocols
            3. Address heart rate and exertion monitoring
            4. Include environmental safety considerations
            5. Consider medical limitations and contraindications
            
            Focus on cardiovascular safety while optimizing training benefits.
            """,
            "recovery": """
            You are an expert recovery and injury prevention coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Ensure sleep recommendations are evidence-based and safe
            2. Validate mobility and stretching protocols
            3. Address pain management appropriately
            4. Include proper progression for recovery protocols
            5. Consider individual injury history and limitations
            
            Focus on recovery effectiveness while preventing further injury.
            """,
            "mental": """
            You are an expert sports psychology coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Ensure psychological safety and appropriate support
            2. Validate stress management techniques
            3. Address mental health considerations
            4. Ensure goal setting is realistic and healthy
            5. Include appropriate professional referral guidance
            
            Focus on psychological wellness while maintaining coaching effectiveness.
            """,
            "general": """
            You are an expert fitness coach regenerating a response based on safety feedback.
            
            IMPROVEMENT PRIORITIES:
            1. Address all safety concerns comprehensively
            2. Ensure information accuracy and evidence-base
            3. Include appropriate disclaimers and warnings
            4. Consider individual limitations and factors
            5. Provide clear, actionable, and safe guidance
            
            Focus on overall fitness safety while maintaining coaching quality.
            """,
        }

    async def regenerate_response(
        self,
        original_response: str,
        original_query: str,
        user_profile: Dict[str, Any],
        reflection_feedback: str,
        improvement_areas: List[str],
        safety_validation: Dict[str, Any],
        coaching_domain: str = "general",
    ) -> Dict[str, Any]:
        """
        Regenerate a coaching response based on reflection feedback.

        Args:
            original_response: The original response that needs improvement
            original_query: The original user query
            user_profile: User's fitness profile and limitations
            reflection_feedback: Detailed feedback from reflection analysis
            improvement_areas: Specific areas identified for improvement
            safety_validation: Safety validation results from reflection
            coaching_domain: The domain of coaching (strength, nutrition, etc.)

        Returns:
            Dictionary containing the improved response and metadata
        """

        self.logger.info(f"Regenerating response for {coaching_domain} domain")

        try:
            # Create improvement prompt
            improvement_prompt = self._create_improvement_prompt(
                original_response=original_response,
                original_query=original_query,
                user_profile=user_profile,
                reflection_feedback=reflection_feedback,
                improvement_areas=improvement_areas,
                safety_validation=safety_validation,
                coaching_domain=coaching_domain,
            )

            # Generate improved response
            improved_response = await self._generate_improved_response(
                improvement_prompt
            )

            # Validate improvement
            improvement_assessment = self._assess_improvement(
                original_response=original_response,
                improved_response=improved_response,
                improvement_areas=improvement_areas,
            )

            return {
                "improved_response": improved_response,
                "improvement_assessment": improvement_assessment,
                "regeneration_metadata": {
                    "domain": coaching_domain,
                    "timestamp": datetime.utcnow().isoformat(),
                    "improvement_areas_addressed": improvement_areas,
                    "safety_issues_addressed": len(
                        safety_validation.get("safety_warnings", [])
                    ),
                    "original_length": len(original_response),
                    "improved_length": len(improved_response),
                },
            }

        except Exception as e:
            self.logger.error(f"Error during response regeneration: {e}")
            return self._create_fallback_regeneration(original_response)

    def _create_improvement_prompt(
        self,
        original_response: str,
        original_query: str,
        user_profile: Dict[str, Any],
        reflection_feedback: str,
        improvement_areas: List[str],
        safety_validation: Dict[str, Any],
        coaching_domain: str,
    ) -> str:
        """Create a domain-specific improvement prompt."""

        domain_prompt = self.improvement_prompts.get(
            coaching_domain, self.improvement_prompts["general"]
        )

        # Extract user context
        user_context = self._extract_user_context(user_profile)

        # Summarize safety concerns
        safety_concerns = self._summarize_safety_concerns(safety_validation)

        # Format improvement areas
        areas_text = (
            ", ".join(improvement_areas) if improvement_areas else "general quality"
        )

        prompt = f"""
        {domain_prompt}
        
        ORIGINAL USER QUERY:
        {original_query}
        
        USER PROFILE CONTEXT:
        {user_context}
        
        ORIGINAL RESPONSE THAT NEEDS IMPROVEMENT:
        {original_response}
        
        REFLECTION FEEDBACK:
        {reflection_feedback}
        
        SPECIFIC AREAS FOR IMPROVEMENT:
        {areas_text}
        
        SAFETY CONCERNS TO ADDRESS:
        {safety_concerns}
        
        REGENERATION INSTRUCTIONS:
        Please create an improved version of the original response that:
        
        1. ADDRESSES SAFETY CONCERNS:
           - Fix any safety issues identified in the reflection
           - Add appropriate warnings and disclaimers
           - Ensure recommendations are safe for the user's profile
           - Include contraindication checks where relevant
        
        2. IMPROVES QUALITY IN IDENTIFIED AREAS:
           - Enhance accuracy and evidence-base
           - Improve completeness and thoroughness
           - Increase clarity and actionability
           - Better address the user's specific query
        
        3. MAINTAINS COACHING EFFECTIVENESS:
           - Keep the helpful and supportive tone
           - Preserve actionable recommendations
           - Maintain personalization to user's goals
           - Ensure practical applicability
        
        4. ENHANCES STRUCTURE AND PRESENTATION:
           - Use clear headings and bullet points
           - Provide step-by-step guidance where appropriate
           - Include specific examples and measurements
           - Add progression and modification options
        
        The improved response should be comprehensive but concise, prioritizing user safety while maintaining coaching quality and effectiveness.
        """

        return prompt

    def _extract_user_context(self, user_profile: Dict[str, Any]) -> str:
        """Extract relevant user context for response improvement."""
        context_parts = []

        if user_profile.get("fitness_level"):
            context_parts.append(f"Fitness Level: {user_profile['fitness_level']}")

        if user_profile.get("goals"):
            goals = (
                user_profile["goals"]
                if isinstance(user_profile["goals"], list)
                else [user_profile["goals"]]
            )
            context_parts.append(f"Goals: {', '.join(goals)}")

        if user_profile.get("limitations"):
            limitations = (
                user_profile["limitations"]
                if isinstance(user_profile["limitations"], list)
                else [user_profile["limitations"]]
            )
            context_parts.append(f"Limitations/Injuries: {', '.join(limitations)}")

        if user_profile.get("allergies"):
            allergies = (
                user_profile["allergies"]
                if isinstance(user_profile["allergies"], list)
                else [user_profile["allergies"]]
            )
            context_parts.append(f"Allergies: {', '.join(allergies)}")

        if user_profile.get("experience_years"):
            context_parts.append(
                f"Experience: {user_profile['experience_years']} years"
            )

        return (
            "\n".join(context_parts)
            if context_parts
            else "No specific user profile information available."
        )

    def _summarize_safety_concerns(self, safety_validation: Dict[str, Any]) -> str:
        """Summarize safety concerns from validation results."""
        concerns = []

        injury_risk = safety_validation.get("injury_risk_score", 1.0)
        if injury_risk < 0.7:
            concerns.append(f"High injury risk identified (score: {injury_risk:.2f})")

        if not safety_validation.get("form_safety_validated", True):
            concerns.append("Form and technique safety issues")

        if not safety_validation.get("contraindications_checked", True):
            concerns.append("Contraindication checks needed")

        if not safety_validation.get("progressive_overload_safe", True):
            concerns.append("Progressive overload safety concerns")

        safety_warnings = safety_validation.get("safety_warnings", [])
        if safety_warnings:
            concerns.extend([f"Warning: {warning}" for warning in safety_warnings])

        return (
            "\n".join(concerns) if concerns else "No major safety concerns identified."
        )

    async def _generate_improved_response(self, improvement_prompt: str) -> str:
        """Generate improved response using the LLM."""

        messages = [
            SystemMessage(
                content="You are an expert fitness coach focused on providing safe, accurate, and helpful coaching responses."
            ),
            HumanMessage(content=improvement_prompt),
        ]

        try:
            response = await self.llm.ainvoke(messages)
            return response.content

        except Exception as e:
            self.logger.error(f"Error generating improved response: {e}")
            return "Unable to generate improved response due to technical issues. Please consult with a qualified fitness professional."

    def _assess_improvement(
        self,
        original_response: str,
        improved_response: str,
        improvement_areas: List[str],
    ) -> Dict[str, Any]:
        """Assess the quality of improvement in the regenerated response."""

        # Basic improvement metrics
        length_improvement = len(improved_response) - len(original_response)

        # Check for safety keywords in improved response
        safety_keywords = [
            "warning",
            "caution",
            "safety",
            "careful",
            "consult",
            "professional",
        ]
        safety_mentions = sum(
            1
            for keyword in safety_keywords
            if keyword.lower() in improved_response.lower()
        )

        # Check for structure improvements
        structure_indicators = [
            "step",
            "first",
            "second",
            "example",
            "tip",
            "important",
        ]
        structure_score = sum(
            1
            for indicator in structure_indicators
            if indicator.lower() in improved_response.lower()
        )

        # Estimate improvement quality
        improvement_score = 0.7  # Base score

        if length_improvement > 0:
            improvement_score += 0.1  # More comprehensive

        if safety_mentions > 0:
            improvement_score += 0.15  # Added safety considerations

        if structure_score > 2:
            improvement_score += 0.05  # Better structured

        improvement_score = min(1.0, improvement_score)

        return {
            "improvement_score": improvement_score,
            "length_change": length_improvement,
            "safety_mentions_added": safety_mentions,
            "structure_improvements": structure_score > 2,
            "areas_likely_addressed": improvement_areas,
            "estimated_quality": improvement_score,
        }

    def _create_fallback_regeneration(self, original_response: str) -> Dict[str, Any]:
        """Create a fallback regeneration when errors occur."""

        fallback_response = f"""
        {original_response}
        
        **Important Safety Note**: Please consult with a qualified fitness professional before implementing any exercise or nutrition recommendations, especially if you have any health conditions or injuries.
        
        **Disclaimer**: This response has been flagged for additional review. For personalized and safe fitness guidance, consider working with a certified trainer or healthcare provider.
        """

        return {
            "improved_response": fallback_response,
            "improvement_assessment": {
                "improvement_score": 0.6,
                "length_change": len(fallback_response) - len(original_response),
                "safety_mentions_added": 2,
                "structure_improvements": False,
                "areas_likely_addressed": ["safety"],
                "estimated_quality": 0.6,
                "fallback_used": True,
            },
            "regeneration_metadata": {
                "domain": "unknown",
                "timestamp": datetime.utcnow().isoformat(),
                "improvement_areas_addressed": ["safety"],
                "safety_issues_addressed": 1,
                "original_length": len(original_response),
                "improved_length": len(fallback_response),
                "error": True,
            },
        }


# Node function for LangGraph integration
async def response_regeneration_node(state: ReflectionAgentState) -> Dict[str, Any]:
    """
    LangGraph node function for response regeneration.

    Takes reflection feedback and regenerates an improved coaching response
    that addresses identified safety and quality concerns.
    """

    logger = logging.getLogger(__name__)
    logger.info("--- Response Regeneration Node Execution ---")

    try:
        # Check if regeneration is needed
        reflection_metadata = state.get("reflection_metadata", {})
        reflection_feedback = reflection_metadata.get("reflection_feedback")

        if not reflection_feedback:
            logger.warning("No reflection feedback available for regeneration")
            return {"regeneration_error": "No reflection feedback available"}

        # Get original response and query
        original_response = state.get("original_response")
        if not original_response:
            # Try to get from messages
            messages = state.get("messages", [])
            for message in reversed(messages):
                if isinstance(message, AIMessage):
                    original_response = message.content
                    break

        if not original_response:
            logger.warning("No original response found for regeneration")
            return {"regeneration_error": "No original response found"}

        # Get user query
        user_query = "No specific query available"
        messages = state.get("messages", [])
        for message in reversed(messages):
            if isinstance(message, HumanMessage):
                user_query = message.content
                break

        # Determine coaching domain
        current_agent = state.get("current_reflection_agent", "general")
        coaching_domain = (
            current_agent.replace("_coach", "").replace("_agent", "")
            if current_agent
            else "general"
        )

        # Initialize regenerator
        regenerator = CoachingResponseRegenerator()

        # Perform regeneration
        regeneration_result = await regenerator.regenerate_response(
            original_response=original_response,
            original_query=user_query,
            user_profile=state.get("user_profile", {}),
            reflection_feedback=reflection_feedback,
            improvement_areas=reflection_metadata.get("improvement_areas", []),
            safety_validation=state.get("safety_validation", {}),
            coaching_domain=coaching_domain,
        )

        # Create improved message
        improved_message = AIMessage(content=regeneration_result["improved_response"])

        # Update state with improved response
        current_messages = state.get("messages", [])
        updated_messages = current_messages + [improved_message]

        # Update quality tracking
        improvement_assessment = regeneration_result["improvement_assessment"]

        updates = {
            "messages": updated_messages,
            "response_quality_after": improvement_assessment["improvement_score"],
            "regeneration_metadata": regeneration_result["regeneration_metadata"],
            "improvement_assessment": improvement_assessment,
        }

        logger.info(
            f"Response regenerated. Improvement score: {improvement_assessment['improvement_score']:.2f}"
        )

        return updates

    except Exception as e:
        logger.error(f"Error in response regeneration node: {e}")
        return {
            "regeneration_error": str(e),
            "regeneration_metadata": {
                "error": True,
                "timestamp": datetime.utcnow().isoformat(),
            },
        }
