"""
Advanced version management for prompts with diff, rollback, and tagging capabilities.
"""

import json
import shutil
from dataclasses import asdict, dataclass
from datetime import datetime
from difflib import unified_diff
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .prompt_models import ChangelogEntry, PromptConfig


@dataclass
class VersionTag:
    """Version tag for semantic labeling."""

    name: str
    version: str
    created_at: datetime
    description: str
    author: str

    def to_dict(self) -> dict:
        return {
            "name": self.name,
            "version": self.version,
            "created_at": self.created_at.isoformat(),
            "description": self.description,
            "author": self.author,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "VersionTag":
        return cls(
            name=data["name"],
            version=data["version"],
            created_at=datetime.fromisoformat(data["created_at"]),
            description=data["description"],
            author=data["author"],
        )


@dataclass
class VersionDiff:
    """Represents differences between two prompt versions."""

    old_version: str
    new_version: str
    content_diff: List[str]
    metadata_changes: Dict[str, Tuple[Any, Any]]
    variables_changes: Dict[str, Tuple[Any, Any]]
    created_at: datetime

    def to_dict(self) -> dict:
        return {
            "old_version": self.old_version,
            "new_version": self.new_version,
            "content_diff": self.content_diff,
            "metadata_changes": self.metadata_changes,
            "variables_changes": self.variables_changes,
            "created_at": self.created_at.isoformat(),
        }


class VersionManager:
    """Advanced version management for prompts."""

    def __init__(self, prompts_dir: str):
        self.prompts_dir = Path(prompts_dir)
        self.versions_dir = self.prompts_dir / ".versions"
        self.tags_dir = self.prompts_dir / ".tags"
        self.diffs_dir = self.prompts_dir / ".diffs"

        # Create directories
        self.versions_dir.mkdir(exist_ok=True)
        self.tags_dir.mkdir(exist_ok=True)
        self.diffs_dir.mkdir(exist_ok=True)

    def save_version(self, config: PromptConfig, prompt_name: str) -> str:
        """Save a new version of a prompt."""
        version = config.metadata.version
        version_dir = self.versions_dir / prompt_name
        version_dir.mkdir(exist_ok=True)

        version_file = version_dir / f"v{version}.json"

        with open(version_file, "w") as f:
            json.dump(config.to_dict(), f, indent=2, default=str)

        return str(version_file)

    def load_version(self, prompt_name: str, version: str) -> PromptConfig:
        """Load a specific version of a prompt."""
        version_file = self.versions_dir / prompt_name / f"v{version}.json"

        if not version_file.exists():
            raise FileNotFoundError(
                f"Version {version} not found for prompt {prompt_name}"
            )

        with open(version_file, "r") as f:
            data = json.load(f)

        return PromptConfig.from_dict(data)

    def list_versions(self, prompt_name: str) -> List[str]:
        """List all versions for a prompt."""
        version_dir = self.versions_dir / prompt_name

        if not version_dir.exists():
            return []

        versions = []
        for version_file in version_dir.glob("v*.json"):
            version = version_file.stem[1:]  # Remove 'v' prefix
            versions.append(version)

        # Sort versions properly (semantic versioning)
        versions.sort(key=lambda v: [int(x) for x in v.split(".")])
        return versions

    def get_latest_version(self, prompt_name: str) -> Optional[str]:
        """Get the latest version number for a prompt."""
        versions = self.list_versions(prompt_name)
        return versions[-1] if versions else None

    def create_diff(
        self, prompt_name: str, old_version: str, new_version: str
    ) -> VersionDiff:
        """Create a diff between two versions."""
        old_config = self.load_version(prompt_name, old_version)
        new_config = self.load_version(prompt_name, new_version)

        # Content diff
        old_content = old_config.prompt.system.splitlines(keepends=True)
        new_content = new_config.prompt.system.splitlines(keepends=True)

        content_diff = list(
            unified_diff(
                old_content,
                new_content,
                fromfile=f"v{old_version}",
                tofile=f"v{new_version}",
                lineterm="",
            )
        )

        # Metadata changes
        metadata_changes = {}
        old_meta = old_config.metadata.to_dict()
        new_meta = new_config.metadata.to_dict()

        for key in set(old_meta.keys()) | set(new_meta.keys()):
            old_val = old_meta.get(key)
            new_val = new_meta.get(key)
            if old_val != new_val:
                metadata_changes[key] = (old_val, new_val)

        # Variables changes
        variables_changes = {}
        if old_config.variables and new_config.variables:
            old_vars = old_config.variables.to_dict()
            new_vars = new_config.variables.to_dict()

            for key in set(old_vars.keys()) | set(new_vars.keys()):
                old_val = old_vars.get(key)
                new_val = new_vars.get(key)
                if old_val != new_val:
                    variables_changes[key] = (old_val, new_val)

        diff = VersionDiff(
            old_version=old_version,
            new_version=new_version,
            content_diff=content_diff,
            metadata_changes=metadata_changes,
            variables_changes=variables_changes,
            created_at=datetime.now(),
        )

        # Save diff for future reference
        self._save_diff(prompt_name, diff)

        return diff

    def _save_diff(self, prompt_name: str, diff: VersionDiff):
        """Save a diff to disk."""
        diff_file = (
            self.diffs_dir
            / f"{prompt_name}_v{diff.old_version}_to_v{diff.new_version}.json"
        )

        with open(diff_file, "w") as f:
            json.dump(diff.to_dict(), f, indent=2, default=str)

    def rollback_to_version(
        self, prompt_name: str, target_version: str, author: str = "System"
    ) -> PromptConfig:
        """Rollback a prompt to a previous version."""
        # Load the target version
        target_config = self.load_version(prompt_name, target_version)

        # Create a new version based on the target
        current_versions = self.list_versions(prompt_name)
        if current_versions:
            latest_version = current_versions[-1]
            version_parts = latest_version.split(".")
            version_parts[-1] = str(int(version_parts[-1]) + 1)
            new_version = ".".join(version_parts)
        else:
            new_version = "1.0.1"

        # Update metadata for rollback
        target_config.metadata.version = new_version
        target_config.metadata.updated_at = datetime.now()

        # Add changelog entry
        changelog_entry = ChangelogEntry(
            version=new_version,
            date=datetime.now(),
            changes=f"Rolled back to version {target_version}",
            author=author,
            breaking_changes=True,
        )
        target_config.metadata.changelog.append(changelog_entry)

        return target_config

    def create_tag(
        self,
        prompt_name: str,
        version: str,
        tag_name: str,
        description: str,
        author: str = "CLI User",
    ) -> VersionTag:
        """Create a version tag."""
        # Verify version exists
        if version not in self.list_versions(prompt_name):
            raise ValueError(f"Version {version} not found for prompt {prompt_name}")

        tag = VersionTag(
            name=tag_name,
            version=version,
            created_at=datetime.now(),
            description=description,
            author=author,
        )

        # Save tag
        tags_file = self.tags_dir / f"{prompt_name}.json"
        tags_data = {}

        if tags_file.exists():
            with open(tags_file, "r") as f:
                tags_data = json.load(f)

        tags_data[tag_name] = tag.to_dict()

        with open(tags_file, "w") as f:
            json.dump(tags_data, f, indent=2)

        return tag

    def list_tags(self, prompt_name: str) -> List[VersionTag]:
        """List all tags for a prompt."""
        tags_file = self.tags_dir / f"{prompt_name}.json"

        if not tags_file.exists():
            return []

        with open(tags_file, "r") as f:
            tags_data = json.load(f)

        return [VersionTag.from_dict(data) for data in tags_data.values()]

    def get_version_by_tag(self, prompt_name: str, tag_name: str) -> Optional[str]:
        """Get version number by tag name."""
        tags = self.list_tags(prompt_name)
        for tag in tags:
            if tag.name == tag_name:
                return tag.version
        return None

    def get_version_history(self, prompt_name: str) -> List[Dict[str, Any]]:
        """Get complete version history with metadata."""
        versions = self.list_versions(prompt_name)
        tags = self.list_tags(prompt_name)

        # Create tag lookup
        tag_lookup = {}
        for tag in tags:
            if tag.version not in tag_lookup:
                tag_lookup[tag.version] = []
            tag_lookup[tag.version].append(tag.name)

        history = []
        for version in versions:
            try:
                config = self.load_version(prompt_name, version)
                history.append(
                    {
                        "version": version,
                        "created_at": config.metadata.created_at,
                        "updated_at": config.metadata.updated_at,
                        "author": config.metadata.author,
                        "tags": tag_lookup.get(version, []),
                        "changelog": [
                            entry.to_dict() for entry in config.metadata.changelog
                        ],
                        "deprecated": config.metadata.deprecated,
                        "experimental": config.metadata.experimental,
                    }
                )
            except Exception:
                # Skip corrupted versions
                continue

        return history

    def cleanup_old_versions(self, prompt_name: str, keep_last: int = 10) -> List[str]:
        """Clean up old versions, keeping only the most recent."""
        versions = self.list_versions(prompt_name)

        if len(versions) <= keep_last:
            return []

        versions_to_delete = versions[:-keep_last]
        deleted_versions = []

        for version in versions_to_delete:
            version_file = self.versions_dir / prompt_name / f"v{version}.json"
            if version_file.exists():
                version_file.unlink()
                deleted_versions.append(version)

        return deleted_versions

    def backup_versions(self, prompt_name: str, backup_dir: str) -> str:
        """Backup all versions of a prompt."""
        source_dir = self.versions_dir / prompt_name
        backup_path = Path(backup_dir) / f"{prompt_name}_versions_backup"

        if source_dir.exists():
            shutil.copytree(source_dir, backup_path, dirs_exist_ok=True)

        return str(backup_path)
