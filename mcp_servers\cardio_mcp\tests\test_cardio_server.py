from unittest.mock import AsyncMock, MagicMock
from unittest.mock import patch, <PERSON><PERSON>
import json

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>

# Import actual MCP types - these should be available since mcp==1.9.1 is in requirements
from mcp.types import (
    CallToolRequest,
    InitializeRequest,
    ListToolsRequest,
    TextContent,
    Tool,
)

from athlea_langgraph.tools.schemas import (
    CalculatePaceZonesSchema,
    CalculateVo2MaxEstimateSchema,
    GenerateRunningRouteSchema,
    PlanIntervalWorkoutSchema,
)

# Imports from the cardio MCP server and related schemas
from mcp_servers.cardio_mcp.server import call_tool as cardio_call_tool_handler
from mcp_servers.cardio_mcp.server import list_tools as cardio_list_tools_handler

# Import tool instances that should be available
from mcp_servers.cardio_mcp.server import assessment_tool, zones_calculator


@pytest.fixture
def mock_request():
    """Provides a mock ListToolsRequest object."""
    return ListToolsRequest(method="tools/list")


@pytest.fixture
def mock_call_tool_request():
    """Provides a mock CallToolRequest object factory."""

    def _create_request(name: str, arguments: dict):
        return CallToolRequest(
            method="tools/call", params={"name": name, "arguments": arguments}
        )

    return _create_request


@pytest.mark.asyncio
async def test_list_tools_cardio_mcp(mock_request):
    """Tests the list_tools handler of the Cardio MCP server."""
    # The actual list_tools function doesn't take parameters
    listed_tools = await cardio_list_tools_handler()

    # Check that we get the expected number of tools
    assert len(listed_tools) >= 3

    tool_names = [t.name for t in listed_tools]
    assert "comprehensive_cardio_assessment" in tool_names
    assert "calculate_training_zones" in tool_names
    assert "calculate_heart_rate_zones" in tool_names

    # Verify tool structure matches MCP Tool specification
    for tool in listed_tools:
        assert isinstance(tool, Tool)
        assert hasattr(tool, "name")
        assert hasattr(tool, "description")
        assert hasattr(tool, "inputSchema")


@pytest.mark.asyncio
async def test_call_tool_comprehensive_cardio_assessment_valid_input(
    mock_call_tool_request,
):
    """Tests call_tool with comprehensive_cardio_assessment and valid input."""

    # Mock the actual method that gets called in the server
    with patch.object(
        assessment_tool, "assess_cardio", new_callable=AsyncMock
    ) as mock_assess:
        # Create a mock return object with dict() method
        mock_result = Mock()
        mock_result.dict.return_value = {"vo2_max": 45.0, "assessment": "Good"}
        mock_assess.return_value = mock_result

        # Create complete assessment data that matches the required schema
        assessment_data = {
            "user_id": "test-user-123",
            "age": 30,
            "gender": "male",
            "weight_kg": 75.0,
            "height_cm": 180.0,
            "cardiovascular_metrics": {
                "resting_heart_rate": 60,
                "max_heart_rate": 190,
                "recovery_heart_rate": 25,
                "blood_pressure_systolic": 120,
                "blood_pressure_diastolic": 80,
                "vo2_max_known": None,
            },
            "performance_testing": {
                "mile_time_seconds": 480,
                "five_k_time_seconds": 1500,
                "ten_k_time_seconds": 3000,
                "step_test_recovery_hr": 120,
                "cooper_test_distance": 2800,
                "walk_test_hr": 130,
                "walk_test_time_seconds": 900,
            },
            "experience": {
                "years_training": 2.0,
                "weekly_frequency": 4,
                "primary_activities": ["running", "cycling"],
                "longest_continuous_activity": 90,
                "competition_experience": False,
                "previous_injuries": [],
                "current_pain_areas": [],
                "training_consistency": 4,
            },
            "goals": {
                "primary_goals": ["improve_endurance", "weight_loss"],
                "target_events": ["5k_race"],
                "goal_timeline": "3_months",
                "weekly_time_availability": 300,
                "intensity_preference": "moderate",
                "motivation_level": 4,
            },
            "equipment_environment": {
                "gym_access": True,
                "home_equipment": ["treadmill"],
                "outdoor_access": True,
                "preferred_environment": "mixed",
                "climate_considerations": ["moderate_climate"],
                "elevation_training": None,
                "pool_access": False,
            },
        }

        tool_call_input = {"assessment_data": json.dumps(assessment_data)}

        result = await cardio_call_tool_handler(
            "comprehensive_cardio_assessment", tool_call_input
        )

        mock_assess.assert_called_once()
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "45.0" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_calculate_training_zones_valid_input(mock_call_tool_request):
    """Tests call_tool with calculate_training_zones and valid input."""

    # Mock the actual method that gets called in the server
    with patch.object(
        zones_calculator, "calculate_training_zones", new_callable=AsyncMock
    ) as mock_zones:
        # Create a mock return object with dict() method
        mock_result = Mock()
        mock_result.dict.return_value = {
            "zones": {"zone1": "recovery", "zone2": "aerobic"}
        }
        mock_zones.return_value = mock_result

        # Create complete training zones data that matches the required schema
        zones_data = {
            "user_id": "test-user-123",
            "sport_type": "running",
            "heart_rate_zones": {
                "age": 30,
                "resting_heart_rate": 60,
                "max_heart_rate": 190,
                "lactate_threshold_hr": None,
                "method": "karvonen",
            },
            "running_pace_zones": {
                "five_k_time_seconds": 1500,
                "ten_k_time_seconds": 3000,
                "half_marathon_time_seconds": None,
                "marathon_time_seconds": None,
                "recent_threshold_pace": None,
            },
            "training_focus": "general_fitness",
        }

        tool_call_input = {"zones_data": json.dumps(zones_data)}

        result = await cardio_call_tool_handler(
            "calculate_training_zones", tool_call_input
        )

        mock_zones.assert_called_once()
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "recovery" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_calculate_heart_rate_zones_valid_input(mock_call_tool_request):
    """Tests call_tool with calculate_heart_rate_zones and valid input."""

    # Mock the actual method that gets called in the server - this is a sync method
    with patch.object(zones_calculator, "calculate_heart_rate_zones") as mock_hr_zones:
        # Create a mock return object with dict() method
        mock_result = Mock()
        mock_result.dict.return_value = {
            "zones": {"zone1": 120, "zone2": 140, "zone3": 160}
        }
        mock_hr_zones.return_value = mock_result

        tool_call_input = {
            "age": 30,
            "resting_heart_rate": 60,
            "max_heart_rate": 180,
            "method": "karvonen",
        }

        result = await cardio_call_tool_handler(
            "calculate_heart_rate_zones", tool_call_input
        )

        mock_hr_zones.assert_called_once()
        assert len(result.content) == 1
        assert isinstance(result.content[0], TextContent)
        assert "120" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_invalid_tool_name_cardio_mcp(mock_call_tool_request):
    """Tests call_tool with an invalid tool name for Cardio MCP."""
    result = await cardio_call_tool_handler("non_existent_cardio_tool", {})

    # Check that result contains error message
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Error" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_comprehensive_cardio_assessment_invalid_json(
    mock_call_tool_request,
):
    """Tests call_tool with comprehensive_cardio_assessment and invalid JSON input."""

    tool_call_input = {"assessment_data": "invalid json string"}

    result = await cardio_call_tool_handler(
        "comprehensive_cardio_assessment", tool_call_input
    )

    # Check that result contains error message
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Error" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_calculate_heart_rate_zones_missing_required_field(
    mock_call_tool_request,
):
    """Tests call_tool with calculate_heart_rate_zones missing required age field."""

    tool_call_input = {
        "resting_heart_rate": 60,
        "max_heart_rate": 180,
    }  # Missing required 'age' field

    result = await cardio_call_tool_handler(
        "calculate_heart_rate_zones", tool_call_input
    )

    # Check that result contains error message
    assert len(result.content) == 1
    assert isinstance(result.content[0], TextContent)
    assert "Error" in result.content[0].text
