import asyncio
import os
from typing import Dict, Any

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage

# Ensure the script can find the root package
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import create_comprehensive_coaching_graph, ComprehensiveCoachingConfig

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
# The test case from the "Golden Set"
TEST_QUERY = "I want to improve my cycling performance for road racing. Can you help me plan training sessions for building power and endurance?"
USER_ID = "test-user-cycling"
THREAD_ID = "test-thread-cycling-1"
# ---------------------

async def run_test(query: str, config: Dict[str, Any]):
    """
    Initializes the graph, invokes it with the test query, and prints the output.
    """
    print("🚀 Initializing Comprehensive Coaching Graph...")
    
    # Use a config object
    graph_config = ComprehensiveCoachingConfig(
        user_id=config["user_id"],
        thread_id=config["thread_id"],
        enable_mem0=False, # Disable memory for this specific test run for isolation
        use_react_agents=True,
        max_iterations=10,
        enable_human_feedback=False,
        enable_reflexion=True,
        max_reflexion_iterations=3
    )

    app = await create_comprehensive_coaching_graph(graph_config)

    print(f"🏃‍♀️ Running test query for user '{config['user_id']}'...")
    print(f"❓ Query: \"{query}\"")

    # The input to the graph is a list of messages
    inputs = {"messages": [HumanMessage(content=query)]}
    
    # Configuration for the invocation, including the thread_id
    run_config = {"configurable": {"thread_id": config["thread_id"], "user_id": config["user_id"]}}

    final_state = None
    try:
        async for event in app.astream_events(inputs, config=run_config, version="v2"):
            if event["event"] == "on_chain_end":
                if event["name"] == "ComprehensiveCoachingGraph":
                     final_state = event["data"]["output"]

        print("\n" + "="*50)
        print("✅ GRAPH EXECUTION COMPLETE")
        print("="*50)

        if final_state:
            # The final state is a dict, we're interested in the 'messages'
            final_message = final_state.get("messages")[-1]
            if hasattr(final_message, 'content'):
                 print("\n💬 Final Response:")
                 print(final_message.content)
            else:
                 print("\n" + str(final_state))
        else:
            print("No final state output found.")

    except Exception as e:
        print(f"\n❌ An error occurred during graph execution: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*50)
    print("🔍 Look for the LangSmith trace URL in the console output above.")
    print("   You will need to copy it into `athlea_langgraph/utils/trace_analyzer.py`")
    print("="*50)


if __name__ == "__main__":
    test_config = {
        "user_id": USER_ID,
        "thread_id": THREAD_ID
    }
    asyncio.run(run_test(TEST_QUERY, test_config)) 