"""
Onboarding Graph

This module creates the enhanced onboarding graph that guides users through
the process of gathering their fitness information, generating a personalized plan,
and creating intelligent training sessions.
"""

import logging
from typing import Any, Dict, Literal, Optional

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from athlea_langgraph.agents.onboarding import (
    check_completion_node,
    generate_plan_node,
    information_gatherer_node,
)

# Import new session generation nodes
from athlea_langgraph.agents.session_generation_nodes import (
    session_generation_node,
    coach_coordination_node,
    user_control_node,
    session_adaptation_node,
    session_flow_router,
)

from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    create_initial_onboarding_state,
)
from athlea_langgraph.services.mem0_memory_service import get_mem0_service

logger = logging.getLogger(__name__)


async def plan_review_node(state: OnboardingState) -> dict:
    """
    This node is a placeholder to create a declarative interruption point.
    The graph's `compile` method is configured to interrupt *after* this node runs,
    allowing the user to review the generated plan.
    """
    logger.info(
        "[Node: plan_review_node] Plan generated. Pausing for user review and approval."
    )
    return {}


class OnboardingGraph:
    """Enhanced onboarding graph with adaptive weekly session generation using multi-agent coordination"""

    def __init__(
        self,
        checkpointer=None,
        enable_mem0: bool = True,
        mem0_use_api: bool = False,
    ):
        """Initialize the onboarding graph with adaptive weekly planning capabilities

        Args:
            checkpointer: Optional checkpointer for state persistence
            enable_mem0: Whether to enable Mem0 memory integration
            mem0_use_api: Whether to use Mem0 API (True) or local instance (False)
        """
        self.checkpointer = checkpointer or MemorySaver()
        self.enable_mem0 = enable_mem0
        self.mem0_use_api = mem0_use_api
        self._graph = None
        self._mem0_service = None

    async def _get_mem0_service(self):
        """Get the Mem0 service instance (lazy initialization)"""
        if self.enable_mem0 and self._mem0_service is None:
            try:
                self._mem0_service = await get_mem0_service(use_api=self.mem0_use_api)
                logger.info("✅ Mem0 service initialized for onboarding")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Mem0 service: {e}")
                self._mem0_service = None
        return self._mem0_service

    def _routing_function(
        self, state: OnboardingState
    ) -> Literal["generatePlan", "END"]:
        """Route based on whether we have enough information."""
        has_enough_info = state.get("has_enough_info", False)

        if has_enough_info:
            logger.info(
                "[Graph Router] hasEnoughInfo is true. Routing to generatePlan."
            )
            return "generatePlan"
        else:
            # If we don't have enough info, end the turn to wait for the user's response
            # to the question just asked by gatherInfo.
            logger.info(
                "[Graph Router] hasEnoughInfo is false. Ending turn to await user response."
            )
            return "END"

    def _after_gather_info_router(
        self, state: OnboardingState
    ) -> Literal["checkCompletion", "END"]:
        """
        Decides whether to end the turn after a greeting or continue to check for completion.
        """
        messages = state.get("messages", [])
        # After gatherInfo, if there are <= 2 messages, it means we've just sent
        # the initial greeting (Human trigger + AI greeting). The turn should end.
        if len(messages) <= 2:
            logger.info(
                f"[Graph Router] Initial turn detected (message count: {len(messages)}). Ending turn to await user response."
            )
            return "END"
        else:
            # On subsequent turns, the user will have replied, so we check for completion.
            logger.info(
                f"[Graph Router] Subsequent turn (message count: {len(messages)}). Proceeding to check completion."
            )
            return "checkCompletion"

    def _after_plan_review_router(
        self, state: OnboardingState
    ) -> Literal["sessionGeneration", "generatePlan"]:
        """
        Routes based on user feedback after reviewing the plan.
        The user's feedback is the output of the interrupted 'plan_review_node'.
        """
        # The user's feedback will be the last message in the state.
        last_message = state["messages"][-1].content.lower()
        if (
            "yes" in last_message
            or "approve" in last_message
            or "looks good" in last_message
        ):
            logger.info(
                "[Graph Router] User approved the plan. Proceeding to session generation."
            )
            return "sessionGeneration"
        else:
            logger.info(
                "[Graph Router] User provided feedback. Looping back to regenerate plan."
            )
            return "generatePlan"

    def _session_routing_function(
        self, state: OnboardingState
    ) -> Literal["sessionGeneration", "complete"]:
        """Route after plan generation to adaptive weekly session generation"""
        # A plan is generated via a tool call within the information_gatherer_node.
        # We check if that tool call output exists in the state.
        if state.get("generated_plan") and not state.get("error"):
            logger.info(
                "[Graph Router] Plan generated successfully. Routing to sessionGeneration."
            )
            return "sessionGeneration"
        else:
            # If no plan, we end the turn, allowing the conversation to continue.
            logger.info(
                "[Graph Router] No plan generated yet. Ending turn to await user response."
            )
            return "complete"

    async def enhanced_information_gathering_node(
        self, state: OnboardingState
    ) -> Dict[str, Any]:
        """Enhanced information gathering with memory context injection"""
        logger.info(
            "🧠 [ONBOARDING] Enhanced information gathering with memory context"
        )

        # Get memory context before processing
        user_id = state.get("user_id")
        if user_id and self.enable_mem0:
            try:
                mem0_service = await self._get_mem0_service()
                if mem0_service:
                    # Retrieve onboarding context
                    context = await mem0_service.get_user_coaching_context(
                        user_id=user_id,
                        query="onboarding fitness goals preferences history",
                        limit=5,
                    )

                    if context:
                        # Inject memory context into state
                        logger.info(
                            f"🧠 Retrieved {len(context)} memory items for onboarding context"
                        )
                        # Handle both dict and string memory formats
                        memory_texts = []
                        for item in context:
                            if isinstance(item, dict):
                                memory_text = item.get("memory", str(item))
                            else:
                                memory_text = str(item)
                            memory_texts.append(f"- {memory_text}")

                        state["memory_context"] = "\n".join(memory_texts)
                    else:
                        logger.info("🔍 No previous onboarding context found")
            except Exception as e:
                logger.error(f"❌ Error retrieving memory context: {e}")

        # Call original information gatherer node
        from athlea_langgraph.agents.onboarding import information_gatherer_node

        result = await information_gatherer_node(state)

        return result

    async def enhanced_plan_generation_node(
        self, state: OnboardingState
    ) -> Dict[str, Any]:
        """Enhanced plan generation with memory storage"""
        logger.info("🧠 [ONBOARDING] Enhanced plan generation with memory storage")

        # Call original plan generation node
        from athlea_langgraph.agents.onboarding import generate_plan_node

        result = await generate_plan_node(state)

        # Store onboarding completion in memory
        user_id = state.get("user_id")
        if user_id and self.enable_mem0 and result.get("generated_plan"):
            try:
                mem0_service = await self._get_mem0_service()
                if mem0_service:
                    plan = result["generated_plan"]
                    sidebar_data = result.get("sidebar_data")

                    # Create comprehensive memory of onboarding completion
                    memory_text = f"""
                    User completed onboarding process:
                    - Generated Plan: {plan.name}
                    - Plan Type: {plan.plan_type}
                    - Level: {plan.level}
                    - Duration: {plan.duration}
                    - Disciplines: {', '.join(plan.disciplines)}
                    - Selected Sports: {', '.join(sidebar_data.selected_sports) if sidebar_data and sidebar_data.selected_sports else 'None'}
                    - Goals: {', '.join(sidebar_data.goals.list) if sidebar_data and sidebar_data.goals and sidebar_data.goals.list else 'None'}
                    - Plan Rationale: {plan.rationale}
                    """

                    await mem0_service.add_coaching_memory(
                        content=memory_text,
                        user_id=user_id,
                        metadata={
                            "type": "onboarding_completion",
                            "plan_id": plan.plan_id,
                            "plan_name": plan.name,
                            "disciplines": plan.disciplines,
                            "level": plan.level,
                        },
                    )

                    logger.info("✅ Stored onboarding completion in Mem0")
            except Exception as e:
                logger.error(f"❌ Error storing onboarding completion: {e}")

        return result

    def create_graph(self) -> StateGraph:
        """Create and configure the enhanced onboarding graph with adaptive weekly planning"""
        logger.info(
            "[OnboardingGraph] Creating enhanced onboarding graph with adaptive weekly session generation"
        )

        # Create the state graph
        graph = StateGraph(OnboardingState)

        # Add onboarding nodes (enhanced if Mem0 enabled)
        if self.enable_mem0:
            graph.add_node("gatherInfo", self.enhanced_information_gathering_node)
            graph.add_node("generatePlan", self.enhanced_plan_generation_node)
            logger.info("🧠 Added enhanced nodes with Mem0 integration")
        else:
            graph.add_node("gatherInfo", information_gatherer_node)
            graph.add_node("generatePlan", generate_plan_node)
            logger.info("📝 Added standard nodes without memory integration")

        # Re-add checkCompletion node
        graph.add_node("checkCompletion", check_completion_node)

        # Add the new plan_review_node
        graph.add_node("plan_review", plan_review_node)

        # Add new session generation nodes
        graph.add_node("sessionGeneration", session_generation_node)
        graph.add_node("coachCoordination", coach_coordination_node)
        graph.add_node("userControl", user_control_node)
        graph.add_node("sessionAdaptation", session_adaptation_node)

        # Corrected onboarding flow
        graph.add_edge(START, "gatherInfo")

        # After gathering info, decide whether to end or check completion
        graph.add_conditional_edges(
            "gatherInfo",
            self._after_gather_info_router,
            {"checkCompletion": "checkCompletion", "END": END},
        )

        # After checking completion, decide whether to generate a plan or end
        graph.add_conditional_edges(
            "checkCompletion",
            self._routing_function,
            {"generatePlan": "generatePlan", "END": END},
        )

        # After the plan is generated, go to the review step
        graph.add_edge("generatePlan", "plan_review")

        # After the review, the graph will be interrupted. When the user resumes,
        # their input will be processed by the router.
        graph.add_conditional_edges(
            "plan_review",
            self._after_plan_review_router,
            {
                "sessionGeneration": "sessionGeneration",
                "generatePlan": "generatePlan",
            },
        )

        # Session generation flow now correctly follows the approval step
        graph.add_edge("sessionGeneration", "coachCoordination")
        graph.add_edge("coachCoordination", "userControl")

        # Conditional routing from userControl
        graph.add_conditional_edges(
            "userControl",
            session_flow_router,
            {
                "user_control": "userControl",  # Loop back for paused state
                "session_adaptation": "sessionAdaptation",
                "complete": END,
            },
        )

        # Session adaptation can either continue or complete
        graph.add_conditional_edges(
            "sessionAdaptation",
            session_flow_router,
            {
                "user_control": "userControl",
                "session_adaptation": "sessionAdaptation",  # Could loop for continuous adaptation
                "complete": END,
            },
        )

        logger.info(
            "[OnboardingGraph] Enhanced graph with adaptive weekly planning created successfully"
        )
        return graph

    def compile(self) -> StateGraph:
        """Compile the graph with checkpointer"""
        if self._graph is None:
            graph = self.create_graph()
            self._graph = graph.compile(
                checkpointer=self.checkpointer,
                interrupt_after=[
                    "plan_review",
                    "userControl",
                ],
            )
            logger.info("[OnboardingGraph] Enhanced graph compiled successfully")

        return self._graph

    async def ainvoke(
        self, input_data: Dict[str, Any], config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Async invoke the graph"""
        graph = self.compile()
        return await graph.ainvoke(input_data, config)

    def invoke(
        self, input_data: Dict[str, Any], config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Sync invoke the graph"""
        graph = self.compile()
        return graph.invoke(input_data, config)

    async def astream(self, input_data: Dict[str, Any], config: Dict[str, Any] = None):
        """Async stream the graph execution"""
        graph = self.compile()
        async for chunk in graph.astream(input_data, config):
            yield chunk

    def stream(self, input_data: Dict[str, Any], config: Dict[str, Any] = None):
        """Sync stream the graph execution"""
        graph = self.compile()
        for chunk in graph.stream(input_data, config):
            yield chunk


def create_onboarding_graph(config=None):
    """Factory function to create an enhanced onboarding graph for LangGraph server

    Args:
        config: RunnableConfig object containing configuration

    Returns:
        StateGraph: Compiled graph ready for execution
    """
    logger.info(
        "[create_onboarding_graph] Creating new enhanced onboarding graph instance for LangGraph server"
    )

    # Create the onboarding graph instance
    onboarding_graph = OnboardingGraph(
        checkpointer=None,  # LangGraph server handles checkpointing
        enable_mem0=True,
        mem0_use_api=False,
    )

    # Return the compiled graph
    return onboarding_graph.compile()


def get_compiled_onboarding_graph(
    checkpointer=None,
    enable_mem0: bool = True,
    mem0_use_api: bool = False,
) -> StateGraph:
    """Get a compiled enhanced onboarding graph ready for use

    Args:
        checkpointer: Optional checkpointer for state persistence
        enable_mem0: Whether to enable Mem0 memory integration
        mem0_use_api: Whether to use Mem0 API (True) or local instance (False)

    Returns:
        StateGraph: Compiled graph ready for execution
    """
    logger.info(
        "[get_compiled_onboarding_graph] Getting compiled enhanced onboarding graph"
    )
    # Corrected: this function was calling create_onboarding_graph which is for the server
    # and doesn't pass the checkpointer. It should instantiate the class directly.
    onboarding_graph = OnboardingGraph(
        checkpointer=checkpointer,
        enable_mem0=enable_mem0,
        mem0_use_api=mem0_use_api,
    )
    return onboarding_graph.compile()


# For backward compatibility and easy imports
def create_initial_state(user_id: str) -> OnboardingState:
    """Create initial onboarding state for a user"""
    return create_initial_onboarding_state(user_id)
