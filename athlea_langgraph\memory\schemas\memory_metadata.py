"""
Enhanced memory metadata schemas for advanced memory features.

Defines structures for importance scoring, summary levels, and memory references.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class SummaryLevel(Enum):
    """Levels of memory summarization."""

    FULL = "full"  # Complete original content
    SUMMARIZED = "summarized"  # LLM-generated summary
    ARCHIVED = "archived"  # Minimal key information
    COMPRESSED = "compressed"  # Ultra-condensed form


class ImportanceDecayMode(Enum):
    """Modes for importance decay over time."""

    LINEAR = "linear"  # Linear decrease over time
    EXPONENTIAL = "exponential"  # Exponential decay
    STEPPED = "stepped"  # Step-wise decrease at intervals
    NONE = "none"  # No decay


@dataclass
class ImportanceScore:
    """Importance scoring for memory entries."""

    base_score: float  # Initial importance (0.0 to 1.0)
    current_score: float  # Current score after decay/boosts
    access_count: int = 0  # Number of times accessed
    last_accessed: Optional[datetime] = None
    decay_mode: ImportanceDecayMode = ImportanceDecayMode.LINEAR
    decay_rate: float = 0.01  # Decay rate per day
    boost_events: List[str] = field(
        default_factory=list
    )  # Events that boosted importance

    def calculate_time_decay(self, current_time: Optional[datetime] = None) -> float:
        """Calculate importance decay based on time since creation."""
        if current_time is None:
            current_time = datetime.now()

        if self.last_accessed is None:
            return self.base_score

        days_since_access = (current_time - self.last_accessed).days

        if self.decay_mode == ImportanceDecayMode.NONE:
            return self.base_score
        elif self.decay_mode == ImportanceDecayMode.LINEAR:
            decay = self.decay_rate * days_since_access
            return max(0.0, self.base_score - decay)
        elif self.decay_mode == ImportanceDecayMode.EXPONENTIAL:
            decay_factor = (1 - self.decay_rate) ** days_since_access
            return self.base_score * decay_factor
        elif self.decay_mode == ImportanceDecayMode.STEPPED:
            # Decrease by decay_rate every 7 days
            steps = days_since_access // 7
            decay = self.decay_rate * steps
            return max(0.0, self.base_score - decay)

        return self.base_score

    def access_boost(self, boost_factor: float = 0.1) -> None:
        """Boost importance when memory is accessed."""
        self.access_count += 1
        self.last_accessed = datetime.now()
        self.current_score = min(1.0, self.current_score + boost_factor)
        self.boost_events.append(f"access_boost_{self.access_count}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "base_score": self.base_score,
            "current_score": self.current_score,
            "access_count": self.access_count,
            "last_accessed": (
                self.last_accessed.isoformat() if self.last_accessed else None
            ),
            "decay_mode": self.decay_mode.value,
            "decay_rate": self.decay_rate,
            "boost_events": self.boost_events,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ImportanceScore":
        """Create from dictionary data."""
        return cls(
            base_score=data["base_score"],
            current_score=data["current_score"],
            access_count=data.get("access_count", 0),
            last_accessed=(
                datetime.fromisoformat(data["last_accessed"])
                if data.get("last_accessed")
                else None
            ),
            decay_mode=ImportanceDecayMode(
                data.get("decay_mode", ImportanceDecayMode.LINEAR.value)
            ),
            decay_rate=data.get("decay_rate", 0.01),
            boost_events=data.get("boost_events", []),
        )


@dataclass
class MemoryReference:
    """Reference to another memory with relationship type."""

    memory_id: str
    relationship_type: str  # "similar", "follow_up", "contradicts", "supports", etc.
    confidence: float = 1.0  # Confidence in the relationship
    created_at: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "memory_id": self.memory_id,
            "relationship_type": self.relationship_type,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryReference":
        """Create from dictionary data."""
        return cls(
            memory_id=data["memory_id"],
            relationship_type=data["relationship_type"],
            confidence=data.get("confidence", 1.0),
            created_at=datetime.fromisoformat(
                data.get("created_at", datetime.now().isoformat())
            ),
        )


@dataclass
class SummaryMetadata:
    """Metadata for summarized memories."""

    original_content_length: int
    summary_content_length: int
    summarization_method: str  # "llm", "extractive", "hybrid"
    summarization_timestamp: datetime
    summary_quality_score: Optional[float] = None  # Quality assessment if available
    key_points: List[str] = field(default_factory=list)

    @property
    def compression_ratio(self) -> float:
        """Calculate compression ratio."""
        if self.original_content_length == 0:
            return 0.0
        return self.summary_content_length / self.original_content_length

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "original_content_length": self.original_content_length,
            "summary_content_length": self.summary_content_length,
            "summarization_method": self.summarization_method,
            "summarization_timestamp": self.summarization_timestamp.isoformat(),
            "summary_quality_score": self.summary_quality_score,
            "key_points": self.key_points,
            "compression_ratio": self.compression_ratio,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SummaryMetadata":
        """Create from dictionary data."""
        return cls(
            original_content_length=data["original_content_length"],
            summary_content_length=data["summary_content_length"],
            summarization_method=data["summarization_method"],
            summarization_timestamp=datetime.fromisoformat(
                data["summarization_timestamp"]
            ),
            summary_quality_score=data.get("summary_quality_score"),
            key_points=data.get("key_points", []),
        )


@dataclass
class AdvancedMemoryMetadata:
    """Enhanced metadata for advanced memory features."""

    importance_score: ImportanceScore
    summary_level: SummaryLevel = SummaryLevel.FULL
    cross_references: List[MemoryReference] = field(default_factory=list)
    summary_metadata: Optional[SummaryMetadata] = None
    embedding_version: str = "v1"  # Track embedding model version
    last_updated: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)

    def add_reference(
        self, memory_id: str, relationship_type: str, confidence: float = 1.0
    ) -> None:
        """Add a cross-reference to another memory."""
        reference = MemoryReference(
            memory_id=memory_id,
            relationship_type=relationship_type,
            confidence=confidence,
        )
        self.cross_references.append(reference)

    def update_summary_level(
        self,
        new_level: SummaryLevel,
        summary_metadata: Optional[SummaryMetadata] = None,
    ) -> None:
        """Update the summary level and metadata."""
        self.summary_level = new_level
        if summary_metadata:
            self.summary_metadata = summary_metadata
        self.last_updated = datetime.now()

    def get_reference_ids(self, relationship_type: Optional[str] = None) -> List[str]:
        """Get memory IDs of references, optionally filtered by relationship type."""
        if relationship_type:
            return [
                ref.memory_id
                for ref in self.cross_references
                if ref.relationship_type == relationship_type
            ]
        return [ref.memory_id for ref in self.cross_references]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "importance_score": self.importance_score.to_dict(),
            "summary_level": self.summary_level.value,
            "cross_references": [ref.to_dict() for ref in self.cross_references],
            "summary_metadata": (
                self.summary_metadata.to_dict() if self.summary_metadata else None
            ),
            "embedding_version": self.embedding_version,
            "last_updated": self.last_updated.isoformat(),
            "tags": self.tags,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AdvancedMemoryMetadata":
        """Create from dictionary data."""
        return cls(
            importance_score=ImportanceScore.from_dict(data["importance_score"]),
            summary_level=SummaryLevel(
                data.get("summary_level", SummaryLevel.FULL.value)
            ),
            cross_references=[
                MemoryReference.from_dict(ref)
                for ref in data.get("cross_references", [])
            ],
            summary_metadata=(
                SummaryMetadata.from_dict(data["summary_metadata"])
                if data.get("summary_metadata")
                else None
            ),
            embedding_version=data.get("embedding_version", "v1"),
            last_updated=datetime.fromisoformat(
                data.get("last_updated", datetime.now().isoformat())
            ),
            tags=data.get("tags", []),
        )
