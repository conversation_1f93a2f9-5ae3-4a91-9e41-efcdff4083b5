#!/usr/bin/env python3
"""
Test Multi-Coach System with Aggregation

This test verifies that:
1. Multiple coaches can be selected for complex queries
2. Multiple coaches execute and provide responses
3. Aggregation node synthesizes responses
4. Aggregation streams as "Athlea" coach to frontend
"""

import asyncio
import logging
from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
    ComprehensiveCoachingConfig,
)

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_multi_coach_system():
    """Test the multi-coach system with a complex query."""
    print("🚀 TESTING MULTI-COACH SYSTEM WITH AGGREGATION")
    print("=" * 60)

    # Create configuration
    config = ComprehensiveCoachingConfig(
        user_id="test_user",
        mongodb_uri="mongodb://localhost:27017",
        thread_id="test_multi_coach",
        enable_memory=False,
        use_react_agents=True,
        max_iterations=3,
        enable_human_feedback=False,  # Disable interrupts for testing
        enable_reflexion=False,
    )

    # Create graph
    print("📊 Creating comprehensive coaching graph...")
    graph = await create_comprehensive_coaching_graph(config)

    # Test query that should trigger multiple coaches
    test_query = "I want to prevent futsal injuries - what should I do for training, nutrition, and recovery?"

    print(f"💬 Test Query: {test_query}")
    print("-" * 60)

    # Create input state
    from langchain_core.messages import HumanMessage

    input_state = {
        "messages": [HumanMessage(content=test_query)],
        "user_id": "test_user",
        "thread_id": "test_multi_coach",
    }

    # Run the graph
    print("🔄 Executing graph...")
    try:
        final_state = await graph.ainvoke(input_state)

        print("\n✅ EXECUTION COMPLETE")
        print("=" * 60)

        # Check results
        required_coaches = final_state.get("required_coaches", [])
        completed_coaches = final_state.get("completed_coaches", [])
        coach_responses = final_state.get("coach_responses", {})
        final_response = final_state.get("final_response", "")
        execution_steps = final_state.get("execution_steps", [])

        print(f"📋 Required Coaches: {required_coaches}")
        print(f"✅ Completed Coaches: {completed_coaches}")
        print(f"📝 Coach Responses Count: {len(coach_responses)}")
        print(f"🔄 Execution Steps: {execution_steps}")

        # Show individual coach responses
        if coach_responses:
            print("\n🤖 INDIVIDUAL COACH RESPONSES:")
            print("-" * 40)
            for coach, response in coach_responses.items():
                print(f"\n**{coach.replace('_', ' ').title()}:**")
                print(f"{response[:200]}{'...' if len(response) > 200 else ''}")

        # Show aggregated response
        if final_response:
            print("\n🎯 ATHLEA COACH AGGREGATED RESPONSE:")
            print("-" * 40)
            print(f"{final_response[:300]}{'...' if len(final_response) > 300 else ''}")

        # Verification
        print("\n🔍 VERIFICATION:")
        print("-" * 20)

        multi_coach_success = len(required_coaches) > 1
        coaches_executed = len(completed_coaches) > 1
        aggregation_success = bool(final_response and len(final_response) > 100)

        print(f"✅ Multi-coach Planning: {'PASS' if multi_coach_success else 'FAIL'}")
        print(f"✅ Multiple Coaches Executed: {'PASS' if coaches_executed else 'FAIL'}")
        print(f"✅ Aggregation Generated: {'PASS' if aggregation_success else 'FAIL'}")

        overall_success = (
            multi_coach_success and coaches_executed and aggregation_success
        )
        print(f"\n🎯 Overall Test: {'PASS ✅' if overall_success else 'FAIL ❌'}")

        if overall_success:
            print("\n🎉 SUCCESS: Multi-coach system working correctly!")
            print("- Multiple specialists were identified")
            print("- Multiple coaches executed successfully")
            print("- Aggregation synthesized their responses")
            print("- Result ready for frontend as 'Athlea' coach")
        else:
            print("\n❌ ISSUES DETECTED:")
            if not multi_coach_success:
                print("- Planning didn't identify multiple coaches")
            if not coaches_executed:
                print("- Multiple coaches didn't execute")
            if not aggregation_success:
                print("- Aggregation didn't generate proper response")

    except Exception as e:
        print(f"❌ ERROR during execution: {e}")
        logger.exception("Full traceback:")
        return False

    return True


async def test_streaming_integration():
    """Test that aggregation properly maps to 'Athlea' in streaming."""
    print("\n" + "=" * 60)
    print("🔄 TESTING STREAMING INTEGRATION")
    print("=" * 60)

    # Import streaming components
    from athlea_langgraph.api.streaming import ComprehensiveCoachingStreamer

    # Create a minimal graph for streaming test
    config = ComprehensiveCoachingConfig(
        user_id="test_stream",
        mongodb_uri="mongodb://localhost:27017",
        thread_id="test_stream",
        enable_memory=False,
        enable_human_feedback=False,
    )

    graph = await create_comprehensive_coaching_graph(config)
    streamer = ComprehensiveCoachingStreamer(graph)

    # Check agent mapping
    aggregation_mapping = streamer.agent_mappings.get("aggregation")
    print(f"🗺️ Aggregation maps to: '{aggregation_mapping}'")

    if aggregation_mapping == "Athlea":
        print("✅ PASS: Aggregation correctly mapped to 'Athlea' coach")
        return True
    else:
        print(
            f"❌ FAIL: Aggregation mapped to '{aggregation_mapping}', expected 'Athlea'"
        )
        return False


async def main():
    """Main test function."""
    print("🧪 MULTI-COACH AGGREGATION SYSTEM TEST")
    print("=" * 60)
    print("Testing the integration of:")
    print("1. Multi-coach planning and execution")
    print("2. Aggregation synthesis")
    print("3. Frontend streaming as 'Athlea' coach")
    print("")

    # Run tests
    test1 = await test_multi_coach_system()
    test2 = await test_streaming_integration()

    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    print(f"Multi-Coach System: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"Streaming Integration: {'✅ PASS' if test2 else '❌ FAIL'}")

    overall = test1 and test2
    print(f"\nOverall: {'🎉 ALL TESTS PASSED' if overall else '❌ SOME TESTS FAILED'}")

    if overall:
        print("\n🚀 READY FOR DEPLOYMENT!")
        print("The multi-coach aggregation system is working correctly.")
        print("Frontend will now receive both specialist AND Athlea coach responses.")

    return overall


if __name__ == "__main__":
    asyncio.run(main())
