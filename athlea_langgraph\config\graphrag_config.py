"""
GraphRAG Configuration for Athlea LangGraph Integration

Configuration for connecting to existing GraphRAG accelerator infrastructure:
- Azure Cognitive Search (ACS) for text chunks and embeddings
- Cosmos DB Gremlin Graph for structured knowledge relationships
- GraphRAG Accelerator API endpoints
"""

import os
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class GraphRAGConfig(BaseModel):
    """Configuration for GraphRAG integration."""

    # Azure Cognitive Search Configuration
    acs_service_name: str = Field(
        default_factory=lambda: os.getenv("AZURE_SEARCH_SERVICE_NAME", "")
    )
    acs_api_key: str = Field(
        default_factory=lambda: os.getenv("AZURE_SEARCH_API_KEY", "")
    )
    acs_index_name: str = Field(default="docs-chunks-index-v3")
    acs_endpoint: str = Field(
        default_factory=lambda: f"https://{os.getenv('AZURE_SEARCH_SERVICE_NAME', '')}.search.windows.net/"
    )

    # Cosmos DB Gremlin Configuration (Microsoft Standard Format)
    cosmos_account_name: str = Field(
        default_factory=lambda: os.getenv("COSMOS_GREMLIN_ENDPOINT", "")
    )
    cosmos_key: str = Field(default_factory=lambda: os.getenv("COSMOS_GREMLIN_KEY", ""))
    cosmos_database: str = Field(
        default_factory=lambda: os.getenv("COSMOS_GREMLIN_DATABASE", "graphragdb")
    )
    cosmos_graph: str = Field(
        default_factory=lambda: os.getenv("COSMOS_GREMLIN_GRAPH", "knowledge-graph")
    )

    # Computed properties following Microsoft's documentation pattern
    @property
    def cosmos_endpoint(self) -> str:
        """Construct the Cosmos DB Gremlin WebSocket endpoint."""
        return f"wss://{self.cosmos_account_name}.gremlin.cosmos.azure.com:443/"

    @property
    def cosmos_username(self) -> str:
        """Construct the Cosmos DB username in the required format."""
        return f"/dbs/{self.cosmos_database}/colls/{self.cosmos_graph}"

    cosmos_partition_key: str = Field(default="/type")
    cosmos_use_ssl: bool = Field(default=True)
    cosmos_connection_timeout_ms: int = Field(default=5000)
    cosmos_request_timeout_ms: int = Field(default=10000)
    cosmos_max_connections: int = Field(default=10)

    # GraphRAG Accelerator API Configuration
    graphrag_api_base_url: Optional[str] = Field(
        default_factory=lambda: os.getenv("GRAPHRAG_API_BASE_URL")
    )
    graphrag_api_key: Optional[str] = Field(
        default_factory=lambda: os.getenv("GRAPHRAG_API_KEY")
    )

    # Query Configuration
    default_top_k: int = Field(default=10)
    default_timeout: int = Field(default=30)
    max_retries: int = Field(default=3)

    # Document Configuration
    test_document_id: str = Field(
        default="a8eb82eb-829e-5eb1-b0b9-65dbad8fa897"
    )  # Futsal study
    test_document_doi: str = Field(default="10.3390/healthcare12141387")

    model_config = ConfigDict(env_file=".env", env_file_encoding="utf-8")


# Global configuration instance
graphrag_config = GraphRAGConfig()


def get_graphrag_config() -> GraphRAGConfig:
    """Get the GraphRAG configuration instance."""
    return graphrag_config


def validate_graphrag_config() -> bool:
    """Validate that required GraphRAG configuration is present."""
    config = get_graphrag_config()

    required_fields = [
        (config.acs_service_name, "Azure Search service name"),
        (config.acs_api_key, "Azure Search API key"),
        (config.cosmos_account_name, "Cosmos DB account name"),
        (config.cosmos_key, "Cosmos DB key"),
    ]

    missing_fields = []
    for field_value, field_name in required_fields:
        if not field_value:
            missing_fields.append(field_name)

    if missing_fields:
        raise ValueError(
            f"Missing required GraphRAG configuration: {', '.join(missing_fields)}"
        )

    return True
