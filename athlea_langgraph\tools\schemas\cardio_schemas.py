"""
Cardio Training Domain Schemas

Pydantic schemas for strict input/output validation of cardio training tools.
These schemas enforce tool-agent contracts and automatic validation.
"""

from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class RouteType(str, Enum):
    """Route configuration types."""

    loop = "loop"
    out_and_back = "out_and_back"
    point_to_point = "point_to_point"


class TerrainType(str, Enum):
    """Terrain preference types."""

    flat = "flat"
    hilly = "hilly"
    mixed = "mixed"
    trails = "trails"
    roads = "roads"


class SafetyLevel(str, Enum):
    """Safety priority levels."""

    low = "low"
    medium = "medium"
    high = "high"


class ExperienceLevel(str, Enum):
    """Training experience levels."""

    beginner = "beginner"
    intermediate = "intermediate"
    advanced = "advanced"
    elite = "elite"


class WorkoutType(str, Enum):
    """Workout types for analysis."""

    easy = "easy"
    tempo = "tempo"
    interval = "interval"
    long_run = "long_run"
    race = "race"


class Sport(str, Enum):
    """Endurance sports."""

    running = "running"
    cycling = "cycling"
    swimming = "swimming"
    triathlon = "triathlon"


# Input Schemas
class RouteGenerationInput(BaseModel):
    """Input schema for route generation."""

    location: str = Field(
        ..., description="Starting location (address, city, or coordinates)"
    )
    distance_km: float = Field(
        ..., ge=1, le=100, description="Desired distance in kilometers"
    )
    route_type: RouteType = Field(
        RouteType.loop, description="Type of route configuration"
    )
    terrain_preference: TerrainType = Field(
        TerrainType.mixed, description="Preferred terrain type"
    )
    safety_priority: SafetyLevel = Field(
        SafetyLevel.medium, description="Safety considerations"
    )
    scenic_preference: bool = Field(
        False, description="Prefer scenic routes over efficiency"
    )
    avoid_traffic: bool = Field(True, description="Avoid high-traffic roads")


class PaceCalculationInput(BaseModel):
    """Input schema for pace zone calculations."""

    reference_performance: Dict[str, Any] = Field(
        ..., description="Recent race or time trial performance"
    )
    training_zones: List[str] = Field(
        ["easy", "tempo", "threshold", "vo2max"], description="Zones to calculate"
    )
    experience_level: ExperienceLevel = Field(
        ExperienceLevel.intermediate, description="Running experience level"
    )
    age: Optional[int] = Field(
        None, ge=13, le=100, description="Age for heart rate calculations"
    )


class HeartRateAnalysisInput(BaseModel):
    """Input schema for heart rate data analysis."""

    hr_data: List[int] = Field(
        ..., description="Heart rate data points (bpm) from workout"
    )
    workout_duration_minutes: int = Field(
        ..., ge=5, le=480, description="Total workout duration"
    )
    max_hr: int = Field(..., ge=120, le=220, description="User's maximum heart rate")
    resting_hr: int = Field(..., ge=35, le=100, description="User's resting heart rate")
    workout_type: WorkoutType = Field(..., description="Type of workout performed")
    perceived_exertion: Optional[int] = Field(
        None, ge=1, le=10, description="RPE scale 1-10"
    )


class EnduranceTrainingInput(BaseModel):
    """Input schema for endurance training plan generation."""

    sport: Sport = Field(..., description="Primary endurance sport")
    goal_event: Dict[str, Any] = Field(..., description="Target event details")
    current_fitness: Dict[str, Any] = Field(..., description="Current fitness level")
    training_preferences: Optional[Dict[str, Any]] = Field(
        None, description="Training preferences"
    )
    constraints: Optional[Dict[str, Any]] = Field(
        None, description="Training constraints"
    )


class CalculatePaceZonesSchema(BaseModel):
    """Schema for pace zone calculation tool."""

    recent_run_pace_min_km: float = Field(
        ..., gt=0, description="Recent run pace in minutes per kilometer"
    )
    heart_rate_max: int = Field(
        ..., ge=120, le=220, description="Maximum heart rate in BPM"
    )
    age: Optional[int] = Field(
        None, ge=13, le=100, description="Age for zone calculations"
    )
    fitness_level: Optional[str] = Field(
        "intermediate", description="Fitness level (beginner, intermediate, advanced)"
    )


class CalculateVo2MaxEstimateSchema(BaseModel):
    """Schema for VO2 max estimation tool."""

    distance_meters: int = Field(..., gt=0, description="Distance covered in meters")
    duration_minutes: float = Field(..., gt=0, description="Duration in minutes")
    age: int = Field(..., ge=13, le=100, description="Age of the athlete")
    gender: Optional[str] = Field(None, description="Gender (male, female, other)")


class GenerateRunningRouteSchema(BaseModel):
    """Schema for running route generation tool."""

    start_location: str = Field(..., description="Starting location for the route")
    distance_km: float = Field(
        ..., gt=0, le=100, description="Desired route distance in kilometers"
    )
    route_preference: Optional[str] = Field(
        "mixed", description="Route preference (scenic, flat, hilly, mixed)"
    )
    surface_type: Optional[str] = Field(
        "mixed", description="Preferred surface type (road, trail, track, mixed)"
    )


class PlanIntervalWorkoutSchema(BaseModel):
    """Schema for interval workout planning tool."""

    goal: str = Field(
        ..., description="Workout goal (speed, endurance, vo2max, lactate_threshold)"
    )
    total_duration_minutes: int = Field(
        ..., ge=15, le=180, description="Total workout duration in minutes"
    )
    intensity_level: str = Field(
        ..., description="Intensity level (low, medium, high, very_high)"
    )
    current_fitness_level: Optional[str] = Field(
        "intermediate",
        description="Current fitness level (beginner, intermediate, advanced)",
    )
