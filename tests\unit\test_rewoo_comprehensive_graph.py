#!/usr/bin/env python3
"""
Test script for the new ReWOO+Reflection Comprehensive Coaching Graph
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from athlea_langgraph.graphs.archived.comprehensive_coaching_graph import (
    create_comprehensive_coaching_graph,
    ComprehensiveCoachingConfig,
    ComprehensiveState,
)


async def test_simple_query():
    """Test the simple path routing."""
    print("🧪 Testing Simple Query Path...")

    config = ComprehensiveCoachingConfig(
        user_id="test_user",
        enable_memory=False,
        use_react_agents=True,
        max_iterations=2,
        enable_human_feedback=False,
        enable_reflexion=True,
        max_reflexion_iterations=1,
        complexity_threshold=0.6,
    )

    try:
        graph = await create_comprehensive_coaching_graph(config)

        # Test with a simple query
        initial_state = ComprehensiveState(
            user_query="How do I do a proper squat?",
            messages=[],
            user_profile="Beginner fitness enthusiast",
        )

        print(f"📝 Query: {initial_state.user_query}")
        print(f"👤 User Profile: {initial_state.user_profile}")

        # Run the graph
        result = await graph.ainvoke(initial_state)

        print(f"✅ Execution Path: {result.get('execution_path', 'Unknown')}")
        print(f"📊 Complexity Score: {result.get('complexity_score', 'Unknown')}")
        print(f"🔄 Execution Steps: {result.get('execution_steps', [])}")
        print(f"🎯 Final Node: {result.get('current_node', 'Unknown')}")
        print(f"🔍 Reflection Score: {result.get('reflection_score', 'N/A')}")
        print(f"💭 Needs Improvement: {result.get('needs_improvement', 'N/A')}")

        return True

    except Exception as e:
        print(f"❌ Error in simple query test: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_complex_query():
    """Test the complex path routing with ReWOO."""
    print("\n🧪 Testing Complex Query Path...")

    config = ComprehensiveCoachingConfig(
        user_id="test_user",
        enable_memory=False,
        use_react_agents=True,
        max_iterations=2,
        enable_human_feedback=False,
        enable_reflexion=True,
        max_reflexion_iterations=1,
        complexity_threshold=0.6,
    )

    try:
        graph = await create_comprehensive_coaching_graph(config)

        # Test with a complex query
        initial_state = ComprehensiveState(
            user_query="Design a comprehensive 12-week strength training program for powerlifting competition prep with periodization and injury prevention strategies",
            messages=[],
            user_profile="Intermediate powerlifter, 2 years experience, preparing for first competition",
        )

        print(f"📝 Query: {initial_state.user_query}")
        print(f"👤 User Profile: {initial_state.user_profile}")

        # Run the graph
        result = await graph.ainvoke(initial_state)

        print(f"✅ Execution Path: {result.get('execution_path', 'Unknown')}")
        print(f"📊 Complexity Score: {result.get('complexity_score', 'Unknown')}")
        print(f"🔄 Execution Steps: {result.get('execution_steps', [])}")
        print(f"🎯 Final Node: {result.get('current_node', 'Unknown')}")
        print(
            f"📋 ReWOO Plan: {result.get('rewoo_plan', 'N/A')[:200]}..."
            if result.get("rewoo_plan")
            else "📋 ReWOO Plan: N/A"
        )
        print(f"🔍 Reflection Score: {result.get('reflection_score', 'N/A')}")
        print(f"💭 Needs Improvement: {result.get('needs_improvement', 'N/A')}")
        print(f"🔁 Reflexion Count: {result.get('reflexion_count', 0)}")

        return True

    except Exception as e:
        print(f"❌ Error in complex query test: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_graph_structure():
    """Test that the graph structure is correct."""
    print("\n🧪 Testing Graph Structure...")

    config = ComprehensiveCoachingConfig(
        enable_memory=False,
        use_react_agents=True,
    )

    try:
        graph = await create_comprehensive_coaching_graph(config)

        print(f"📊 Total Nodes: {len(graph.nodes)}")
        print(f"🔗 Node Names: {list(graph.nodes.keys())}")

        # Check for required nodes
        required_nodes = [
            "reasoning",
            "planning",
            "complexity_assessment",
            "simple_router",
            "complex_planner",
            "complex_executor",
            "complex_router",
            "aggregation",
            "reflection",
            "strength_coach",
            "cardio_coach",
            "nutrition_coach",
            "recovery_coach",
            "mental_coach",
            "cycling_coach",
        ]

        missing_nodes = [node for node in required_nodes if node not in graph.nodes]

        if missing_nodes:
            print(f"❌ Missing nodes: {missing_nodes}")
            return False
        else:
            print("✅ All required nodes present")
            return True

    except Exception as e:
        print(f"❌ Error in graph structure test: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting ReWOO+Reflection Comprehensive Graph Tests")
    print("=" * 60)

    # Set up environment
    os.environ.setdefault("OPENAI_API_KEY", "test-key")

    tests = [
        ("Graph Structure", test_graph_structure()),
        ("Simple Query", test_simple_query()),
        ("Complex Query", test_complex_query()),
    ]

    results = []
    for test_name, test_coro in tests:
        print(f"\n🔍 Running {test_name} Test...")
        try:
            result = await test_coro
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} Test PASSED")
            else:
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            print(f"💥 {test_name} Test CRASHED: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} {status}")

    print(f"\n🎯 Results: {passed}/{total} tests passed")

    if passed == total:
        print(
            "🎉 All tests passed! ReWOO+Reflection implementation is working correctly."
        )
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
