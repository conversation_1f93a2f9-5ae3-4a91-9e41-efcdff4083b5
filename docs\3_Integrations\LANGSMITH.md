# LangSmith Integration

This document explains how the SpecializedCoachGraph framework is integrated with LangSmith for comprehensive monitoring, debugging, and analytics.

## Overview

All specialized coach graphs are available in LangGraph Studio and are fully integrated with LangSmith for end-to-end tracing. This provides deep visibility into the agent's execution, from complexity analysis to final response generation.

### Available Traced Graphs

The following specialized coach graphs automatically send traces to LangSmith:

-   `specialized_strength_coach`
-   `specialized_nutrition_coach`
-   `specialized_cardio_coach`
-   `specialized_recovery_coach`
-   `specialized_mental_coach`
-   `specialized_cycling_coach`

### Configuration

To enable tracing, ensure these environment variables are set:

```bash
# Required for LangSmith
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key

# Optional: To associate traces with a project
LANGCHAIN_PROJECT=your_project_name
```

The graphs also accept a runtime parameter to toggle tracing:
```json
{
  "enable_langsmith_tracing": true
}
```

## What is Traced?

The LangSmith integration automatically captures a rich set of data for each graph execution, providing a detailed view of the agent's reasoning process.

### 1. Complexity Assessment

-   **LLM-based Complexity Score**: The initial score assigned to the user's query.
-   **Routing Decision**: The path chosen based on the score (e.g., `simple` vs. `complex`).
-   **Reasoning**: The rationale behind the routing decision.

### 2. Execution Paths

-   **Simple Path**: Traces for direct execution, including timing and results.
-   **ReWOO+Reflection Path**: Detailed traces for complex queries, including:
    -   Parallel execution of multiple domain-expert "workers".
    -   The success or failure of each worker.
    -   Dependencies between workers.
-   **Reflection Improvements**: Traces showing how the reflection step refined or improved the initial response.

### 3. Performance and Quality Metrics

-   **Total Execution Time**: End-to-end latency for the request.
-   **LLM Calls**: The number of LLM calls, token usage, and cost.
-   **Response Quality Scores**: Any internal scores generated for safety, accuracy, or actionability.

## Monitoring in LangSmith

By monitoring these traces, you can gain deep insights into the performance and behavior of your agents.

### Key Metrics to Monitor

1.  **Complexity Assessment Accuracy**: Are queries being routed correctly? Compare the initial complexity score against the actual execution path taken. A high rate of mis-routing may indicate that the assessment prompt or threshold needs tuning.
2.  **ReWOO Worker Performance**: For complex queries, are all workers succeeding? Monitor the success rate and execution time of each worker to identify bottlenecks or persistent failures.
3.  **Response Quality**: How often is the reflection step triggered? Track the frequency of reflection improvements to gauge the quality of the initial responses.
4.  **Execution Efficiency**: What is the average time-to-response for simple vs. complex queries? Monitor the latency distribution to ensure the system is meeting performance targets (e.g., simple queries ≤ 5s, complex queries ≤ 60s).

### Example LangSmith Queries

You can use LangSmith's query capabilities to build dashboards and alerts.

**Query Complexity Distribution:**
```sql
SELECT 
  tags, -- or another field identifying the coach
  run_type,
  COUNT(*) as query_count,
  AVG(latency) as avg_latency_ms
FROM traces 
WHERE name LIKE 'specialized_%_coach'
GROUP BY tags, run_type
```

**ReWOO Worker Success Rate:**
```sql
SELECT 
  name as worker_name,
  AVG(CASE WHEN error IS NULL THEN 1.0 ELSE 0.0 END) as success_rate,
  AVG(latency) as avg_latency_ms
FROM traces 
WHERE parent_run_id IN (
    SELECT run_id FROM traces WHERE name = 'ReWOOWorker'
)
GROUP BY worker_name
```

## Debugging with LangSmith

LangSmith is an invaluable tool for debugging agent behavior.

-   **Investigating Mis-routes**: If a simple query is routed down a complex path, you can inspect the "Complexity Assessment" trace to see the LLM's reasoning and score. This can help you tune the `complexity_threshold_simple` parameter.
-   **Diagnosing Worker Failures**: If a ReWOO worker fails, you can examine its specific trace to see the inputs, prompt, and any errors that occurred, helping you debug the worker's prompt or underlying tool.
-   **Analyzing Poor Responses**: If a response quality is low, you can compare the initial response with the output from the reflection step to understand how it was improved and refine the agent's prompts accordingly.

This deep integration with LangSmith provides the observability needed to confidently develop, deploy, and continuously improve the specialized coaching agents. 