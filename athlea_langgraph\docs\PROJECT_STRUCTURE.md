# 📁 Athlea LangGraph Project Structure

## 🎯 **Overview**

This document describes the organized structure of the `python-langgraph` project following Python best practices for file organization.

## 📂 **Project Structure**

```
python-langgraph/
├── 🏠 ROOT (Core Files)
│   ├── start_backend.py              # Main backend server
│   ├── .env                          # Environment variables
│   ├── .env.local                    # Local environment overrides
│   ├── .gitignore                    # Git ignore patterns
│   ├── README.md                     # Project documentation
│   ├── requirements.txt              # Python dependencies
│   ├── requirements-dev.txt          # Development dependencies
│   ├── requirements-mcp.txt          # MCP dependencies
│   ├── pyproject.toml                # Project configuration
│   ├── poetry.lock                   # Poetry lock file
│   ├── pytest.ini                    # Test configuration
│   └── setup.py                      # Package setup
│
├── 🤖 athlea_langgraph/              # Core LangGraph Implementation
│   ├── __init__.py
│   ├── agents/                       # LangGraph agents
│   ├── graphs/                       # Workflow graphs
│   ├── tools/                        # Custom tools
│   ├── services/                     # Service implementations
│   ├── config/                       # Configuration
│   └── prompts/                      # Prompt templates
│
├── 🔗 integration/                   # External Integrations
│   ├── __init__.py
│   └── n8n/                          # n8n Integration
│       ├── __init__.py
│       ├── README.md
│       └── n8n_langgraph_integration.py  # FastAPI wrapper for n8n
│
├── 🌊 workflows/                     # Workflow Templates
│   └── n8n/                          # n8n Workflow Templates
│       ├── README.md
│       ├── n8n_coaching_workflow_template.json
│       └── n8n_production_workflow.json
│
├── 🧪 tests/                         # Test Suite
│   ├── __init__.py
│   └── n8n_integration/              # n8n Integration Tests
│       ├── __init__.py
│       ├── README.md
│       ├── test_langgraph_n8n_integration.py
│       ├── test_n8n_cloud_webhook.py
│       ├── test_complete_integration.py
│       └── test_n8n_simple.py
│
├── 📚 docs/                          # Documentation
│   └── integration/                  # Integration Documentation
│       ├── README.md
│       ├── LANGGRAPH_N8N_INTEGRATION_GUIDE.md
│       └── PRODUCTION_SETUP_GUIDE.md
│
├── 🛠️ scripts/                       # Utility Scripts
│   ├── n8n/                          # n8n-specific scripts
│   └── organize_project_files.py     # File organization script
│
└── ⚙️ config/                        # Configuration Files
    └── n8n/                          # n8n-specific configuration
```

## 🎯 **Directory Purposes**

### **Root Directory** (`/`)
- **Purpose**: Core project files and configuration
- **Key Files**: 
  - `start_backend.py` - Main application entry point (kept in root as requested)
  - `.env` - Environment variables and secrets
  - `requirements.txt` - Production dependencies

### **Integration Directory** (`/integration/`)
- **Purpose**: External system integrations
- **Current**: n8n Cloud integration with FastAPI wrapper
- **Future**: Slack, Discord, Zapier integrations

### **Workflows Directory** (`/workflows/`)
- **Purpose**: Workflow templates and configurations
- **Current**: n8n workflow JSON templates
- **Future**: GitHub Actions, Jenkins pipelines

### **Tests Directory** (`/tests/`)
- **Purpose**: Comprehensive test suite
- **Structure**: Organized by integration type
- **Coverage**: Unit tests, integration tests, end-to-end tests

### **Docs Directory** (`/docs/`)
- **Purpose**: Project documentation
- **Structure**: Organized by topic/integration
- **Content**: Setup guides, API documentation, troubleshooting

### **Scripts Directory** (`/scripts/`)
- **Purpose**: Utility and automation scripts
- **Content**: Deployment scripts, file organization, maintenance tools

## 🚀 **Quick Start Commands**

### **Run the Main Backend**
```bash
# Backend stays in root as requested
python start_backend.py
```

### **Run n8n Integration Server**
```bash
# Now properly organized
python integration/n8n/n8n_langgraph_integration.py
```

### **Run Integration Tests**
```bash
# Complete test suite
python tests/n8n_integration/test_complete_integration.py

# Specific webhook tests
python tests/n8n_integration/test_n8n_cloud_webhook.py
```

### **Import n8n Workflows**
```bash
# Production workflow
workflows/n8n/n8n_production_workflow.json

# Template workflow
workflows/n8n/n8n_coaching_workflow_template.json
```

## 📋 **File Organization Benefits**

### **✅ Clear Separation of Concerns**
- Core LangGraph code in `athlea_langgraph/`
- External integrations in `integration/`
- Tests organized by functionality
- Documentation by topic

### **✅ Scalable Structure**
- Easy to add new integrations (`integration/slack/`, `integration/discord/`)
- Clear test organization (`tests/slack_integration/`)
- Modular documentation (`docs/slack/`, `docs/api/`)

### **✅ Python Best Practices**
- Proper `__init__.py` files for packages
- Clear module imports
- Organized dependencies
- Proper package structure

### **✅ Developer Experience**
- README files in each directory
- Clear file naming conventions
- Logical grouping of related files
- Easy navigation and discovery

## 🔄 **Migration Notes**

### **Import Path Updates**
If you have existing code that imports the moved files, update paths:
```python
# OLD
from n8n_langgraph_integration import app

# NEW
from integration.n8n.n8n_langgraph_integration import app
```

### **Relative Paths**
Test files now use relative paths from their new locations:
```python
# Tests can import from project root
import sys
sys.path.append('../../')
from athlea_langgraph.services import coaching_service
```

## 🛡️ **Security & Configuration**

### **Environment Files**
- `.env` - Production environment variables
- `.env.local` - Local development overrides
- Both files remain in project root for easy access

### **Sensitive Data**
- n8n API keys in `.env`
- Webhook URLs documented but not hardcoded
- Configuration separated from implementation

## 🎉 **Next Steps**

1. **✅ Files Organized** - All files moved to proper directories
2. **✅ README Created** - Documentation in each directory
3. **✅ Tests Updated** - All test paths verified
4. **🚀 Ready to Use** - Run tests and integration

Your project is now properly organized and ready for production deployment! 