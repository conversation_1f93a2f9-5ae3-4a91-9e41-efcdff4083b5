"""
Research Validation Service

Prevents hallucination of fake studies by detecting research claims in coach responses
and automatically triggering knowledge graph queries for verification.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

logger = logging.getLogger(__name__)

@dataclass
class ResearchClaim:
    """Represents a detected research claim in a response."""
    claim_text: str
    confidence: float
    claim_type: str  # 'study', 'statistics', 'citation', 'recent_research'
    start_pos: int
    end_pos: int

@dataclass
class ValidationResult:
    """Result of research validation."""
    has_research_claims: bool
    claims: List[ResearchClaim]
    should_query_knowledge_graph: bool
    suggested_query: Optional[str] = None
    risk_level: str = "low"  # low, medium, high

class ResearchValidationService:
    """
    Service to detect and validate research claims before they're sent to users.
    
    This prevents coaches from hallucinating fake studies by:
    1. Detecting research claims in responses
    2. Triggering knowledge graph queries for validation
    3. Flagging potentially hallucinated content
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Patterns that indicate research claims
        self.research_patterns = [
            # Study citations
            r"(?:study|research|investigation|trial|analysis)\s+(?:by|from|in|published|shows|found|indicates|demonstrates)",
            r"(?:recent|latest|new|2024|2023|2022)\s+(?:study|research|findings|evidence)",
            r"(?:journal|paper|publication|article)\s+(?:published|in|from)",
            
            # Statistical claims
            r"\d+%\s+(?:of|increase|decrease|improvement|reduction|more|less)",
            r"(?:significantly|statistically)\s+(?:higher|lower|better|worse|improved)",
            r"(?:correlation|association|link|relationship)\s+(?:between|with)",
            
            # Citation formats
            r"\([A-Z][a-z]+\s+et\s+al\.?,?\s+\d{4}\)",  # (Smith et al., 2023)
            r"\([A-Z][a-z]+\s+&\s+[A-Z][a-z]+,?\s+\d{4}\)",  # (Smith & Jones, 2023)
            r"(?:https?://|www\.)",  # URLs
            r"(?:doi:|DOI:)\s*[\d\.\/]+",  # DOI references
            
            # Authority claims
            r"(?:Harvard|Stanford|Mayo Clinic|American|International)\s+(?:study|research|university|college)",
            r"(?:peer.reviewed|peer reviewed|meta.analysis|systematic review)",
            r"(?:randomized|controlled|clinical)\s+trial",
            
            # Specific research language
            r"(?:according to|research shows|studies indicate|evidence suggests|data shows|research suggests)",
            r"(?:proven|demonstrated|established|confirmed)\s+(?:by|through)\s+(?:research|studies)",
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.research_patterns]
        
        # High-risk phrases that almost always indicate hallucination
        self.high_risk_patterns = [
            r"(?:click here|read more|full study|download|access)",  # Fake links
            r"(?:study #\d+|reference \d+|see appendix)",  # Fake reference numbers
            r"(?:as seen in|detailed in|outlined in)\s+(?:table|figure|chart)\s+\d+",  # Fake figures
        ]
        
        self.high_risk_compiled = [re.compile(pattern, re.IGNORECASE) for pattern in self.high_risk_patterns]

    def detect_research_claims(self, response_text: str) -> List[ResearchClaim]:
        """Detect research claims in a response text."""
        claims = []
        
        # Check standard research patterns
        for pattern in self.compiled_patterns:
            for match in pattern.finditer(response_text):
                claim = ResearchClaim(
                    claim_text=match.group(),
                    confidence=0.7,
                    claim_type="research_reference",
                    start_pos=match.start(),
                    end_pos=match.end()
                )
                claims.append(claim)
        
        # Check high-risk patterns (likely hallucination)
        for pattern in self.high_risk_compiled:
            for match in pattern.finditer(response_text):
                claim = ResearchClaim(
                    claim_text=match.group(),
                    confidence=0.95,
                    claim_type="high_risk_hallucination",
                    start_pos=match.start(),
                    end_pos=match.end()
                )
                claims.append(claim)
        
        return claims

    def validate_response(self, response_text: str, user_query: str) -> ValidationResult:
        """
        Validate a coach response for research claims.
        
        Args:
            response_text: The coach's response to validate
            user_query: The original user query
            
        Returns:
            ValidationResult with validation details
        """
        claims = self.detect_research_claims(response_text)
        
        if not claims:
            return ValidationResult(
                has_research_claims=False,
                claims=[],
                should_query_knowledge_graph=False,
                risk_level="low"
            )
        
        # Determine risk level
        high_risk_claims = [c for c in claims if c.claim_type == "high_risk_hallucination"]
        
        # Check for fake citation patterns (high risk)
        citation_patterns = [r"\([A-Z][a-z]+\s+et\s+al\.?,?\s+\d{4}\)", r"(?:https?://|www\.)", r"(?:doi:|DOI:)\s*[\d\.\/]+"]
        has_citations = any(re.search(pattern, response_text, re.IGNORECASE) for pattern in citation_patterns)
        
        # Authority claims without verification are also high risk
        authority_patterns = [r"(?:Harvard|Stanford|Mayo Clinic|American|International)\s+(?:study|research)"]
        has_authority_claims = any(re.search(pattern, response_text, re.IGNORECASE) for pattern in authority_patterns)
        
        if high_risk_claims or has_citations or has_authority_claims:
            risk_level = "high"
        elif len(claims) > 3:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        # Generate suggested knowledge graph query
        suggested_query = self._generate_knowledge_graph_query(user_query, claims)
        
        return ValidationResult(
            has_research_claims=True,
            claims=claims,
            should_query_knowledge_graph=True,
            suggested_query=suggested_query,
            risk_level=risk_level
        )

    def _generate_knowledge_graph_query(self, user_query: str, claims: List[ResearchClaim]) -> str:
        """Generate a knowledge graph query based on detected claims and user query."""
        
        # Extract key topics from user query
        query_lower = user_query.lower()
        
        # Map common terms to knowledge graph entities
        topic_mapping = {
            "nutrition": ["nutrition", "diet", "supplements", "protein", "carbohydrates"],
            "strength": ["strength training", "resistance training", "muscle building", "hypertrophy"],
            "cardio": ["cardiovascular", "aerobic", "endurance", "running", "cycling"],
            "recovery": ["recovery", "sleep", "rest", "regeneration", "inflammation"],
            "injury": ["injury prevention", "rehabilitation", "biomechanics", "movement"],
            "performance": ["athletic performance", "sports science", "training adaptations"]
        }
        
        # Find matching topics
        relevant_topics = []
        for topic, keywords in topic_mapping.items():
            if any(keyword in query_lower for keyword in keywords):
                relevant_topics.append(topic)
        
        # Generate query based on topics and claims
        if relevant_topics:
            primary_topic = relevant_topics[0]
            return f"Research evidence on {primary_topic} related to: {user_query[:100]}"
        else:
            return f"Scientific evidence for: {user_query[:100]}"

    def should_trigger_knowledge_graph(self, validation_result: ValidationResult) -> bool:
        """Determine if knowledge graph should be triggered based on validation."""
        return (
            validation_result.has_research_claims and
            (validation_result.risk_level in ["medium", "high"] or len(validation_result.claims) >= 2)
        )

# Global service instance
_research_validation_service = None

def get_research_validation_service() -> ResearchValidationService:
    """Get the global research validation service instance."""
    global _research_validation_service
    if _research_validation_service is None:
        _research_validation_service = ResearchValidationService()
    return _research_validation_service


def validate_coach_response(response_text: str, user_query: str) -> ValidationResult:
    """
    Convenience function to validate a coach response.
    
    Usage:
        result = validate_coach_response(coach_response, user_query)
        if result.should_query_knowledge_graph:
            # Trigger knowledge graph query
            knowledge_result = await query_knowledge_graph(result.suggested_query)
    """
    service = get_research_validation_service()
    return service.validate_response(response_text, user_query) 