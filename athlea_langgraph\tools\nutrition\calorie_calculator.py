"""
Nutrition Calorie Calculator Tool

Provides macro calculation, caloric needs assessment, and nutritional target setting
for various fitness goals and user profiles.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool, tool
from pydantic import BaseModel, Field

from ..base_tool import BaseDomainTool
from ..schemas.nutrition_schemas import (
    CalorieCalculationInput,
    CalorieCalculationOutput,
    MacroTargetsInput,
    MacroTargetsOutput,
)

logger = logging.getLogger(__name__)


class NutritionCalorieCalculator(BaseDomainTool):
    """
    Calorie and macro calculator tool for nutrition domain.

    Calculates daily caloric needs, macro targets, and nutritional requirements
    based on user profile, goals, and activity level.
    """

    domain: str = "nutrition"
    name: str = "nutrition_calorie_calculator"
    description: str = (
        "Calculate daily caloric needs and macro targets for nutrition planning"
    )

    def __init__(self):
        super().__init__()
        self._bmr_formulas = {
            "mifflin_st_jeor": self._calculate_mifflin_st_jeor,
            "harris_benedict": self._calculate_harris_benedict,
            "katch_mcardle": self._calculate_katch_mcardle,
        }
        self._activity_multipliers = {
            "sedentary": 1.2,
            "lightly_active": 1.375,
            "moderately_active": 1.55,
            "very_active": 1.725,
            "extremely_active": 1.9,
        }

    async def calculate_daily_calories(
        self, calc_input: CalorieCalculationInput
    ) -> CalorieCalculationOutput:
        """Calculate daily caloric needs based on user profile."""
        try:
            # Extract user data
            age = calc_input.age
            gender = calc_input.gender
            weight_kg = calc_input.weight_kg
            height_cm = calc_input.height_cm
            activity_level = calc_input.activity_level
            body_fat_percentage = calc_input.body_fat_percentage
            goal = calc_input.goal

            # Calculate BMR using most appropriate formula
            if body_fat_percentage:
                bmr = self._calculate_katch_mcardle(weight_kg, body_fat_percentage)
                formula_used = "katch_mcardle"
            else:
                bmr = self._calculate_mifflin_st_jeor(age, gender, weight_kg, height_cm)
                formula_used = "mifflin_st_jeor"

            # Calculate TDEE (Total Daily Energy Expenditure)
            activity_multiplier = self._activity_multipliers.get(activity_level, 1.55)
            tdee = bmr * activity_multiplier

            # Adjust for goal
            goal_adjusted_calories = self._adjust_calories_for_goal(tdee, goal)

            return CalorieCalculationOutput(
                bmr=round(bmr),
                tdee=round(tdee),
                target_calories=round(goal_adjusted_calories),
                formula_used=formula_used,
                activity_multiplier=activity_multiplier,
                goal_adjustment=goal_adjusted_calories - tdee,
                recommendations=self._generate_calorie_recommendations(
                    goal, goal_adjusted_calories
                ),
            )

        except Exception as e:
            logger.error(f"Calorie calculation failed: {e}")
            return CalorieCalculationOutput(
                bmr=0,
                tdee=0,
                target_calories=0,
                formula_used="unknown",
                activity_multiplier=1.0,
                goal_adjustment=0,
                recommendations=[],
                error=str(e),
            )

    async def calculate_macro_targets(
        self, macro_input: MacroTargetsInput
    ) -> MacroTargetsOutput:
        """Calculate macro targets based on calories and goals."""
        try:
            target_calories = macro_input.target_calories
            goal = macro_input.goal
            dietary_preferences = macro_input.dietary_preferences or []
            protein_preference = macro_input.protein_preference

            # Get macro distribution for goal
            macro_percentages = self._get_macro_distribution(goal, dietary_preferences)

            # Calculate macro targets
            protein_calories = target_calories * macro_percentages["protein"]
            carb_calories = target_calories * macro_percentages["carbs"]
            fat_calories = target_calories * macro_percentages["fat"]

            # Convert to grams (protein: 4 cal/g, carbs: 4 cal/g, fat: 9 cal/g)
            protein_grams = protein_calories / 4
            carb_grams = carb_calories / 4
            fat_grams = fat_calories / 9

            # Adjust protein based on preference
            if protein_preference:
                protein_grams = self._adjust_protein_for_preference(
                    protein_grams, protein_preference, target_calories
                )

            return MacroTargetsOutput(
                target_calories=target_calories,
                protein_grams=round(protein_grams, 1),
                carb_grams=round(carb_grams, 1),
                fat_grams=round(fat_grams, 1),
                protein_percentage=round(
                    (protein_grams * 4 / target_calories) * 100, 1
                ),
                carb_percentage=round((carb_grams * 4 / target_calories) * 100, 1),
                fat_percentage=round((fat_grams * 9 / target_calories) * 100, 1),
                distribution_rationale=self._explain_macro_distribution(
                    goal, macro_percentages
                ),
                meal_distribution=self._suggest_meal_distribution(
                    protein_grams, carb_grams, fat_grams
                ),
            )

        except Exception as e:
            logger.error(f"Macro calculation failed: {e}")
            return MacroTargetsOutput(
                target_calories=macro_input.target_calories,
                protein_grams=0,
                carb_grams=0,
                fat_grams=0,
                protein_percentage=0,
                carb_percentage=0,
                fat_percentage=0,
                distribution_rationale="",
                meal_distribution={},
                error=str(e),
            )

    def _calculate_mifflin_st_jeor(
        self, age: int, gender: str, weight_kg: float, height_cm: float
    ) -> float:
        """Calculate BMR using Mifflin-St Jeor equation."""
        bmr = (10 * weight_kg) + (6.25 * height_cm) - (5 * age)
        if gender.lower() == "male":
            bmr += 5
        else:  # female
            bmr -= 161
        return bmr

    def _calculate_harris_benedict(
        self, age: int, gender: str, weight_kg: float, height_cm: float
    ) -> float:
        """Calculate BMR using Harris-Benedict equation."""
        if gender.lower() == "male":
            bmr = 88.362 + (13.397 * weight_kg) + (4.799 * height_cm) - (5.677 * age)
        else:  # female
            bmr = 447.593 + (9.247 * weight_kg) + (3.098 * height_cm) - (4.330 * age)
        return bmr

    def _calculate_katch_mcardle(
        self, weight_kg: float, body_fat_percentage: float
    ) -> float:
        """Calculate BMR using Katch-McArdle equation (requires body fat %)."""
        lean_body_mass = weight_kg * (1 - body_fat_percentage / 100)
        bmr = 370 + (21.6 * lean_body_mass)
        return bmr

    def _adjust_calories_for_goal(self, tdee: float, goal: str) -> float:
        """Adjust TDEE based on fitness goal."""
        goal_adjustments = {
            "weight_loss": -500,  # 1 lb/week loss
            "aggressive_weight_loss": -750,  # 1.5 lb/week loss
            "weight_gain": 300,  # ~0.5 lb/week gain
            "muscle_gain": 200,  # Conservative surplus
            "maintenance": 0,  # No adjustment
            "body_recomposition": -200,  # Slight deficit
            "performance": 100,  # Slight surplus for performance
        }

        adjustment = goal_adjustments.get(goal, 0)
        return tdee + adjustment

    def _get_macro_distribution(
        self, goal: str, dietary_preferences: List[str]
    ) -> Dict[str, float]:
        """Get macro percentage distribution based on goal."""
        base_distributions = {
            "weight_loss": {"protein": 0.30, "carbs": 0.35, "fat": 0.35},
            "muscle_gain": {"protein": 0.25, "carbs": 0.45, "fat": 0.30},
            "maintenance": {"protein": 0.25, "carbs": 0.40, "fat": 0.35},
            "performance": {"protein": 0.20, "carbs": 0.50, "fat": 0.30},
            "body_recomposition": {"protein": 0.35, "carbs": 0.30, "fat": 0.35},
        }

        distribution = base_distributions.get(goal, base_distributions["maintenance"])

        # Adjust for dietary preferences
        if "high_protein" in dietary_preferences:
            distribution["protein"] = min(0.40, distribution["protein"] + 0.10)
            distribution["carbs"] -= 0.05
            distribution["fat"] -= 0.05

        if "low_carb" in dietary_preferences:
            reduction = min(0.15, distribution["carbs"] - 0.15)
            distribution["carbs"] -= reduction
            distribution["fat"] += reduction

        if "keto" in dietary_preferences:
            distribution = {"protein": 0.25, "carbs": 0.05, "fat": 0.70}

        return distribution

    def _adjust_protein_for_preference(
        self, protein_grams: float, preference: str, target_calories: int
    ) -> float:
        """Adjust protein based on user preference."""
        if preference == "high":
            # 1.2-1.6g per lb body weight equivalent
            return max(protein_grams, target_calories * 0.30 / 4)
        elif preference == "moderate":
            return max(protein_grams, target_calories * 0.25 / 4)
        elif preference == "low":
            return min(protein_grams, target_calories * 0.20 / 4)
        return protein_grams

    def _generate_calorie_recommendations(
        self, goal: str, target_calories: float
    ) -> List[str]:
        """Generate recommendations based on calorie targets."""
        recommendations = []

        if goal == "weight_loss":
            recommendations.extend(
                [
                    "Track your food intake to ensure you stay within calorie targets",
                    "Focus on nutrient-dense, lower-calorie foods",
                    "Include adequate protein to preserve muscle mass",
                    "Consider meal timing around workouts",
                ]
            )
        elif goal == "muscle_gain":
            recommendations.extend(
                [
                    "Eat in a controlled surplus to minimize fat gain",
                    "Prioritize protein throughout the day",
                    "Time carbohydrates around workouts",
                    "Don't exceed 300-500 calorie surplus",
                ]
            )
        elif goal == "maintenance":
            recommendations.extend(
                [
                    "Focus on food quality over strict calorie counting",
                    "Listen to hunger and satiety cues",
                    "Maintain consistent meal patterns",
                    "Adjust based on activity level changes",
                ]
            )
        elif goal == "aggressive_weight_loss":
            recommendations.extend(
                [
                    "Ensure a significant but safe calorie deficit, monitor for adverse effects",
                    "Prioritize protein intake to minimize muscle loss during rapid weight loss",
                    "Focus on very high-satiety, low-calorie-density foods (e.g., vegetables, lean protein)",
                    "Consider professional guidance for aggressive protocols due to potential risks",
                ]
            )

        return recommendations

    def _explain_macro_distribution(
        self, goal: str, percentages: Dict[str, float]
    ) -> str:
        """Explain the rationale behind macro distribution."""
        explanations = {
            "weight_loss": "Higher protein (30%) to preserve muscle, moderate carbs and fats for satiety and hormone production",
            "muscle_gain": "Moderate protein (25%) for muscle synthesis, higher carbs (45%) for training fuel and recovery",
            "performance": "Higher carbs (50%) for energy and glycogen replenishment, adequate protein for recovery",
            "body_recomposition": "High protein (35%) for muscle preservation and building, lower carbs to promote fat utilization",
        }

        return explanations.get(
            goal, "Balanced macro distribution for general health and fitness"
        )

    def _suggest_meal_distribution(
        self, protein_g: float, carb_g: float, fat_g: float
    ) -> Dict[str, Any]:
        """Suggest how to distribute macros across meals."""
        return {
            "breakfast": {
                "protein": round(protein_g * 0.25, 1),
                "carbs": round(carb_g * 0.30, 1),
                "fat": round(fat_g * 0.30, 1),
            },
            "lunch": {
                "protein": round(protein_g * 0.30, 1),
                "carbs": round(carb_g * 0.35, 1),
                "fat": round(fat_g * 0.25, 1),
            },
            "dinner": {
                "protein": round(protein_g * 0.30, 1),
                "carbs": round(carb_g * 0.25, 1),
                "fat": round(fat_g * 0.30, 1),
            },
            "snacks": {
                "protein": round(protein_g * 0.15, 1),
                "carbs": round(carb_g * 0.10, 1),
                "fat": round(fat_g * 0.15, 1),
            },
        }


# LangChain Tool Wrappers
@tool
def calculate_daily_calories(user_data: str) -> str:
    """
    Calculate daily caloric needs based on user profile and goals.

    Use this tool for:
    - Determining baseline caloric requirements
    - Setting calorie targets for specific goals
    - Understanding metabolic rate
    - Planning caloric intake strategies

    Input should be JSON string with user data:
    {
        "age": 30,
        "gender": "female",
        "weight_kg": 65,
        "height_cm": 165,
        "activity_level": "moderately_active",
        "goal": "weight_loss",
        "body_fat_percentage": 25
    }
    """
    try:
        import json

        data = json.loads(user_data)

        calc_input = CalorieCalculationInput(**data)
        calculator = NutritionCalorieCalculator()

        # For demo purposes, return synchronous result
        bmr = calculator._calculate_mifflin_st_jeor(
            calc_input.age,
            calc_input.gender,
            calc_input.weight_kg,
            calc_input.height_cm,
        )

        activity_multiplier = calculator._activity_multipliers.get(
            calc_input.activity_level, 1.55
        )
        tdee = bmr * activity_multiplier
        target_calories = calculator._adjust_calories_for_goal(tdee, calc_input.goal)

        result = {
            "bmr": round(bmr),
            "tdee": round(tdee),
            "target_calories": round(target_calories),
            "formula_used": "mifflin_st_jeor",
            "activity_multiplier": activity_multiplier,
            "goal_adjustment": target_calories - tdee,
            "recommendations": calculator._generate_calorie_recommendations(
                calc_input.goal, target_calories
            ),
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        return f"Calorie calculation failed: {str(e)}"


@tool
def calculate_macro_targets(macro_data: str) -> str:
    """
    Calculate macro targets (protein, carbs, fat) based on calorie goals.

    Use this tool for:
    - Setting macronutrient targets
    - Planning meal composition
    - Optimizing nutrition for specific goals
    - Creating balanced meal plans

    Input should be JSON string:
    {
        "target_calories": 1800,
        "goal": "weight_loss",
        "dietary_preferences": ["high_protein"],
        "protein_preference": "high"
    }
    """
    try:
        import json

        data = json.loads(macro_data)

        macro_input = MacroTargetsInput(**data)
        calculator = NutritionCalorieCalculator()

        # Calculate macro distribution
        macro_percentages = calculator._get_macro_distribution(
            macro_input.goal, macro_input.dietary_preferences or []
        )

        target_calories = macro_input.target_calories
        protein_grams = (target_calories * macro_percentages["protein"]) / 4
        carb_grams = (target_calories * macro_percentages["carbs"]) / 4
        fat_grams = (target_calories * macro_percentages["fat"]) / 9

        result = {
            "target_calories": target_calories,
            "protein_grams": round(protein_grams, 1),
            "carb_grams": round(carb_grams, 1),
            "fat_grams": round(fat_grams, 1),
            "protein_percentage": round(macro_percentages["protein"] * 100, 1),
            "carb_percentage": round(macro_percentages["carbs"] * 100, 1),
            "fat_percentage": round(macro_percentages["fat"] * 100, 1),
            "distribution_rationale": calculator._explain_macro_distribution(
                macro_input.goal, macro_percentages
            ),
            "meal_distribution": calculator._suggest_meal_distribution(
                protein_grams, carb_grams, fat_grams
            ),
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        return f"Macro calculation failed: {str(e)}"
