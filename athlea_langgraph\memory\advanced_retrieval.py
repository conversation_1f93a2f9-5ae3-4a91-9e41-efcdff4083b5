"""
Advanced Retrieval Pipeline for Enhanced Memory Search

Implements sophisticated search and ranking algorithms including:
- Hybrid search (semantic + keyword + metadata)
- Multi-factor relevance scoring
- Context-aware retrieval strategies
- Query expansion and optimization
"""

import logging
import math
import re
from collections import Counter
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple

from .mem0_adapter import Mem0Adapter, MemoryType
from .schemas.domain_schemas import CoachingDomain
from .schemas.memory_metadata import ImportanceScore, SummaryLevel

logger = logging.getLogger(__name__)


@dataclass
class SearchConfig:
    """Configuration for advanced search operations."""

    semantic_weight: float = 0.6  # Weight for semantic similarity
    keyword_weight: float = 0.3  # Weight for keyword matching
    metadata_weight: float = 0.1  # Weight for metadata matching
    recency_boost: float = 0.2  # Boost factor for recent memories
    importance_boost: float = 0.3  # Boost factor for important memories
    domain_boost: float = 0.2  # Boost factor for domain relevance
    max_age_days: Optional[int] = None  # Maximum age for memories (None = no limit)
    min_relevance_score: float = 0.1  # Minimum relevance threshold


@dataclass
class RetrievalContext:
    """Context for retrieval operations."""

    user_id: str
    session_type: str  # "coaching", "check_in", "planning", etc.
    current_domains: List[CoachingDomain] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    recent_interactions: List[str] = field(default_factory=list)
    time_context: Optional[str] = None  # "morning", "evening", "post_workout", etc.


@dataclass
class RankedMemory:
    """Memory with computed relevance ranking."""

    memory_id: str
    content: str
    original_score: float
    keyword_score: float
    metadata_score: float
    recency_score: float
    importance_score: float
    domain_score: float
    final_score: float
    ranking_factors: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AdvancedRetrievalPipeline:
    """
    Advanced retrieval pipeline with sophisticated ranking and search capabilities.

    Features:
    - Hybrid search combining multiple signals
    - Context-aware retrieval strategies
    - Multi-factor relevance scoring
    - Query expansion and optimization
    """

    def __init__(self, mem0_adapter: Mem0Adapter):
        """
        Initialize advanced retrieval pipeline.

        Args:
            mem0_adapter: Configured mem0 adapter instance
        """
        self.mem0_adapter = mem0_adapter
        self.query_cache = {}  # Cache for expensive query operations
        self.stopwords = {
            "a",
            "an",
            "and",
            "are",
            "as",
            "at",
            "be",
            "by",
            "for",
            "from",
            "has",
            "he",
            "in",
            "is",
            "it",
            "its",
            "of",
            "on",
            "that",
            "the",
            "to",
            "was",
            "will",
            "with",
        }

    async def hybrid_search(
        self,
        user_id: str,
        query: str,
        search_config: SearchConfig = SearchConfig(),
        limit: int = 10,
    ) -> List[RankedMemory]:
        """
        Perform hybrid search combining semantic, keyword, and metadata signals.

        Args:
            user_id: User identifier
            query: Search query
            search_config: Configuration for search weights and parameters
            limit: Maximum number of results

        Returns:
            List of ranked memories with detailed scoring
        """
        # Expand query for better recall
        expanded_query = await self.expand_query(query, user_id)

        # Get initial semantic results
        semantic_memories = await self.mem0_adapter.search_memories(
            user_id=user_id,
            query=expanded_query,
            limit=limit * 3,  # Get more candidates for reranking
        )

        # Calculate keyword scores
        keyword_scores = self._calculate_keyword_scores(query, semantic_memories)

        # Calculate metadata scores
        metadata_scores = self._calculate_metadata_scores(query, semantic_memories)

        # Rank memories using hybrid scoring
        ranked_memories = []
        for i, memory in enumerate(semantic_memories):
            memory_id = memory.get("id", f"mem_{i}")
            content = memory.get("content", "")

            # Get individual scores
            semantic_score = memory.get("score", 0.0)
            keyword_score = keyword_scores.get(memory_id, 0.0)
            metadata_score = metadata_scores.get(memory_id, 0.0)

            # Calculate additional factors
            recency_score = self._calculate_recency_score(
                memory, search_config.max_age_days
            )
            importance_score = self._calculate_importance_score(memory)
            domain_score = self._calculate_domain_score(memory, query)

            # Compute final weighted score
            final_score = (
                semantic_score * search_config.semantic_weight
                + keyword_score * search_config.keyword_weight
                + metadata_score * search_config.metadata_weight
                + recency_score * search_config.recency_boost
                + importance_score * search_config.importance_boost
                + domain_score * search_config.domain_boost
            )

            # Skip memories below relevance threshold
            if final_score < search_config.min_relevance_score:
                continue

            ranked_memory = RankedMemory(
                memory_id=memory_id,
                content=content,
                original_score=semantic_score,
                keyword_score=keyword_score,
                metadata_score=metadata_score,
                recency_score=recency_score,
                importance_score=importance_score,
                domain_score=domain_score,
                final_score=final_score,
                ranking_factors={
                    "semantic": semantic_score * search_config.semantic_weight,
                    "keyword": keyword_score * search_config.keyword_weight,
                    "metadata": metadata_score * search_config.metadata_weight,
                    "recency": recency_score * search_config.recency_boost,
                    "importance": importance_score * search_config.importance_boost,
                    "domain": domain_score * search_config.domain_boost,
                },
                metadata=memory.get("metadata", {}),
            )

            ranked_memories.append(ranked_memory)

        # Sort by final score and return top results
        ranked_memories.sort(key=lambda m: m.final_score, reverse=True)

        logger.info(
            f"Hybrid search for '{query}' returned {len(ranked_memories)} ranked results"
        )
        return ranked_memories[:limit]

    async def rank_memories(
        self,
        memories: List[Dict[str, Any]],
        context: RetrievalContext,
        search_config: SearchConfig = SearchConfig(),
    ) -> List[RankedMemory]:
        """
        Re-rank a list of memories based on retrieval context.

        Args:
            memories: List of memory dictionaries
            context: Retrieval context for ranking
            search_config: Search configuration

        Returns:
            List of re-ranked memories
        """
        ranked_memories = []

        for i, memory in enumerate(memories):
            memory_id = memory.get("id", f"mem_{i}")
            content = memory.get("content", "")

            # Base score from original retrieval
            base_score = memory.get("score", 0.0)

            # Context-specific scoring
            session_boost = self._calculate_session_relevance(memory, context)
            preference_boost = self._calculate_preference_alignment(memory, context)
            temporal_boost = self._calculate_temporal_relevance(memory, context)

            # Combine scores
            final_score = base_score + session_boost + preference_boost + temporal_boost

            ranked_memory = RankedMemory(
                memory_id=memory_id,
                content=content,
                original_score=base_score,
                keyword_score=0.0,  # Not recalculated in reranking
                metadata_score=0.0,
                recency_score=0.0,
                importance_score=0.0,
                domain_score=0.0,
                final_score=final_score,
                ranking_factors={
                    "base": base_score,
                    "session_relevance": session_boost,
                    "preference_alignment": preference_boost,
                    "temporal_relevance": temporal_boost,
                },
                metadata=memory.get("metadata", {}),
            )

            ranked_memories.append(ranked_memory)

        # Sort by final score
        ranked_memories.sort(key=lambda m: m.final_score, reverse=True)

        logger.info(f"Re-ranked {len(memories)} memories based on context")
        return ranked_memories

    async def expand_query(self, query: str, user_id: str) -> str:
        """
        Expand query with synonyms and user-specific terms.

        Args:
            query: Original query
            user_id: User identifier for personalization

        Returns:
            Expanded query string
        """
        # Cache check
        cache_key = f"{user_id}:{query.lower()}"
        if cache_key in self.query_cache:
            return self.query_cache[cache_key]

        # Clean and tokenize query
        query_lower = query.lower()
        words = re.findall(r"\b\w+\b", query_lower)
        words = [w for w in words if w not in self.stopwords]

        # Fitness-specific synonym expansion
        fitness_synonyms = {
            "workout": ["exercise", "training", "session"],
            "strength": ["lifting", "weights", "resistance"],
            "cardio": ["cardiovascular", "endurance", "aerobic"],
            "diet": ["nutrition", "eating", "food"],
            "weight": ["mass", "pounds", "kg"],
            "muscle": ["muscles", "muscular", "gains"],
            "run": ["running", "jog", "jogging"],
            "goal": ["target", "objective", "aim"],
        }

        expanded_terms = set(words)
        for word in words:
            if word in fitness_synonyms:
                expanded_terms.update(fitness_synonyms[word])

        # Get user-specific terms from recent memories
        try:
            recent_memories = await self.mem0_adapter.search_memories(
                user_id=user_id,
                query="",  # Get recent memories
                limit=20,
            )

            # Extract frequently used terms
            user_terms = []
            for memory in recent_memories:
                content = memory.get("content", "").lower()
                memory_words = re.findall(r"\b\w+\b", content)
                user_terms.extend(memory_words)

            # Add most common user terms that relate to query
            user_term_counts = Counter(user_terms)
            for term, count in user_term_counts.most_common(10):
                if count > 2 and any(w in term or term in w for w in words):
                    expanded_terms.add(term)

        except Exception as e:
            logger.warning(f"Failed to get user-specific terms: {e}")

        # Combine terms into expanded query
        expanded_query = " ".join(expanded_terms)

        # Cache the result
        self.query_cache[cache_key] = expanded_query

        logger.debug(f"Expanded query '{query}' to '{expanded_query}'")
        return expanded_query

    async def get_contextual_memories(
        self,
        user_id: str,
        session_type: str,
        query: str,
        limit: int = 10,
    ) -> List[Dict[str, Any]]:
        """
        Get memories with context-aware retrieval strategies.

        Args:
            user_id: User identifier
            session_type: Type of session (coaching, check_in, planning)
            query: Search query
            limit: Maximum results

        Returns:
            List of contextually relevant memories
        """
        # Create retrieval context
        context = RetrievalContext(
            user_id=user_id,
            session_type=session_type,
        )

        # Adjust search strategy based on session type
        if session_type == "coaching":
            # Focus on goals, preferences, and recent progress
            search_config = SearchConfig(
                semantic_weight=0.5,
                importance_boost=0.4,
                recency_boost=0.3,
            )

        elif session_type == "check_in":
            # Focus on recent activities and progress
            search_config = SearchConfig(
                recency_boost=0.5,
                importance_boost=0.2,
                metadata_weight=0.2,
            )

        elif session_type == "planning":
            # Focus on goals and historical patterns
            search_config = SearchConfig(
                semantic_weight=0.7,
                importance_boost=0.3,
                recency_boost=0.1,
            )

        else:
            # Default balanced approach
            search_config = SearchConfig()

        # Perform hybrid search
        ranked_memories = await self.hybrid_search(
            user_id=user_id,
            query=query,
            search_config=search_config,
            limit=limit,
        )

        # Convert back to dictionary format for compatibility
        contextual_memories = []
        for ranked_memory in ranked_memories:
            memory_dict = {
                "id": ranked_memory.memory_id,
                "content": ranked_memory.content,
                "score": ranked_memory.final_score,
                "metadata": ranked_memory.metadata,
                "ranking_factors": ranked_memory.ranking_factors,
            }
            contextual_memories.append(memory_dict)

        logger.info(
            f"Retrieved {len(contextual_memories)} contextual memories for {session_type} session"
        )
        return contextual_memories

    def _calculate_keyword_scores(
        self, query: str, memories: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Calculate keyword matching scores for memories."""
        query_words = set(re.findall(r"\b\w+\b", query.lower()))
        query_words = query_words - self.stopwords

        keyword_scores = {}

        for memory in memories:
            memory_id = memory.get("id", "")
            content = memory.get("content", "").lower()
            content_words = set(re.findall(r"\b\w+\b", content))

            # Calculate Jaccard similarity for keywords
            intersection = query_words.intersection(content_words)
            union = query_words.union(content_words)

            jaccard_score = len(intersection) / len(union) if union else 0.0

            # Boost for exact phrase matches
            phrase_boost = 1.0
            if len(query_words) > 1:
                query_phrase = " ".join(query_words)
                if query_phrase in content:
                    phrase_boost = 1.5

            keyword_scores[memory_id] = jaccard_score * phrase_boost

        return keyword_scores

    def _calculate_metadata_scores(
        self, query: str, memories: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Calculate metadata-based relevance scores."""
        metadata_scores = {}

        for memory in memories:
            memory_id = memory.get("id", "")
            metadata = memory.get("metadata", {})

            score = 0.0

            # Memory type relevance
            memory_type = metadata.get("memory_type", "")
            if "goal" in query.lower() and memory_type == "goal":
                score += 0.3
            elif "workout" in query.lower() and memory_type == "workout_log":
                score += 0.3
            elif "preference" in query.lower() and memory_type == "preference":
                score += 0.3

            # Domain relevance
            primary_domain = metadata.get("primary_domain", "")
            if primary_domain and any(
                domain_word in query.lower()
                for domain_word in [primary_domain, primary_domain.replace("_", " ")]
            ):
                score += 0.2

            metadata_scores[memory_id] = score

        return metadata_scores

    def _calculate_recency_score(
        self, memory: Dict[str, Any], max_age_days: Optional[int]
    ) -> float:
        """Calculate recency-based relevance score."""
        metadata = memory.get("metadata", {})
        timestamp_str = metadata.get("timestamp")

        if not timestamp_str:
            return 0.0

        try:
            timestamp = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
            age_days = (datetime.now() - timestamp.replace(tzinfo=None)).days

            if max_age_days and age_days > max_age_days:
                return 0.0

            # Exponential decay with half-life of 30 days
            decay_factor = 0.5 ** (age_days / 30.0)
            return decay_factor

        except (ValueError, AttributeError):
            return 0.0

    def _calculate_importance_score(self, memory: Dict[str, Any]) -> float:
        """Calculate importance-based relevance score."""
        metadata = memory.get("metadata", {})

        # Check for importance score in metadata
        importance_data = metadata.get("importance_score")
        if importance_data and isinstance(importance_data, dict):
            return importance_data.get("current_score", 0.0)

        # Fallback: use access count and memory type
        access_count = metadata.get("access_count", 0)
        memory_type = metadata.get("memory_type", "")

        # Base importance by type
        type_importance = {
            "goal": 0.8,
            "preference": 0.7,
            "injury": 0.9,
            "progress": 0.6,
            "conversation": 0.4,
        }

        base_score = type_importance.get(memory_type, 0.5)
        access_boost = min(0.3, access_count * 0.05)  # Cap at 0.3

        return base_score + access_boost

    def _calculate_domain_score(self, memory: Dict[str, Any], query: str) -> float:
        """Calculate domain relevance score."""
        metadata = memory.get("metadata", {})
        primary_domain = metadata.get("primary_domain", "")

        if not primary_domain:
            return 0.0

        # Check if query relates to the memory's domain
        query_lower = query.lower()
        domain_keywords = {
            "strength": ["strength", "lifting", "weights", "muscle"],
            "cardio": ["cardio", "endurance", "heart rate"],
            "running": ["running", "run", "marathon", "pace"],
            "nutrition": ["nutrition", "diet", "food", "calories"],
            "recovery": ["recovery", "rest", "sleep", "injury"],
            "mental": ["mental", "motivation", "mindset"],
        }

        if primary_domain in domain_keywords:
            keywords = domain_keywords[primary_domain]
            if any(keyword in query_lower for keyword in keywords):
                return 1.0

        return 0.0

    def _calculate_session_relevance(
        self, memory: Dict[str, Any], context: RetrievalContext
    ) -> float:
        """Calculate session-specific relevance boost."""
        metadata = memory.get("metadata", {})
        memory_type = metadata.get("memory_type", "")

        relevance_map = {
            "coaching": {
                "goal": 0.3,
                "preference": 0.2,
                "progress": 0.2,
                "conversation": 0.1,
            },
            "check_in": {
                "progress": 0.3,
                "workout_log": 0.2,
                "conversation": 0.1,
            },
            "planning": {
                "goal": 0.4,
                "progress": 0.2,
                "preference": 0.1,
            },
        }

        session_map = relevance_map.get(context.session_type, {})
        return session_map.get(memory_type, 0.0)

    def _calculate_preference_alignment(
        self, memory: Dict[str, Any], context: RetrievalContext
    ) -> float:
        """Calculate alignment with user preferences."""
        # This would use the user preferences from context
        # For now, return 0.0 as placeholder
        return 0.0

    def _calculate_temporal_relevance(
        self, memory: Dict[str, Any], context: RetrievalContext
    ) -> float:
        """Calculate temporal context relevance."""
        if not context.time_context:
            return 0.0

        metadata = memory.get("metadata", {})

        # Boost memories from similar time contexts
        if (
            context.time_context == "post_workout"
            and metadata.get("memory_type") == "workout_log"
        ):
            return 0.2
        elif (
            context.time_context == "morning"
            and "morning" in memory.get("content", "").lower()
        ):
            return 0.1

        return 0.0
