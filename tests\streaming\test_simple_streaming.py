#!/usr/bin/env python3
"""
Simple test to verify LLM streaming works independently of LangGraph
"""

import asyncio
import time
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage


async def test_direct_llm_streaming():
    """Test streaming directly from LLM without LangGraph"""
    print("🧪 Testing direct LLM streaming (bypassing LangGraph)")

    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7, streaming=True)

    start_time = time.time()
    first_token_time = None
    total_content = ""
    token_count = 0

    async for chunk in llm.astream(
        [HumanMessage(content="Explain how to start cycling training in detail")]
    ):
        current_time = time.time()

        if hasattr(chunk, "content") and chunk.content:
            if first_token_time is None:
                first_token_time = current_time
                print(f"⏱️  Time to first token: {first_token_time - start_time:.2f}s")

            token_count += 1
            total_content += chunk.content

            print(
                f"Token {token_count}: '{chunk.content}' ({len(chunk.content)} chars)"
            )

            # Only show first 10 tokens to avoid spam
            if token_count > 10:
                print("... (showing only first 10 tokens)")
                break

    print(f"\n📊 Summary:")
    print(f"  - Total tokens received: {token_count}")
    print(f"  - Total content length: {len(total_content)}")
    print(f"  - Average chars per token: {len(total_content)/token_count:.1f}")
    print(f"  - Content preview: {total_content[:200]}...")


async def test_langgraph_streaming():
    """Test streaming through a simple LangGraph node"""
    print("\n🧪 Testing LangGraph node streaming")

    from langchain_core.messages import AIMessage
    from langgraph.graph import StateGraph, START, END
    from typing import Dict, Any, List
    from langchain_core.messages import BaseMessage

    # Simple state
    class SimpleState:
        messages: List[BaseMessage] = []

    async def llm_node(state: Dict[str, Any]) -> Dict[str, Any]:
        """Simple LLM node that streams"""
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7, streaming=True)

        content = ""
        async for chunk in llm.astream(
            [HumanMessage(content="Give a brief cycling tip")]
        ):
            if hasattr(chunk, "content") and chunk.content:
                content += chunk.content

        return {"messages": [AIMessage(content=content)]}

    # Build simple graph
    builder = StateGraph(Dict[str, Any])
    builder.add_node("llm", llm_node)
    builder.add_edge(START, "llm")
    builder.add_edge("llm", END)
    graph = builder.compile()

    start_time = time.time()
    token_count = 0

    async for event in graph.astream_events({"messages": []}, version="v1"):
        if event.get("event") == "on_chain_stream":
            chunk = event.get("data", {}).get("chunk")
            if chunk and hasattr(chunk, "content") and chunk.content:
                token_count += 1
                print(
                    f"LangGraph Token {token_count}: '{chunk.content}' ({len(chunk.content)} chars)"
                )

    print(f"\n📊 LangGraph Summary:")
    print(f"  - Total tokens from LangGraph: {token_count}")
    print(f"  - Total time: {time.time() - start_time:.2f}s")


async def main():
    print("🔬 LLM Streaming Test - Direct vs LangGraph")
    print("=" * 60)

    await test_direct_llm_streaming()
    await test_langgraph_streaming()

    print("\n" + "=" * 60)
    print("✅ Test complete!")


if __name__ == "__main__":
    asyncio.run(main())
