{"name": "Athlea LangGraph Production Integration", "nodes": [{"parameters": {"httpMethod": "POST", "path": "0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef", "options": {}}, "id": "webhook-trigger-prod", "name": "Production Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validate-message", "leftValue": "={{ $json.message }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}, {"id": "validate-user-id", "leftValue": "={{ $json.user_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "validate-input-prod", "name": "Validate Required Fields", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://localhost:8001/n8n/webhooks/coaching", "authentication": "none", "requestMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "n8n-athlea-integration"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "={{ $json.message }}"}, {"name": "user_id", "value": "={{ $json.user_id }}"}, {"name": "coach_type", "value": "={{ $json.coach_type || 'general' }}"}, {"name": "thread_id", "value": "={{ $json.thread_id }}"}, {"name": "session_data", "value": "={{ $json.session_data || {} }}"}]}, "options": {"timeout": 60000, "retry": {"enabled": true, "maxRetries": 3}}}, "id": "call-langgraph-api", "name": "Call LangGraph API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"jsCode": "// Process LangGraph response and prepare for automation\nconst response = $input.all()[0].json;\n\n// Check if the request was successful\nif (response.status !== 'success') {\n  throw new Error(`LangGraph API error: ${response.message || 'Unknown error'}`);\n}\n\n// Extract and structure the data\nconst processedData = {\n  // Original request data\n  original_message: $('Production Webhook').item.json.message,\n  user_id: response.n8n_data?.user_id,\n  coach_type: response.n8n_data?.coach_type,\n  \n  // LangGraph response\n  coaching_response: response.coaching_response,\n  thread_id: response.thread_id,\n  execution_time: response.execution_time,\n  tools_used: response.tools_used || [],\n  reasoning_steps: response.reasoning_steps || [],\n  \n  // Automation flags\n  needs_follow_up: response.n8n_data?.needs_follow_up || false,\n  has_workout_plan: response.tools_used?.includes('airtable_search') || false,\n  has_nutrition_advice: response.tools_used?.includes('nutrition_tools') || false,\n  \n  // Timestamps\n  request_time: $('Production Webhook').item.json.session_data?.timestamp,\n  response_time: response.n8n_data?.timestamp,\n  \n  // For email/notifications\n  user_email: $('Production Webhook').item.json.user_email,\n  \n  // Summary for notifications\n  summary: {\n    question: $('Production Webhook').item.json.message.substring(0, 100),\n    answer_preview: response.coaching_response.substring(0, 200),\n    tools_count: response.tools_used?.length || 0,\n    duration: `${response.execution_time}s`\n  }\n};\n\nreturn processedData;"}, "id": "process-response", "name": "Process LangGraph Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-email", "leftValue": "={{ $json.user_email }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "check-email-available", "name": "Check Email Available", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"subject": "Your Athlea Coaching Session - {{ $json.coach_type }} Coach", "emailType": "html", "toEmail": "={{ $json.user_email }}", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n  <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n    <h1 style=\"margin: 0; font-size: 28px;\">🏋️ Your Athlea Coaching Session</h1>\n    <p style=\"margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;\">Personalized {{ $json.coach_type }} coaching results</p>\n  </div>\n  \n  <div style=\"background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;\">\n    <h3 style=\"color: #2c3e50; margin-top: 0; font-size: 18px;\">📝 Your Question</h3>\n    <p style=\"font-style: italic; color: #5a6c7d; font-size: 16px; line-height: 1.5;\">\"{{ $json.original_message }}\"</p>\n  </div>\n  \n  <div style=\"background-color: #e8f5e8; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;\">\n    <h3 style=\"color: #155724; margin-top: 0; font-size: 18px;\">🎯 Your Coach's Response</h3>\n    <div style=\"color: #155724; font-size: 16px; line-height: 1.6;\">{{ $json.coaching_response }}</div>\n  </div>\n  \n  <div style=\"background-color: #fff3cd; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;\">\n    <h3 style=\"color: #856404; margin-top: 0; font-size: 18px;\">🧠 Reasoning Process</h3>\n    <ul style=\"color: #856404; font-size: 14px; line-height: 1.5; padding-left: 20px;\">\n      {{#each $json.reasoning_steps}}\n      <li style=\"margin-bottom: 8px;\">{{this}}</li>\n      {{/each}}\n    </ul>\n  </div>\n  \n  <div style=\"background-color: #e3f2fd; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;\">\n    <h3 style=\"color: #1565c0; margin-top: 0; font-size: 18px;\">⚙️ Session Details</h3>\n    <div style=\"color: #1565c0; font-size: 14px;\">\n      <p><strong>Coach Type:</strong> {{ $json.coach_type }}</p>\n      <p><strong>Session Duration:</strong> {{ $json.execution_time }}s</p>\n      <p><strong>Tools Used:</strong> {{ $json.tools_used.join(', ') }}</p>\n      <p><strong>Thread ID:</strong> {{ $json.thread_id }}</p>\n    </div>\n  </div>\n  \n  <div style=\"text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #e9ecef;\">\n    <p style=\"color: #6c757d; font-size: 14px; margin-bottom: 20px;\">Continue your fitness journey with Athlea!</p>\n    <div style=\"background-color: #667eea; color: white; padding: 15px; border-radius: 5px; display: inline-block;\">\n      <strong>🚀 Ready for your next session?</strong>\n    </div>\n  </div>\n</div>", "options": {}}, "id": "send-coaching-email", "name": "Send Coaching Summary <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"success\",\n  \"message\": \"Coaching session completed successfully\",\n  \"data\": {\n    \"coaching_response\": $json.coaching_response,\n    \"thread_id\": $json.thread_id,\n    \"execution_time\": $json.execution_time,\n    \"tools_used\": $json.tools_used,\n    \"coach_type\": $json.coach_type,\n    \"summary\": $json.summary\n  },\n  \"timestamp\": $json.response_time\n} }}", "options": {}}, "id": "success-response", "name": "Return Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"error\",\n  \"message\": \"Missing required fields. Please provide 'message' and 'user_id'\",\n  \"required_fields\": [\"message\", \"user_id\"],\n  \"optional_fields\": [\"coach_type\", \"user_email\", \"thread_id\", \"session_data\"],\n  \"received_data\": $json\n} }}", "options": {"responseCode": 400}}, "id": "error-response", "name": "Return Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"success\",\n  \"message\": \"Coaching session completed (no email sent)\",\n  \"data\": {\n    \"coaching_response\": $json.coaching_response,\n    \"thread_id\": $json.thread_id,\n    \"execution_time\": $json.execution_time,\n    \"tools_used\": $json.tools_used,\n    \"coach_type\": $json.coach_type,\n    \"summary\": $json.summary\n  },\n  \"timestamp\": $json.response_time\n} }}", "options": {}}, "id": "success-no-email", "name": "Return Success (No Email)", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Production Webhook": {"main": [[{"node": "Validate Required Fields", "type": "main", "index": 0}]]}, "Validate Required Fields": {"main": [[{"node": "Call LangGraph API", "type": "main", "index": 0}], [{"node": "Return Validation Error", "type": "main", "index": 0}]]}, "Call LangGraph API": {"main": [[{"node": "Process LangGraph Response", "type": "main", "index": 0}]]}, "Process LangGraph Response": {"main": [[{"node": "Check Email Available", "type": "main", "index": 0}]]}, "Check Email Available": {"main": [[{"node": "Send Coaching Summary <PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Return Success (No Email)", "type": "main", "index": 0}]]}, "Send Coaching Summary Email": {"main": [[{"node": "Return Success Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "00000000-0000-0000-0000-000000000001", "meta": {"templateCredsSetupCompleted": true, "instanceId": "athlea-production-instance"}, "id": "athlea-production-workflow", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "1", "name": "athlea-coaching"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "2", "name": "production"}]}