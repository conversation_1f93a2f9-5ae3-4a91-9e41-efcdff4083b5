"""
Tool Input/Output Validators

Provides validation utilities for tool-agent contract enforcement.
"""

import logging
from typing import Any, Type

from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)


class ToolInputValidator:
    """Validates tool inputs against contract schemas."""

    @staticmethod
    def validate(input_data: Any, schema: Type[BaseModel]) -> BaseModel:
        """
        Validate input data against schema.

        Args:
            input_data: Data to validate
            schema: Pydantic schema class

        Returns:
            Validated data instance

        Raises:
            ValidationError: If validation fails
        """
        try:
            if isinstance(input_data, dict):
                return schema(**input_data)
            else:
                return schema(input_data)
        except ValidationError as e:
            logger.error(f"Input validation failed: {e}")
            raise


class ToolOutputValidator:
    """Validates tool outputs against contract schemas."""

    @staticmethod
    def validate(output_data: Any, schema: Type[BaseModel]) -> BaseModel:
        """
        Validate output data against schema.

        Args:
            output_data: Data to validate
            schema: Pydantic schema class

        Returns:
            Validated data instance

        Raises:
            ValidationError: If validation fails
        """
        try:
            if isinstance(output_data, dict):
                return schema(**output_data)
            else:
                return schema(output_data)
        except ValidationError as e:
            logger.error(f"Output validation failed: {e}")
            raise
