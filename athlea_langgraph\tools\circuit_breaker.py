"""
Circuit Breaker Implementation for Hardened Tools

Provides a robust circuit breaker pattern for protecting against
cascading failures in external service calls.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Optional

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreakerError(Exception):
    """Exception raised when circuit breaker is open."""

    def __init__(self, message: str, retry_after: int):
        super().__init__(message)
        self.retry_after = retry_after


class CircuitBreaker:
    """
    Circuit breaker implementation with configurable thresholds.

    Features:
    - Failure threshold detection
    - Automatic recovery testing
    - Configurable timeout periods
    - Metrics tracking
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception,
    ):
        """
        Initialize circuit breaker.

        Args:
            name: Circuit breaker identifier
            failure_threshold: Number of failures before opening
            recovery_timeout: Seconds to wait before testing recovery
            expected_exception: Exception type to track for failures
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception

        # State management
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.next_attempt_time: Optional[float] = None

        logger.info(f"Circuit breaker '{name}' initialized")

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.

        Args:
            func: Function to execute (can be async or sync)
            *args, **kwargs: Arguments to pass to function

        Returns:
            Function result

        Raises:
            CircuitBreakerError: When circuit is open
            Exception: Original exception if function fails
        """
        # Check if circuit should transition from OPEN to HALF_OPEN
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info(f"Circuit breaker '{self.name}' transitioning to HALF_OPEN")
            else:
                retry_after = (
                    int(self.next_attempt_time - time.time())
                    if self.next_attempt_time
                    else self.recovery_timeout
                )
                raise CircuitBreakerError(
                    f"Circuit breaker '{self.name}' is OPEN",
                    retry_after=max(retry_after, 1),
                )

        try:
            # Execute the function (handle both sync and async)
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            # Success - reset failure count and close circuit
            self._on_success()
            return result

        except self.expected_exception as e:
            # Expected failure - record and potentially open circuit
            self._on_failure()
            raise e

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt recovery."""
        if self.next_attempt_time is None:
            return True
        return time.time() >= self.next_attempt_time

    def _on_success(self):
        """Handle successful function execution."""
        if self.state == CircuitState.HALF_OPEN:
            logger.info(
                f"Circuit breaker '{self.name}' recovery successful, closing circuit"
            )

        self.failure_count = 0
        self.last_failure_time = None
        self.next_attempt_time = None
        self.state = CircuitState.CLOSED

    def _on_failure(self):
        """Handle failed function execution."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        logger.warning(
            f"Circuit breaker '{self.name}' failure {self.failure_count}/{self.failure_threshold}"
        )

        # Open circuit if threshold exceeded
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self.next_attempt_time = time.time() + self.recovery_timeout

            logger.error(
                f"Circuit breaker '{self.name}' OPENED due to {self.failure_count} failures. "
                f"Will retry at {datetime.fromtimestamp(self.next_attempt_time)}"
            )

    def reset(self):
        """Manually reset the circuit breaker to CLOSED state."""
        logger.info(f"Manually resetting circuit breaker '{self.name}'")
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.next_attempt_time = None

    @property
    def is_open(self) -> bool:
        """Check if circuit breaker is open."""
        return self.state == CircuitState.OPEN

    @property
    def is_closed(self) -> bool:
        """Check if circuit breaker is closed."""
        return self.state == CircuitState.CLOSED

    @property
    def is_half_open(self) -> bool:
        """Check if circuit breaker is half-open."""
        return self.state == CircuitState.HALF_OPEN

    def get_stats(self) -> dict:
        """Get circuit breaker statistics."""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "next_attempt_time": self.next_attempt_time,
            "recovery_timeout": self.recovery_timeout,
        }

    # Backward compatibility methods
    def can_execute(self) -> bool:
        """Check if execution is allowed (backward compatibility)."""
        if self.state == CircuitState.CLOSED:
            return True
        elif self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info(f"Circuit breaker '{self.name}' transitioning to HALF_OPEN")
                return True
            return False
        else:  # HALF_OPEN
            return True

    def record_success(self):
        """Record successful execution (backward compatibility)."""
        self._on_success()

    def record_failure(self):
        """Record failed execution (backward compatibility)."""
        self._on_failure()
