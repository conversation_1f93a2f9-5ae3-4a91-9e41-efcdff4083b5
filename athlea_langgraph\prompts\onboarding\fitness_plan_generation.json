{"metadata": {"name": "fitness_plan_generation", "version": "1.1.0", "description": "Generates personalized fitness plans based on user goals and onboarding information. Creates structured plans with phases and example sessions.", "author": "Athlea System", "created_at": "2025-01-22T00:00:00.000Z", "updated_at": "2025-01-22T00:00:00.000Z", "prompt_type": "onboarding", "tags": ["onboarding", "plan-generation", "fitness-plans", "personalization", "structured-output"], "changelog": [{"version": "1.0.0", "date": "2025-06-06T19:12:00.000Z", "changes": "Initial creation for fitness plan generation", "author": "System", "breaking_changes": false}, {"version": "1.1.0", "date": "2025-01-22T00:00:00.000Z", "changes": "Moved to dedicated onboarding folder with enhanced guidance and examples", "author": "Athlea System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are an expert fitness coach. Based on the user's goals and the summarized information, generate a personalized fitness plan structure.\n\nUser Goals: {user_goals}\n\nUser Information Summary:\n{summary_string}\n\nGenerate a comprehensive, personalized fitness plan that:\n1. **Addresses all user goals** mentioned in their information\n2. **Considers their experience level** to set appropriate intensity and complexity\n3. **Fits their time commitment** and availability constraints\n4. **Utilizes their available equipment** and training environment\n5. **Respects any medical conditions or injuries** mentioned\n6. **Aligns with their priorities** and seasonal considerations\n\n**Plan Structure Requirements:**\n- Create a unique planId (UUID format)\n- Infer planType, level, and disciplines from the goals/summary\n- Generate a catchy, motivating name that reflects their goals\n- Provide a detailed description explaining the plan's approach\n- Estimate realistic total duration based on their goals and availability\n- Write a clear rationale explaining why this plan suits the user\n- Create 1-3 progressive phases with names, durations, and DETAILED descriptions\n- Generate 2-3 diverse EXAMPLE sessions that represent the plan's variety\n\n**Important Considerations:**\n- Multi-sport athletes: Balance training across disciplines\n- Injury history: Modify exercises and progressions accordingly\n- Time constraints: Optimize session efficiency\n- Equipment limitations: Suggest alternatives when needed\n- Goal specificity: Include sport-specific training elements\n\nRespond ONLY with the structured plan details conforming to the provided JSON schema.", "context_template": "User Goals: {user_goals}\n\nUser Information Summary:\n{summary_string}", "user_template": null, "examples": [{"title": "Multi-sport athlete plan example", "user_goals": "Run a faster 5k (Running), Improve backhand consistency (Tennis)", "summary_info": "Sports: Running, Tennis | Experience: Intermediate running, beginner tennis | Time: 4x/week, 60 min sessions | Equipment: Home treadmill, local tennis courts", "plan_focus": "Balanced running speed development and tennis skill improvement with 2 sessions per sport weekly"}, {"title": "Strength-focused plan example", "user_goals": "Build muscle, Increase bench press max (Strength Training)", "summary_info": "Sports: Strength Training | Experience: Beginner | Time: 3x/week, 45 min | Equipment: Full gym access", "plan_focus": "Progressive overload strength program with emphasis on compound movements and bench press development"}], "instructions": "Create a comprehensive fitness plan that directly addresses all user goals and constraints. Use the JSON schema structure precisely.", "constraints": ["Must address ALL user goals mentioned", "Plan complexity must match user experience level", "Sessions must fit within stated time commitments", "Equipment requirements must match user access", "Include safety considerations for any injuries/conditions", "Output must conform to provided JSON schema"]}, "variables": {"temperature": 0.3, "max_tokens": 4000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["user_goals", "summary_string"], "max_length": 15000, "min_length": 100, "required_fields": ["user_goals"], "allowed_variables": ["user_goals", "summary_string"]}}