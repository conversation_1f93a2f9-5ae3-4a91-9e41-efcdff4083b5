"""
External Services MCP Server

Exposes external service integrations via Model Context Protocol (MCP).
Includes Azure Maps, Google Search, Airtable, and other external APIs.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolResult,
    GetPromptResult,
    InitializeResult,
    Prompt,
    PromptArgument,
)
from mcp.types import TextContent
from mcp.types import TextContent as TextContentType
from mcp.types import Tool

from athlea_langgraph.tools.external import (
    AirtableMCPTool,
    AzureMapsTool,
    GoogleMapsElevationTool,
)
from athlea_langgraph.tools.schemas.external_schemas import (
    AirtableQueryInput,
    LocationSearchInput,
    WeatherInput,
    WebSearchInput,
    WikipediaSearchInput,
)


# Placeholder tool classes for missing external services
class GoogleSearchTool:
    """Placeholder for Google Search functionality."""

    async def search_web(self, input_data):
        return {
            "results": [{"title": "Mock search result", "url": "https://example.com"}]
        }


class WeatherAPITool:
    """Placeholder for Weather API functionality."""

    async def get_weather(self, input_data):
        return {"temperature": 20, "condition": "sunny", "location": "mock"}


class WikipediaTool:
    """Placeholder for Wikipedia functionality."""

    async def search_articles(self, input_data):
        return {"articles": [{"title": "Mock article", "summary": "Mock summary"}]}


# Use AirtableMCPTool as AirtableTool
AirtableTool = AirtableMCPTool

logger = logging.getLogger(__name__)

# Initialize external service tools
azure_maps = AzureMapsTool()
google_search = GoogleSearchTool()
airtable = AirtableTool()
weather_api = WeatherAPITool()
wikipedia = WikipediaTool()

app = Server("external-mcp-server")

EXTERNAL_TOOLS = [
    Tool(
        name="search_locations",
        description="Search for locations using Azure Maps API with detailed geographic information",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Location search query (address, place name, coordinates)",
                },
                "country_filter": {
                    "type": "string",
                    "description": "ISO country code to filter results (e.g., 'US', 'CA', 'GB')",
                },
                "category_filter": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Category filters (gym, restaurant, park, hospital, etc.)",
                },
                "radius_km": {
                    "type": "number",
                    "minimum": 0.1,
                    "maximum": 100,
                    "description": "Search radius in kilometers",
                    "default": 10,
                },
                "max_results": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 20,
                    "description": "Maximum number of results to return",
                    "default": 5,
                },
                "include_details": {
                    "type": "boolean",
                    "description": "Include detailed information (hours, contact, etc.)",
                    "default": True,
                },
            },
            "required": ["query"],
        },
    ),
    Tool(
        name="get_route_directions",
        description="Get turn-by-turn directions between two locations with travel time and distance",
        inputSchema={
            "type": "object",
            "properties": {
                "origin": {
                    "type": "string",
                    "description": "Starting location (address or coordinates)",
                },
                "destination": {
                    "type": "string",
                    "description": "Destination location (address or coordinates)",
                },
                "travel_mode": {
                    "type": "string",
                    "enum": ["driving", "walking", "cycling", "public_transit"],
                    "description": "Mode of transportation",
                    "default": "driving",
                },
                "optimize_for": {
                    "type": "string",
                    "enum": ["time", "distance", "traffic"],
                    "description": "Route optimization preference",
                    "default": "time",
                },
                "avoid": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Route features to avoid (tolls, highways, ferries)",
                },
                "departure_time": {
                    "type": "string",
                    "description": "Departure time (ISO 8601 format) for traffic-aware routing",
                },
            },
            "required": ["origin", "destination"],
        },
    ),
    Tool(
        name="search_web",
        description="Search the web using Google Search API for current information and research",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query string"},
                "search_type": {
                    "type": "string",
                    "enum": ["web", "images", "news", "videos"],
                    "description": "Type of search to perform",
                    "default": "web",
                },
                "date_range": {
                    "type": "string",
                    "enum": ["day", "week", "month", "year", "all"],
                    "description": "Time range for search results",
                    "default": "all",
                },
                "site_filter": {
                    "type": "string",
                    "description": "Limit search to specific website (e.g., 'site:reddit.com')",
                },
                "max_results": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 20,
                    "description": "Maximum number of results",
                    "default": 10,
                },
                "safe_search": {
                    "type": "boolean",
                    "description": "Enable safe search filtering",
                    "default": True,
                },
            },
            "required": ["query"],
        },
    ),
    Tool(
        name="query_airtable",
        description="Query Airtable databases for structured data retrieval and management",
        inputSchema={
            "type": "object",
            "properties": {
                "base_id": {
                    "type": "string",
                    "description": "Airtable base identifier",
                },
                "table_name": {
                    "type": "string",
                    "description": "Name of the table to query",
                },
                "filter_formula": {
                    "type": "string",
                    "description": "Airtable filter formula for record selection",
                },
                "fields": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific fields to retrieve",
                },
                "sort": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "field": {"type": "string"},
                            "direction": {"type": "string", "enum": ["asc", "desc"]},
                        },
                    },
                    "description": "Sort configuration",
                },
                "max_records": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 100,
                    "description": "Maximum records to return",
                    "default": 20,
                },
            },
            "required": ["base_id", "table_name"],
        },
    ),
    Tool(
        name="get_weather_data",
        description="Get current weather and forecast data for training planning",
        inputSchema={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "Location for weather data (city, coordinates)",
                },
                "data_type": {
                    "type": "string",
                    "enum": ["current", "forecast", "historical"],
                    "description": "Type of weather data",
                    "default": "current",
                },
                "forecast_days": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 14,
                    "description": "Number of forecast days",
                    "default": 5,
                },
                "include_hourly": {
                    "type": "boolean",
                    "description": "Include hourly forecast data",
                    "default": False,
                },
                "units": {
                    "type": "string",
                    "enum": ["metric", "imperial"],
                    "description": "Unit system for weather data",
                    "default": "metric",
                },
                "weather_alerts": {
                    "type": "boolean",
                    "description": "Include weather warnings and alerts",
                    "default": True,
                },
            },
            "required": ["location"],
        },
    ),
    Tool(
        name="search_wikipedia",
        description="Search Wikipedia for educational content and research information",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Wikipedia search query"},
                "language": {
                    "type": "string",
                    "description": "Wikipedia language code (en, es, fr, etc.)",
                    "default": "en",
                },
                "max_results": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Maximum number of articles",
                    "default": 3,
                },
                "summary_sentences": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Number of summary sentences",
                    "default": 3,
                },
                "include_images": {
                    "type": "boolean",
                    "description": "Include article images",
                    "default": False,
                },
            },
            "required": ["query"],
        },
    ),
    Tool(
        name="find_nearby_facilities",
        description="Find nearby fitness facilities, gyms, parks, or sports venues",
        inputSchema={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "Current location or search center",
                },
                "facility_type": {
                    "type": "string",
                    "enum": [
                        "gym",
                        "fitness_center",
                        "swimming_pool",
                        "running_track",
                        "tennis_court",
                        "basketball_court",
                        "park",
                        "trail",
                        "bike_path",
                    ],
                    "description": "Type of facility to search for",
                },
                "radius_km": {
                    "type": "number",
                    "minimum": 0.5,
                    "maximum": 50,
                    "description": "Search radius in kilometers",
                    "default": 5,
                },
                "price_range": {
                    "type": "string",
                    "enum": ["free", "budget", "mid_range", "premium", "any"],
                    "description": "Price range preference",
                    "default": "any",
                },
                "amenities": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Required amenities (parking, showers, equipment, etc.)",
                },
                "rating_minimum": {
                    "type": "number",
                    "minimum": 1,
                    "maximum": 5,
                    "description": "Minimum rating threshold",
                    "default": 3.0,
                },
                "open_now": {
                    "type": "boolean",
                    "description": "Only show currently open facilities",
                    "default": False,
                },
            },
            "required": ["location", "facility_type"],
        },
    ),
    Tool(
        name="calculate_travel_carbon",
        description="Calculate carbon footprint for different travel modes to training locations",
        inputSchema={
            "type": "object",
            "properties": {
                "origin": {"type": "string", "description": "Starting location"},
                "destination": {
                    "type": "string",
                    "description": "Destination location",
                },
                "travel_modes": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Travel modes to compare (car, public_transit, cycling, walking)",
                    "default": ["car", "public_transit", "cycling", "walking"],
                },
                "vehicle_type": {
                    "type": "string",
                    "enum": ["gasoline", "diesel", "hybrid", "electric", "motorcycle"],
                    "description": "Vehicle type for car travel",
                    "default": "gasoline",
                },
                "occupancy": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 8,
                    "description": "Number of people traveling",
                    "default": 1,
                },
            },
            "required": ["origin", "destination"],
        },
    ),
]


@app.list_tools()
async def list_tools() -> List[Tool]:
    """List all available external service tools."""
    return EXTERNAL_TOOLS


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """Execute external service tool calls."""
    try:
        if name == "search_locations":
            location_input = LocationSearchInput(**arguments)
            result = await azure_maps.search_locations(location_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "get_route_directions":
            result = await azure_maps.get_directions(
                arguments["origin"],
                arguments["destination"],
                travel_mode=arguments.get("travel_mode", "driving"),
                optimize_for=arguments.get("optimize_for", "time"),
            )
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "search_web":
            search_input = WebSearchInput(**arguments)
            result = await google_search.search_web(search_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "query_airtable":
            airtable_input = AirtableQueryInput(**arguments)
            result = await airtable.query_table(airtable_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "get_weather_data":
            weather_input = WeatherInput(**arguments)
            result = await weather_api.get_weather(weather_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "search_wikipedia":
            wiki_input = WikipediaSearchInput(**arguments)
            result = await wikipedia.search_articles(wiki_input)
            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "find_nearby_facilities":
            # Enhanced location search with facility-specific filters
            location = arguments["location"]
            facility_type = arguments["facility_type"]
            radius = arguments.get("radius_km", 5)

            search_query = f"{facility_type} near {location}"
            category_filters = [facility_type, "fitness", "sports"]

            result = await azure_maps.search_locations(
                LocationSearchInput(
                    query=search_query,
                    category_filter=category_filters,
                    radius_km=radius,
                    max_results=arguments.get("max_results", 10),
                    include_details=True,
                )
            )

            # Filter by additional criteria
            filtered_results = []
            for location_result in result.locations:
                rating = getattr(location_result, "rating", 3.0)
                if rating >= arguments.get("rating_minimum", 3.0):
                    filtered_results.append(location_result)

            result.locations = filtered_results[: arguments.get("max_results", 10)]

            return CallToolResult(
                content=[
                    TextContent(type="text", text=json.dumps(result.dict(), indent=2))
                ]
            )

        elif name == "calculate_travel_carbon":
            # Simplified carbon footprint calculation
            origin = arguments["origin"]
            destination = arguments["destination"]
            modes = arguments.get(
                "travel_modes", ["car", "public_transit", "cycling", "walking"]
            )

            # Get distance for calculation
            route_result = await azure_maps.get_directions(
                origin, destination, "driving"
            )
            distance_km = route_result.get("distance_km", 10)  # Default if API fails

            # Carbon factors (kg CO2 per km per person)
            carbon_factors = {
                "car": 0.21,  # Average gasoline car
                "public_transit": 0.05,  # Bus/train average
                "cycling": 0.0,
                "walking": 0.0,
                "motorcycle": 0.15,
                "electric": 0.05,  # Considering electricity grid mix
            }

            vehicle_type = arguments.get("vehicle_type", "gasoline")
            if vehicle_type == "electric":
                carbon_factors["car"] = 0.05
            elif vehicle_type == "hybrid":
                carbon_factors["car"] = 0.12

            occupancy = arguments.get("occupancy", 1)

            results = {}
            for mode in modes:
                factor = carbon_factors.get(mode, 0.21)
                carbon_per_person = distance_km * factor
                total_carbon = (
                    carbon_per_person * occupancy
                    if mode == "car"
                    else carbon_per_person
                )

                results[mode] = {
                    "distance_km": distance_km,
                    "carbon_kg_co2": round(total_carbon, 3),
                    "carbon_per_person_kg": round(carbon_per_person, 3),
                    "occupancy": occupancy if mode == "car" else 1,
                }

            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(results, indent=2))]
            )

        else:
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


@app.list_prompts()
async def list_prompts() -> List[Prompt]:
    """List available external service prompts."""
    return [
        Prompt(
            name="location_research",
            description="Research locations and facilities for training activities",
            arguments=[
                PromptArgument(
                    name="activity",
                    description="Type of training activity",
                    required=True,
                ),
                PromptArgument(
                    name="location",
                    description="General location or area",
                    required=True,
                ),
                PromptArgument(
                    name="requirements",
                    description="Specific requirements or preferences",
                    required=False,
                ),
            ],
        ),
        Prompt(
            name="travel_planning",
            description="Plan efficient travel routes for training sessions",
            arguments=[
                PromptArgument(
                    name="locations",
                    description="Training locations to visit",
                    required=True,
                ),
                PromptArgument(
                    name="constraints",
                    description="Time, budget, or other constraints",
                    required=True,
                ),
                PromptArgument(
                    name="preferences", description="Travel preferences", required=False
                ),
            ],
        ),
    ]


@app.get_prompt()
async def get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
    """Get external service prompts."""
    if name == "location_research":
        activity = arguments.get("activity", "")
        location = arguments.get("location", "")
        requirements = arguments.get("requirements", "")

        prompt_text = f"""
        Research optimal locations and facilities for training activities:
        
        Activity Type: {activity}
        Location Area: {location}
        Requirements: {requirements}
        
        Please provide:
        1. Recommended facilities with detailed information
        2. Location accessibility and transportation options
        3. Facility amenities and suitability for the activity
        4. Pricing information and membership options
        5. Operating hours and availability
        6. Safety considerations and reviews
        
        Include practical logistics for efficient training planning.
        """

        return GetPromptResult(
            description="Location and facility research",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    elif name == "travel_planning":
        locations = arguments.get("locations", "")
        constraints = arguments.get("constraints", "")
        preferences = arguments.get("preferences", "")

        prompt_text = f"""
        Plan efficient travel routes for training sessions:
        
        Training Locations: {locations}
        Constraints: {constraints}
        Preferences: {preferences}
        
        Please provide:
        1. Optimized route planning with travel times
        2. Alternative transportation options comparison
        3. Cost analysis for different travel modes
        4. Environmental impact considerations
        5. Schedule optimization recommendations
        6. Contingency planning for weather or delays
        
        Focus on efficiency, cost-effectiveness, and sustainability.
        """

        return GetPromptResult(
            description="Travel route planning",
            messages=[TextContentType(type="text", text=prompt_text)],
        )

    else:
        raise ValueError(f"Unknown prompt: {name}")


async def main():
    """Run the external MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            protocolVersion="2024-11-05",
            serverInfo={"name": "external-mcp-server", "version": "0.1.0"},
        )


if __name__ == "__main__":
    asyncio.run(main())
