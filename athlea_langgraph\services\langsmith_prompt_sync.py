"""
LangSmith Prompt Synchronization Service

Automatically synchronizes local JSON prompts to LangSmith's Prompt Engineering interface
when the LangGraph dev server starts. This ensures your prompt updates are immediately
available in the LangSmith playground for testing and iteration.
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Set
import re

from langsmith import Client
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate

from ..utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class LangSmithPromptSyncer:
    """Handles automatic synchronization of local prompts to LangSmith."""

    def __init__(self):
        self.client: Optional[Client] = None
        self.uploaded_prompts: Set[str] = set()
        self.project_name = os.getenv("LANGSMITH_PROJECT", "athlea-coaching")

    def _get_client(self) -> Optional[Client]:
        """Get or create LangSmith client."""
        if self.client is None:
            api_key = os.getenv("LANGSMITH_API_KEY")
            if not api_key:
                logger.warning("⚠️ LANGSMITH_API_KEY not set - prompt sync disabled")
                return None

            try:
                self.client = Client()
                logger.info(
                    f"📝 LangSmith prompt sync enabled - project: {self.project_name}"
                )
                return self.client
            except Exception as e:
                logger.error(f"❌ Failed to initialize LangSmith client: {e}")
                return None

        return self.client

    def _convert_json_to_langchain_prompt(self, config) -> Optional[object]:
        """Convert our JSON prompt config to LangChain prompt format."""
        try:
            # Get the system prompt content
            system_content = config.prompt.system
            if not system_content:
                return None

            # Extract template variables from the prompt text (look for {variable} patterns)
            # This finds variables like {user_input}, {context}, etc.
            variable_pattern = r"\{([^{}]+)\}"
            found_variables = re.findall(variable_pattern, system_content)

            # Check if we have user/human template content
            user_content = None
            if hasattr(config.prompt, "user_template") and config.prompt.user_template:
                user_content = config.prompt.user_template

            # Create a ChatPromptTemplate if we have user content, otherwise use system only
            if user_content:
                # If we have both system and user prompts
                prompt = ChatPromptTemplate.from_messages(
                    [("system", system_content), ("human", user_content)]
                )
            else:
                # If we only have system prompt, check if it has variables
                if found_variables:
                    # Create PromptTemplate for single prompt with variables
                    prompt = PromptTemplate(
                        template=system_content, input_variables=found_variables
                    )
                else:
                    # Create simple ChatPromptTemplate with no variables
                    prompt = ChatPromptTemplate.from_messages(
                        [("system", system_content)]
                    )

            return prompt

        except Exception as e:
            logger.error(f"Error converting prompt config: {e}")
            return None

    def _check_if_prompt_exists(self, prompt_name: str) -> bool:
        """Check if prompt already exists in LangSmith."""
        try:
            client = self._get_client()
            if not client:
                return False

            # Try to pull the prompt - if it exists, this won't raise an error
            client.pull_prompt(prompt_name)
            return True

        except Exception:
            # Prompt doesn't exist or other error
            return False

    def _get_existing_prompts(self) -> set:
        """Get list of existing prompt names in LangSmith."""
        try:
            client = self._get_client()
            if not client:
                return set()

            existing = set()
            # Get prompts from our project/organization
            for result in client.list_prompts():
                if isinstance(result, tuple) and len(result) == 2:
                    key, value = result
                    if key == "repos" and isinstance(value, list):
                        for prompt in value:
                            if hasattr(prompt, "repo_handle"):
                                existing.add(prompt.repo_handle)

            return existing
        except Exception as e:
            logger.warning(f"Could not fetch existing prompts: {e}")
            return set()

    async def sync_prompt_to_langsmith(self, prompt_name: str, prompt_config) -> bool:
        """Sync a single prompt to LangSmith."""
        try:
            client = self._get_client()
            if not client:
                return False

            # Skip if already uploaded in this session
            if prompt_name in self.uploaded_prompts:
                return True

            # Convert to LangChain format
            langchain_prompt = self._convert_json_to_langchain_prompt(prompt_config)
            if not langchain_prompt:
                logger.error(
                    f"   ❌ Failed to convert {prompt_name} to LangChain format"
                )
                return False

            # Use push_prompt (correct LangSmith API method)
            url = client.push_prompt(prompt_name, object=langchain_prompt)

            logger.info(f"   ✅ Uploaded: {prompt_name}")
            self.uploaded_prompts.add(prompt_name)
            return True

        except Exception as e:
            logger.error(f"   ❌ Upload failed for {prompt_name}: {e}")
            return False

    async def sync_all_prompts(self, force: bool = False) -> Dict[str, int]:
        """Sync all local prompts to LangSmith."""
        try:
            client = self._get_client()
            if not client:
                return {"skipped": 0, "uploaded": 0, "failed": 0}

            logger.info("🚀 Starting automatic prompt sync to LangSmith...")

            # Get existing prompts to avoid duplicates (unless force=True)
            existing_prompts = set() if force else self._get_existing_prompts()

            # Get local prompts
            prompt_loader = await get_prompt_loader()
            local_prompts = await prompt_loader.list_prompts()  # Make this await

            results = {"skipped": 0, "uploaded": 0, "failed": 0}

            for prompt_name in local_prompts:
                try:
                    # Skip if already exists and not forcing
                    if not force and prompt_name in existing_prompts:
                        logger.debug(f"   ⏭️ Skipping {prompt_name} (already exists)")
                        results["skipped"] += 1
                        continue

                    # Load prompt config
                    prompt_config = await prompt_loader.load_prompt(prompt_name)

                    # Sync to LangSmith
                    success = await self.sync_prompt_to_langsmith(
                        prompt_name, prompt_config
                    )

                    if success:
                        results["uploaded"] += 1
                    else:
                        results["failed"] += 1

                except Exception as e:
                    logger.error(f"   ❌ Error processing {prompt_name}: {e}")
                    results["failed"] += 1

            total = results["uploaded"] + results["failed"] + results["skipped"]
            logger.info(
                f"✅ Prompt sync complete: {results['uploaded']} uploaded, {results['skipped']} skipped, {results['failed']} failed (Total: {total})"
            )

            return results

        except Exception as e:
            logger.error(f"❌ Prompt sync failed: {e}")
            return {"skipped": 0, "uploaded": 0, "failed": 0}

    def sync_prompt_sync(self, prompt_name: str, prompt_config) -> bool:
        """Synchronous version of sync_prompt_to_langsmith."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.sync_prompt_to_langsmith(prompt_name, prompt_config)
            )
        finally:
            loop.close()

    def sync_all_prompts_sync(self, force: bool = False) -> Dict[str, int]:
        """Synchronous version of sync_all_prompts for CLI usage."""
        try:
            client = self._get_client()
            if not client:
                return {"skipped": 0, "uploaded": 0, "failed": 0}

            logger.info("🚀 Starting manual prompt sync to LangSmith...")

            # Get existing prompts to avoid duplicates (unless force=True)
            existing_prompts = set() if force else self._get_existing_prompts()

            # Get local prompts using sync loader - FIX: Use sync method
            from ..utils.prompt_loader import PromptLoader

            prompt_loader = PromptLoader()
            local_prompts = prompt_loader.list_prompts_sync()  # Use sync method

            results = {"skipped": 0, "uploaded": 0, "failed": 0}

            for prompt_name in local_prompts:
                try:
                    # Skip if already exists and not forcing
                    if not force and prompt_name in existing_prompts:
                        logger.info(f"⏭️  Skipping {prompt_name} (already exists)")
                        results["skipped"] += 1
                        continue

                    # Load and convert the prompt - FIX: Use sync method
                    config = prompt_loader.load_prompt_sync(prompt_name)
                    langchain_prompt = self._convert_json_to_langchain_prompt(config)

                    if not langchain_prompt:
                        logger.warning(
                            f"❌ Failed to convert {prompt_name} to LangChain format"
                        )
                        results["failed"] += 1
                        continue

                    # Upload to LangSmith using push_prompt
                    # Correct API: push_prompt(prompt_identifier, *, object=..., **kwargs)
                    client.push_prompt(
                        prompt_name,  # prompt_identifier as positional argument
                        object=langchain_prompt,  # LangChain prompt object as keyword argument
                        description=config.metadata.description,
                        tags=[
                            config.metadata.prompt_type.value,
                            "athlea-coaching",
                            "auto-sync",
                        ],
                        is_public=False,  # Keep prompts private
                    )

                    logger.info(f"✅ Uploaded {prompt_name} to LangSmith")
                    results["uploaded"] += 1

                except Exception as e:
                    logger.error(f"❌ Failed to sync {prompt_name}: {e}")
                    results["failed"] += 1

            logger.info(
                f"✅ Prompt sync complete: {results['uploaded']} uploaded, {results['skipped']} skipped, {results['failed']} failed"
            )
            return results

        except Exception as e:
            logger.error(f"❌ Error during prompt sync: {e}")
            return {"skipped": 0, "uploaded": 0, "failed": 0}


# Global syncer instance
_prompt_syncer: Optional[LangSmithPromptSyncer] = None


def get_prompt_syncer() -> LangSmithPromptSyncer:
    """Get or create global prompt syncer instance."""
    global _prompt_syncer
    if _prompt_syncer is None:
        _prompt_syncer = LangSmithPromptSyncer()
    return _prompt_syncer


async def auto_sync_prompts_on_startup() -> None:
    """Automatically sync prompts to LangSmith on startup."""
    try:
        syncer = get_prompt_syncer()

        # Check if LangSmith is available
        if not syncer._get_client():
            logger.info("⏭️ LangSmith not configured, skipping prompt sync")
            return

        logger.info("🔄 Auto-syncing prompts to LangSmith on startup...")

        # Sync all prompts (skip existing ones)
        results = await syncer.sync_all_prompts(force=False)

        if results["uploaded"] > 0:
            logger.info(
                f"🎉 Successfully uploaded {results['uploaded']} new prompts to LangSmith!"
            )

    except Exception as e:
        logger.error(f"❌ Auto-sync failed: {e}")
