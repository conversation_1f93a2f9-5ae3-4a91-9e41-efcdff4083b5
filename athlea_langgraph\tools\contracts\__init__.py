"""
Tool-Agent Contract Enforcement System

This module implements strict schema validation and access control for tool-agent
interactions. It enforces that agents can only access tools within their domain
and that all tool calls are properly validated.

Following Proposal 2B: Implement Tool-Agent Contracts
"""

from .access_control import AgentToolAccessController
from .contract_manager import ToolContractManager
from .validators import ToolInputValidator, ToolOutputValidator

__all__ = [
    "ToolContractManager",
    "ToolInputValidator",
    "ToolOutputValidator",
    "AgentToolAccessController",
]
