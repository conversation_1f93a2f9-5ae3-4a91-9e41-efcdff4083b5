"""
Data models for externalized prompt management system.

This module defines the core data structures for storing and managing
prompts with versioning, metadata, and validation capabilities.
"""

import json
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


class PromptType(Enum):
    """Types of prompts in the system."""

    COACH = "coach"
    REASONING = "reasoning"
    PLANNING = "planning"
    ONBOARDING = "onboarding"
    SYSTEM = "system"
    REACT = "react"


@dataclass
class ChangelogEntry:
    """Represents a single change in the prompt's history."""

    version: str
    date: str  # ISO format datetime string
    changes: str
    author: str
    breaking_changes: bool = False


@dataclass
class PromptExample:
    """Example input/output pair for prompt demonstration."""

    input: str = None
    output: str = None
    description: Optional[str] = None

    # Support both old (input/output) and new (user/assistant) formats
    user: str = None
    assistant: str = None
    assistant_tool_call: Optional[Dict[str, Any]] = None
    assistant_response_after_tool: Optional[str] = None

    def __post_init__(self):
        """Normalize the example to use input/output internally."""
        # If user/assistant format is provided, convert to input/output
        if self.user is not None and self.input is None:
            self.input = self.user
        if self.assistant is not None and self.output is None:
            # Combine assistant response with tool call info if present
            response = self.assistant
            if self.assistant_tool_call:
                response += f"\n\n[Tool Call: {self.assistant_tool_call.get('tool_name', 'unknown')}]"
            if self.assistant_response_after_tool:
                response += f"\n\n{self.assistant_response_after_tool}"
            self.output = response

        # Ensure we have input and output
        if self.input is None:
            self.input = ""
        if self.output is None:
            self.output = ""


@dataclass
class PromptValidation:
    """Validation rules for prompt content."""

    required_context: List[str] = field(default_factory=list)
    max_length: Optional[int] = None
    min_length: Optional[int] = None
    required_fields: List[str] = field(default_factory=list)
    allowed_variables: List[str] = field(default_factory=list)


@dataclass
class PromptVariables:
    """Model configuration variables associated with the prompt."""

    temperature: float = 0.7
    max_tokens: int = 4000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: List[str] = field(default_factory=list)


@dataclass
class PromptMetadata:
    """Metadata information for prompt tracking and management."""

    name: str
    version: str
    description: str
    author: str
    created_at: str  # ISO format datetime string
    updated_at: str  # ISO format datetime string
    prompt_type: PromptType
    tags: List[str] = field(default_factory=list)
    changelog: List[ChangelogEntry] = field(default_factory=list)
    deprecated: bool = False
    experimental: bool = False


@dataclass
class PromptContent:
    """The actual prompt content and templates."""

    system: str
    context_template: Optional[str] = None
    user_template: Optional[str] = None
    examples: List[PromptExample] = field(default_factory=list)
    instructions: Optional[str] = None
    constraints: List[str] = field(default_factory=list)


@dataclass
class PromptConfig:
    """Complete prompt configuration including all components."""

    metadata: PromptMetadata
    prompt: PromptContent
    variables: PromptVariables = field(default_factory=PromptVariables)
    validation: PromptValidation = field(default_factory=PromptValidation)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the prompt config to a dictionary for JSON serialization."""

        def convert_dataclass(obj):
            if hasattr(obj, "__dataclass_fields__"):
                result = {}
                for field_name, field_def in obj.__dataclass_fields__.items():
                    value = getattr(obj, field_name)
                    if isinstance(value, Enum):
                        result[field_name] = value.value
                    elif hasattr(value, "__dataclass_fields__"):
                        result[field_name] = convert_dataclass(value)
                    elif isinstance(value, list):
                        result[field_name] = [
                            (
                                convert_dataclass(item)
                                if hasattr(item, "__dataclass_fields__")
                                else item
                            )
                            for item in value
                        ]
                    else:
                        result[field_name] = value
                return result
            return obj

        return convert_dataclass(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PromptConfig":
        """Create a PromptConfig from a dictionary (loaded from JSON)."""
        # Convert metadata
        metadata_data = data["metadata"]
        metadata_data["prompt_type"] = PromptType(metadata_data["prompt_type"])

        changelog = []
        for entry_data in metadata_data.get("changelog", []):
            changelog.append(ChangelogEntry(**entry_data))
        metadata_data["changelog"] = changelog

        metadata = PromptMetadata(**metadata_data)

        # Convert prompt content
        prompt_data = data["prompt"]
        examples = []
        for example_data in prompt_data.get("examples", []):
            examples.append(PromptExample(**example_data))
        prompt_data["examples"] = examples

        prompt = PromptContent(**prompt_data)

        # Convert variables and validation
        variables = PromptVariables(**data.get("variables", {}))
        validation = PromptValidation(**data.get("validation", {}))

        return cls(
            metadata=metadata, prompt=prompt, variables=variables, validation=validation
        )

    def to_json(self, indent: int = 2) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)

    @classmethod
    def from_json(cls, json_str: str) -> "PromptConfig":
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)

    def validate(self) -> List[str]:
        """Validate the prompt configuration and return any errors."""
        errors = []

        # Validate version format (semantic versioning)
        version_parts = self.metadata.version.split(".")
        if len(version_parts) != 3:
            errors.append(
                f"Version must be in MAJOR.MINOR.PATCH format, got: {self.metadata.version}"
            )
        else:
            try:
                [int(part) for part in version_parts]
            except ValueError:
                errors.append(
                    f"Version parts must be integers, got: {self.metadata.version}"
                )

        # Validate prompt content
        if not self.prompt.system.strip():
            errors.append("System prompt cannot be empty")

        # Validate content length
        if (
            self.validation.min_length
            and len(self.prompt.system) < self.validation.min_length
        ):
            errors.append(
                f"Prompt too short: {len(self.prompt.system)} < {self.validation.min_length}"
            )

        if (
            self.validation.max_length
            and len(self.prompt.system) > self.validation.max_length
        ):
            errors.append(
                f"Prompt too long: {len(self.prompt.system)} > {self.validation.max_length}"
            )

        # Validate temperature range
        if not 0.0 <= self.variables.temperature <= 2.0:
            errors.append(
                f"Temperature must be between 0.0 and 2.0, got: {self.variables.temperature}"
            )

        # Validate max_tokens
        if self.variables.max_tokens <= 0:
            errors.append(
                f"max_tokens must be positive, got: {self.variables.max_tokens}"
            )

        return errors

    def get_rendered_prompt(self, context: Dict[str, Any] = None) -> str:
        """Get the rendered prompt with context variables substituted."""
        context = context or {}

        # Start with system prompt
        rendered = self.prompt.system

        # Add context template if available
        if self.prompt.context_template and context:
            try:
                context_section = self.prompt.context_template.format(**context)
                rendered += f"\n\n{context_section}"
            except KeyError as e:
                # Missing context variable - return base prompt
                pass

        # Add examples if available
        if self.prompt.examples:
            examples_section = "\n\nExamples:"
            for i, example in enumerate(self.prompt.examples, 1):
                examples_section += f"\n\nExample {i}:"
                examples_section += f"\nInput: {example.input}"
                examples_section += f"\nOutput: {example.output}"
                if example.description:
                    examples_section += f"\nNote: {example.description}"
            rendered += examples_section

        # Add instructions if available
        if self.prompt.instructions:
            rendered += f"\n\nInstructions:\n{self.prompt.instructions}"

        # Add constraints if available
        if self.prompt.constraints:
            constraints_section = "\n\nConstraints:"
            for constraint in self.prompt.constraints:
                constraints_section += f"\n- {constraint}"
            rendered += constraints_section

        return rendered
