"""
Formatting utilities for CLI output.
"""

from datetime import datetime
from typing import List

import click

from athlea_langgraph.utils.prompt_models import PromptConfig


def format_prompt_list(prompts: List[str], verbose: bool = False) -> str:
    """Format a list of prompt names for display."""
    if not prompts:
        return "No prompts found."

    if verbose:
        # Group by type for verbose output
        coaches = [p for p in prompts if "coach" in p]
        reasoning = [
            p
            for p in prompts
            if any(
                word in p for word in ["template", "extraction", "completion", "system"]
            )
        ]
        system = [p for p in prompts if p.startswith(("goal", "sample"))]
        other = [p for p in prompts if p not in coaches + reasoning + system]

        output = []
        if coaches:
            output.append("🏋️  Coach Prompts:")
            output.extend([f"   • {p}" for p in sorted(coaches)])

        if reasoning:
            output.append("🧠 Reasoning Prompts:")
            output.extend([f"   • {p}" for p in sorted(reasoning)])

        if system:
            output.append("⚙️  System Prompts:")
            output.extend([f"   • {p}" for p in sorted(system)])

        if other:
            output.append("📄 Other Prompts:")
            output.extend([f"   • {p}" for p in sorted(other)])

        return "\n".join(output)
    else:
        # Simple list for non-verbose output
        return "\n".join([f"• {p}" for p in sorted(prompts)])


def format_prompt_details(config: PromptConfig, verbose: bool = False) -> str:
    """Format prompt configuration details for display."""
    output = []

    # Header
    output.append("=" * 60)
    output.append(f"📝 Prompt: {config.metadata.name}")
    output.append("=" * 60)

    # Metadata
    output.append("📊 METADATA")
    output.append("-" * 20)
    output.append(f"Name: {config.metadata.name}")
    output.append(f"Version: {config.metadata.version}")
    output.append(f"Type: {config.metadata.prompt_type.value}")
    output.append(f"Tags: {', '.join(config.metadata.tags)}")
    output.append(f"Author: {config.metadata.author}")
    output.append(f"Created: {config.metadata.created_at}")
    output.append(f"Updated: {config.metadata.updated_at}")

    if config.metadata.description:
        output.append(f"Description: {config.metadata.description}")

    if config.metadata.deprecated:
        output.append("⚠️  Status: DEPRECATED")
    elif config.metadata.experimental:
        output.append("🧪 Status: EXPERIMENTAL")
    else:
        output.append("✅ Status: Stable")

    # Variables
    if verbose and config.variables:
        output.append("\n⚙️  VARIABLES")
        output.append("-" * 20)
        if config.variables.temperature != 0.7:
            output.append(f"Temperature: {config.variables.temperature}")
        if config.variables.max_tokens != 4000:
            output.append(f"Max Tokens: {config.variables.max_tokens}")
        if config.variables.top_p != 1.0:
            output.append(f"Top P: {config.variables.top_p}")
        if config.variables.frequency_penalty != 0.0:
            output.append(f"Frequency Penalty: {config.variables.frequency_penalty}")
        if config.variables.presence_penalty != 0.0:
            output.append(f"Presence Penalty: {config.variables.presence_penalty}")
        if config.variables.stop_sequences:
            output.append(f"Stop Sequences: {config.variables.stop_sequences}")

    # Validation
    if verbose and config.validation:
        output.append("\n🔍 VALIDATION")
        output.append("-" * 20)
        output.append(f"Min Length: {config.validation.min_length}")
        output.append(f"Max Length: {config.validation.max_length}")
        if config.validation.required_context:
            output.append(
                f"Required Context: {', '.join(config.validation.required_context)}"
            )
        if config.validation.required_fields:
            output.append(
                f"Required Fields: {', '.join(config.validation.required_fields)}"
            )

    # Changelog
    if verbose and config.metadata.changelog:
        output.append("\n📚 CHANGELOG")
        output.append("-" * 20)
        for entry in config.metadata.changelog[-3:]:  # Show last 3 entries
            date_str = (
                entry.date.strftime("%Y-%m-%d")
                if isinstance(entry.date, datetime)
                else entry.date
            )
            output.append(f"v{entry.version} ({date_str}) - {entry.changes}")

    # Prompt content
    output.append("\n📋 PROMPT CONTENT")
    output.append("-" * 20)
    content = config.prompt.system
    if len(content) > 500 and not verbose:
        content = content[:500] + "..."
    output.append(content)

    if config.prompt.examples and verbose:
        output.append("\n💡 EXAMPLES")
        output.append("-" * 20)
        for i, example in enumerate(config.prompt.examples, 1):
            output.append(f"Example {i}:")
            output.append(f"  Input: {example.input}")
            output.append(f"  Output: {example.output}")
            if example.explanation:
                output.append(f"  Notes: {example.explanation}")

    return "\n".join(output)


def format_version_list(versions: List[str], current: str = None) -> str:
    """Format a list of prompt versions."""
    if not versions:
        return "No versions found."

    output = []
    for version in sorted(versions, reverse=True):
        marker = " (current)" if version == current else ""
        output.append(f"• v{version}{marker}")

    return "\n".join(output)


def format_diff(
    old_content: str, new_content: str, old_version: str, new_version: str
) -> str:
    """Format a diff between two prompt versions."""
    # Simple diff - in a real implementation, you might use difflib
    output = []
    output.append(f"Comparing v{old_version} → v{new_version}")
    output.append("=" * 50)

    old_lines = old_content.split("\n")
    new_lines = new_content.split("\n")

    # Very basic diff - show first few different lines
    output.append("Changes detected:")
    if len(old_lines) != len(new_lines):
        output.append(f"• Line count: {len(old_lines)} → {len(new_lines)}")

    for i, (old_line, new_line) in enumerate(zip(old_lines[:10], new_lines[:10])):
        if old_line != new_line:
            output.append(f"• Line {i+1}:")
            output.append(f"  - {old_line[:80]}")
            output.append(f"  + {new_line[:80]}")

    return "\n".join(output)


def format_stats(stats: dict) -> str:
    """Format system statistics."""
    output = []
    output.append("📊 Prompt System Statistics")
    output.append("=" * 40)

    for key, value in stats.items():
        # Format key nicely
        formatted_key = key.replace("_", " ").title()
        output.append(f"{formatted_key}: {value}")

    return "\n".join(output)
