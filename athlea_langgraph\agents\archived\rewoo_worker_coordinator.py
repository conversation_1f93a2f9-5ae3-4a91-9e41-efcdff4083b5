"""
ReWOO Worker Coordinator for Athlea Coaching System.

Implements the "Worker" phase of the ReWOO pattern, coordinating parallel
execution of coaching agents without observation loops for optimal performance.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from athlea_langgraph.agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    strength_coach_node,
)
from athlea_langgraph.states.rewoo_state import (
    ReWOOConfig,
    ReWOOState,
    Task,
    WorkerResult,
)

logger = logging.getLogger(__name__)


class ReWOOWorkerCoordinator:
    """
    ReWOO Worker Coordinator for parallel coaching agent execution.

    Manages the parallel execution of specialized coaching agents without
    observation loops, enabling efficient multi-domain coaching responses.
    """

    def __init__(self, config: Optional[ReWOOConfig] = None):
        """
        Initialize the ReWOO Worker Coordinator.

        Args:
            config: ReWOO configuration settings
        """
        self.config = config or {}

        # Map domain types to their corresponding coach nodes
        self.worker_agents = {
            "strength": strength_coach_node,
            "nutrition": nutrition_coach_node,
            "cardio": cardio_coach_node,
            "recovery": recovery_coach_node,
            "mental": mental_coach_node,
            "cycling": cycling_coach_node,
        }

    async def execute_workers(self, state: ReWOOState) -> ReWOOState:
        """
        Execute worker agents in parallel for their assigned tasks.

        Args:
            state: Current ReWOO state with execution plan

        Returns:
            Updated state with worker results
        """

        execution_start_time = time.time()
        logger.info("🚀 ReWOO Workers: Starting parallel task execution")

        if not state.get("execution_plan") or not state.get("active_tasks"):
            logger.warning("⚠️ No execution plan or tasks found")
            return state

        execution_plan = state["execution_plan"]
        tasks = state["active_tasks"]

        try:
            # Execute tasks based on strategy
            if execution_plan["execution_strategy"] == "parallel":
                worker_results = await self._execute_parallel(tasks, state)
            elif execution_plan["execution_strategy"] == "sequential":
                worker_results = await self._execute_sequential(tasks, state)
            else:  # hybrid
                worker_results = await self._execute_hybrid(tasks, state)

            # Update execution status
            execution_status = {}
            for task_id, result in worker_results.items():
                execution_status[task_id] = (
                    "completed" if result["success"] else "failed"
                )

            execution_time = time.time() - execution_start_time
            logger.info(
                f"✅ ReWOO Workers: Completed {len(worker_results)} tasks in {execution_time:.2f}s"
            )

            # Create summary message
            successful_tasks = sum(1 for r in worker_results.values() if r["success"])
            failed_tasks = len(worker_results) - successful_tasks

            summary_message = f"🔄 Parallel execution complete: {successful_tasks} successful, {failed_tasks} failed tasks"

            return {
                **state,
                "worker_results": worker_results,
                "parallel_execution_status": execution_status,
                "execution_start_time": execution_start_time,
                "messages": state["messages"] + [AIMessage(content=summary_message)],
            }

        except Exception as e:
            logger.error(f"❌ ReWOO Workers execution error: {str(e)}")
            return await self._handle_execution_failure(state, str(e))

    async def _execute_parallel(
        self, tasks: List[Task], state: ReWOOState
    ) -> Dict[str, WorkerResult]:
        """
        Execute all tasks in parallel.

        Args:
            tasks: List of tasks to execute
            state: Current state

        Returns:
            Dictionary of task results
        """

        logger.info(f"⚡ Executing {len(tasks)} tasks in parallel")

        # Create async tasks for each worker
        async_tasks = []
        for task in tasks:
            async_task = asyncio.create_task(
                self._execute_single_task(task, state),
                name=f"worker_{task['task_type']}_{task['task_id']}",
            )
            async_tasks.append(async_task)

        # Wait for all tasks to complete with timeout
        timeout = self.config.get("task_timeout", 30)
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*async_tasks, return_exceptions=True),
                timeout=timeout * len(tasks),  # Scale timeout with number of tasks
            )

            # Process results
            worker_results = {}
            for i, result in enumerate(results):
                task = tasks[i]
                if isinstance(result, Exception):
                    worker_results[task["task_id"]] = self._create_error_result(
                        task, str(result)
                    )
                else:
                    worker_results[task["task_id"]] = result

            return worker_results

        except asyncio.TimeoutError:
            logger.warning(
                f"⏰ Parallel execution timeout after {timeout * len(tasks)}s"
            )
            # Cancel remaining tasks and return partial results
            for task in async_tasks:
                if not task.done():
                    task.cancel()

            # Create timeout results for incomplete tasks
            worker_results = {}
            for i, task in enumerate(tasks):
                if i < len(results) and not isinstance(results[i], Exception):
                    worker_results[task["task_id"]] = results[i]
                else:
                    worker_results[task["task_id"]] = self._create_timeout_result(task)

            return worker_results

    async def _execute_sequential(
        self, tasks: List[Task], state: ReWOOState
    ) -> Dict[str, WorkerResult]:
        """
        Execute tasks sequentially (fallback mode).

        Args:
            tasks: List of tasks to execute
            state: Current state

        Returns:
            Dictionary of task results
        """

        logger.info(f"🔄 Executing {len(tasks)} tasks sequentially")

        worker_results = {}

        # Sort tasks by priority (highest first)
        sorted_tasks = sorted(tasks, key=lambda t: t["priority"], reverse=True)

        for task in sorted_tasks:
            try:
                result = await self._execute_single_task(task, state)
                worker_results[task["task_id"]] = result

                # Update state with intermediate results for context
                state = {
                    **state,
                    "worker_results": {
                        **state.get("worker_results", {}),
                        **worker_results,
                    },
                }

            except Exception as e:
                logger.error(f"❌ Sequential task {task['task_id']} failed: {str(e)}")
                worker_results[task["task_id"]] = self._create_error_result(
                    task, str(e)
                )

        return worker_results

    async def _execute_hybrid(
        self, tasks: List[Task], state: ReWOOState
    ) -> Dict[str, WorkerResult]:
        """
        Execute tasks using hybrid parallel/sequential strategy.

        Args:
            tasks: List of tasks to execute
            state: Current state

        Returns:
            Dictionary of task results
        """

        max_parallel = self.config.get("max_parallel_workers", 4)
        logger.info(
            f"🔀 Executing {len(tasks)} tasks in hybrid mode (max {max_parallel} parallel)"
        )

        worker_results = {}

        # Sort tasks by priority
        sorted_tasks = sorted(tasks, key=lambda t: t["priority"], reverse=True)

        # Execute in batches
        for i in range(0, len(sorted_tasks), max_parallel):
            batch = sorted_tasks[i : i + max_parallel]

            logger.info(
                f"📦 Processing batch {i//max_parallel + 1}: {len(batch)} tasks"
            )

            # Execute batch in parallel
            batch_results = await self._execute_parallel(batch, state)
            worker_results.update(batch_results)

            # Update state with batch results for context in next batch
            state = {
                **state,
                "worker_results": {**state.get("worker_results", {}), **batch_results},
            }

        return worker_results

    async def _execute_single_task(self, task: Task, state: ReWOOState) -> WorkerResult:
        """
        Execute a single task with a specific worker agent.

        Args:
            task: Task to execute
            state: Current state

        Returns:
            Worker result
        """

        start_time = time.time()
        task_id = task["task_id"]
        task_type = task["task_type"]

        logger.info(f"🔧 Executing task {task_id} ({task_type})")

        try:
            # Get the appropriate worker agent
            worker_func = self.worker_agents.get(task_type)
            if not worker_func:
                raise ValueError(
                    f"No worker agent available for task type: {task_type}"
                )

            # Create task-specific state
            task_state = self._create_task_state(task, state)

            # Execute the worker agent
            result_state = await worker_func(task_state)

            # Extract result from the worker's response
            worker_response = self._extract_worker_response(result_state)

            execution_time = time.time() - start_time

            # Create successful result
            result = WorkerResult(
                {
                    "task_id": task_id,
                    "worker_agent": task["worker_agent"],
                    "success": True,
                    "result": worker_response,
                    "confidence_score": self._calculate_confidence(result_state),
                    "execution_time": execution_time,
                    "tools_used": self._extract_tools_used(result_state),
                    "warnings": self._extract_warnings(result_state),
                    "error_message": None,
                }
            )

            logger.info(
                f"✅ Task {task_id} completed successfully in {execution_time:.2f}s"
            )
            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"❌ Task {task_id} failed after {execution_time:.2f}s: {str(e)}"
            )
            return self._create_error_result(task, str(e), execution_time)

    def _create_task_state(
        self, task: Task, global_state: ReWOOState
    ) -> Dict[str, Any]:
        """
        Create a task-specific state for worker execution.

        Args:
            task: Task definition
            global_state: Global ReWOO state

        Returns:
            Task-specific state for worker agent
        """

        # Create a focused state for the worker agent
        task_state = {
            "messages": [HumanMessage(content=task["description"])],
            "user_query": task["description"],
            "user_profile": global_state.get("user_profile", {}),
            "routing_decision": task["task_type"],
            "pending_agents": [],
            "plan": [],
            "current_step": 0,
            "domain_contributions": {},
            "required_domains": [task["task_type"]],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": True,
            # Task-specific metadata
            "task_metadata": {
                "task_id": task["task_id"],
                "task_type": task["task_type"],
                "priority": task["priority"],
                "original_query": global_state.get("user_query"),
            },
        }

        return task_state

    def _extract_worker_response(self, result_state: Dict[str, Any]) -> str:
        """
        Extract the worker's response from the result state.

        Args:
            result_state: State returned by worker agent

        Returns:
            Worker's response text
        """

        messages = result_state.get("messages", [])
        if messages:
            # Get the last AI message
            for message in reversed(messages):
                if hasattr(message, "content") and isinstance(message, AIMessage):
                    return message.content

        # Fallback: look for other response fields
        return (
            result_state.get("final_response", "")
            or result_state.get("response", "")
            or result_state.get("aggregated_plan", "")
            or "Worker completed task but no response found"
        )

    def _calculate_confidence(self, result_state: Dict[str, Any]) -> float:
        """
        Calculate confidence score for worker result.

        Args:
            result_state: State returned by worker agent

        Returns:
            Confidence score (0.0-1.0)
        """

        # Base confidence
        confidence = 0.8

        # Adjust based on response length (longer responses often more comprehensive)
        response = self._extract_worker_response(result_state)
        if len(response) > 200:
            confidence += 0.1
        elif len(response) < 50:
            confidence -= 0.2

        # Adjust based on tools used (more tools often indicate thorough analysis)
        tools_used = self._extract_tools_used(result_state)
        if len(tools_used) > 1:
            confidence += 0.1

        # Check for warnings (warnings might indicate issues)
        warnings = self._extract_warnings(result_state)
        if warnings:
            confidence -= 0.1 * len(warnings)

        return max(0.1, min(1.0, confidence))

    def _extract_tools_used(self, result_state: Dict[str, Any]) -> List[str]:
        """
        Extract list of tools used by the worker.

        Args:
            result_state: State returned by worker agent

        Returns:
            List of tool names used
        """

        # Look for tool usage in various places
        tools_used = []

        # Check if there's a tools_used field
        if "tools_used" in result_state:
            tools_used.extend(result_state["tools_used"])

        # Check messages for tool calls (simplified detection)
        messages = result_state.get("messages", [])
        for message in messages:
            if hasattr(message, "additional_kwargs") and message.additional_kwargs:
                if "tool_calls" in message.additional_kwargs:
                    tool_calls = message.additional_kwargs["tool_calls"]
                    for tool_call in tool_calls:
                        if "function" in tool_call:
                            tools_used.append(
                                tool_call["function"].get("name", "unknown")
                            )

        return list(set(tools_used))  # Remove duplicates

    def _extract_warnings(self, result_state: Dict[str, Any]) -> List[str]:
        """
        Extract warnings from worker execution.

        Args:
            result_state: State returned by worker agent

        Returns:
            List of warning messages
        """

        warnings = []

        # Check for explicit warnings field
        if "warnings" in result_state:
            warnings.extend(result_state["warnings"])

        # Check for safety concerns or limitations mentioned
        response = self._extract_worker_response(result_state)
        response_lower = response.lower()

        warning_keywords = [
            "warning",
            "caution",
            "be careful",
            "consult",
            "doctor",
            "injury",
            "pain",
        ]
        for keyword in warning_keywords:
            if keyword in response_lower:
                warnings.append(f"Response contains {keyword}")
                break

        return warnings

    def _create_error_result(
        self, task: Task, error_message: str, execution_time: float = 0.0
    ) -> WorkerResult:
        """
        Create an error result for a failed task.

        Args:
            task: Task that failed
            error_message: Error message
            execution_time: Time spent before failure

        Returns:
            Error WorkerResult
        """

        return WorkerResult(
            {
                "task_id": task["task_id"],
                "worker_agent": task["worker_agent"],
                "success": False,
                "result": f"Task failed: {error_message}",
                "confidence_score": 0.0,
                "execution_time": execution_time,
                "tools_used": [],
                "warnings": ["Task execution failed"],
                "error_message": error_message,
            }
        )

    def _create_timeout_result(self, task: Task) -> WorkerResult:
        """
        Create a timeout result for a task that exceeded time limit.

        Args:
            task: Task that timed out

        Returns:
            Timeout WorkerResult
        """

        timeout = self.config.get("task_timeout", 30)
        return WorkerResult(
            {
                "task_id": task["task_id"],
                "worker_agent": task["worker_agent"],
                "success": False,
                "result": f"Task timed out after {timeout} seconds",
                "confidence_score": 0.0,
                "execution_time": timeout,
                "tools_used": [],
                "warnings": ["Task execution timeout"],
                "error_message": f"Timeout after {timeout}s",
            }
        )

    async def _handle_execution_failure(
        self, state: ReWOOState, error_message: str
    ) -> ReWOOState:
        """
        Handle complete execution failure.

        Args:
            state: Current state
            error_message: Error that occurred

        Returns:
            State with error handling
        """

        logger.error(f"🚨 Complete execution failure: {error_message}")

        # Create failure results for all tasks
        worker_results = {}
        execution_status = {}

        for task in state.get("active_tasks", []):
            worker_results[task["task_id"]] = self._create_error_result(
                task, error_message
            )
            execution_status[task["task_id"]] = "failed"

        return {
            **state,
            "worker_results": worker_results,
            "parallel_execution_status": execution_status,
            "messages": state["messages"]
            + [
                AIMessage(
                    content=f"❌ Parallel execution failed: {error_message}. Falling back to simplified response."
                )
            ],
        }


async def rewoo_worker_coordinator_node(state: ReWOOState) -> ReWOOState:
    """
    ReWOO Worker Coordinator node for LangGraph integration.

    Args:
        state: Current ReWOO state

    Returns:
        Updated state with worker results
    """

    config = state.get("coordination_metadata", {}).get("config")
    coordinator = ReWOOWorkerCoordinator(config)

    return await coordinator.execute_workers(state)
