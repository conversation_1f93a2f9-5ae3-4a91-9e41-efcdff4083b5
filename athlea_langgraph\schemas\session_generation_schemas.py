"""
Session Generation Tool Schemas

Pydantic models for validating inputs and outputs of session generation tools.
Supports strength training, running, cycling, and recovery session generation.
"""

from datetime import datetime
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator

# --- Base Models ---


class Position(BaseModel):
    """Represents a geographical position."""

    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lon: float = Field(..., ge=-180, le=180, description="Longitude")


class SessionSegment(BaseModel):
    """Base class for session segments."""

    id: str = Field(..., description="Unique segment identifier")
    session_id: str = Field(..., description="Parent session identifier")
    segment_order: int = Field(..., ge=1, description="Order in session")
    name: str = Field(..., description="Segment name")
    segment_type: str = Field(..., description="Type of segment")
    repeat_count: int = Field(default=1, ge=1, description="Number of repetitions")
    segment_description: Optional[str] = Field(None, description="Detailed description")


# --- Strength Training Models ---


class StrengthSegment(SessionSegment):
    """Strength training session segment."""

    sets: int = Field(..., ge=1, description="Number of sets")
    reps: str = Field(
        ..., description="Repetitions (e.g., '8-10' or 'Hold for 30-60 seconds')"
    )
    intensity: str = Field(
        ..., description="Intensity specification (e.g., '75% 1RM or RPE 8')"
    )
    rest_period: str = Field(..., description="Rest period between sets")


class StrengthSession(BaseModel):
    """Complete strength training session."""

    id: str = Field(..., description="Unique session identifier")
    session_name: str = Field(..., description="Session name")
    session_description: str = Field(..., description="Session description")
    duration: int = Field(..., ge=1, description="Session duration in minutes")
    skill_category: str = Field(..., description="Skill category")
    training_category: str = Field(..., description="Training focus category")
    intensity_measure: str = Field(..., description="Intensity measurement method")
    stress_level_measure: str = Field(
        ..., description="Stress level measurement method"
    )
    intensity_level: int = Field(..., ge=1, le=10, description="Intensity level (1-10)")
    stress_level: int = Field(..., ge=1, le=10, description="Stress level (1-10)")
    coaching_points: str = Field(..., description="Coaching guidance")
    common_errors: str = Field(..., description="Common mistakes to avoid")
    exercise_distribution: str = Field(..., description="Exercise distribution pattern")
    program_id: str = Field(..., description="Associated program identifier")
    name_from_program_description: str = Field(..., description="Program name")
    segments: List[StrengthSegment] = Field(..., description="Session segments")


# --- Running Models ---


class RunningSegment(SessionSegment):
    """Running session segment."""

    duration: str = Field(..., description="Segment duration (HH:MM:SS format)")
    intensity: str = Field(..., description="Intensity percentage or specification")
    cadence: str = Field(..., description="Cadence specification")


class RunningSession(BaseModel):
    """Complete running session."""

    id: str = Field(..., description="Unique session identifier")
    session_type: Literal["run"] = Field(default="run", description="Session type")
    run_category: str = Field(..., description="Running category")
    session_name: str = Field(..., description="Session name")
    session_description: str = Field(..., description="Session description")
    duration: str = Field(..., description="Total duration (HH:MM:SS format)")
    distance: float = Field(..., gt=0, description="Distance in kilometers")
    skill_category: str = Field(..., description="Skill level category")
    intensity_measure: str = Field(..., description="Intensity measurement method")
    stress_level_measure: str = Field(
        ..., description="Stress level measurement method"
    )
    intensity_level: str = Field(..., description="Intensity level specification")
    stress_level: int = Field(..., ge=1, le=10, description="Stress level (1-10)")
    coaching_points: str = Field(..., description="Coaching guidance")
    common_errors: str = Field(..., description="Common mistakes to avoid")
    zone_distribution: str = Field(..., description="Training zone distribution")
    segments: List[RunningSegment] = Field(..., description="Session segments")


# --- Cycling Models ---


class CyclingSegment(BaseModel):
    """Cycling session segment."""

    duration: str = Field(..., description="Segment duration in minutes")
    intensity: str = Field(..., description="Power output (e.g., '100-120W')")
    ftp: str = Field(..., description="FTP percentage (e.g., '50-60%')")
    rpm: str = Field(..., description="RPM specification (e.g., '80-90 RPM')")
    rationale: str = Field(..., description="Segment purpose/rationale")
    zone: int = Field(..., ge=1, le=7, description="Training zone (1-7)")


class CyclingSession(BaseModel):
    """Complete cycling session."""

    id: str = Field(..., description="Unique session identifier")
    session_type: Literal["bike"] = Field(default="bike", description="Session type")
    details: str = Field(..., description="Session details/description")
    duration: str = Field(..., description="Total duration in minutes")
    skill_category: str = Field(..., description="Skill level category")
    cycling_category: Optional[str] = Field(None, description="Cycling disciplines")
    intensity: str = Field(..., description="Overall intensity level")
    distance: Optional[float] = Field(None, gt=0, description="Distance in kilometers")
    location: Optional[str] = Field(None, description="Location type")
    segments: List[CyclingSegment] = Field(..., description="Session segments")


# --- Recovery Models ---


class RecoveryActivity(BaseModel):
    """Recovery session activity."""

    name: str = Field(..., description="Activity name")
    duration: int = Field(..., ge=1, description="Duration in minutes")
    intensity: str = Field(..., description="Intensity level")
    description: str = Field(..., description="Activity description")
    benefits: str = Field(..., description="Expected benefits")


class RecoverySession(BaseModel):
    """Complete recovery session."""

    id: str = Field(..., description="Unique session identifier")
    session_type: Literal["recovery"] = Field(
        default="recovery", description="Session type"
    )
    session_name: str = Field(..., description="Session name")
    session_description: str = Field(..., description="Session description")
    duration: int = Field(..., ge=1, description="Total duration in minutes")
    recovery_focus: str = Field(..., description="Primary recovery focus")
    intensity_level: str = Field(..., description="Intensity level")
    coaching_points: str = Field(..., description="Coaching guidance")
    activities: List[RecoveryActivity] = Field(..., description="Recovery activities")


# --- Input Models ---


class StrengthSessionInput(BaseModel):
    """Input for strength session generation."""

    command: Literal["strength"] = Field(..., description="Command type")
    date: str = Field(..., description="Session date (YYYY-MM-DD format)")
    intensity: str = Field(
        default="high", description="Intensity level (low, moderate, high)"
    )
    duration: int = Field(default=60, ge=1, le=300, description="Duration in minutes")
    skill_category: str = Field(
        default="General Strength", description="Skill category"
    )
    training_category: str = Field(default="Full Body", description="Training category")
    intensity_measure: str = Field(
        default="RPE", description="Intensity measurement method"
    )
    stress_level_measure: str = Field(
        default="Subjective", description="Stress level measurement"
    )

    @field_validator("date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format."""
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")

    @field_validator("intensity")
    @classmethod
    def validate_intensity(cls, v: str) -> str:
        """Validate intensity level."""
        if v.lower() not in ["low", "moderate", "high"]:
            raise ValueError("Intensity must be 'low', 'moderate', or 'high'")
        return v.lower()


class RunningSessionInput(BaseModel):
    """Input for running session generation."""

    command: Literal["running"] = Field(..., description="Command type")
    date: str = Field(..., description="Session date (YYYY-MM-DD format)")
    distance: float = Field(
        default=5.0, gt=0, le=100, description="Distance in kilometers"
    )
    intensity: str = Field(
        default="moderate", description="Intensity level (low, moderate, high)"
    )
    run_category: str = Field(default="General Aerobic", description="Running category")
    skill_category: str = Field(default="Intermediate", description="Skill level")
    intensity_measure: str = Field(
        default="% HRR", description="Intensity measurement method"
    )
    stress_level_measure: str = Field(
        default="RPE", description="Stress level measurement"
    )

    @field_validator("date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format."""
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")

    @field_validator("intensity")
    @classmethod
    def validate_intensity(cls, v: str) -> str:
        """Validate intensity level."""
        if v.lower() not in ["low", "moderate", "high"]:
            raise ValueError("Intensity must be 'low', 'moderate', or 'high'")
        return v.lower()


class CyclingSessionInput(BaseModel):
    """Input for cycling session generation."""

    command: Literal["cycling"] = Field(..., description="Command type")
    date: str = Field(..., description="Session date (YYYY-MM-DD format)")
    duration: int = Field(default=45, ge=1, le=300, description="Duration in minutes")
    intensity: str = Field(
        default="moderate", description="Intensity level (low, moderate, high)"
    )
    distance: Optional[float] = Field(
        default=20.0, gt=0, le=200, description="Distance in kilometers"
    )
    location: str = Field(default="Indoor", description="Location type")
    cycling_category: List[str] = Field(
        default=["Road"], description="Cycling disciplines"
    )
    skill_category: str = Field(default="Basic Riding", description="Skill level")

    @field_validator("date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format."""
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")

    @field_validator("intensity")
    @classmethod
    def validate_intensity(cls, v: str) -> str:
        """Validate intensity level."""
        if v.lower() not in ["low", "moderate", "high"]:
            raise ValueError("Intensity must be 'low', 'moderate', or 'high'")
        return v.lower()


class RecoverySessionInput(BaseModel):
    """Input for recovery session generation."""

    command: Literal["recovery"] = Field(..., description="Command type")
    date: str = Field(..., description="Session date (YYYY-MM-DD format)")
    duration: int = Field(default=30, ge=1, le=120, description="Duration in minutes")
    recovery_focus: str = Field(default="Active Recovery", description="Recovery focus")
    intensity_level: str = Field(default="Very Low", description="Intensity level")

    @field_validator("date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        """Validate date format."""
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")


# --- Unified Input/Output Models ---


class SessionGenerationInput(BaseModel):
    """Unified input for session generation tools."""

    command: Literal["strength", "running", "cycling", "recovery"] = Field(
        ..., description="Session type to generate"
    )
    date: str = Field(..., description="Session date (YYYY-MM-DD format)")

    # Fields for all commands, to be validated by specific models later
    intensity: Optional[str] = Field(None, description="Intensity level")
    duration: Optional[int] = Field(None, description="Duration in minutes")
    skill_category: Optional[str] = Field(None, description="Skill category")
    training_category: Optional[str] = Field(None, description="Training category")
    intensity_measure: Optional[str] = Field(
        None, description="Intensity measurement method"
    )
    stress_level_measure: Optional[str] = Field(
        None, description="Stress level measurement"
    )
    distance: Optional[float] = Field(None, description="Distance in kilometers")
    run_category: Optional[str] = Field(None, description="Running category")
    location: Optional[str] = Field(None, description="Location type")
    cycling_category: Optional[List[str]] = Field(
        None, description="Cycling disciplines"
    )
    recovery_focus: Optional[str] = Field(None, description="Recovery focus")
    # No specific intensity_level for recovery here, handled by default in its own input model if needed

    @model_validator(mode="before")  # Changed to 'before' to set defaults first
    @classmethod
    def set_command_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Set default values based on command type before full validation."""
        command = values.get("command")
        if command == "strength":
            values.setdefault("intensity", "high")
            values.setdefault("duration", 60)
            values.setdefault("skill_category", "General Strength")
            values.setdefault("training_category", "Full Body")
            values.setdefault("intensity_measure", "RPE")
            values.setdefault("stress_level_measure", "Subjective")
        elif command == "running":
            values.setdefault("distance", 5.0)
            values.setdefault("intensity", "moderate")
            values.setdefault("run_category", "General Aerobic")
            values.setdefault("skill_category", "Intermediate")
            values.setdefault("intensity_measure", "% HRR")
            values.setdefault("stress_level_measure", "RPE")
        elif command == "cycling":
            values.setdefault("duration", 45)
            values.setdefault("intensity", "moderate")
            values.setdefault("distance", 20.0)
            values.setdefault("location", "Indoor")
            values.setdefault("cycling_category", ["Road"])
            values.setdefault("skill_category", "Basic Riding")
        elif command == "recovery":
            values.setdefault("duration", 30)
            values.setdefault("recovery_focus", "Active Recovery")
            values.setdefault(
                "intensity", "Very Low"
            )  # Changed from intensity_level to intensity

        return values

    @model_validator(mode="after")
    def validate_all_fields(self) -> "SessionGenerationInput":
        """Validate all fields, including command-specific ones using their respective models."""
        try:
            datetime.strptime(self.date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Date must be in YYYY-MM-DD format")

        if self.command == "strength":
            StrengthSessionInput(
                command=self.command,
                date=self.date,
                intensity=self.intensity,
                duration=self.duration,
                skill_category=self.skill_category,
                training_category=self.training_category,
                intensity_measure=self.intensity_measure,
                stress_level_measure=self.stress_level_measure,
            )
        elif self.command == "running":
            RunningSessionInput(
                command=self.command,
                date=self.date,
                distance=self.distance,
                intensity=self.intensity,
                run_category=self.run_category,
                skill_category=self.skill_category,
                intensity_measure=self.intensity_measure,
                stress_level_measure=self.stress_level_measure,
            )
        elif self.command == "cycling":
            CyclingSessionInput(
                command=self.command,
                date=self.date,
                duration=self.duration,
                intensity=self.intensity,
                distance=self.distance,
                location=self.location,
                cycling_category=self.cycling_category,
                skill_category=self.skill_category,
            )
        elif self.command == "recovery":
            RecoverySessionInput(
                command=self.command,
                date=self.date,
                duration=self.duration,
                recovery_focus=self.recovery_focus,
                intensity_level=self.intensity,  # Corrected from intensity_level to intensity
            )

        # General intensity validation (applies if intensity field is used by command)
        if (
            self.intensity and self.command != "recovery"
        ):  # recovery uses intensity_level
            if self.intensity.lower() not in ["low", "moderate", "high"]:
                raise ValueError(
                    "Intensity must be 'low', 'moderate', or 'high' for this command"
                )
        elif self.command == "recovery" and self.intensity:
            if self.intensity.lower() not in ["very low", "low", "moderate"]:
                raise ValueError(
                    "Intensity for recovery must be 'very low', 'low', or 'moderate'"
                )

        return self


class SessionGenerationOutput(BaseModel):
    """Output for session generation tools."""

    success: bool = Field(..., description="Whether the operation succeeded")
    command: str = Field(..., description="Command that was executed")
    error_type: Optional[str] = Field(None, description="Type of error if failed")
    message: str = Field(..., description="Success or error message")
    request_id: str = Field(..., description="Unique request identifier")
    execution_time_ms: int = Field(..., description="Execution time in milliseconds")

    # Session data (only one will be populated based on command)
    strength_session: Optional[StrengthSession] = Field(
        None, description="Generated strength session"
    )
    running_session: Optional[RunningSession] = Field(
        None, description="Generated running session"
    )
    cycling_session: Optional[CyclingSession] = Field(
        None, description="Generated cycling session"
    )
    recovery_session: Optional[RecoverySession] = Field(
        None, description="Generated recovery session"
    )

    # Fallback data
    fallback_response: Optional[Dict[str, Any]] = Field(
        None, description="Fallback session data"
    )

    @model_validator(mode="after")
    def validate_session_data(self) -> "SessionGenerationOutput":
        """Validate that exactly one session type is populated when successful."""
        if self.success:
            sessions = [
                self.strength_session,
                self.running_session,
                self.cycling_session,
                self.recovery_session,
            ]
            populated_sessions = [s for s in sessions if s is not None]

            if len(populated_sessions) != 1:
                raise ValueError(
                    "Exactly one session type must be populated when successful"
                )

        return self
