"""
Prompt management commands for CRUD operations.
"""

import sys
from pathlib import Path
from typing import Optional
import os
import shutil
from datetime import datetime

import click

from athlea_langgraph.utils.prompt_migration import PromptMigrator
from athlea_langgraph.utils.prompt_models import PromptConfig, PromptType

from ..utils.formatters import format_prompt_details, format_prompt_list


@click.group()
def prompt():
    """Manage prompts (create, read, update, delete)."""
    pass


@prompt.command()
@click.argument("name", required=False)
@click.option(
    "--type",
    "prompt_type",
    type=click.Choice(["coach", "reasoning", "system"]),
    help="Filter by prompt type",
)
@click.option(
    "--tag", multiple=True, help="Filter by tags (can be used multiple times)"
)
@click.pass_context
def list(ctx, name: Optional[str], prompt_type: Optional[str], tag: tuple):
    """List all prompts with optional filtering."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()
    prompts = loader.list_prompts()

    # Apply filters
    if name:
        prompts = [p for p in prompts if name.lower() in p.lower()]

    if prompt_type or tag:
        filtered_prompts = []
        for prompt_name in prompts:
            try:
                config = loader.load_prompt(prompt_name)

                # Type filter
                if prompt_type and config.metadata.prompt_type.value != prompt_type:
                    continue

                # Tag filter
                if tag and not any(t in config.metadata.tags for t in tag):
                    continue

                filtered_prompts.append(prompt_name)
            except Exception:
                # If we can't load the config, skip filtering
                if not prompt_type and not tag:
                    filtered_prompts.append(prompt_name)

        prompts = filtered_prompts

    if not prompts:
        click.echo("No prompts found matching the criteria.")
        return

    click.echo(format_prompt_list(prompts, cli_ctx.verbose))


@prompt.command()
@click.argument("name")
@click.pass_context
def show(ctx, name: str):
    """Show detailed information about a specific prompt."""
    cli_ctx = ctx.find_root().obj

    try:
        loader = cli_ctx.get_loader()
        config = loader.load_prompt(name)
        click.echo(format_prompt_details(config, cli_ctx.verbose))
    except Exception as e:
        click.echo(f"Error loading prompt '{name}': {e}", err=True)
        sys.exit(1)


@prompt.command()
@click.argument("name")
@click.option(
    "--type",
    "prompt_type",
    type=click.Choice(["coach", "reasoning", "system"]),
    default="coach",
    help="Type of prompt to create",
)
@click.option("--description", help="Description of the prompt")
@click.option("--author", default="CLI User", help="Author name")
@click.option("--tags", help="Comma-separated list of tags")
@click.option("--template", help="Use an existing prompt as a template")
@click.option(
    "--file", "content_file", type=click.File("r"), help="Read prompt content from file"
)
@click.pass_context
def create(
    ctx,
    name: str,
    prompt_type: str,
    description: Optional[str],
    author: str,
    tags: Optional[str],
    template: Optional[str],
    content_file,
):
    """Create a new prompt interactively or from file."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    # Check if prompt already exists
    try:
        loader.load_prompt(name)
        click.echo(
            f"Error: Prompt '{name}' already exists. Use 'update' to modify it.",
            err=True,
        )
        sys.exit(1)
    except:
        # Good, prompt doesn't exist
        pass

    # Get content
    if content_file:
        content = content_file.read()
        click.echo(f"Loaded content from file ({len(content)} characters)")
    elif template:
        try:
            template_config = loader.load_prompt(template)
            content = template_config.prompt.system
            click.echo(f"Using '{template}' as template")
        except Exception as e:
            click.echo(f"Error loading template '{template}': {e}", err=True)
            sys.exit(1)
    else:
        click.echo(
            "Enter prompt content (Press Ctrl+D when finished, Ctrl+C to cancel):"
        )
        try:
            content = sys.stdin.read()
        except KeyboardInterrupt:
            click.echo("\nCancelled.")
            sys.exit(0)

    if not content.strip():
        click.echo("Error: Prompt content cannot be empty.", err=True)
        sys.exit(1)

    # Parse tags
    tag_list = []
    if tags:
        tag_list = [t.strip() for t in tags.split(",")]

    # Add default tags based on type
    default_tags = {
        "coach": ["coaching"],
        "reasoning": ["reasoning"],
        "system": ["system"],
    }
    tag_list.extend(default_tags.get(prompt_type, []))
    tag_list = list(set(tag_list))  # Remove duplicates

    # Create the prompt using PromptMigrator
    migrator = PromptMigrator(Path(cli_ctx.prompts_dir))

    try:
        config = migrator.create_prompt_config(
            name.upper() + "_PROMPT",  # Variable name format
            content,
            "cli_create",
            PromptType(prompt_type),
            description=description,
            author=author,
            tags=tag_list,
        )

        # Save the prompt
        output_path = loader.save_prompt(config, name)
        click.echo(f"✅ Created prompt '{name}' at {output_path}")

    except Exception as e:
        click.echo(f"Error creating prompt: {e}", err=True)
        sys.exit(1)


@prompt.command()
@click.argument("name")
@click.option("--content", help="New prompt content")
@click.option("--description", help="Update description")
@click.option("--tags", help="Update tags (comma-separated)")
@click.option("--add-tags", help="Add tags (comma-separated)")
@click.option("--remove-tags", help="Remove tags (comma-separated)")
@click.option(
    "--file", "content_file", type=click.File("r"), help="Read new content from file"
)
@click.option("--editor", is_flag=True, help="Open content in default editor")
@click.pass_context
def update(
    ctx,
    name: str,
    content: Optional[str],
    description: Optional[str],
    tags: Optional[str],
    add_tags: Optional[str],
    remove_tags: Optional[str],
    content_file,
    editor: bool,
):
    """Update an existing prompt."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    try:
        # Load existing prompt
        config = loader.load_prompt(name)
        click.echo(
            f"Updating prompt '{name}' (current version: {config.metadata.version})"
        )

        # Update content
        if content_file:
            new_content = content_file.read()
            config.prompt.system = new_content
            click.echo(f"Updated content from file ({len(new_content)} characters)")
        elif content:
            config.prompt.system = content
            click.echo(f"Updated content ({len(content)} characters)")
        elif editor:
            # This would open an editor - simplified for now
            click.echo(
                "Editor functionality not implemented yet. Use --content or --file instead."
            )
            return

        # Update metadata
        if description:
            config.metadata.description = description
            click.echo(f"Updated description: {description}")

        # Handle tags
        current_tags = set(config.metadata.tags)

        if tags:
            # Replace all tags
            new_tags = [t.strip() for t in tags.split(",")]
            config.metadata.tags = new_tags
            click.echo(f"Updated tags: {', '.join(new_tags)}")
        else:
            # Add/remove specific tags
            if add_tags:
                add_list = [t.strip() for t in add_tags.split(",")]
                current_tags.update(add_list)
                click.echo(f"Added tags: {', '.join(add_list)}")

            if remove_tags:
                remove_list = [t.strip() for t in remove_tags.split(",")]
                current_tags.difference_update(remove_list)
                click.echo(f"Removed tags: {', '.join(remove_list)}")

            config.metadata.tags = list(current_tags)

        # Increment version (simple increment)
        version_parts = config.metadata.version.split(".")
        version_parts[-1] = str(int(version_parts[-1]) + 1)
        config.metadata.version = ".".join(version_parts)

        # Add changelog entry
        from datetime import datetime

        from athlea_langgraph.utils.prompt_models import ChangelogEntry

        changelog_entry = ChangelogEntry(
            version=config.metadata.version,
            date=datetime.now(),
            changes="Updated via CLI",
            author="CLI User",
            breaking_changes=False,
        )
        config.metadata.changelog.append(changelog_entry)
        config.metadata.updated_at = datetime.now()

        # Save updated prompt
        output_path = loader.save_prompt(config, name)
        click.echo(f"✅ Updated prompt '{name}' to version {config.metadata.version}")
        click.echo(f"   Saved to: {output_path}")

    except Exception as e:
        click.echo(f"Error updating prompt '{name}': {e}", err=True)
        sys.exit(1)


@prompt.command()
@click.argument("name")
@click.option("--force", is_flag=True, help="Force delete without confirmation")
@click.pass_context
def delete(ctx, name: str, force: bool):
    """Delete a prompt (with safety confirmation)."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    try:
        # Load prompt to verify it exists
        config = loader.load_prompt(name)

        if not force:
            click.echo(f"About to delete prompt: {name}")
            click.echo(f"Type: {config.metadata.prompt_type.value}")
            click.echo(f"Version: {config.metadata.version}")
            click.echo(f"Tags: {', '.join(config.metadata.tags)}")

            if not click.confirm("\nAre you sure you want to delete this prompt?"):
                click.echo("Cancelled.")
                return

        # Find and delete the file
        prompts_dir = Path(cli_ctx.prompts_dir)

        # Look for the file in subdirectories
        found_file = None
        for subdir in prompts_dir.rglob(f"{name}.json"):
            found_file = subdir
            break

        if found_file and found_file.exists():
            found_file.unlink()
            click.echo(f"✅ Deleted prompt '{name}' from {found_file}")
        else:
            click.echo(f"Warning: Could not find file for prompt '{name}'", err=True)

    except Exception as e:
        click.echo(f"Error deleting prompt '{name}': {e}", err=True)
        sys.exit(1)


@prompt.command()
@click.argument("name")
@click.argument("new_name")
@click.pass_context
def rename(ctx, name: str, new_name: str):
    """Rename a prompt."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    try:
        # Check if new name already exists
        try:
            loader.load_prompt(new_name)
            click.echo(f"Error: Prompt '{new_name}' already exists.", err=True)
            sys.exit(1)
        except:
            pass  # Good, new name doesn't exist

        # Load existing prompt
        config = loader.load_prompt(name)

        # Update the name in metadata
        config.metadata.name = new_name

        # Increment version and add changelog
        version_parts = config.metadata.version.split(".")
        version_parts[-1] = str(int(version_parts[-1]) + 1)
        config.metadata.version = ".".join(version_parts)

        from datetime import datetime

        from athlea_langgraph.utils.prompt_models import ChangelogEntry

        changelog_entry = ChangelogEntry(
            version=config.metadata.version,
            date=datetime.now(),
            changes=f"Renamed from '{name}' to '{new_name}'",
            author="CLI User",
            breaking_changes=True,  # Renaming is a breaking change
        )
        config.metadata.changelog.append(changelog_entry)
        config.metadata.updated_at = datetime.now()

        # Save with new name and delete old file
        output_path = loader.save_prompt(config, new_name)

        # Delete old file
        prompts_dir = Path(cli_ctx.prompts_dir)
        for old_file in prompts_dir.rglob(f"{name}.json"):
            old_file.unlink()
            break

        click.echo(f"✅ Renamed prompt '{name}' to '{new_name}'")
        click.echo(f"   New location: {output_path}")

    except Exception as e:
        click.echo(f"Error renaming prompt: {e}", err=True)
        sys.exit(1)


@prompt.command()
@click.option(
    "--backup/--no-backup", default=True, help="Create backup before deletion"
)
@click.option(
    "--keep-structure/--remove-all",
    default=True,
    help="Keep directory structure or remove everything",
)
@click.option("--force", is_flag=True, help="Skip confirmation prompts")
def clear(backup: bool, keep_structure: bool, force: bool):
    """Clear all prompt files from the prompts directory."""
    # Define paths
    prompts_dir = Path("athlea_langgraph/prompts")

    if not prompts_dir.exists():
        click.echo(f"❌ Prompts directory not found: {prompts_dir}")
        return

    # Count files
    files = []
    for root, dirs, filenames in os.walk(prompts_dir):
        for filename in filenames:
            if filename.endswith((".json", ".yaml", ".yml", ".txt")):
                files.append(Path(root) / filename)

    if not files:
        click.echo("ℹ️  No prompt files found to remove.")
        return

    click.echo(f"📊 Found {len(files)} prompt files to remove")

    # Show some examples
    if not force:
        for i, file_path in enumerate(files[:3]):
            rel_path = file_path.relative_to(prompts_dir)
            click.echo(f"   - {rel_path}")
        if len(files) > 3:
            click.echo(f"   ... and {len(files) - 3} more files")

    # Confirmation
    if not force:
        if not click.confirm(f"\n🗑️  Remove {len(files)} prompt files?", abort=True):
            return

    # Create backup
    backup_created = None
    if backup:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = prompts_dir.parent / f"prompts_backup_{timestamp}"

        try:
            shutil.copytree(prompts_dir, backup_dir)
            click.echo(f"✅ Backup created: {backup_dir}")
            backup_created = backup_dir
        except Exception as e:
            click.echo(f"❌ Backup failed: {e}")
            if not force and not click.confirm("Continue without backup?", abort=True):
                return

    # Remove files
    removed_count = 0

    if keep_structure:
        # Remove only files, keep directories
        for file_path in files:
            try:
                file_path.unlink()
                removed_count += 1
            except Exception as e:
                click.echo(f"❌ Failed to remove {file_path}: {e}")
    else:
        # Remove entire directory and recreate
        try:
            shutil.rmtree(prompts_dir)
            prompts_dir.mkdir(parents=True, exist_ok=True)
            click.echo(f"🗑️  Removed entire directory structure")
            removed_count = len(files)
        except Exception as e:
            click.echo(f"❌ Failed to remove directory: {e}")
            return

    # Summary
    click.echo(f"\n✅ Removed {removed_count} prompt files")
    if backup_created:
        click.echo(f"📦 Backup available at: {backup_created}")

    click.echo(f"📁 Prompts directory: {prompts_dir}")
