"""
Modular Agent Tools Manager

This module provides domain-specific tool initialization and distribution for the modular
coaching agents. Each agent receives only the tools relevant to their expertise domain,
following multi-agent best practices.

Updated to integrate with Phase 2: Tool Layer Organization
- Domain-specific tool organization (Proposal 2A)
- Tool-agent contract enforcement (Proposal 2B)
- MCP server integration for external tools

Based on the TypeScript implementation pattern in athlea-web/app/api/coaching/lib/agents/
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool, tool

# from .contracts.contract_manager import ToolContractViolation, get_contract_manager
from .mcp_client_manager import get_mcp_client_manager
from .nutrition.calorie_calculator import (
    calculate_daily_calories,
    calculate_macro_targets,
)
from .strength.exercise_database import (
    get_exercise_progression,
    search_strength_exercises,
)

logger = logging.getLogger(__name__)


class ModularAgentToolsManager:
    """
    Manages tool distribution to modular coaching agents based on domain expertise.

    Now includes:
    - Domain-specific tool organization
    - Contract enforcement and validation
    - Schema-based tool access control
    - MCP server integration for external tools
    """

    def __init__(self):
        self._tools_initialized = False
        self._available_tools: Dict[str, BaseTool] = {}
        self._mcp_tools: Dict[str, List[BaseTool]] = {}
        # self.contract_manager = get_contract_manager()
        self.contract_manager = None  # Temporarily disable

    async def initialize_tools(self):
        """Initialize all available tools with error handling and contract validation."""
        if self._tools_initialized:
            return

        logger.info(
            "🔧 Initializing tools for modular agents with contract enforcement and MCP integration..."
        )

        # Initialize domain-specific tools (Phase 2A)
        await self._initialize_strength_domain_tools()
        await self._initialize_nutrition_domain_tools()
        await self._initialize_cardio_domain_tools()
        await self._initialize_recovery_domain_tools()
        await self._initialize_mental_domain_tools()

        # Initialize external tools (existing)
        await self._initialize_airtable_tools()
        await self._initialize_azure_search_tools()
        await self._initialize_session_generation_tools()
        await self._initialize_location_tools()

        # Initialize MCP tools
        await self._initialize_mcp_tools()

        self._tools_initialized = True

        # Log contract statistics
        stats = (
            self.contract_manager.get_contract_stats() if self.contract_manager else {}
        )
        total_tools = len(self._available_tools)
        total_mcp_tools = sum(len(tools) for tools in self._mcp_tools.values())

        logger.info(
            f"✅ Initialized {total_tools} direct tools + {total_mcp_tools} MCP tools for modular agents"
        )
        logger.info(
            f"📋 Contract enforcement: {stats['total_contracts']} contracts across {len(stats['domains'])} domains"
        )

    async def _initialize_mcp_tools(self):
        """Initialize tools from MCP servers."""
        try:
            mcp_manager = await get_mcp_client_manager()

            if not mcp_manager.is_available():
                logger.warning("MCP not available - using direct tools only")
                return

            # Load MCP tools by category
            self._mcp_tools["external"] = await mcp_manager.get_external_tools()
            self._mcp_tools["location"] = await mcp_manager.get_location_tools()
            self._mcp_tools["data"] = await mcp_manager.get_data_tools()

            tool_count = sum(len(tools) for tools in self._mcp_tools.values())
            logger.info(f"✅ MCP tools initialized: {tool_count} tools available")

        except Exception as e:
            logger.warning(f"⚠ MCP tool initialization failed: {e}")
            # Fallback to direct tools only
            self._mcp_tools = {}

    async def _initialize_strength_domain_tools(self):
        """Initialize strength training domain tools with contract validation."""
        try:
            # Register domain-specific tools
            self._available_tools["search_strength_exercises"] = (
                search_strength_exercises
            )
            self._available_tools["get_exercise_progression"] = get_exercise_progression

            # Add legacy strength assessment for backward compatibility
            self._available_tools["strength_assessment"] = (
                self._create_strength_assessment_tool()
            )

            # Add GraphRAG tool for evidence-based strength training research
            self._available_tools["graphrag_search"] = self._create_graphrag_tool()

            logger.info(
                "✅ Strength domain tools initialized with contract enforcement"
            )
        except Exception as e:
            logger.warning(f"⚠ Strength domain tools failed to initialize: {e}")

    async def _initialize_nutrition_domain_tools(self):
        """Initialize nutrition domain tools with contract validation."""
        try:
            # Register domain-specific tools
            self._available_tools["calculate_daily_calories"] = calculate_daily_calories
            self._available_tools["calculate_macro_targets"] = calculate_macro_targets

            # Add legacy macro calculator for backward compatibility
            self._available_tools["macro_calculator"] = (
                self._create_macro_calculator_tool()
            )

            # Add GraphRAG tool for evidence-based nutrition research
            self._available_tools["graphrag_search"] = self._create_graphrag_tool()

            logger.info(
                "✅ Nutrition domain tools initialized with contract enforcement"
            )
        except Exception as e:
            logger.warning(f"⚠ Nutrition domain tools failed to initialize: {e}")

    async def _initialize_cardio_domain_tools(self):
        """Initialize cardio domain tools (placeholder for future implementation)."""
        try:
            # Placeholder for cardio domain tools
            self._available_tools["cardio_assessment"] = (
                self._create_cardio_assessment_tool()
            )

            # Add GraphRAG tool for evidence-based cardio training research
            self._available_tools["graphrag_search"] = self._create_graphrag_tool()

            logger.info("✅ Cardio domain tools initialized (legacy mode)")
        except Exception as e:
            logger.warning(f"⚠ Cardio domain tools failed to initialize: {e}")

    async def _initialize_recovery_domain_tools(self):
        """Initialize recovery domain tools (placeholder for future implementation)."""
        try:
            # Placeholder for recovery domain tools
            self._available_tools["recovery_assessment"] = (
                self._create_recovery_assessment_tool()
            )

            # Add GraphRAG tool for evidence-based recovery research
            self._available_tools["graphrag_search"] = self._create_graphrag_tool()

            logger.info("✅ Recovery domain tools initialized (legacy mode)")
        except Exception as e:
            logger.warning(f"⚠ Recovery domain tools failed to initialize: {e}")

    async def _initialize_mental_domain_tools(self):
        """Initialize mental training domain tools (placeholder for future implementation)."""
        try:
            # Placeholder for mental domain tools
            self._available_tools["mental_assessment"] = (
                self._create_mental_assessment_tool()
            )

            # Add GraphRAG tool for evidence-based mental training research
            self._available_tools["graphrag_search"] = self._create_graphrag_tool()

            logger.info("✅ Mental domain tools initialized (legacy mode)")
        except Exception as e:
            logger.warning(f"⚠ Mental domain tools failed to initialize: {e}")

    # ===============================
    # External Tools (Existing)
    # ===============================

    async def _initialize_airtable_tools(self):
        """Initialize Airtable MCP tools."""
        try:
            from athlea_langgraph.tools.external.airtable_mcp import (
                get_airtable_langchain_tool,
            )

            airtable_tool = get_airtable_langchain_tool()
            self._available_tools["airtable_search"] = airtable_tool
            logger.info("✅ Airtable MCP tools initialized")
        except Exception as e:
            logger.warning(f"⚠ Airtable tools failed to initialize: {e}")

    async def _initialize_azure_search_tools(self):
        """Initialize Azure Search tools."""
        try:
            azure_search_tool = self._create_azure_search_langchain_tool()
            self._available_tools["azure_search"] = azure_search_tool
            logger.info("✅ Azure Search tools initialized")
        except Exception as e:
            logger.warning(f"⚠ Azure Search tools failed to initialize: {e}")

    async def _initialize_session_generation_tools(self):
        """Initialize session generation tools."""
        try:
            from athlea_langgraph.tools.external.session_generation import (
                SessionGenerationLangChainTool,
                SessionGenerationTool,
            )

            session_tool = SessionGenerationTool()
            session_langchain_tool = SessionGenerationLangChainTool(session_tool)
            self._available_tools["session_generation"] = session_langchain_tool
            logger.info("✅ Session generation tools initialized")
        except Exception as e:
            logger.warning(f"⚠ Session generation tools failed to initialize: {e}")

    async def _initialize_location_tools(self):
        """Initialize location-based tools (Maps, Elevation)."""
        try:
            # Google Maps Elevation
            google_maps_tool = self._create_google_maps_langchain_tool()
            self._available_tools["google_maps_elevation"] = google_maps_tool

            # Azure Maps
            azure_maps_tool = self._create_azure_maps_langchain_tool()
            self._available_tools["azure_maps"] = azure_maps_tool

            logger.info("✅ Location tools initialized")
        except Exception as e:
            logger.warning(f"⚠ Location tools failed to initialize: {e}")

    # ===============================
    # Tool Creators (LangChain Wrappers) - Legacy Support
    # ===============================

    def _create_azure_search_langchain_tool(self) -> BaseTool:
        """Create Azure Search tool as LangChain tool."""
        from athlea_langgraph.tools.external.azure_search_retriever import (
            AzureSearchRetrieverTool,
        )

        azure_search = AzureSearchRetrieverTool()

        @tool
        def azure_cognitive_search(query: str) -> str:
            """Search for research-based information on fitness, nutrition, training, and exercise science.

            Use this tool for:
            - Evidence-based training information
            - Exercise science research
            - Nutrition guidelines and research
            - Recovery strategies and protocols
            - Sports psychology research

            Input should be a descriptive search query about fitness/training topics."""
            try:
                result = azure_search.search(query)
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Azure search failed: {str(e)}"

        return azure_cognitive_search

    def _create_google_maps_langchain_tool(self) -> BaseTool:
        """Create Google Maps Elevation tool as LangChain tool."""
        from athlea_langgraph.tools.external.google_maps_elevation import (
            GoogleMapsElevationTool,
        )

        google_maps = GoogleMapsElevationTool()

        @tool
        def google_maps_elevation(route_params: str) -> str:
            """Get elevation profile for running/cycling routes.

            Use this tool for:
            - Route elevation analysis
            - Training route difficulty assessment
            - Hill training recommendations
            - Altitude training considerations

            Input should be route coordinates or location parameters."""
            try:
                result = google_maps.get_elevation(route_params)
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Google Maps elevation query failed: {str(e)}"

        return google_maps_elevation

    def _create_azure_maps_langchain_tool(self) -> BaseTool:
        """Create Azure Maps tool as LangChain tool."""
        from athlea_langgraph.tools.external.azure_maps import AzureMapsTool

        azure_maps = AzureMapsTool()

        @tool
        def azure_maps_search(location_params: str) -> str:
            """Search for locations, routes, and geographic information.

            Use this tool for:
            - Finding training locations (gyms, parks, trails)
            - Route planning and navigation
            - Geographic analysis for outdoor activities
            - Location-based recommendations

            Input should be location search parameters or coordinates."""
            try:
                result = azure_maps.search_location(location_params)
                return json.dumps(result, indent=2)
            except Exception as e:
                return f"Azure Maps search failed: {str(e)}"

        return azure_maps_search

    # Legacy assessment tools for backward compatibility
    def _create_strength_assessment_tool(self) -> BaseTool:
        """Create strength assessment tool (legacy)."""

        @tool
        def assess_strength(user_data: str) -> str:
            """Assess user's strength training needs and provide recommendations.

            Use this tool for:
            - Evaluating current strength levels
            - Identifying muscle imbalances
            - Recommending strength training programs
            - Setting strength-based goals

            Input should be JSON string with user strength data."""
            try:
                import json

                data = json.loads(user_data)

                # Sample assessment logic
                assessment = {
                    "strength_level": "intermediate",
                    "recommendations": [
                        "Focus on compound movements",
                        "Progressive overload principle",
                        "Include unilateral exercises",
                    ],
                    "program_suggestions": [
                        "Upper/lower split",
                        "Full body routine",
                        "Push/pull/legs",
                    ],
                }

                return json.dumps(assessment, indent=2)

            except Exception as e:
                return f"Strength assessment failed: {str(e)}"

        return assess_strength

    def _create_macro_calculator_tool(self) -> BaseTool:
        """Create macro calculator tool for nutrition."""

        @tool
        def calculate_macros(user_data: str) -> str:
            """Calculate macronutrient requirements based on user goals and profile.

            Use this tool to:
            - Calculate daily caloric needs
            - Determine protein/carb/fat ratios
            - Adjust for specific goals (bulking/cutting/maintenance)
            - Account for activity level

            Input should be user data with weight, height, age, activity level, goals.
            """
            try:
                data = (
                    json.loads(user_data) if isinstance(user_data, str) else user_data
                )

                weight = data.get("weight", 70)  # kg
                activity_level = data.get("activity_level", "moderate").lower()
                goals = data.get("goals", ["maintenance"])

                # Basic BMR calculation (simplified)
                bmr = weight * 22  # Rough approximation

                # Activity multipliers
                multipliers = {"low": 1.2, "moderate": 1.5, "high": 1.8}
                tdee = bmr * multipliers.get(activity_level, 1.5)

                # Goal adjustments
                if (
                    "weight loss" in str(goals).lower()
                    or "fat loss" in str(goals).lower()
                ):
                    calories = tdee - 300
                elif (
                    "muscle building" in str(goals).lower()
                    or "bulking" in str(goals).lower()
                ):
                    calories = tdee + 300
                else:
                    calories = tdee

                macros = {
                    "daily_calories": int(calories),
                    "protein_grams": int(weight * 2.2),  # 2.2g per kg bodyweight
                    "carbs_grams": int(calories * 0.4 / 4),  # 40% of calories
                    "fat_grams": int(calories * 0.25 / 9),  # 25% of calories
                    "goal_adjustment": "Moderate deficit/surplus applied",
                    "recommendations": "Adjust portions based on training days",
                }

                return json.dumps(macros, indent=2)
            except Exception as e:
                return f"Macro calculation failed: {str(e)}"

        return calculate_macros

    def _create_cardio_assessment_tool(self) -> BaseTool:
        """Create cardio assessment tool."""

        @tool
        def assess_cardio(user_data: str) -> str:
            """Assess cardiovascular fitness and training needs.

            Use this tool to:
            - Evaluate current cardio fitness level
            - Determine appropriate training zones
            - Recommend starting intensity and duration
            - Assess injury risk factors

            Input should be user profile with cardio history and goals."""
            try:
                data = (
                    json.loads(user_data) if isinstance(user_data, str) else user_data
                )

                experience = data.get("running_experience", "beginner").lower()
                current_times = data.get("current_5k_time", "30 minutes")
                goals = data.get("goals", ["general fitness"])

                assessment = {
                    "fitness_level": experience,
                    "training_zones": {
                        "zone_1": "60-70% max HR (easy pace)",
                        "zone_2": "70-80% max HR (moderate pace)",
                        "zone_3": "80-90% max HR (hard pace)",
                    },
                    "weekly_frequency": "3-4 sessions per week",
                    "progression": "Increase duration by 10% weekly",
                    "current_baseline": current_times,
                    "focus_areas": goals,
                }

                return json.dumps(assessment, indent=2)
            except Exception as e:
                return f"Cardio assessment failed: {str(e)}"

        return assess_cardio

    def _create_recovery_assessment_tool(self) -> BaseTool:
        """Create recovery assessment tool."""

        @tool
        def assess_recovery(user_data: str) -> str:
            """Assess recovery needs and current recovery practices.

            Use this tool to:
            - Evaluate sleep quality and patterns
            - Assess stress levels and training load
            - Identify recovery deficits
            - Recommend recovery protocols

            Input should be user data with sleep, stress, and training information."""
            try:
                data = (
                    json.loads(user_data) if isinstance(user_data, str) else user_data
                )

                sleep_hours = data.get("sleep_hours", "7-8")
                stress_level = data.get("stress_level", "moderate").lower()
                training_volume = data.get("training_volume", "moderate").lower()

                recovery_score = 7  # Default moderate score
                if stress_level == "high" or training_volume == "high":
                    recovery_score -= 2
                if "poor" in sleep_hours.lower() or "5" in sleep_hours:
                    recovery_score -= 2

                assessment = {
                    "recovery_score": f"{recovery_score}/10",
                    "sleep_status": sleep_hours,
                    "stress_impact": stress_level,
                    "training_load": training_volume,
                    "recommendations": [
                        "Prioritize 7-9 hours of quality sleep",
                        "Include active recovery sessions",
                        "Manage stress through relaxation techniques",
                        "Consider periodization of training load",
                    ],
                    "priority_areas": ["sleep optimization", "stress management"],
                }

                return json.dumps(assessment, indent=2)
            except Exception as e:
                return f"Recovery assessment failed: {str(e)}"

        return assess_recovery

    def _create_mental_assessment_tool(self) -> BaseTool:
        """Create mental training assessment tool."""

        @tool
        def assess_mental_training(user_data: str) -> str:
            """Assess mental training needs and motivation patterns.

            Use this tool to:
            - Evaluate motivation levels and patterns
            - Identify mental barriers to consistency
            - Assess goal-setting effectiveness
            - Recommend mental training strategies

            Input should be user data with motivation history and challenges."""
            try:
                data = (
                    json.loads(user_data) if isinstance(user_data, str) else user_data
                )

                motivation_level = data.get("motivation_level", "moderate").lower()
                biggest_challenge = data.get("biggest_challenge", "consistency").lower()
                previous_attempts = data.get("previous_attempts", "some")

                mental_barriers = []
                if "consistency" in biggest_challenge:
                    mental_barriers.append("consistency issues")
                if "motivation" in biggest_challenge:
                    mental_barriers.append("motivation fluctuations")
                if "time" in biggest_challenge:
                    mental_barriers.append("time management")

                assessment = {
                    "motivation_level": motivation_level,
                    "primary_barriers": mental_barriers,
                    "habit_formation_stage": (
                        "action" if previous_attempts == "multiple" else "preparation"
                    ),
                    "recommended_strategies": [
                        "Start with micro-habits (5-10 minutes)",
                        "Use implementation intentions (if-then planning)",
                        "Create accountability systems",
                        "Focus on process goals over outcome goals",
                    ],
                    "mental_training_focus": [
                        "consistency",
                        "self-efficacy",
                        "intrinsic motivation",
                    ],
                }

                return json.dumps(assessment, indent=2)
            except Exception as e:
                return f"Mental assessment failed: {str(e)}"

        return assess_mental_training

    # ===============================
    # Domain-Specific Tool Distribution (Enhanced with MCP)
    # ===============================

    def get_strength_agent_tools(self) -> List[BaseTool]:
        """Get tools specific to strength coaching (direct + MCP)."""
        tools = []

        # Core direct tools for strength coaching
        tool_names = [
            "search_strength_exercises",  # Direct strength tools
            "get_exercise_progression",  # Direct strength tools
            "airtable_search",  # Exercise databases, programs
            "azure_search",  # Research and evidence
            "session_generation",  # Create strength workouts
            "strength_assessment",  # Assess user capabilities
        ]

        # Add direct tools
        for tool_name in tool_names:
            if tool_name in self._available_tools:
                tools.append(self._available_tools[tool_name])

        # Add relevant MCP tools
        mcp_location_tools = self._mcp_tools.get("location", [])
        for tool in mcp_location_tools:
            if tool.name in ["search_locations", "find_nearby_facilities"]:
                tools.append(tool)  # For finding gyms, fitness centers

        logger.info(f"Strength agent tools: {[t.name for t in tools]}")
        return tools

    def get_nutrition_agent_tools(self) -> List[BaseTool]:
        """Get tools specific to nutrition coaching (direct + MCP)."""
        tools = []

        # Core direct tools for nutrition coaching
        tool_names = [
            "calculate_daily_calories",  # Direct nutrition tools
            "calculate_macro_targets",  # Direct nutrition tools
            "azure_search",  # Nutrition research
            "session_generation",  # Create meal plans
            "macro_calculator",  # Calculate nutritional needs
        ]

        # Add direct tools
        for tool_name in tool_names:
            if tool_name in self._available_tools:
                tools.append(self._available_tools[tool_name])

        # Add relevant MCP tools
        mcp_data_tools = self._mcp_tools.get("data", [])
        for tool in mcp_data_tools:
            if tool.name in ["query_airtable", "search_web"]:
                tools.append(tool)  # For recipe search, nutrition databases

        logger.info(f"Nutrition agent tools: {[t.name for t in tools]}")
        return tools

    def get_cardio_agent_tools(self) -> List[BaseTool]:
        """Get tools specific to cardio coaching (direct + MCP)."""
        tools = []

        # Core direct tools for cardio coaching
        tool_names = [
            "airtable_search",  # Running programs, equipment
            "azure_search",  # Research and protocols
            "session_generation",  # Create cardio sessions
            "google_maps_elevation",  # Route elevation profiles
            "azure_maps",  # Route planning
            "cardio_assessment",  # Assess cardio fitness
        ]

        # Add direct tools
        for tool_name in tool_names:
            if tool_name in self._available_tools:
                tools.append(self._available_tools[tool_name])

        # Add ALL MCP location tools - cardio coaches need comprehensive location tools
        mcp_location_tools = self._mcp_tools.get("location", [])
        tools.extend(mcp_location_tools)  # Route planning, elevation, facility finding

        # Add weather tools for outdoor training
        mcp_data_tools = self._mcp_tools.get("data", [])
        for tool in mcp_data_tools:
            if tool.name in ["get_weather_data"]:
                tools.append(tool)

        logger.info(f"Cardio agent tools: {[t.name for t in tools]}")
        return tools

    def get_recovery_agent_tools(self) -> List[BaseTool]:
        """Get tools specific to recovery coaching (direct + MCP)."""
        tools = []

        # Core direct tools for recovery coaching
        tool_names = [
            "azure_search",  # Recovery research
            "session_generation",  # Create recovery sessions
            "recovery_assessment",  # Assess recovery needs
        ]

        # Add direct tools
        for tool_name in tool_names:
            if tool_name in self._available_tools:
                tools.append(self._available_tools[tool_name])

        # Add relevant MCP tools for research and resources
        mcp_data_tools = self._mcp_tools.get("data", [])
        for tool in mcp_data_tools:
            if tool.name in ["search_web", "search_wikipedia"]:
                tools.append(tool)  # For recovery technique research

        logger.info(f"Recovery agent tools: {[t.name for t in tools]}")
        return tools

    def get_mental_agent_tools(self) -> List[BaseTool]:
        """Get tools specific to mental coaching (direct + MCP)."""
        tools = []

        # Core direct tools for mental coaching
        tool_names = [
            "azure_search",  # Psychology research
            "mental_assessment",  # Assess mental training needs
        ]

        # Add direct tools
        for tool_name in tool_names:
            if tool_name in self._available_tools:
                tools.append(self._available_tools[tool_name])

        # Add relevant MCP tools for research and resources
        mcp_data_tools = self._mcp_tools.get("data", [])
        for tool in mcp_data_tools:
            if tool.name in ["search_web", "search_wikipedia"]:
                tools.append(tool)  # For mental health resources and research

        logger.info(f"Mental agent tools: {[t.name for t in tools]}")
        return tools

    async def get_mcp_tools_for_agent(self, agent_domain: str) -> List[BaseTool]:
        """Get MCP tools for a specific agent domain."""
        if not self._tools_initialized:
            await self.initialize_tools()

        mcp_manager = await get_mcp_client_manager()
        return await mcp_manager.get_tools_for_agent(agent_domain)

    def get_all_available_tools(self) -> Dict[str, BaseTool]:
        """Get all available tools (direct only)."""
        return self._available_tools.copy()

    def get_all_tools_with_mcp(self) -> Dict[str, Any]:
        """Get all available tools including MCP tools."""
        return {
            "direct_tools": self._available_tools.copy(),
            "mcp_tools": self._mcp_tools.copy(),
        }

    def _validate_tool_access(
        self, agent_domain: str, tool_name: str, agent_permissions: List[str]
    ):
        """Validate agent's access to a tool based on domain and permissions."""
        if self.contract_manager:
            return self.contract_manager.validate_agent_tool_access(
                agent_domain, tool_name, agent_permissions
            )
        return True  # Bypass validation if contract manager is disabled


# Global instance
_modular_tools_manager = ModularAgentToolsManager()


async def get_modular_tools_manager() -> ModularAgentToolsManager:
    """Get the initialized modular tools manager."""
    if not _modular_tools_manager._tools_initialized:
        await _modular_tools_manager.initialize_tools()
    return _modular_tools_manager
