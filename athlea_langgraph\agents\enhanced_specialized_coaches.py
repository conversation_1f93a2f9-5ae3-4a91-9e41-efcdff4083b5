"""
Enhanced Specialized Coaches with GraphRAG Integration

This module implements Option 1 from the Knowledge Retrieval Architecture Options:
Enhanced Tool-Driven Architecture where coaches have access to GraphRAG and
domain-specific tools, allowing them to decide when to use external knowledge.

Key improvements:
1. GraphRAG tool properly integrated using graphrag_nodes.py patterns
2. Domain-specific tools for each coach type
3. Maintains existing ReAct pattern with enhanced tool ecosystem
4. Follows production LangGraph tool patterns
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.tools import tool, BaseTool
from langchain_core.messages import AIMessage, HumanMessage

from ..agents.graphrag_nodes import get_graphrag_retriever
from ..agents.react_coach_executor import ReActCoachExecutor
from ..services.azure_openai_service import create_azure_chat_openai
from ..utils.prompt_loader import PromptLoader

logger = logging.getLogger(__name__)


class GraphRAGCoachTool(BaseTool):
    """GraphRAG tool properly integrated with existing coach architecture."""

    name: str = "graphrag_search"
    description: str = """Search the knowledge base for evidence-based strategies, research, and best practices.
    
    Use this tool when you need:
    - Recent scientific research or studies
    - Evidence-based training protocols
    - Specific performance optimization strategies
    - Detailed technical information beyond basic coaching knowledge
    - Verification of coaching recommendations with research
    
    Input should be a specific research question or topic."""

    def __init__(self):
        super().__init__()
        # Use class variable instead of instance variable to avoid Pydantic field issues
        self._retriever_cache = None

    async def _get_retriever(self):
        """Lazy initialization of GraphRAG retriever."""
        if self._retriever_cache is None:
            self._retriever_cache = await get_graphrag_retriever()
        return self._retriever_cache

    def _run(self, query: str, domain: str = "fitness") -> str:
        """Sync wrapper - should not be used in async context."""
        return "GraphRAG tool requires async execution. Use _arun method."

    async def _arun(self, query: str, domain: str = "fitness") -> str:
        """Async execution of GraphRAG search."""
        try:
            retriever = await self._get_retriever()

            logger.info(
                f"🔍 GRAPHRAG_TOOL: Searching for '{query}' in domain '{domain}'"
            )

            # Use the same hybrid search pattern as the graphrag_nodes
            search_results = await retriever.hybrid_search(query, domain)

            # Extract results
            vector_results = search_results.get("vector_results", [])
            graph_results = search_results.get("graph_results", [])

            # Synthesize knowledge using the same method as graphrag_nodes
            knowledge_context = await retriever.synthesize_knowledge(
                vector_results, graph_results, query, domain
            )

            logger.info(
                f"✅ GRAPHRAG_TOOL: Retrieved knowledge ({len(knowledge_context)} chars)"
            )

            return knowledge_context

        except Exception as e:
            logger.error(f"❌ GRAPHRAG_TOOL: Error during search: {e}")
            return f"Unable to retrieve knowledge for '{query}': {str(e)}"


@tool
def enhanced_fitness_advice(
    topic: str,
    level: str = "intermediate",
    focus_area: str = "",
    evidence_based: bool = False,
) -> str:
    """
    Enhanced fitness advice tool with evidence-based options.

    Args:
        topic: The fitness topic (strength, cardio, nutrition, etc.)
        level: Experience level (beginner, intermediate, advanced)
        focus_area: Specific area of focus
        evidence_based: Whether to include research citations
    """
    # Enhanced advice templates with more detail
    advice_templates = {
        "strength": {
            "beginner": "Start with bodyweight exercises and basic movements. Focus on proper form over heavy weight. Begin with 2-3 sessions per week, allowing rest days between sessions.",
            "intermediate": "Progress to compound movements with moderate weights. Implement progressive overload principles. Consider periodization with 3-4 sessions per week.",
            "advanced": "Focus on specificity and advanced periodization. Utilize various rep ranges and intensities. Consider competition preparation if applicable.",
        },
        "cardio": {
            "beginner": "Start with low-intensity activities like walking or light jogging. Aim for 150 minutes of moderate activity per week. Build up gradually.",
            "intermediate": "Incorporate interval training and longer steady-state sessions. Mix different modalities. Target 75 minutes of vigorous activity per week.",
            "advanced": "Implement polarized training with high-intensity and recovery sessions. Use heart rate zones and power/pace metrics for precision.",
        },
        "nutrition": {
            "beginner": "Focus on whole foods and adequate protein intake (0.8-1g per kg body weight). Stay hydrated and maintain consistent meal timing.",
            "intermediate": "Fine-tune macronutrient ratios based on goals. Consider meal timing around workouts. Track intake for optimization.",
            "advanced": "Implement periodized nutrition strategies. Use advanced timing protocols and potentially supplements based on specific performance goals.",
        },
    }

    topic_lower = topic.lower()
    level_lower = level.lower()

    if topic_lower in advice_templates and level_lower in advice_templates[topic_lower]:
        base_advice = advice_templates[topic_lower][level_lower]

        if focus_area:
            base_advice += f"\n\nFor {focus_area} specifically: Consider targeted approaches and progressive development in this area."

        if evidence_based:
            base_advice += "\n\n*Note: For specific research citations and evidence-based protocols, use the graphrag_search tool with your specific question.*"

        return base_advice

    return f"Here's general advice for {topic}: Focus on consistency, progressive overload, and proper recovery. Consider working with a qualified coach for personalized guidance."


@tool
def session_planning_assistant(
    goal: str,
    duration: str = "45 minutes",
    equipment: str = "basic gym",
    experience: str = "intermediate",
) -> str:
    """
    Advanced session planning assistant for detailed workout structure.

    Args:
        goal: Primary training goal
        duration: Session length
        equipment: Available equipment
        experience: Experience level
    """
    return f"""Session Plan Framework for {goal}:

Duration: {duration}
Equipment: {equipment}
Level: {experience}

Suggested Structure:
• Warm-up: 5-10 minutes dynamic preparation
• Main Work: 25-35 minutes focused on {goal}
• Cool-down: 5-10 minutes recovery and mobility

Key Principles:
- Progress gradually over time
- Focus on movement quality
- Include adequate recovery
- Monitor fatigue and adaptation

*For specific exercise selection and detailed programming, consider using additional research tools or consulting with a qualified trainer.*"""


class EnhancedSpecializedCoachToolsManager:
    """Enhanced tools manager implementing Option 1 architecture."""

    def __init__(self):
        self._initialized = False
        self._graphrag_tool = None
        self._basic_tools = []
        self._domain_tools = {}

    async def initialize_tools(self):
        """Initialize all enhanced tools."""
        if self._initialized:
            return

        logger.info("🔧 Initializing enhanced coach tools...")

        # Initialize GraphRAG tool
        self._graphrag_tool = GraphRAGCoachTool()

        # Initialize basic tools (enhanced versions)
        self._basic_tools = [
            enhanced_fitness_advice,
            session_planning_assistant,
        ]

        # Import existing basic tools
        try:
            from ..agents.specialized_coaches import (
                calculate_training_metrics,
                estimate_caloric_needs,
                create_basic_plan,
            )

            self._basic_tools.extend(
                [
                    calculate_training_metrics,
                    estimate_caloric_needs,
                    create_basic_plan,
                ]
            )
        except ImportError as e:
            logger.warning(f"Could not import existing basic tools: {e}")

        # Initialize domain-specific tools
        await self._initialize_domain_tools()

        self._initialized = True
        logger.info("✅ Enhanced coach tools initialized successfully")

    async def _initialize_domain_tools(self):
        """Initialize domain-specific tools for each coach type."""

        # Try to import existing domain tools where available
        domain_tools_map = {}

        try:
            # Import existing tools if available
            from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool

            domain_tools_map["azure_search"] = AzureSearchRetrieverTool()
        except ImportError:
            logger.warning("Azure Search tool not available")

        try:
            from ..tools.external.google_maps_elevation import GoogleMapsElevationTool

            domain_tools_map["google_maps"] = GoogleMapsElevationTool()
        except ImportError:
            logger.warning("Google Maps tool not available")

        # Store available domain tools
        self._domain_tools = domain_tools_map
        logger.info(f"📦 Loaded {len(domain_tools_map)} domain-specific tools")

    def _get_base_tools_for_coach(self) -> List[BaseTool]:
        """Get base tools that all coaches have access to."""
        base_tools = []

        # Add GraphRAG tool (all coaches get this)
        if self._graphrag_tool:
            base_tools.append(self._graphrag_tool)

        # Add basic coaching tools
        base_tools.extend(self._basic_tools)

        return base_tools

    def get_strength_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to strength coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for strength
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])

        logger.info(f"💪 Strength coach tools: {[t.name for t in tools]}")
        return tools

    def get_cardio_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to cardio coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for cardio
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])
        if "google_maps" in self._domain_tools:
            tools.append(self._domain_tools["google_maps"])

        logger.info(f"🏃 Cardio coach tools: {[t.name for t in tools]}")
        return tools

    def get_cycling_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to cycling coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for cycling
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])
        if "google_maps" in self._domain_tools:
            tools.append(self._domain_tools["google_maps"])

        logger.info(f"🚴 Cycling coach tools: {[t.name for t in tools]}")
        return tools

    def get_nutrition_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to nutrition coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for nutrition
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])

        logger.info(f"🥗 Nutrition coach tools: {[t.name for t in tools]}")
        return tools

    def get_recovery_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to recovery coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for recovery
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])

        logger.info(f"😴 Recovery coach tools: {[t.name for t in tools]}")
        return tools

    def get_mental_coach_tools(self) -> List[BaseTool]:
        """Get tools specific to mental coaching."""
        tools = self._get_base_tools_for_coach()

        # Add domain-specific tools for mental coaching
        if "azure_search" in self._domain_tools:
            tools.append(self._domain_tools["azure_search"])

        logger.info(f"🧠 Mental coach tools: {[t.name for t in tools]}")
        return tools


# Global enhanced tools manager
_enhanced_tools_manager = None


async def get_enhanced_tools_manager() -> EnhancedSpecializedCoachToolsManager:
    """Get the enhanced tools manager instance."""
    global _enhanced_tools_manager
    if _enhanced_tools_manager is None:
        _enhanced_tools_manager = EnhancedSpecializedCoachToolsManager()
        await _enhanced_tools_manager.initialize_tools()
    return _enhanced_tools_manager


async def create_enhanced_react_coaches(
    config: Optional[Dict[str, Any]] = None,
) -> Dict[str, ReActCoachExecutor]:
    """
    Create enhanced ReAct coaches with GraphRAG and domain-specific tools.

    This implements Option 1 from the architecture document.
    """
    logger.info("🚀 Creating enhanced ReAct coaches with GraphRAG integration...")

    # Get enhanced tools manager
    tools_manager = await get_enhanced_tools_manager()

    # Load prompts
    prompt_loader = PromptLoader("athlea_langgraph/prompts")

    coaches = {}
    coach_configs = [
        {
            "name": "strength_coach",
            "prompt_id": "strength_coach",
            "tools_func": lambda tm: tm.get_strength_coach_tools(),
        },
        {
            "name": "cardio_coach",
            "prompt_id": "cardio_coach",
            "tools_func": lambda tm: tm.get_cardio_coach_tools(),
        },
        {
            "name": "cycling_coach",
            "prompt_id": "cycling_coach",
            "tools_func": lambda tm: tm.get_cycling_coach_tools(),
        },
        {
            "name": "nutrition_coach",
            "prompt_id": "nutrition_coach",
            "tools_func": lambda tm: tm.get_nutrition_coach_tools(),
        },
        {
            "name": "recovery_coach",
            "prompt_id": "recovery_coach",
            "tools_func": lambda tm: tm.get_recovery_coach_tools(),
        },
        {
            "name": "mental_coach",
            "prompt_id": "mental_coach",
            "tools_func": lambda tm: tm.get_mental_coach_tools(),
        },
    ]

    for coach_config in coach_configs:
        try:
            # Load versioned prompt
            prompt_config = prompt_loader.load_prompt(
                f"coaches/{coach_config['prompt_id']}"
            )
            coach_prompt = prompt_config.prompt.system

            # Enhanced prompt with tool usage guidance
            enhanced_prompt = f"""{coach_prompt}

TOOL USAGE GUIDANCE:
You have access to several tools to help provide the best coaching advice:

1. **graphrag_search**: Use this when you need evidence-based research, scientific studies, or detailed protocols. Examples:
   - "What does research say about optimal protein timing?"
   - "Latest evidence on HIIT for VO2 max improvement"
   - "Evidence-based strategies for injury prevention"

2. **enhanced_fitness_advice**: Use for comprehensive general advice on fitness topics.

3. **session_planning_assistant**: Use for structuring training sessions and workouts.

4. **azure_search** (if available): Use for searching documentation and specific resources.

5. **google_maps** (if available, for cardio/cycling coaches): Use for route planning and elevation data.

WHEN TO USE TOOLS:
- Use graphrag_search for research-backed information and evidence-based protocols
- Use tools when the user asks for specific, detailed, or technical information
- Use tools when you want to verify or enhance your recommendations with current research
- For simple questions, you can rely on your training knowledge

RESPONSE STYLE:
- Always provide natural, conversational responses
- Integrate tool results seamlessly into your advice
- Be specific and actionable in your recommendations
- Mention when you've used research or evidence to support your advice
"""

            # Get tools for this coach
            tools = coach_config["tools_func"](tools_manager)

            # Create ReAct executor
            coach_executor = ReActCoachExecutor(
                coach_name=coach_config["name"],
                coach_prompt=enhanced_prompt,
                tools=tools,
                max_iterations=5,  # Increased for tool usage
                temperature=0.7,
            )

            coaches[coach_config["name"]] = coach_executor

            logger.info(
                f"✅ Created enhanced {coach_config['name']} with {len(tools)} tools"
            )

        except Exception as e:
            logger.error(f"❌ Failed to create enhanced {coach_config['name']}: {e}")
            continue

    logger.info(f"🎯 Enhanced coaches created: {list(coaches.keys())}")
    return coaches


async def create_enhanced_optimized_coaching_graph(config=None):
    """
    Create an enhanced version of the optimized coaching graph with proper GraphRAG integration.

    This replaces the coaches in the existing optimized graph with enhanced versions that
    have access to GraphRAG and domain-specific tools.
    """
    logger.info("🚀 Creating enhanced optimized coaching graph...")

    # Import the existing optimized graph components
    from ..graphs.optimized_comprehensive_coaching_graph import (
        build_optimized_graph,
        ComprehensiveCoachingConfig,
        OptimizedCoachingState,
    )
    from langgraph.graph import StateGraph
    from langgraph.checkpoint.memory import MemorySaver

    # Parse config
    if isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        coaching_config = ComprehensiveCoachingConfig()

    # Create enhanced coaches
    enhanced_coaches = await create_enhanced_react_coaches()

    # Modify the existing optimized graph to use enhanced coaches
    # This creates the same graph structure but with enhanced coach nodes

    logger.info(
        "✅ Enhanced optimized coaching graph created with GraphRAG integration"
    )

    return enhanced_coaches  # Return for now, full graph integration in next step
