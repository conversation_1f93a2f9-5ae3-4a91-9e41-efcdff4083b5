# LangGraph Studio Setup Guide

This guide provides complete instructions for setting up and running LangGraph Studio to test your Athlea coaching agents interactively.

## 📋 Overview

LangGraph Studio is a web-based interface that allows you to:
- Test individual coaching agents in isolation
- Interact with the complete multi-agent coaching system
- Visualize graph execution and agent interactions
- Debug agent behavior and tool usage
- Configure agent parameters in real-time

## 🛠️ Prerequisites

Before starting, ensure you have:

1. **Python 3.8+** installed on your system
2. **Virtual environment** (recommended)
3. **Project dependencies** installed
4. **Environment variables** configured (`.env.local`)

## 📦 Installation Steps

### Step 1: Activate Your Virtual Environment

```bash
# Navigate to your project directory
cd /path/to/python-langgraph

# Activate virtual environment
source venv/bin/activate  # On macOS/Linux
# OR
venv\Scripts\activate     # On Windows
```

### Step 2: Install Project Dependencies

```bash
# Install the project in editable mode
pip install -e .

# Verify installation
python -c "import athlea_langgraph; print('✓ Package installed successfully')"
```

### Step 3: Install LangGraph CLI with API Server

```bash
# Install LangGraph CLI with in-memory runtime
pip install -U "langgraph-cli[inmem]"

# Verify installation
langgraph --version
```

**Important**: The `[inmem]` extra is required for the API server components that power LangGraph Studio.

## 🚀 Running LangGraph Studio

### Basic Command

```bash
# Start LangGraph Studio (default port 8000)
langgraph dev
```

### Advanced Options

```bash
# Use custom port
langgraph dev --port 8001

# Bind to all interfaces (for remote access)
langgraph dev --host 0.0.0.0

# Show verbose output
langgraph dev --verbose

# View all available options
langgraph dev --help
```

## 🌐 Accessing the Interface

1. **Start the server**: Run `langgraph dev` in your terminal
2. **Wait for startup**: Look for successful compilation messages
3. **Open browser**: Navigate to `http://localhost:8000`
4. **Verify graphs**: You should see 8 available graphs in the dropdown

### Expected Startup Logs

Look for these success indicators:

```
2025-05-29T08:12:28.894024Z [info] Comprehensive coaching graph compiled successfully
2025-05-29T08:12:36.559963Z [info] Cycling agent test graph compiled successfully
```

## 📊 Available Graphs

Your LangGraph Studio instance includes:

### 1. **Comprehensive System**
- **Graph**: `coaching_agent`
- **Purpose**: Full multi-agent coaching experience
- **Features**: Routing, multiple specialists, aggregation

### 2. **Individual Agents** (for isolated testing)
- **`head_coach`**: Routing and clarification logic
- **`strength_agent`**: Strength training with ReAct tools
- **`nutrition_agent`**: Nutrition coaching and macro calculations
- **`cardio_agent`**: Cardiovascular/endurance training
- **`recovery_agent`**: Recovery and regeneration strategies
- **`mental_agent`**: Sports psychology and motivation
- **`cycling_agent`**: Cycling-specific performance coaching

## 🧪 Testing Your Agents

### Quick Start Testing

1. **Select a graph** from the dropdown (start with individual agents)
2. **Configure parameters** (defaults work for testing)
3. **Send a test message**

### Example Test Queries

#### Strength Agent
```
"Create a beginner strength training program for muscle building"
"How do I improve my deadlift technique?"
"What's the best rep range for hypertrophy?"
```

#### Nutrition Agent
```
"Calculate my macros for muscle gain - I'm 180lbs, 6ft, moderately active"
"Create a meal plan for cutting fat while maintaining muscle"
"What should I eat before and after workouts?"
```

#### Cardio Agent
```
"Design a 5K training plan for a beginner"
"How can I improve my running endurance?"
"What heart rate zones should I train in?"
```

#### Recovery Agent
```
"How should I recover after intense leg workouts?"
"I'm feeling overtrained - what should I do?"
"Optimize my sleep for better recovery"
```

#### Mental Agent
```
"Help me stay motivated with my fitness goals"
"I lose focus during long workouts - any tips?"
"How do I set realistic and achievable fitness goals?"
```

### Configuration Options

Each graph supports customizable parameters:

- **`user_id`**: Unique identifier for the user session
- **`thread_id`**: Session identifier for conversation continuity
- **`enable_memory`**: Enable/disable conversation memory
- **`max_iterations`**: Maximum ReAct agent iterations (1-10)
- **`temperature`**: LLM response creativity (0.0-2.0)

## 🔧 Troubleshooting

### Common Issues

#### 1. Command Not Found
```bash
zsh: command not found: langgraph
```
**Solution**: Install the CLI
```bash
pip install langgraph-cli
```

#### 2. Missing API Server
```bash
Error: Required package 'langgraph-api' is not installed.
```
**Solution**: Install with API components
```bash
pip install -U "langgraph-cli[inmem]"
```

#### 3. Import Errors
```bash
ModuleNotFoundError: No module named 'athlea_langgraph'
```
**Solution**: Install project dependencies
```bash
pip install -e .
```

#### 4. Port Already in Use
```bash
OSError: [Errno 48] Address already in use
```
**Solution**: Use different port
```bash
langgraph dev --port 8001
```

#### 5. Graph Compilation Errors
**Check**: 
- All imports are available
- Environment variables are set (`.env.local`)
- Dependencies are installed

### Environment Variables

Ensure your `.env.local` file contains:

```env
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_ENDPOINT=your_endpoint
AZURE_OPENAI_API_VERSION=2024-12-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment

# Optional: MongoDB for memory (if using memory features)
MONGODB_URI=mongodb://localhost:27017

# Optional: Tool APIs
AIRTABLE_API_KEY=your_key
GOOGLE_MAPS_API_KEY=your_key
AZURE_MAPS_SUBSCRIPTION_KEY=your_key
```

## 📝 Usage Tips

### Development Workflow

1. **Start with individual agents** for focused testing
2. **Test tool functionality** by asking specific questions
3. **Verify ReAct reasoning** by checking agent thought processes
4. **Move to comprehensive system** once individual agents work
5. **Use memory features** for conversation continuity testing

### Debugging Strategy

1. **Check startup logs** for compilation errors
2. **Test individual tools** before complex workflows
3. **Use verbose logging** for detailed execution traces
4. **Monitor API usage** and rate limits
5. **Verify environment variables** are loaded correctly

### Performance Optimization

- **Disable memory** for faster individual agent testing
- **Reduce max_iterations** for quicker responses during development
- **Use lower temperature** for more consistent outputs during testing

## 🎯 Next Steps

Once LangGraph Studio is running successfully:

1. **Test all individual agents** to verify functionality
2. **Try the comprehensive coaching system** with complex queries
3. **Experiment with different configurations** and parameters
4. **Monitor tool usage and ReAct reasoning** for optimization opportunities
5. **Document any issues** for further investigation

## 📚 Additional Resources

- **Individual Agents Guide**: See `INDIVIDUAL_AGENTS_README.md`
- **Tool Integration**: See `TOOL_INTEGRATION.md` 
- **Testing Guide**: See `TESTING.md`
- **Project Status**: See `PROJECT_STATUS.md`

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** for specific error messages
2. **Verify environment setup** according to this guide
3. **Test individual components** before the full system
4. **Review existing documentation** in the `docs/` folder

Remember: LangGraph Studio is a powerful development tool that significantly speeds up agent testing and debugging workflows! 