"""
PromptLoader utility for managing externalized prompts with versioning and caching.

This module provides the core functionality for loading, validating, and managing
prompts stored in external JSON files with full versioning support.
"""

import asyncio
import json
import logging
import os
import re
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

# LangSmith integration for prompt tracking
try:
    from langchain_core.tracers.context import tracing_v2_enabled
    from langsmith import Client, traceable

    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False

    # Create dummy decorator if LangSmith not available
    def traceable(func):
        return func


from .prompt_models import (
    PromptConfig,
    PromptContent,
    PromptMetadata,
    PromptType,
    PromptValidation,
    PromptVariables,
)

logger = logging.getLogger(__name__)

# Initialize Lang<PERSON>mith client if available
_langsmith_client = None
if LANGSMITH_AVAILABLE and os.getenv("LANGSMITH_TRACING") == "true":
    try:
        _langsmith_client = Client()
        logger.info("LangSmith client initialized for prompt tracking")
    except Exception as e:
        logger.warning(f"Failed to initialize LangSmith client: {e}")


class PromptNotFoundError(Exception):
    """Raised when a requested prompt cannot be found."""

    pass


class PromptValidationError(Exception):
    """Raised when prompt validation fails."""

    pass


class PromptVersionError(Exception):
    """Raised when there's an issue with prompt versioning."""

    pass


class PromptLoader:
    """
    Utility class for loading and managing externalized prompts.

    Provides functionality for:
    - Loading prompts from JSON files
    - Version management and resolution
    - Caching for performance
    - Validation and error handling
    - Template rendering with context
    """

    def __init__(self, prompts_dir: str = None):
        """
        Initialize the PromptLoader.

        Args:
            prompts_dir: Path to the prompts directory. Defaults to athlea_langgraph/prompts
        """
        if prompts_dir is None:
            # Default to the prompts directory relative to this file
            current_dir = Path(__file__).parent.parent
            prompts_dir = current_dir / "prompts"

        self.prompts_dir = Path(prompts_dir)
        self._cache: Dict[str, PromptConfig] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._initialized = False

        logger.info(f"PromptLoader initialized with directory: {self.prompts_dir}")

    async def initialize(self) -> None:
        """Initialize the prompt loader and scan for available prompts."""
        if not self.prompts_dir.exists():
            raise FileNotFoundError(f"Prompts directory not found: {self.prompts_dir}")

        await self._scan_prompts()
        self._initialized = True
        logger.info("PromptLoader initialized successfully")

    async def _scan_prompts(self) -> None:
        """Scan the prompts directory for available prompt files."""
        logger.info("Scanning prompts directory...")

        # Use asyncio.to_thread to avoid blocking the event loop
        prompt_files = await asyncio.to_thread(
            lambda: list(self.prompts_dir.rglob("*.json"))
        )
        logger.info(f"Found {len(prompt_files)} prompt files")

        for file_path in prompt_files:
            try:
                # Check file timestamp for cache invalidation (using asyncio.to_thread)
                timestamp = await asyncio.to_thread(lambda: file_path.stat().st_mtime)
                relative_path = str(file_path.relative_to(self.prompts_dir))
                self._file_timestamps[relative_path] = timestamp
            except Exception as e:
                logger.warning(f"Error scanning prompt file {file_path}: {e}")

    @traceable(run_type="retriever", name="prompt_loader")
    async def load_prompt(
        self, prompt_id: str, version: str = "latest"
    ) -> PromptConfig:
        """
        Load a specific prompt with version support.

        Args:
            prompt_id: Identifier for the prompt (e.g., "strength_coach", "reasoning_node")
            version: Version to load ("latest" for most recent, or specific version like "1.2.0")

        Returns:
            PromptConfig object with the loaded prompt

        Raises:
            PromptNotFoundError: If the prompt cannot be found
            PromptValidationError: If the prompt fails validation
            PromptVersionError: If the requested version is not available
        """
        if not self._initialized:
            await self.initialize()

        cache_key = f"{prompt_id}:{version}"

        # Track prompt loading in LangSmith
        metadata = {
            "prompt_id": prompt_id,
            "version": version,
            "cache_key": cache_key,
            "timestamp": datetime.now().isoformat(),
        }

        # Check cache first
        if cache_key in self._cache:
            # Check if file has been modified
            if not await self._is_file_modified(prompt_id):
                if _langsmith_client:
                    try:
                        # Log cache hit
                        _langsmith_client.create_run(
                            name=f"prompt_load_cache_hit",
                            run_type="retriever",
                            inputs={"prompt_id": prompt_id, "version": version},
                            outputs={"cache_hit": True},
                            extra=metadata,
                            tags=["prompt_loading", "cache_hit"],
                        )
                    except Exception as e:
                        logger.debug(f"LangSmith logging failed: {e}")

                return self._cache[cache_key]

        # Load from file and get the file path
        prompt_config, file_path = await self._load_from_file_with_path(
            prompt_id, version
        )

        # Validate the prompt
        validation_errors = prompt_config.validate()
        if validation_errors:
            error_msg = f"Prompt validation failed for {prompt_id}: {'; '.join(validation_errors)}"
            logger.error(error_msg)

            # Log validation error to LangSmith
            if _langsmith_client:
                try:
                    _langsmith_client.create_run(
                        name=f"prompt_load_validation_error",
                        run_type="retriever",
                        inputs={"prompt_id": prompt_id, "version": version},
                        outputs={"error": error_msg},
                        extra={**metadata, "validation_errors": validation_errors},
                        tags=["prompt_loading", "validation_error"],
                    )
                except Exception as e:
                    logger.debug(f"LangSmith logging failed: {e}")

            raise PromptValidationError(error_msg)

        # Cache the result
        self._cache[cache_key] = prompt_config

        # Log successful load to LangSmith
        if _langsmith_client:
            try:
                prompt_content_preview = (
                    prompt_config.prompt.system[:200] + "..."
                    if len(prompt_config.prompt.system) > 200
                    else prompt_config.prompt.system
                )

                _langsmith_client.create_run(
                    name=f"prompt_load_success",
                    run_type="retriever",
                    inputs={"prompt_id": prompt_id, "version": version},
                    outputs={
                        "prompt_name": prompt_config.metadata.name,
                        "prompt_version": prompt_config.metadata.version,
                        "prompt_type": prompt_config.metadata.prompt_type,
                        "content_preview": prompt_content_preview,
                        "character_count": len(prompt_config.prompt.system),
                    },
                    extra={
                        **metadata,
                        "file_path": str(file_path),
                        "tags": prompt_config.metadata.tags,
                        "experimental": prompt_config.metadata.experimental,
                        "deprecated": prompt_config.metadata.deprecated,
                    },
                    tags=[
                        "prompt_loading",
                        "success",
                        f"type_{prompt_config.metadata.prompt_type}",
                    ],
                )
            except Exception as e:
                logger.debug(f"LangSmith logging failed: {e}")

        logger.info(
            f"Loaded prompt {prompt_id} version {prompt_config.metadata.version}"
        )
        return prompt_config

    async def _load_from_file(self, prompt_id: str, version: str) -> PromptConfig:
        """Load prompt from JSON file."""
        prompt_config, _ = await self._load_from_file_with_path(prompt_id, version)
        return prompt_config

    async def _load_from_file_with_path(
        self, prompt_id: str, version: str
    ) -> tuple[PromptConfig, Path]:
        """Load prompt from JSON file and return both config and file path."""
        file_path = await self._find_prompt_file(prompt_id)

        if not await asyncio.to_thread(lambda: file_path.exists()):
            raise PromptNotFoundError(f"Prompt file not found for: {prompt_id}")

        try:
            # Use asyncio.to_thread for file I/O operations
            def read_file():
                with open(file_path, "r", encoding="utf-8") as f:
                    return json.load(f)

            data = await asyncio.to_thread(read_file)
            prompt_config = PromptConfig.from_dict(data)

            # Handle version resolution
            if version != "latest" and prompt_config.metadata.version != version:
                # For now, we only support "latest" - in future, implement version history
                raise PromptVersionError(
                    f"Version {version} not found for {prompt_id}. Available: {prompt_config.metadata.version}"
                )

            return prompt_config, file_path

        except json.JSONDecodeError as e:
            raise PromptValidationError(f"Invalid JSON in prompt file {file_path}: {e}")
        except Exception as e:
            raise PromptNotFoundError(f"Error loading prompt {prompt_id}: {e}")

    async def _find_prompt_file(self, prompt_id: str) -> Path:
        """Find the prompt file for a given prompt ID."""
        # Try direct file name first
        direct_path = self.prompts_dir / f"{prompt_id}.json"
        if await asyncio.to_thread(lambda: direct_path.exists()):
            return direct_path

        # Search in subdirectories
        def search_subdirs():
            for subdir in self.prompts_dir.iterdir():
                if subdir.is_dir():
                    file_path = subdir / f"{prompt_id}.json"
                    if file_path.exists():
                        return file_path
            return None

        subdir_result = await asyncio.to_thread(search_subdirs)
        if subdir_result:
            return subdir_result

        # Try recursive search
        matches = await asyncio.to_thread(
            lambda: list(self.prompts_dir.rglob(f"{prompt_id}.json"))
        )
        if matches:
            return matches[0]

        return (
            self.prompts_dir / f"{prompt_id}.json"
        )  # Return expected path for error reporting

    async def _is_file_modified(self, prompt_id: str) -> bool:
        """Check if the prompt file has been modified since last load."""
        file_path = await self._find_prompt_file(prompt_id)
        if not await asyncio.to_thread(lambda: file_path.exists()):
            return True

        relative_path = str(file_path.relative_to(self.prompts_dir))
        current_timestamp = await asyncio.to_thread(lambda: file_path.stat().st_mtime)
        cached_timestamp = self._file_timestamps.get(relative_path)

        if cached_timestamp is None or current_timestamp > cached_timestamp:
            self._file_timestamps[relative_path] = current_timestamp
            return True

        return False

    async def get_all_versions(self, prompt_id: str) -> List[str]:
        """
        Get all available versions for a prompt.

        Args:
            prompt_id: Identifier for the prompt

        Returns:
            List of available version strings

        Note: Currently returns only the latest version.
              Future implementation will support version history.
        """
        try:
            prompt_config = await self.load_prompt(prompt_id, "latest")
            return [prompt_config.metadata.version]
        except PromptNotFoundError:
            return []

    async def list_prompts(self, prompt_type: Optional[PromptType] = None) -> List[str]:
        """
        List all available prompts.

        Args:
            prompt_type: Optional filter by prompt type

        Returns:
            List of prompt IDs
        """
        if not self._initialized:
            await self.initialize()

        prompts = []
        for file_path in await asyncio.to_thread(
            lambda: list(self.prompts_dir.rglob("*.json"))
        ):
            try:
                prompt_id = file_path.stem
                if prompt_type:
                    # Load prompt to check type
                    config = await self.load_prompt(prompt_id)
                    if config.metadata.prompt_type == prompt_type:
                        prompts.append(prompt_id)
                else:
                    prompts.append(prompt_id)
            except Exception as e:
                logger.warning(f"Error processing prompt file {file_path}: {e}")

        return sorted(prompts)

    def validate_prompt(self, prompt_config: PromptConfig) -> bool:
        """
        Validate a prompt configuration.

        Args:
            prompt_config: PromptConfig to validate

        Returns:
            True if valid, False otherwise
        """
        errors = prompt_config.validate()
        if errors:
            logger.error(f"Prompt validation errors: {'; '.join(errors)}")
            return False
        return True

    async def render_prompt(
        self, prompt_id: str, context: Dict[str, Any] = None, version: str = "latest"
    ) -> str:
        """
        Render a prompt with context variables.

        Args:
            prompt_id: Identifier for the prompt
            context: Dictionary of variables for template substitution
            version: Version of the prompt to use

        Returns:
            Rendered prompt string
        """
        prompt_config = await self.load_prompt(prompt_id, version)
        return prompt_config.get_rendered_prompt(context)

    def save_prompt(
        self, prompt_config: PromptConfig, prompt_id: Optional[str] = None
    ) -> Path:
        """
        Save a prompt configuration to file.

        Args:
            prompt_config: PromptConfig to save
            prompt_id: Optional custom prompt ID (defaults to metadata.name)

        Returns:
            Path where the prompt was saved
        """
        if not self.validate_prompt(prompt_config):
            raise PromptValidationError("Cannot save invalid prompt")

        # Determine save location
        if prompt_id is None:
            prompt_id = prompt_config.metadata.name

        # Determine subdirectory based on prompt type
        subdir_map = {
            PromptType.COACH: "coaches",
            PromptType.REASONING: "reasoning",
            PromptType.PLANNING: "reasoning",
            PromptType.ONBOARDING: "onboarding",
            PromptType.SYSTEM: "system",
            PromptType.REACT: "system",
        }

        subdir = subdir_map.get(prompt_config.metadata.prompt_type, "system")
        file_path = self.prompts_dir / subdir / f"{prompt_id}.json"

        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Save to file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(prompt_config.to_json())

        # Invalidate cache
        cache_key = f"{prompt_id}:latest"
        if cache_key in self._cache:
            del self._cache[cache_key]

        logger.info(f"Saved prompt {prompt_id} to {file_path}")
        return file_path

    def clear_cache(self) -> None:
        """Clear the prompt cache."""
        self._cache.clear()
        self._file_timestamps.clear()
        logger.info("Prompt cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_prompts": len(self._cache),
            "tracked_files": len(self._file_timestamps),
            "cache_keys": list(self._cache.keys()),
        }

    def list_prompts_sync(self, prompt_type: Optional[PromptType] = None) -> List[str]:
        """
        Synchronous version of list_prompts.

        Args:
            prompt_type: Optional filter by prompt type

        Returns:
            List of prompt IDs
        """
        import asyncio

        if not self._initialized:
            # Initialize synchronously
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    raise RuntimeError(
                        "Cannot initialize PromptLoader synchronously in async context"
                    )
                else:
                    loop.run_until_complete(self.initialize())
            except RuntimeError:
                asyncio.run(self.initialize())

        prompts = []
        for file_path in self.prompts_dir.rglob("*.json"):
            try:
                prompt_id = file_path.stem
                if prompt_type:
                    # Load prompt to check type
                    config = self.load_prompt_sync(prompt_id)
                    if config.metadata.prompt_type == prompt_type:
                        prompts.append(prompt_id)
                else:
                    prompts.append(prompt_id)
            except Exception as e:
                logger.warning(f"Error processing prompt file {file_path}: {e}")

        return sorted(prompts)

    def load_prompt_sync(self, prompt_id: str, version: str = "latest") -> PromptConfig:
        """
        Synchronous version of load_prompt.

        Args:
            prompt_id: Identifier for the prompt
            version: Version to load

        Returns:
            PromptConfig object
        """
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                raise RuntimeError("Cannot use load_prompt_sync in async context")
            else:
                return loop.run_until_complete(self.load_prompt(prompt_id, version))
        except RuntimeError:
            return asyncio.run(self.load_prompt(prompt_id, version))


# Global prompt loader instance
_prompt_loader: Optional[PromptLoader] = None


async def get_prompt_loader(prompts_dir: str = None) -> PromptLoader:
    """
    Get the global prompt loader instance.

    Args:
        prompts_dir: Optional custom prompts directory

    Returns:
        PromptLoader instance
    """
    global _prompt_loader

    if _prompt_loader is None or prompts_dir is not None:
        _prompt_loader = PromptLoader(prompts_dir)

    if not _prompt_loader._initialized:
        await _prompt_loader.initialize()

    return _prompt_loader


async def load_prompt(
    prompt_id: str, version: str = "latest", context: Dict[str, Any] = None
) -> str:
    """
    Convenience function to load and render a prompt.

    Args:
        prompt_id: Identifier for the prompt
        version: Version to load
        context: Context variables for rendering

    Returns:
        Rendered prompt string
    """
    loader = await get_prompt_loader()
    return await loader.render_prompt(prompt_id, context, version)


def get_prompt_loader_sync(prompts_dir: str = None) -> PromptLoader:
    """
    Synchronous version of get_prompt_loader for backwards compatibility.

    Args:
        prompts_dir: Optional custom prompts directory

    Returns:
        PromptLoader instance (initialized synchronously)
    """
    global _prompt_loader

    if _prompt_loader is None or prompts_dir is not None:
        _prompt_loader = PromptLoader(prompts_dir)

    # Initialize synchronously if not already done
    if not _prompt_loader._initialized:
        import asyncio

        try:
            # Try to get current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context - this should not happen for sync usage
                raise RuntimeError(
                    "Cannot initialize PromptLoader synchronously in async context"
                )
            else:
                loop.run_until_complete(_prompt_loader.initialize())
        except RuntimeError:
            # No event loop exists, create one
            asyncio.run(_prompt_loader.initialize())

    return _prompt_loader


def load_prompt_sync(
    prompt_id: str, version: str = "latest", context: Dict[str, Any] = None
) -> str:
    """
    Synchronous convenience function to load and render a prompt.
    This is a wrapper around the async version for backwards compatibility.

    Args:
        prompt_id: Identifier for the prompt
        version: Version to load
        context: Context variables for rendering

    Returns:
        Rendered prompt string
    """
    import asyncio

    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # We're in an async context, use asyncio.to_thread to avoid blocking
            async def _wrapper():
                return await load_prompt(prompt_id, version, context)

            # This will be handled by the caller using asyncio.to_thread
            raise RuntimeError("Use async load_prompt or wrap with asyncio.to_thread")
        else:
            # We're not in an async context, can run directly
            return loop.run_until_complete(load_prompt(prompt_id, version, context))
    except RuntimeError:
        # No event loop exists, create one
        return asyncio.run(load_prompt(prompt_id, version, context))
