#!/usr/bin/env python3
"""
Test Fixed ReAct Specialized Coaches

This script tests the corrected ReAct implementation that follows the official
LangGraph pattern to fix the tool message flow issues.
"""

import asyncio
import logging
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_fixed_react_coaches():
    """Test the fixed ReAct specialized coaches implementation."""
    print("🧪 Testing Fixed ReAct Specialized Coaches")
    print("=" * 60)

    try:
        # Import the fixed implementation
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.specialized_coaches_react_fixed import (
            get_fixed_specialized_coach,
            list_fixed_available_coaches,
        )
        from athlea_langgraph.state import AgentState

        # Test scenarios
        test_scenarios = [
            {
                "coach": "strength_coach",
                "query": "What is RPE and how should I use it in my strength training?",
                "description": "Should search for RPE information first, then provide explanation",
            },
            {
                "coach": "nutrition_coach",
                "query": "What are the benefits of creatine supplementation?",
                "description": "Should search for creatine research first, then provide summary",
            },
        ]

        # List available coaches
        coaches = await list_fixed_available_coaches()
        print(f"📋 Available coaches: {coaches}")
        print()

        # Test each scenario
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"📋 Scenario {i}/{len(test_scenarios)}")
            print(f"🎯 Testing Fixed ReAct {scenario['coach']}")
            print(f"Query: {scenario['query']}")
            print(f"Expected: {scenario['description']}")
            print("-" * 80)

            try:
                # Get the coach
                coach = await get_fixed_specialized_coach(scenario["coach"])
                if not coach:
                    print(f"❌ Coach {scenario['coach']} not available")
                    continue

                # Create state with user query
                state = AgentState(
                    messages=[HumanMessage(content=scenario["query"])],
                    user_query=scenario["query"],
                )

                # Invoke the coach
                print("🚀 Invoking fixed ReAct coach...")
                result = await coach.invoke(state)

                # Display results
                messages = result.get("messages", [])
                print(f"✅ Completed - got {len(messages)} messages")

                # Show the conversation flow
                for j, msg in enumerate(messages):
                    msg_type = type(msg).__name__
                    content_preview = (
                        str(msg.content)[:150] + "..."
                        if len(str(msg.content)) > 150
                        else str(msg.content)
                    )

                    if hasattr(msg, "tool_calls") and msg.tool_calls:
                        print(
                            f"  Message {j+1}: {msg_type} - Tool calls: {[tc['name'] for tc in msg.tool_calls]}"
                        )
                    else:
                        print(f"  Message {j+1}: {msg_type} - {content_preview}")

                print("✅ Test completed successfully!")

            except Exception as e:
                print(f"❌ Error in scenario {i}: {e}")
                import traceback

                traceback.print_exc()

            print("\n" + "=" * 80 + "\n")

        print("🎉 Fixed ReAct testing completed!")

    except Exception as e:
        print(f"❌ Failed to test fixed ReAct coaches: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_fixed_react_coaches())
