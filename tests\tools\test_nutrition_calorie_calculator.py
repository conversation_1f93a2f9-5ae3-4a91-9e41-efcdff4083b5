"""
Tests for the NutritionCalorieCalculator tool.
"""

import pytest

from athlea_langgraph.tools.nutrition.calorie_calculator import (
    NutritionCalorieCalculator,
)
from athlea_langgraph.tools.schemas.nutrition_schemas import (
    CalorieCalculationInput,
    CalorieCalculationOutput,
    MacroTargetsInput,
    MacroTargetsOutput,
)


@pytest.fixture
def calculator() -> NutritionCalorieCalculator:
    """Fixture to provide an instance of the NutritionCalorieCalculator."""
    return NutritionCalorieCalculator()


# Test cases for BMR calculations
# Mifflin-St Jeor
@pytest.mark.parametrize(
    "age, gender, weight_kg, height_cm, expected_bmr",
    [
        (30, "male", 80, 180, 1780),  # Corrected Male example
        (30, "female", 60, 165, 1320.25),  # Corrected Female example
        (45, "male", 90, 175, 1773.75),  # Corrected
        (25, "female", 55, 160, 1264),  # Corrected
        (60, "male", 70, 190, 1592.5),  # Corrected
    ],
)
def test_calculate_mifflin_st_jeor(
    calculator: NutritionCalorieCalculator,
    age: int,
    gender: str,
    weight_kg: float,
    height_cm: float,
    expected_bmr: float,
):
    """Test the _calculate_mifflin_st_jeor method."""
    bmr = calculator._calculate_mifflin_st_jeor(age, gender, weight_kg, height_cm)
    assert bmr == pytest.approx(
        expected_bmr, abs=1
    )  # abs=1 to account for rounding in examples


# Harris-Benedict
@pytest.mark.parametrize(
    "age, gender, weight_kg, height_cm, expected_bmr",
    [
        (30, "male", 80, 180, 1853.632),  # Corrected Male example
        (30, "female", 60, 165, 1383.683),  # Corrected Female example
        (45, "male", 90, 175, 1878.452),  # Corrected
        (25, "female", 55, 160, 1343.608),  # Corrected
    ],
)
def test_calculate_harris_benedict(
    calculator: NutritionCalorieCalculator,
    age: int,
    gender: str,
    weight_kg: float,
    height_cm: float,
    expected_bmr: float,
):
    """Test the _calculate_harris_benedict method."""
    bmr = calculator._calculate_harris_benedict(age, gender, weight_kg, height_cm)
    assert bmr == pytest.approx(expected_bmr, abs=1)


# Katch-McArdle
@pytest.mark.parametrize(
    "weight_kg, body_fat_percentage, expected_bmr",
    [
        (80, 15, 1838.8),  # Corrected: 80kg, 15% BF -> LBM = 68kg
        (
            60,
            25,
            1342,
        ),  # Correct: 60kg, 25% BF -> LBM = 45kg (This one was likely passing)
        (90, 20, 1925.2),  # Corrected: 90kg, 20% BF -> LBM = 72kg
        (70, 10, 1730.8),  # Corrected: 70kg, 10% BF -> LBM = 63kg
    ],
)
def test_calculate_katch_mcardle(
    calculator: NutritionCalorieCalculator,
    weight_kg: float,
    body_fat_percentage: float,
    expected_bmr: float,
):
    """Test the _calculate_katch_mcardle method."""
    bmr = calculator._calculate_katch_mcardle(weight_kg, body_fat_percentage)
    assert bmr == pytest.approx(expected_bmr, abs=1)


# Test for _adjust_calories_for_goal
@pytest.mark.parametrize(
    "tdee, goal, expected_adjusted_calories",
    [
        (2000, "weight_loss", 1500),
        (2000, "aggressive_weight_loss", 1250),
        (2000, "weight_gain", 2300),
        (2000, "muscle_gain", 2200),
        (2000, "maintenance", 2000),
        (2000, "body_recomposition", 1800),
        (2000, "performance", 2100),
        (2000, "unknown_goal", 2000),  # Test default case
        (2500, "weight_loss", 2000),
    ],
)
def test_adjust_calories_for_goal(
    calculator: NutritionCalorieCalculator,
    tdee: float,
    goal: str,
    expected_adjusted_calories: float,
):
    """Test the _adjust_calories_for_goal method."""
    adjusted_calories = calculator._adjust_calories_for_goal(tdee, goal)
    assert adjusted_calories == pytest.approx(expected_adjusted_calories)


# Tests for the main calculate_daily_calories method
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, input_data, expected_output_partial",
    [
        (
            "male_mifflin_maintenance",
            CalorieCalculationInput(
                age=35,
                gender="male",
                weight_kg=85,
                height_cm=175,
                activity_level="moderately_active",
                goal="maintenance",
            ),
            {
                "bmr": 1774,
                "tdee": 2749,
                "target_calories": 2749,
                "formula_used": "mifflin_st_jeor",
            },
        ),
        (
            "female_katch_weight_loss",
            CalorieCalculationInput(
                age=28,
                gender="female",
                weight_kg=65,
                height_cm=160,
                activity_level="lightly_active",
                body_fat_percentage=22.0,
                goal="weight_loss",
            ),
            # LBM = 65 * (1 - 0.22) = 50.7 kg. BMR = 370 + (21.6 * 50.7) = 1465
            # TDEE = 1465 * 1.375 = 2014
            # Target Calories = 2014 - 500 = 1514
            {
                "bmr": 1465,
                "tdee": 2014,
                "target_calories": 1514,
                "formula_used": "katch_mcardle",
            },
        ),
        (
            "male_mifflin_muscle_gain_very_active",
            CalorieCalculationInput(
                age=25,
                gender="male",
                weight_kg=70,
                height_cm=180,
                activity_level="very_active",
                goal="muscle_gain",
            ),
            # BMR = (10*70) + (6.25*180) - (5*25) + 5 = 700 + 1125 - 125 + 5 = 1705
            # TDEE = 1705 * 1.725 = 2941
            # Target Calories = 2941 + 200 = 3141
            {
                "bmr": 1705,
                "tdee": 2941,
                "target_calories": 3141,
                "formula_used": "mifflin_st_jeor",
            },
        ),
        (
            "female_mifflin_aggressive_loss_sedentary",
            CalorieCalculationInput(
                age=40,
                gender="female",
                weight_kg=75,
                height_cm=165,
                activity_level="sedentary",
                goal="aggressive_weight_loss",
            ),
            # BMR = (10*75) + (6.25*165) - (5*40) - 161 = 750 + 1031.25 - 200 - 161 = 1420.25
            # TDEE = 1420.25 * 1.2 = 1704.3
            # Target Calories = 1704.3 - 750 = 954.3
            {
                "bmr": 1420,
                "tdee": 1704,
                "target_calories": 954,
                "formula_used": "mifflin_st_jeor",
            },
        ),
    ],
)
async def test_calculate_daily_calories(
    calculator: NutritionCalorieCalculator,
    test_id: str,
    input_data: CalorieCalculationInput,
    expected_output_partial: dict,
):
    """Test the main calculate_daily_calories method."""
    result = await calculator.calculate_daily_calories(input_data)

    assert result.bmr == pytest.approx(expected_output_partial["bmr"], abs=1)
    assert result.tdee == pytest.approx(expected_output_partial["tdee"], abs=1)
    assert result.target_calories == pytest.approx(
        expected_output_partial["target_calories"], abs=1
    )
    assert result.formula_used == expected_output_partial["formula_used"]
    assert result.error is None
    assert len(result.recommendations) > 0


@pytest.mark.asyncio
async def test_calculate_daily_calories_error_handling(
    calculator: NutritionCalorieCalculator,
):
    """Test calculate_daily_calories with problematic input (though Pydantic should catch most)."""
    # Pydantic will likely raise validation error before method is called for type issues.
    # This tests a scenario where a non-Pydantic error might occur if logic was different.
    # For this tool, an invalid activity_level not in the dict might be a simulated case if not for pydantic.
    # However, CalorieCalculationInput uses Literal for activity_level, so this is hard to test at this layer.

    # Let's simulate an internal error by patching a helper to raise an exception
    # For now, we'll assume Pydantic handles input validation and focus on expected paths.
    # A more direct way to test this would be to mock a helper function if it could fail.

    # Test with a valid input that might cause issues if a helper was flawed
    # For instance, if _adjust_calories_for_goal was to fail for some reason.
    # Given the current structure, a direct failure test without mocking is tricky.
    pass  # Placeholder, as direct error injection without mocking is complex here.


# Tests for _get_macro_distribution
@pytest.mark.parametrize(
    "goal, dietary_preferences, expected_distribution",
    [
        (
            "weight_loss",
            [],
            {"protein": 0.30, "carbs": 0.35, "fat": 0.35},
        ),
        (
            "muscle_gain",
            ["high_protein"],
            # protein: 0.25 + 0.10 = 0.35
            # carbs: 0.45 - 0.05 = 0.40
            # fat: 0.30 - 0.05 = 0.25
            {"protein": 0.35, "carbs": 0.40, "fat": 0.25},
        ),
        (
            "maintenance",
            ["low_carb"],
            # carbs: 0.40 -> target 0.25 (0.40 - 0.15 = 0.25)
            # reduction = 0.15
            # fat: 0.35 + 0.15 = 0.50
            {"protein": 0.25, "carbs": 0.25, "fat": 0.50},
        ),
        (
            "performance",
            ["keto"],
            {"protein": 0.25, "carbs": 0.05, "fat": 0.70},
        ),
        (
            "body_recomposition",
            ["high_protein", "low_carb"],
            # Start with body_recomp: P:0.35, C:0.30, F:0.35
            # High protein: P: 0.35+0.10=0.40 (capped at 0.40), C: 0.30-0.05=0.25, F: 0.35-0.05=0.30
            # Low carb: C: 0.25 -> reduction = min(0.15, 0.25-0.15) = min(0.15, 0.10) = 0.10
            # C: 0.25-0.10=0.15, F: 0.30+0.10=0.40
            {"protein": 0.40, "carbs": 0.15, "fat": 0.40},
        ),
        (
            "unknown_goal",  # Should default to maintenance
            [],
            {"protein": 0.25, "carbs": 0.40, "fat": 0.35},
        ),
    ],
)
def test_get_macro_distribution(
    calculator: NutritionCalorieCalculator,
    goal: str,
    dietary_preferences: list[str],
    expected_distribution: dict,
):
    """Test the _get_macro_distribution method."""
    distribution = calculator._get_macro_distribution(goal, dietary_preferences)
    assert distribution["protein"] == pytest.approx(
        expected_distribution["protein"], abs=0.01
    )
    assert distribution["carbs"] == pytest.approx(
        expected_distribution["carbs"], abs=0.01
    )
    assert distribution["fat"] == pytest.approx(expected_distribution["fat"], abs=0.01)


# Tests for _adjust_protein_for_preference
@pytest.mark.parametrize(
    "protein_grams, preference, target_calories, expected_protein_grams",
    [
        (100, "high", 2000, 150),  # 2000 * 0.30 / 4 = 150. 150 > 100.
        (160, "high", 2000, 160),  # 150 < 160.
        (100, "standard", 2000, 100),
        (
            100,
            "low",
            2000,
            100,
        ),  # No specific 'low' adjustment implemented currently other than default
        (120, "high", 2500, 187.5),  # 2500 * 0.30 / 4 = 187.5
    ],
)
def test_adjust_protein_for_preference(
    calculator: NutritionCalorieCalculator,
    protein_grams: float,
    preference: str,
    target_calories: int,
    expected_protein_grams: float,
):
    """Test the _adjust_protein_for_preference method."""
    adjusted_protein = calculator._adjust_protein_for_preference(
        protein_grams, preference, target_calories
    )
    assert adjusted_protein == pytest.approx(expected_protein_grams)


# Tests for the main calculate_macro_targets method
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, input_data, expected_output_partial",
    [
        (
            "maintenance_default",
            MacroTargetsInput(target_calories=2500, goal="maintenance"),
            # Maintenance: P:0.25, C:0.40, F:0.35
            # P = 2500*0.25/4 = 156.25g
            # C = 2500*0.40/4 = 250g
            # F = 2500*0.35/9 = 97.22g
            {
                "protein_grams": 156.2,
                "carb_grams": 250.0,
                "fat_grams": 97.2,
                "protein_percentage": 25.0,
            },
        ),
        (
            "muscle_gain_high_protein_pref",
            MacroTargetsInput(
                target_calories=3000,
                goal="muscle_gain",
                dietary_preferences=["high_protein"],
                protein_preference="high",
            ),
            # Muscle gain base: P:0.25, C:0.45, F:0.30
            # Pref high_protein: P:0.35, C:0.40, F:0.25
            # Initial P grams = 3000 * 0.35 / 4 = 262.5g
            # Pref high protein adj: target_calories * 0.30 / 4 = 3000 * 0.30 / 4 = 225. Max(262.5, 225) = 262.5
            # C = 3000 * 0.40 / 4 = 300g
            # F = 3000 * 0.25 / 9 = 83.33g
            {
                "protein_grams": 262.5,
                "carb_grams": 300.0,
                "fat_grams": 83.3,
                "protein_percentage": 35.0,
            },
        ),
        (
            "weight_loss_keto",
            MacroTargetsInput(
                target_calories=1800,
                goal="weight_loss",  # Goal doesn't matter if keto is specified
                dietary_preferences=["keto"],
            ),
            # Keto: P:0.25, C:0.05, F:0.70
            # P = 1800*0.25/4 = 112.5g
            # C = 1800*0.05/4 = 22.5g
            # F = 1800*0.70/9 = 140g
            {
                "protein_grams": 112.5,
                "carb_grams": 22.5,
                "fat_grams": 140.0,
                "fat_percentage": 70.0,
            },
        ),
    ],
)
async def test_calculate_macro_targets(
    calculator: NutritionCalorieCalculator,
    test_id: str,
    input_data: MacroTargetsInput,
    expected_output_partial: dict,
):
    """Test the main calculate_macro_targets method."""
    result = await calculator.calculate_macro_targets(input_data)

    assert result.protein_grams == pytest.approx(
        expected_output_partial["protein_grams"], abs=0.1
    )
    assert result.carb_grams == pytest.approx(
        expected_output_partial["carb_grams"], abs=0.1
    )
    assert result.fat_grams == pytest.approx(
        expected_output_partial["fat_grams"], abs=0.1
    )
    if "protein_percentage" in expected_output_partial:
        assert result.protein_percentage == pytest.approx(
            expected_output_partial["protein_percentage"], abs=0.1
        )
    if "fat_percentage" in expected_output_partial:
        assert result.fat_percentage == pytest.approx(
            expected_output_partial["fat_percentage"], abs=0.1
        )
    assert result.error is None
    assert len(result.distribution_rationale) > 0
    assert result.meal_distribution is not None


@pytest.mark.asyncio
async def test_calculate_macro_targets_error_handling(
    calculator: NutritionCalorieCalculator,
):
    """Test calculate_macro_targets with problematic input."""
    # Pydantic handles most input validation. This is a placeholder for internal logic errors.
    pass  # Placeholder


# Tests for helper recommendation/explanation functions
@pytest.mark.parametrize(
    "goal, target_calories, expected_keywords",
    [
        ("weight_loss", 1500, ["track", "nutrient-dense", "protein"]),
        ("muscle_gain", 3000, ["surplus", "protein", "carbohydrates"]),
        ("maintenance", 2200, ["quality", "consistent", "adjust"]),
    ],
)
def test_generate_calorie_recommendations(
    calculator: NutritionCalorieCalculator,
    goal: str,
    target_calories: float,
    expected_keywords: list[str],
):
    """Test the _generate_calorie_recommendations method."""
    recommendations = calculator._generate_calorie_recommendations(
        goal, target_calories
    )
    assert isinstance(recommendations, list)
    assert len(recommendations) > 0
    for rec in recommendations:
        assert isinstance(rec, str)
    # Check for presence of keywords
    full_text = " ".join(recommendations).lower()
    for keyword in expected_keywords:
        assert keyword in full_text


@pytest.mark.parametrize(
    "goal, percentages, expected_keywords",
    [
        (
            "muscle_gain",
            {"protein": 0.30, "carbs": 0.45, "fat": 0.25},
            ["muscle synthesis", "training fuel", "recovery"],
        ),
        (
            "weight_loss",
            {"protein": 0.35, "carbs": 0.30, "fat": 0.35},
            ["preserve muscle", "satiety", "hormone"],
        ),
    ],
)
def test_explain_macro_distribution(
    calculator: NutritionCalorieCalculator,
    goal: str,
    percentages: dict,
    expected_keywords: list[str],
):
    """Test the _explain_macro_distribution method."""
    explanation = calculator._explain_macro_distribution(goal, percentages)
    assert isinstance(explanation, str)
    assert len(explanation) > 0
    explanation_lower = explanation.lower()
    for keyword in expected_keywords:
        assert keyword in explanation_lower


@pytest.mark.parametrize(
    "protein_g, carb_g, fat_g, num_meals",
    [
        (150, 250, 80, 4),  # Typical 4 meal scenario
        (200, 300, 100, 5),  # Typical 5 meal scenario
        (100, 150, 50, 3),  # Typical 3 meal scenario
    ],
)
def test_suggest_meal_distribution(
    calculator: NutritionCalorieCalculator,
    protein_g: float,
    carb_g: float,
    fat_g: float,
    num_meals: int,
):
    """Test the _suggest_meal_distribution method."""
    # The internal logic for num_meals is fixed, so we just test if it produces output
    # We're mostly interested if it runs and returns the expected structure.
    distribution = calculator._suggest_meal_distribution(protein_g, carb_g, fat_g)
    assert isinstance(distribution, dict)
    # The tool returns breakfast, lunch, dinner, snacks - so num_meals is effectively 4
    expected_meals = ["breakfast", "lunch", "dinner", "snacks"]
    actual_num_meals = len(expected_meals)

    for meal in expected_meals:
        assert meal in distribution
        assert "protein" in distribution[meal]
        assert "carbs" in distribution[meal]
        assert "fat" in distribution[meal]

    # Check if total distributed macros are close to input, accounting for rounding
    total_p = sum(distribution[meal]["protein"] for meal in expected_meals)
    total_c = sum(distribution[meal]["carbs"] for meal in expected_meals)
    total_f = sum(distribution[meal]["fat"] for meal in expected_meals)

    assert total_p == pytest.approx(
        protein_g, abs=1.0 * actual_num_meals
    )  # Allow for small rounding diff per meal
    assert total_c == pytest.approx(carb_g, abs=1.0 * actual_num_meals)
    assert total_f == pytest.approx(fat_g, abs=1.0 * actual_num_meals)


# Example of how to test a tool-decorated function if needed:
# from athlea_langgraph.tools.nutrition.calorie_calculator import calculate_daily_calories as calculate_daily_calories_tool
# import json
# @pytest.mark.asyncio
# async def test_calculate_daily_calories_tool_decorator():
#     user_data_dict = {
#         "age": 30, "gender": "male", "weight_kg": 80, "height_cm": 180,
#         "activity_level": "moderately_active", "goal": "maintenance"
#     }
#     user_data_json = json.dumps(user_data_dict)
#     result_json = await calculate_daily_calories_tool(user_data_json)
#     result = json.loads(result_json)
#     assert result["target_calories"] == pytest.approx(2765, abs=1)
