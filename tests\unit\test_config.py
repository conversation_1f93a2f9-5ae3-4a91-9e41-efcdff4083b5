import pytest
from unittest.mock import patch, MagicMock
from pydantic import ValidationError

from athlea_langgraph.config import (
    config,
    BaseConfig,
    Environment,
    AzureOpenAIConfig,
    AzureMapsConfig,
    AzureSearchConfig,
    EmbeddingConfig,
    DatabaseConfig,
    AirtableConfig,
    Mem0Config,
    get_config_for_environment,
    validate_config,
    ConfigValidationError,
)


class TestBaseConfig:
    def test_environment_validation(self):
        config_instance = BaseConfig(environment="development")
        assert config_instance.environment == Environment.DEVELOPMENT

        config_instance = BaseConfig(environment="PRODUCTION")
        assert config_instance.environment == Environment.PRODUCTION

    def test_invalid_environment(self):
        with pytest.raises(ValueError):
            BaseConfig(environment="invalid")

    def test_default_values(self):
        config_instance = BaseConfig()
        assert config_instance.environment == Environment.DEVELOPMENT
        assert config_instance.debug is False


class TestAzureOpenAIConfig:
    @patch.dict(
        "os.environ",
        {
            "AZURE_OPENAI_API_KEY": "test-key",
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_DEPLOYMENT_NAME": "test-deployment",
        },
    )
    def test_valid_config(self):
        config_instance = AzureOpenAIConfig()
        assert config_instance.api_key == "test-key"
        assert config_instance.endpoint == "https://test.openai.azure.com/"
        assert config_instance.deployment_name == "test-deployment"

    def test_missing_required_fields(self):
        with pytest.raises(ValidationError):
            AzureOpenAIConfig()


class TestAzureMapsConfig:
    @patch.dict("os.environ", {"AZURE_MAPS_SUBSCRIPTION_KEY": "test-subscription-key"})
    def test_valid_config(self):
        config_instance = AzureMapsConfig()
        assert config_instance.subscription_key == "test-subscription-key"
        assert config_instance.api_version == "1.0"
        assert config_instance.base_url == "https://atlas.microsoft.com/"


class TestAzureSearchConfig:
    @patch.dict(
        "os.environ",
        {
            "AZURE_SEARCH_SERVICE_NAME": "test-service",
            "AZURE_SEARCH_API_KEY": "test-api-key",
        },
    )
    def test_valid_config(self):
        config_instance = AzureSearchConfig()
        assert config_instance.service_name == "test-service"
        assert config_instance.api_key == "test-api-key"
        assert config_instance.endpoint == "https://test-service.search.windows.net/"


class TestEmbeddingConfig:
    @patch.dict(
        "os.environ",
        {
            "EMBEDDING_API_KEY": "test-embedding-key",
            "EMBEDDING_ENDPOINT": "https://test-embedding.com/",
        },
    )
    def test_valid_config(self):
        config_instance = EmbeddingConfig()
        assert config_instance.api_key == "test-embedding-key"
        assert config_instance.endpoint == "https://test-embedding.com/"


class TestDatabaseConfig:
    @patch.dict("os.environ", {"MONGODB_URI": "mongodb://localhost:27017/test"})
    def test_valid_config(self):
        config_instance = DatabaseConfig()
        assert config_instance.mongodb_uri == "mongodb://localhost:27017/test"


class TestAirtableConfig:
    @patch.dict(
        "os.environ",
        {
            "AIRTABLE_API_KEY": "test-airtable-key",
            "AIRTABLE_STRENGTH_BASE_ID": "strength-base",
            "AIRTABLE_CYCLING_BASE_ID": "cycling-base",
            "AIRTABLE_RUNNING_BASE_ID": "running-base",
        },
    )
    def test_valid_config(self):
        config_instance = AirtableConfig()
        assert config_instance.api_key == "test-airtable-key"
        assert config_instance.strength_base_id == "strength-base"
        assert config_instance.cycling_base_id == "cycling-base"
        assert config_instance.running_base_id == "running-base"


class TestMem0Config:
    @patch.dict(
        "os.environ",
        {
            "MEM0_API_KEY": "test-mem0-key",
            "PINECONE_API_KEY": "test-pinecone-key",
            "PINECONE_ENVIRONMENT": "us-west-1",
        },
    )
    def test_valid_config(self):
        config_instance = Mem0Config()
        assert config_instance.api_key == "test-mem0-key"
        assert config_instance.pinecone_api_key == "test-pinecone-key"
        assert config_instance.pinecone_environment == "us-west-1"

    def test_optional_fields(self):
        config_instance = Mem0Config()
        assert config_instance.api_key is None
        assert config_instance.pinecone_api_key is None
        assert config_instance.pinecone_environment == "us-east-1"


class TestConfigValidation:
    @patch.dict(
        "os.environ",
        {
            "AZURE_OPENAI_API_KEY": "test-key",
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_MAPS_SUBSCRIPTION_KEY": "test-maps-key",
            "AZURE_SEARCH_SERVICE_NAME": "test-search",
            "AZURE_SEARCH_API_KEY": "test-search-key",
            "EMBEDDING_API_KEY": "test-embedding-key",
            "EMBEDDING_ENDPOINT": "https://test-embedding.com/",
            "MONGODB_URI": "mongodb://localhost:27017/test",
            "AIRTABLE_API_KEY": "test-airtable-key",
        },
    )
    def test_valid_config_validation(self):
        config_instance = BaseConfig()
        errors = validate_config(config_instance)
        assert len(errors) == 0

    def test_missing_required_config_validation(self):
        config_instance = BaseConfig()
        errors = validate_config(config_instance)
        assert len(errors) > 0
        assert any("Azure OpenAI API key is required" in error for error in errors)


class TestGetConfigForEnvironment:
    def test_development_config(self):
        config_instance = get_config_for_environment("development")
        assert config_instance.environment == Environment.DEVELOPMENT

    def test_production_config(self):
        config_instance = get_config_for_environment("production")
        assert config_instance.environment == Environment.PRODUCTION

    @patch.dict("os.environ", {"ENVIRONMENT": "production"})
    def test_environment_from_env_var(self):
        config_instance = get_config_for_environment()
        assert config_instance.environment == Environment.PRODUCTION
