{"metadata": {"name": "completion_check", "version": "1.1.0", "description": "Determines if the onboarding information gathering process is complete by checking for all required categories and user readiness.", "author": "Athlea System", "created_at": "2025-01-22T00:00:00.000Z", "updated_at": "2025-01-22T00:00:00.000Z", "prompt_type": "onboarding", "tags": ["onboarding", "completion-check", "validation", "decision-making"], "changelog": [{"version": "1.0.0", "date": "2025-05-30T13:36:12.552153", "changes": "Initial migration from hardcoded prompt", "author": "<PERSON><PERSON>", "breaking_changes": false}, {"version": "1.1.0", "date": "2025-01-22T00:00:00.000Z", "changes": "Moved to dedicated onboarding folder with enhanced validation criteria and examples", "author": "Athlea System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "Analyze the provided conversation history, focusing ONLY on information explicitly stated by the USER.\n\n**Your Task:** Verify if the USER has provided information covering ALL of the following categories:\n1.  **Specific Fitness Goal(s):** At least one clear goal mentioned for their primary sport(s).\n2.  **Experience Level:** Some statement about their experience (overall or per sport).\n3.  **Time Commitment:** Details on days/week, duration, or time of day.\n4.  **Equipment Access:** Mention of available equipment or workout location relevant to their goals.\n5.  **Priorities/Connections/Seasonality:** Statement on how goals relate, primary focus, or relevant time constraints.\n\n**Additional Check:** Examine the VERY LAST user message. Does it clearly indicate readiness to proceed or explicitly ask for the plan (e.g., \"Okay\", \"Yes\", \"Let's do it\", \"Generate the plan\", \"Sounds good\", \"That's everything\", confirming the last piece of info)?\n\n**Response Rules:**\n- Respond ONLY with the word \"true\" IF AND ONLY IF:\n    - There is clear evidence from the USER for **ALL 5 required categories** listed above.\n    - AND the **last user message** indicates readiness to proceed.\n- Respond ONLY with the word \"false\" otherwise (if any category is missing OR the user isn't ready).\n\nDo not provide any explanation or other text.", "context_template": "Conversation History:\n{conversation_history}", "user_template": null, "examples": [{"scenario": "All categories covered and user confirms readiness", "conversation_summary": "User mentioned running goals, intermediate experience, 3x/week availability, home gym access, prioritizes endurance, and said 'Sounds good, let's create the plan!'", "expected_output": "true"}, {"scenario": "Missing equipment information", "conversation_summary": "User mentioned running goals, beginner level, 4x/week availability, but no equipment/location details mentioned", "expected_output": "false"}, {"scenario": "Complete info but not ready example", "conversation_summary": "User provided all required information but last message was 'I need to think about it more'", "expected_output": "false"}], "instructions": "Check for all 5 required categories AND user readiness. Respond only with 'true' or 'false'.", "constraints": ["Must check ALL 5 required information categories", "Must verify user readiness in last message", "Response must be exactly 'true' or 'false'", "No explanations or additional text allowed", "Focus only on explicit user statements"]}, "variables": {"temperature": 0.0, "max_tokens": 10, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": ["true", "false"]}, "validation": {"required_context": ["conversation_history"], "max_length": 8000, "min_length": 10, "required_fields": [], "allowed_variables": ["conversation_history"]}}