{"name": "Athlea LangGraph Coaching Integration", "nodes": [{"parameters": {"httpMethod": "POST", "path": "/webhook/coaching", "options": {}}, "id": "11c633b4-e32c-4ef0-9c8f-8c6b3e0d1234", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "1234abcd-5678-90ef-ghij-klmnopqrstuv", "leftValue": "={{ $json.coach_type }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "22d744c5-f43d-5fg1-ad9g-9d7c4f1e2345", "name": "Validate Input", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://localhost:8001/coaching/execute", "authentication": "none", "requestMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "message", "value": "={{ $json.message }}"}, {"name": "user_id", "value": "={{ $json.user_id }}"}, {"name": "coach_type", "value": "={{ $json.coach_type || 'general' }}"}, {"name": "thread_id", "value": "={{ $json.thread_id }}"}, {"name": "session_data", "value": "={{ $json.session_data || {} }}"}]}, "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 2}}}, "id": "33e855d6-064e-6gh2-be0h-0e8d5g2f3456", "name": "Execute LangGraph Coaching", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"jsCode": "// Process the coaching response and format for further use\nconst response = $input.all()[0].json;\n\n// Extract key information\nconst processedData = {\n  user_id: response.metadata?.user_id,\n  coach_type: response.metadata?.coach_type,\n  coaching_response: response.response,\n  tools_used: response.tools_used || [],\n  reasoning_steps: response.reasoning_steps || [],\n  execution_time: response.execution_time,\n  thread_id: response.thread_id,\n  timestamp: response.metadata?.timestamp || new Date().toISOString(),\n  \n  // Extract actionable insights\n  needs_follow_up: response.tools_used.includes('calendar') || response.tools_used.includes('email'),\n  workout_plan_generated: response.tools_used.includes('airtable_search'),\n  nutrition_advice_given: response.tools_used.includes('nutrition_tools'),\n  \n  // Format for different outputs\n  summary: {\n    user_message: $('Webhook Trigger').item.json.message,\n    coach_response: response.response.substring(0, 200) + '...',\n    tools_count: response.tools_used.length,\n    session_duration: response.execution_time\n  }\n};\n\nreturn processedData;"}, "id": "44f966e7-175f-7hi3-cf1i-1f9e6h3g4567", "name": "Process Coaching Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "5678efgh-9012-34ij-klmn-opqrstuvwxyz", "leftValue": "={{ $json.needs_follow_up }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "55107f8-286g-8jk4-dg2j-2g0f7i4h5678", "name": "Check for Follow-up Actions", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:8001/n8n/trigger", "authentication": "none", "requestMethod": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "workflow_id", "value": "follow-up-notifications"}, {"name": "user_id", "value": "={{ $json.user_id }}"}, {"name": "input_data", "value": "={{ { \n  \"coaching_response\": $json.coaching_response,\n  \"tools_used\": $json.tools_used,\n  \"thread_id\": $json.thread_id,\n  \"timestamp\": $json.timestamp\n} }}"}]}}, "id": "66218g9-397h-9lm5-eh3k-3h1g8j5i6789", "name": "Trigger Follow-up Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 100]}, {"parameters": {"subject": "Your Coaching Session Summary", "emailType": "html", "toEmail": "={{ $('Webhook Trigger').item.json.user_email }}", "message": "=<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #2c3e50;\">🏋️ Your Coaching Session Summary</h2>\n  \n  <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n    <h3 style=\"color: #34495e; margin-top: 0;\">Session Details</h3>\n    <p><strong>Coach Type:</strong> {{ $json.coach_type }}</p>\n    <p><strong>Session Duration:</strong> {{ $json.execution_time }} seconds</p>\n    <p><strong>Tools Used:</strong> {{ $json.tools_used.join(', ') }}</p>\n  </div>\n  \n  <div style=\"background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n    <h3 style=\"color: #27ae60; margin-top: 0;\">Your Question</h3>\n    <p style=\"font-style: italic;\">\"{{ $('Webhook Trigger').item.json.message }}\"</p>\n  </div>\n  \n  <div style=\"background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n    <h3 style=\"color: #1976d2; margin-top: 0;\">Coach's Response</h3>\n    <p>{{ $json.coaching_response }}</p>\n  </div>\n  \n  <div style=\"background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n    <h3 style=\"color: #856404; margin-top: 0;\">Reasoning Process</h3>\n    <ul>\n      {{#each $json.reasoning_steps}}\n      <li>{{this}}</li>\n      {{/each}}\n    </ul>\n  </div>\n  \n  <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;\">\n    <p style=\"color: #6c757d; font-size: 14px;\">Continue your fitness journey with Athlea!</p>\n  </div>\n</div>", "options": {}}, "id": "77329h0-4a8i-anp6-fi4l-4i2h9k6j789a", "name": "Send Email Summary", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"success\",\n  \"coaching_response\": $json.coaching_response,\n  \"thread_id\": $json.thread_id,\n  \"execution_time\": $json.execution_time,\n  \"tools_used\": $json.tools_used,\n  \"timestamp\": $json.timestamp,\n  \"summary\": $json.summary\n} }}", "options": {}}, "id": "8843ai1-5b9j-boq7-gj5m-5j3i0l7k89ab", "name": "Return Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"status\": \"error\",\n  \"message\": \"Missing required fields: coach_type, user_id, or message\",\n  \"received_data\": $json\n} }}", "options": {"responseCode": 400}}, "id": "9954bj2-6c0k-cpr8-hk6n-6k4j1m8l90bc", "name": "Return Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Validate Input", "type": "main", "index": 0}]]}, "Validate Input": {"main": [[{"node": "Execute LangGraph Coaching", "type": "main", "index": 0}], [{"node": "Return Error", "type": "main", "index": 0}]]}, "Execute LangGraph Coaching": {"main": [[{"node": "Process Coaching Response", "type": "main", "index": 0}]]}, "Process Coaching Response": {"main": [[{"node": "Check for Follow-up Actions", "type": "main", "index": 0}]]}, "Check for Follow-up Actions": {"main": [[{"node": "Trigger Follow-up Workflow", "type": "main", "index": 0}, {"node": "Send Email Summary", "type": "main", "index": 0}], [{"node": "Return Response", "type": "main", "index": 0}]]}, "Trigger Follow-up Workflow": {"main": [[{"node": "Return Response", "type": "main", "index": 0}]]}, "Send Email Summary": {"main": [[{"node": "Return Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "00000000-0000-0000-0000-000000000000", "meta": {"templateCredsSetupCompleted": true, "instanceId": "12345678901234567890123456789012"}, "id": "123", "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "1", "name": "athlea-coaching"}, {"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "2", "name": "langgraph-integration"}]}