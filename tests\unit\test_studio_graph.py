"""
Test script for the LangGraph Studio compatible coaching graph.

This script allows you to test the studio graph locally before using LangGraph Studio.
"""

import asyncio
import logging
import os

from athlea_langgraph.graphs.studio_graph import create_studio_graph, create_test_graph

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_studio_graph_creation():
    """Test creating the studio graph with different configurations."""
    print("🧪 Testing Studio Graph Creation")
    print("=" * 40)

    # Test 1: Default configuration
    print("\n📋 Test 1: Default Configuration")
    try:
        graph = await create_studio_graph()
        print(f"✅ Default graph created successfully")
        print(f"   Nodes: {len(graph.nodes)}")
        print(f"   Node names: {list(graph.nodes.keys())}")
    except Exception as e:
        print(f"❌ Default graph creation failed: {e}")

    # Test 2: Memory disabled configuration
    print("\n📋 Test 2: Memory Disabled Configuration")
    try:
        config = {
            "user_id": "test_user",
            "mongodb_uri": "mongodb://localhost:27017",
            "enable_memory": False,
        }
        graph = await create_studio_graph(config)
        print(f"✅ Memory-disabled graph created successfully")
        print(f"   Nodes: {len(graph.nodes)}")
    except Exception as e:
        print(f"❌ Memory-disabled graph creation failed: {e}")

    # Test 3: Test graph function
    print("\n📋 Test 3: Test Graph Function")
    try:
        graph = await create_test_graph()
        print(f"✅ Test graph created successfully")
        print(f"   Nodes: {len(graph.nodes)}")
    except Exception as e:
        print(f"❌ Test graph creation failed: {e}")


async def test_graph_execution():
    """Test basic graph execution."""
    print("\n🚀 Testing Basic Graph Execution")
    print("=" * 40)

    try:
        # Create a test graph without memory for simplicity
        graph = await create_test_graph()

        # Test input
        test_input = {
            "messages": [],
            "user_query": "I want to start strength training",
            "user_profile": {},
            "routing_decision": None,
            "pending_agents": None,
            "plan": None,
            "current_step": None,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            "current_plan": None,
            "is_onboarding": False,
            # Studio-specific fields
            "current_node": None,
            "execution_steps": [],
            "debug_info": {},
            "awaiting_human_input": False,
            "human_input_prompt": None,
            # Initialize specialist responses
            "strength_response": None,
            "running_response": None,
            "cardio_response": None,
            "cycling_response": None,
            "nutrition_response": None,
            "recovery_response": None,
            "mental_response": None,
            "reasoning_output": None,
            "clarification_output": None,
            "aggregated_response": None,
        }

        print(f"📤 Input: {test_input['user_query']}")

        # Run a few steps to test
        config = {"configurable": {"thread_id": "test_thread"}}

        step_count = 0
        async for step in graph.astream(test_input, config):
            step_count += 1
            for node_name, state_update in step.items():
                current_node = state_update.get("current_node", "unknown")
                execution_steps = state_update.get("execution_steps", [])

                print(f"📍 Step {step_count}: {node_name}")
                print(f"   Current node: {current_node}")
                print(f"   Execution path: {execution_steps}")

                # Check for responses
                for response_field in [
                    "reasoning_output",
                    "clarification_output",
                    "aggregated_response",
                ]:
                    if response_field in state_update and state_update[response_field]:
                        response_text = str(state_update[response_field])[:100]
                        print(f"   {response_field}: {response_text}...")

                # Limit steps for testing
                if step_count >= 5:
                    print("🛑 Limiting test to 5 steps")
                    break

            if step_count >= 5:
                break

        print(f"✅ Graph execution test completed ({step_count} steps)")

    except Exception as e:
        print(f"❌ Graph execution test failed: {e}")
        import traceback

        traceback.print_exc()


async def main():
    """Run all studio graph tests."""
    print("🎯 LangGraph Studio Graph Testing")
    print("=" * 50)

    await test_studio_graph_creation()
    await test_graph_execution()

    print(f"\n🏁 All tests completed!")
    print("\n📝 Next Steps:")
    print(
        "1. Download LangGraph Studio from: https://github.com/langchain-ai/langgraph-studio/releases"
    )
    print("2. Install Docker Desktop or Orbstack")
    print("3. Open this project folder in LangGraph Studio")
    print("4. Select the 'coaching_agent' graph")
    print("5. Start debugging your coaching agent visually!")


if __name__ == "__main__":
    asyncio.run(main())
