"""
GraphRAG Tool for Athlea LangGraph Integration

LangChain-compatible tool that integrates the unified GraphRAG service
into the existing Athlea tool framework.
"""

import logging
from typing import Any, Dict

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ...services.graphrag_service import get_graphrag_service, GraphRAGQueryInput
from ..base_tool import BaseDomainTool as AthleaBaseTool

logger = logging.getLogger(__name__)


class GraphRAGToolInput(BaseModel):
    """Input schema for GraphRAG tool."""

    query: str = Field(
        description="Natural language query about sports science, injury prevention, or training methods"
    )
    document_id: str = Field(
        default=None, description="Specific research document ID to focus on (optional)"
    )
    doi: str = Field(
        default=None, description="DOI of specific research paper to analyze (optional)"
    )
    entities: list = Field(
        default=None,
        description="Specific entities to investigate (e.g., ['FIFA 11+', 'HIIT']) (optional)",
    )
    include_causalities: bool = Field(
        default=True,
        description="Whether to extract causal relationships from the knowledge graph",
    )
    top_k: int = Field(
        default=10, description="Maximum number of results to return from each source"
    )


class GraphRAGLangChainTool(BaseTool):
    """LangChain-compatible tool for GraphRAG queries."""

    name: str = "graphrag_query"
    description: str = """Query the comprehensive knowledge graph for evidence-based sports science information.
    
    This tool combines textual research evidence with structured knowledge relationships to provide:
    - Research-backed answers with specific citations
    - Causal relationships (what prevents/causes/improves what)
    - Actionable training protocols with dosages and conditions
    - Evidence quality and confidence scores
    
    Perfect for answering questions about:
    - Injury prevention strategies (e.g., "How does FIFA 11+ prevent injuries?")
    - Training effectiveness (e.g., "What's the optimal HIIT protocol for athletes?")
    - Exercise relationships (e.g., "Does Pilates improve flexibility?")
    - Sport-specific interventions (e.g., "Best training for futsal players")
    
    Use this when you need evidence-based, research-supported answers with specific protocols and conditions."""

    args_schema: type[BaseModel] = GraphRAGToolInput
    graphrag_service: Any = Field(default=None)

    def __init__(self):
        super().__init__()
        self.graphrag_service = get_graphrag_service()

    async def _arun(self, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            # Convert LangChain tool input to GraphRAG service input
            graphrag_input = GraphRAGQueryInput(
                query=kwargs.get("query", ""),
                document_id=kwargs.get("document_id"),
                doi=kwargs.get("doi"),
                entities=kwargs.get("entities"),
                include_acs=True,
                include_graph=True,
                top_k=kwargs.get("top_k", 10),
            )

            result = await self.graphrag_service.execute_graphrag_query(graphrag_input)

            if result.success:
                return self._format_graphrag_output(
                    result, kwargs.get("include_causalities", True)
                )
            else:
                return f"GraphRAG query failed: {result.message}"

        except Exception as e:
            logger.error(f"GraphRAG tool error: {e}")
            return f"Error executing GraphRAG query: {str(e)}"

    def _run(self, **kwargs) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return "GraphRAG tool requires async execution. Cannot run sync in async context."
            else:
                return loop.run_until_complete(self._arun(**kwargs))
        except Exception as e:
            return f"Error running GraphRAG tool: {str(e)}"

    def _format_graphrag_output(
        self, result: Any, include_causalities: bool = True
    ) -> str:
        """Format GraphRAG output for LangChain tool response."""
        output_parts = []

        # Add synthesis summary
        if result.synthesis:
            output_parts.append(f"## Research Summary\n{result.synthesis}\n")

        # Add key findings from ACS
        if result.acs_results and result.acs_chunk_count > 0:
            output_parts.append(
                f"## Textual Evidence ({result.acs_chunk_count} research chunks found)"
            )
            if hasattr(result.acs_results, "results") and result.acs_results["results"]:
                for i, chunk in enumerate(
                    result.acs_results["results"][:3], 1
                ):  # Top 3 chunks
                    if "content" in chunk:
                        content = (
                            chunk["content"][:300] + "..."
                            if len(chunk["content"]) > 300
                            else chunk["content"]
                        )
                        score = chunk.get("score", "N/A")
                        source = chunk.get("source", "Unknown")
                        output_parts.append(
                            f"**Evidence {i}** (Score: {score}, Source: {source}):\n{content}\n"
                        )

        # Add structured relationships from graph
        if result.graph_entity_count > 0:
            output_parts.append(f"## Knowledge Graph Relationships")
            output_parts.append(
                f"Found {result.graph_entity_count} entities with {result.graph_relationship_count} relationships\n"
            )

        # Add extracted causalities
        if include_causalities and result.causalities:
            output_parts.append("## ⚡ Causal Relationships")
            for causality in result.causalities:
                conf = causality.get("confidence", 0)
                relationship = causality.get("relationship", "UNKNOWN")
                source = causality.get("source_entity", "Unknown")
                target = causality.get("target_entity", "Unknown")
                condition = causality.get("condition", "")
                dosage = causality.get("dosage", "")

                causal_text = f"**{source} {relationship} {target}**"
                if conf > 0:
                    causal_text += f" (Confidence: {conf:.1f})"
                if condition:
                    causal_text += f"\n  - Condition: {condition}"
                if dosage:
                    causal_text += f"\n  - Dosage: {dosage}"

                output_parts.append(causal_text + "\n")

        # Add actionable recommendations
        if result.recommendations:
            output_parts.append("## 🎯 Actionable Recommendations")
            for i, rec in enumerate(result.recommendations, 1):
                output_parts.append(f"{i}. {rec}")
            output_parts.append("")

        # Add metadata
        output_parts.append(f"## Query Metadata")
        output_parts.append(f"- Execution time: {result.execution_time_ms}ms")
        output_parts.append(f"- Request ID: {result.request_id}")

        return "\n".join(output_parts)


class GraphRAGAthleaTool(AthleaBaseTool):
    """Athlea framework-compatible GraphRAG tool."""

    domain: str = "research"
    name: str = "graphrag_query"
    description: str = (
        "Query knowledge graph for evidence-based sports science information"
    )

    def __init__(self):
        super().__init__()
        self.graphrag_service = get_graphrag_service()

    async def _execute(self, **kwargs) -> Dict[str, Any]:
        """Execute GraphRAG query using Athlea tool framework."""
        try:
            graphrag_input = GraphRAGQueryInput(**kwargs)
            result = await self.graphrag_service.execute_graphrag_query(graphrag_input)

            return {
                "success": result.success,
                "data": result.model_dump(),
                "message": result.message,
                "execution_time_ms": result.execution_time_ms,
            }

        except Exception as e:
            logger.error(f"GraphRAG Athlea tool error: {e}")
            return {
                "success": False,
                "data": None,
                "message": f"GraphRAG tool execution failed: {str(e)}",
                "error": str(e),
            }

    def to_langchain_tool(self) -> GraphRAGLangChainTool:
        """Convert to LangChain-compatible tool."""
        return GraphRAGLangChainTool()


# Factory function for creating GraphRAG tools
def create_graphrag_tool() -> GraphRAGLangChainTool:
    """Create a new GraphRAG LangChain tool instance."""
    return GraphRAGLangChainTool()


def create_graphrag_athlea_tool() -> GraphRAGAthleaTool:
    """Create a new GraphRAG Athlea tool instance."""
    return GraphRAGAthleaTool()
