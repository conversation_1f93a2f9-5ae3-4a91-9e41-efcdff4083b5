#!/usr/bin/env python3
"""
Delete Specific Prompts from LangSmith

Deletes the specific prompts shown in the screenshot from LangSmith's online interface.
This does NOT touch your local files - only the online LangSmith prompts.
"""

import os
import sys

try:
    from langsmith import Client
except ImportError:
    print("❌ Error: langsmith package not found.")
    print("Install with: pip install langsmith")
    sys.exit(1)


# List of specific prompts to delete (from the screenshot)
PROMPTS_TO_DELETE = [
    "coaching-mental-coach",
    "onboarding-finalize",
    "onboarding-plan",
    "onboarding-history",
    "onboarding-goal-prioritization",
    "onboarding-goals",
    "onboarding-intro",
    "onboarding-greeting",
    "onboarding-completion",
    "onboarding-generate-plan",
    "coaching-supervisor-clarification",
    "coaching-planning-node",
    "coaching-reasoning-node",
    "coaching-recovery-coach",
    "coaching-nutrition-coach",
    "coaching-cycling-coach",
    "coaching-cardio-coach",
    "coaching-strength-coach",
]


def main():
    """Delete the specific prompts from LangSmith."""

    print("🎯 Deleting Specific Prompts from LangSmith")
    print("=" * 50)
    print("⚠️  This will delete specific prompts from LangSmith's online interface")
    print("   (Your local files will NOT be touched)")
    print()

    # Check API key
    api_key = os.getenv("LANGSMITH_API_KEY")
    if not api_key:
        print("❌ Error: LANGSMITH_API_KEY environment variable not set.")
        print("Get your API key from: https://smith.langchain.com/settings")
        print("Then set it: export LANGSMITH_API_KEY='lsv2_...'")
        sys.exit(1)

    # Initialize client
    try:
        client = Client()
        print("✅ Connected to LangSmith")
    except Exception as e:
        print(f"❌ Failed to connect to LangSmith: {e}")
        sys.exit(1)

    # Show what we're going to delete
    print(f"\n📋 Prompts to delete ({len(PROMPTS_TO_DELETE)} total):")
    for i, prompt_name in enumerate(PROMPTS_TO_DELETE, 1):
        print(f"   {i:2d}. {prompt_name}")

    # Confirmation
    print(
        f"\n⚠️  Are you sure you want to delete these {len(PROMPTS_TO_DELETE)} prompts?"
    )
    confirm = input("Type 'DELETE' to confirm: ").strip()

    if confirm != "DELETE":
        print("❌ Deletion cancelled.")
        return

    print(f"\n🗑️  Deleting {len(PROMPTS_TO_DELETE)} prompts...")

    deleted_count = 0
    failed_count = 0

    for prompt_name in PROMPTS_TO_DELETE:
        try:
            client.delete_prompt(prompt_name)
            print(f"   ✅ Deleted: {prompt_name}")
            deleted_count += 1
        except Exception as e:
            print(f"   ❌ Failed to delete {prompt_name}: {e}")
            failed_count += 1

    print(f"\n📊 Deletion Summary:")
    print(f"   ✅ Successfully deleted: {deleted_count}")
    print(f"   ❌ Failed to delete: {failed_count}")
    print(f"   📝 Total prompts processed: {len(PROMPTS_TO_DELETE)}")

    if deleted_count > 0:
        print(f"\n🎉 Successfully deleted {deleted_count} prompts from LangSmith!")

    if failed_count > 0:
        print(f"\n⚠️  {failed_count} prompts failed to delete. They might:")
        print("   - Already be deleted")
        print("   - Have different names")
        print("   - Be protected or shared")


if __name__ == "__main__":
    main()
