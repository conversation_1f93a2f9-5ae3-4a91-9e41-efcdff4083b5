"""
Standard message templates for the Athlea LangGraph system.
"""

# Error message templates with parameterized placeholders
ERROR_MESSAGES = {
    "TIMEOUT": "Operation timed out after {timeout} seconds",
    "VALIDATION_ERROR": "Input validation failed: {details}",
    "API_ERROR": "External API error: {service} returned {status}",
    "NETWORK_ERROR": "Network connection failed: {details}",
    "AUTHENTICATION_ERROR": "Authentication failed: {details}",
    "RATE_LIMIT_ERROR": "Rate limit exceeded. Retry after {retry_after} seconds",
    "CIRCUIT_BREAKER_OPEN": "Service temporarily unavailable. Circuit breaker is open",
    "CONFIGURATION_ERROR": "Configuration error: {details}",
    "DATABASE_ERROR": "Database operation failed: {details}",
    "MEMORY_ERROR": "Memory operation failed: {details}",
    "SYNTHESIS_TIMEOUT": "Synthesis timed out after {timeout} seconds",
    "INVALID_INPUT": "Invalid input provided: {input_value}",
    "RESOURCE_NOT_FOUND": "Resource not found: {resource_id}",
    "PERMISSION_DENIED": "Permission denied for operation: {operation}",
}

# Success message templates
SUCCESS_MESSAGES = {
    "OPERATION_COMPLETED": "Operation completed successfully in {duration:.2f}ms",
    "DATA_SAVED": "Data saved successfully",
    "CONFIGURATION_LOADED": "Configuration loaded successfully",
    "SERVICE_INITIALIZED": "Service {service_name} initialized successfully",
    "MEMORY_UPDATED": "Memory updated successfully",
    "NODE_STARTED": "Node started: {node_name}",
    "NODE_ENDED": "Node ended: {node_name} (duration: {duration:.2f}s)",
    "CONNECTION_ESTABLISHED": "Connection established with {service}",
    "CACHE_UPDATED": "Cache updated successfully for {key}",
}

# Domain-specific coaching response templates
COACHING_RESPONSES = {
    "STRENGTH": "Great question about strength training! {response}",
    "CARDIO": "Good question about cardio! The best cardio depends on your goals - HIIT for efficiency, steady-state for endurance, or activities you enjoy for consistency. What's your current fitness level and what do you enjoy doing?",
    "NUTRITION": "Excellent question about nutrition! A balanced approach with lean proteins, whole grains, healthy fats, and plenty of fruits and vegetables is key. Stay hydrated and consider your activity level when planning meals. What are your specific nutrition goals?",
    "RECOVERY": "Recovery is crucial for progress! Focus on quality sleep (7-9 hours), proper hydration, gentle stretching, and managing stress. Listen to your body and don't underestimate rest days. How's your current recovery routine?",
    "MENTAL": "Mental training is as important as physical! Visualization, goal setting, positive self-talk, and mindfulness can boost performance. Consistency in mental practices builds mental strength. What mental challenges are you facing?",
    "CYCLING": "Great interest in cycling! Whether for fitness or transport, cycling is excellent cardio. Focus on proper bike fit, gradual distance increases, and consistent training. What type of cycling interests you most?",
    "ATHLEA": "I'm here to help with your fitness journey! Whether it's strength training, nutrition, cardio, or any other aspect of health and fitness, I can provide personalized guidance. What specific area would you like to focus on today?",
    "GENERAL": "I'm here to help with your fitness and wellness goals. What would you like to work on?",
}

# User-facing thinking messages
THINKING_MESSAGES = {
    "PROCESSING": "🤔 Thinking...",
    "ANALYZING": "🔍 Analyzing your request...",
    "GENERATING": "✨ Generating response...",
    "SYNTHESIZING": "🧠 Synthesizing information...",
    "SEARCHING": "🔎 Searching for relevant information...",
    "PLANNING": "📋 Planning your session...",
    "CALCULATING": "🧮 Calculating recommendations...",
    "REVIEWING": "📝 Reviewing your progress...",
}

# Operation progress messages
PROGRESS_MESSAGES = {
    "STARTING": "Starting {operation}...",
    "IN_PROGRESS": "{operation} in progress... ({progress}%)",
    "COMPLETING": "Completing {operation}...",
    "FINISHED": "{operation} completed successfully",
    "INITIALIZING": "Initializing {service}...",
    "CONNECTING": "Connecting to {service}...",
    "PROCESSING_DATA": "Processing {data_type} data...",
}

# Fallback messages for error handling
FALLBACK_MESSAGES = {
    "SERVICE_UNAVAILABLE": "The {service} service is temporarily unavailable. Please try again later.",
    "GENERIC_ERROR": "An unexpected error occurred. Please try again or contact support.",
    "TIMEOUT_FALLBACK": "The operation is taking longer than expected. Please try again.",
    "TOOL_FALLBACK": "The {tool_name} service is temporarily unavailable. Please try again later.",
    "API_FALLBACK": "Unable to reach {api_name}. Using cached data where available.",
    "NETWORK_FALLBACK": "Network connectivity issues detected. Some features may be limited.",
}

# Validation error messages
VALIDATION_MESSAGES = {
    "REQUIRED_FIELD": "Field '{field}' is required",
    "INVALID_FORMAT": "Field '{field}' has invalid format: {format}",
    "OUT_OF_RANGE": "Field '{field}' must be between {min_val} and {max_val}",
    "INVALID_CHOICE": "Field '{field}' must be one of: {choices}",
    "REQUIRED_FOR_OPERATION": "{field} is required for {operation} operation",
    "CANNOT_EXCEED": "{field} cannot exceed {limit}",
    "MUST_BE_POSITIVE": "{field} must be positive",
    "DATE_FORMAT_ERROR": "Date must be in YYYY-MM-DD format",
    "INTENSITY_VALIDATION": "Intensity must be 'low', 'moderate', or 'high'",
    "EMAIL_FORMAT_ERROR": "Please provide a valid email address",
    "PASSWORD_STRENGTH_ERROR": "Password must be at least 8 characters long",
}

# Specialist emojis for coaching contexts
SPECIALIST_EMOJIS = {
    "Strength Coach": "💪",
    "Running Coach": "🏃",
    "Cardio Coach": "❤️",
    "Cycling Coach": "🚴",
    "Nutrition Coach": "🥗",
    "Recovery Coach": "🧘",
    "Mental Coach": "🧠",
    "Head Coach": "👨‍🏫",
    "Wellness Coach": "🌟",
    "Mobility Coach": "🤸",
}

# Default coaching recommendations
DEFAULT_RECOMMENDATIONS = {
    "SLEEP_PRIORITY": "Prioritize 7-9 hours of quality sleep",
    "ACTIVE_RECOVERY": "Include active recovery sessions",
    "STRESS_MANAGEMENT": "Manage stress through relaxation techniques",
    "TRAINING_PERIODIZATION": "Consider periodization of training load",
    "MICRO_HABITS": "Start with micro-habits (5-10 minutes)",
    "IMPLEMENTATION_INTENTIONS": "Use implementation intentions (if-then planning)",
    "ACCOUNTABILITY": "Create accountability systems",
    "PROCESS_GOALS": "Focus on process goals over outcome goals",
    "FORM_FOCUS": "Focus on controlled breathing throughout each movement",
    "LISTEN_TO_BODY": "Listen to your body and avoid forcing movements",
    "GRADUAL_INCREASE": "Start with 2-3 sessions per week and gradually increase frequency",
    "OPTIMAL_STRETCH_TIME": "Hold each stretch for at least 30 seconds for optimal benefits",
    "HYDRATION": "Stay hydrated before, during, and after exercise",
    "WARM_UP": "Always include a proper warm-up before intense exercise",
}
