# MCP Server Testing Framework

This document describes the comprehensive testing framework for the MCP (Model Context Protocol) servers in the Athlea project.

## Overview

The testing framework ensures that all MCP servers work correctly both individually and as part of the larger ecosystem. It uses actual MCP types and protocols to provide fully representative testing of the MCP interaction.

## Test Structure

```
mcp_servers/
├── conftest.py                 # Shared pytest fixtures and utilities
├── test_integration.py         # Cross-server integration tests
├── strength_mcp/
│   └── tests/
│       └── test_server.py     # Strength-specific tests
├── nutrition_mcp/
│   └── tests/
│       └── test_server.py     # Nutrition-specific tests
├── cardio_mcp/
│   └── tests/
│       └── test_server.py     # Cardio-specific tests
├── external_mcp/
│   └── tests/
│       └── test_server.py     # External tools tests
└── README_TESTING.md          # This file
```

## Key Features

### ✅ Fully Representative MCP Testing

- Uses actual MCP types: `Tool`, `TextContent`, `ListToolsRequest`, `CallToolRequest`
- Tests real MCP server initialization and communication patterns
- Validates MCP protocol compliance
- No fallback mocks - tests the actual MCP interaction

### ✅ Comprehensive Test Coverage

- **Unit Tests**: Individual tool functionality
- **Integration Tests**: Cross-server interactions
- **Performance Tests**: Load testing and timing
- **Error Handling**: Input validation and exception handling
- **Protocol Tests**: MCP specification compliance

### ✅ Domain-Specific Testing

Each MCP server has dedicated tests for its specific tools:

- **Strength MCP**: `search_strength_exercises`, `generate_strength_program`
- **Nutrition MCP**: `calculate_daily_calories`, `generate_meal_plan`, `track_hydration`
- **Cardio MCP**: `generate_running_route`, `calculate_pace_zones`, `calculate_vo2max_estimate`, `plan_interval_workout`
- **External MCP**: `search_locations`, `search_web`, `query_airtable`, `get_weather_data`, etc.

## Running Tests

### Quick Start

```bash
# Run all tests with the automated test runner
cd /Users/<USER>/python-langgraph
python run_mcp_tests.py
```

### Individual Test Suites

```bash
# Test specific MCP server
pytest mcp_servers/strength_mcp/tests/ -v

# Test with coverage
pytest mcp_servers/ --cov=mcp_servers --cov-report=html

# Run integration tests only
pytest mcp_servers/test_integration.py -m integration

# Run performance tests
pytest mcp_servers/ -m slow
```

### Test Markers

- `@pytest.mark.integration`: Integration tests across servers
- `@pytest.mark.unit`: Unit tests for individual components
- `@pytest.mark.slow`: Performance and load tests
- `@pytest.mark.mcp`: MCP-specific functionality tests

## Test Fixtures and Utilities

### Shared Fixtures (`conftest.py`)

- `mcp_client_factory`: Creates mock MCP clients for testing
- `sample_tool_arguments`: Provides valid test data for all tools
- `sample_tool_responses`: Provides expected responses for mocking
- `mcp_validators`: Validates MCP protocol compliance
- `performance_timer`: Times test execution

### Request Factories

```python
# Create MCP requests for testing
mock_call_tool_request_factory("tool_name", {"arg": "value"})
mock_list_tools_request()
mock_initialize_request()
```

## What Makes Tests "Fully Representative"

### 1. Real MCP Types
```python
# ✅ Uses actual MCP types
from mcp.types import Tool, TextContent
from mcp.server.models import CallToolRequest, ListToolsRequest

# ❌ No longer uses mock fallbacks
# class ToolCall:  # Mock fallback
#     def __init__(self, name: str, input: dict):
#         self.name = name
#         self.input = input
```

### 2. Actual Server Instances
```python
# Tests use the real server apps
from mcp_servers.strength_mcp.server import app as strength_app

# Verify server structure
assert isinstance(strength_app, Server)
assert hasattr(strength_app, '_list_tools_handler')
```

### 3. Protocol Compliance Testing
```python
def assert_valid_mcp_tool(tool: Tool) -> None:
    """Validates tools follow MCP specification."""
    assert isinstance(tool, Tool)
    assert hasattr(tool, 'name')
    assert hasattr(tool, 'description')
    assert hasattr(tool, 'inputSchema')

def assert_valid_mcp_response(response: List[Any]) -> None:
    """Validates responses follow MCP specification."""
    for item in response:
        assert isinstance(item, (TextContent, ImageContent, EmbeddedResource))
```

### 4. End-to-End Workflow Testing
```python
# Test complete MCP interaction flow
async def test_mcp_server_integration():
    # 1. Initialize server
    # 2. List tools
    # 3. Call tools with proper MCP requests
    # 4. Validate MCP responses
```

## Test Categories

### Unit Tests
- Individual tool functionality
- Input validation (Pydantic schemas)
- Error handling
- Tool response formatting

### Integration Tests
- Server initialization
- Cross-server tool availability
- Tool name uniqueness
- Performance under load
- Error propagation

### Protocol Tests
- MCP request/response format validation
- Tool schema compliance
- Server capability reporting
- Communication pattern verification

## Dependencies

The testing framework requires:

```
pytest==8.3.5              # Test framework
pytest-asyncio==0.24.0     # Async test support
pytest-cov==6.1.1          # Coverage reporting
mcp==1.9.1                  # Actual MCP library
fastapi==0.115.12           # Server framework
pydantic==2.11.5            # Input validation
```

## Coverage Reporting

```bash
# Generate HTML coverage report
pytest mcp_servers/ --cov=mcp_servers --cov-report=html

# View report
open htmlcov/index.html
```

## Performance Testing

```python
@pytest.mark.asyncio
async def test_tool_call_performance():
    """Ensures tools complete within reasonable time."""
    with performance_timer() as get_time:
        result = await server._call_tool_handler(request)
    
    execution_time = get_time()
    assert execution_time < 1.0  # Should complete within 1 second
```

## Error Testing

```python
@pytest.mark.asyncio 
async def test_tool_exception_handling():
    """Tests proper MCP error responses."""
    mock_tool.side_effect = Exception("Database connection failed")
    
    with pytest.raises(HTTPException) as exc_info:
        await server._call_tool_handler(request)
    
    assert exc_info.value.status_code == 500
    assert "Database connection failed" in str(exc_info.value.detail)
```

## Best Practices

### 1. Use Real MCP Types
Always import and use actual MCP types instead of creating mocks.

### 2. Test Protocol Compliance
Use the provided validators to ensure responses follow MCP specification.

### 3. Mock External Dependencies
Mock the underlying tool functions, not the MCP layer:

```python
# ✅ Mock the actual tool function
exercise_db_tool.search_strength_exercises = AsyncMock(return_value={"exercises": []})

# ❌ Don't mock the MCP layer
# mock_mcp_server = MagicMock()
```

### 4. Test Both Success and Failure Cases
Include tests for valid inputs, invalid inputs, and error conditions.

### 5. Validate Performance
Include timing assertions for critical paths.

## Troubleshooting

### MCP Import Errors
If you see import errors for MCP types:
```bash
# Ensure MCP is installed
pip install mcp==1.9.1

# Check installation
python -c "from mcp.types import Tool; print('MCP available')"
```

### Test Discovery Issues
```bash
# Ensure pytest can find tests
export PYTHONPATH=/Users/<USER>/python-langgraph
pytest --collect-only mcp_servers/
```

### Coverage Issues
```bash
# Install coverage dependencies
pip install pytest-cov

# Run with coverage
pytest --cov=mcp_servers --cov-report=term-missing
```

## Integration with CI/CD

The test framework is designed to integrate with continuous integration:

```yaml
# .github/workflows/test.yml
- name: Run MCP Server Tests
  run: |
    cd python-langgraph
    python run_mcp_tests.py
```

## Summary

This testing framework provides:

- ✅ **Full MCP Protocol Compliance**: Uses actual MCP types and patterns
- ✅ **Comprehensive Coverage**: Unit, integration, performance, and error tests
- ✅ **Domain-Specific Testing**: Each server tested thoroughly
- ✅ **Easy Execution**: Simple test runner script
- ✅ **CI/CD Ready**: Designed for automated testing
- ✅ **Performance Monitoring**: Load testing and timing assertions
- ✅ **Error Validation**: Proper exception handling verification

The tests are now fully representative of actual MCP interaction, eliminating the need for fallback mocks and ensuring your MCP servers work correctly in real-world scenarios. 