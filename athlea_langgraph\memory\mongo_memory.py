"""
MongoDB Memory Integration for LangGraph

This module provides MongoDB-based memory capabilities including:
1. MongoSaver: Checkpointing for session state persistence
2. MongoMemoryStore: Long-term memory with semantic search
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from langchain_core.load import dumpd, loads
from langchain_core.messages import BaseMessage
from langgraph.checkpoint.base import (
    BaseCheckpointSaver,
    Checkpoint,
    CheckpointMetadata,
    CheckpointTuple,
)
from langgraph.checkpoint.serde.base import SerializerProtocol
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import DuplicateKeyError

logger = logging.getLogger(__name__)


class MongoJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles MongoDB ObjectId and other BSON types."""

    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class MongoCompatibleSerializer(JsonPlusSerializer):
    """MongoDB-compatible serializer that handles ObjectId and other BSON types."""

    def dumps(self, obj: Any) -> bytes:
        """Serialize object to bytes, handling MongoDB types."""
        try:
            # First, convert any MongoDB-specific types to JSON-serializable types
            json_compatible = self._make_json_compatible(obj)
            return super().dumps(json_compatible)
        except Exception as e:
            logger.warning(
                f"Serialization failed with JsonPlusSerializer, falling back to custom encoder: {e}"
            )
            # Fallback to custom JSON encoder
            json_str = json.dumps(
                json_compatible, cls=MongoJSONEncoder, ensure_ascii=False
            )
            return json_str.encode("utf-8")

    def _make_json_compatible(self, obj: Any) -> Any:
        """Recursively convert MongoDB types and LangChain objects to JSON-compatible types."""
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, BaseMessage):
            # Use LangChain's built-in serialization for message objects
            try:
                return dumpd(obj)
            except Exception as e:
                logger.warning(f"Failed to serialize LangChain message: {e}")
                # Fallback to basic dict representation
                return {
                    "type": obj.__class__.__name__,
                    "content": getattr(obj, "content", ""),
                    "additional_kwargs": getattr(obj, "additional_kwargs", {}),
                    "response_metadata": getattr(obj, "response_metadata", {}),
                }
        elif isinstance(obj, dict):
            return {
                key: self._make_json_compatible(value) for key, value in obj.items()
            }
        elif isinstance(obj, (list, tuple)):
            return [self._make_json_compatible(item) for item in obj]
        else:
            return obj


class MongoSaver(BaseCheckpointSaver):
    """
    MongoDB-based checkpoint saver for LangGraph state persistence.

    Compatible with LangGraph 0.4.7+ checkpoint interface.
    """

    def __init__(
        self,
        connection_string: str,
        database: str = "athlea_coaching",
        collection: str = "checkpoints",
        serde: Optional[SerializerProtocol] = None,
    ):
        # Use our MongoDB-compatible serializer by default
        super().__init__(serde=serde or MongoCompatibleSerializer())
        self.connection_string = connection_string
        self.database_name = database
        self.collection_name = collection
        self._client: Optional[AsyncIOMotorClient] = None
        self._db: Optional[AsyncIOMotorDatabase] = None

    async def _get_db(self) -> AsyncIOMotorDatabase:
        """Get or create database connection."""
        if self._client is None:
            self._client = AsyncIOMotorClient(self.connection_string)
            self._db = self._client[self.database_name]

            # Create indexes for better performance
            collection = self._db[self.collection_name]
            try:
                await collection.create_index(
                    [("thread_id", 1), ("checkpoint_id", 1)], unique=True
                )
            except Exception:
                # Index may already exist, ignore
                pass
            try:
                await collection.create_index(
                    [("thread_id", 1), ("parent_checkpoint_id", 1)]
                )
            except Exception:
                # Index may already exist, ignore
                pass

        return self._db

    def _clean_for_mongodb(self, obj: Any) -> Any:
        """Clean object to ensure MongoDB compatibility by converting ObjectId and LangChain objects to strings."""
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, BaseMessage):
            # Use LangChain's built-in serialization for message objects
            try:
                return dumpd(obj)
            except Exception as e:
                logger.warning(f"Failed to serialize LangChain message: {e}")
                # Fallback to basic dict representation
                return {
                    "type": obj.__class__.__name__,
                    "content": getattr(obj, "content", ""),
                    "additional_kwargs": getattr(obj, "additional_kwargs", {}),
                    "response_metadata": getattr(obj, "response_metadata", {}),
                }
        elif isinstance(obj, dict):
            return {key: self._clean_for_mongodb(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._clean_for_mongodb(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj  # MongoDB handles datetime natively
        else:
            return obj

    async def aget_tuple(self, config: Dict[str, Any]) -> Optional[CheckpointTuple]:
        """Get a checkpoint tuple by config."""
        thread_id = config.get("configurable", {}).get("thread_id")
        checkpoint_id = config.get("configurable", {}).get("checkpoint_id")

        if not thread_id:
            return None

        db = await self._get_db()
        collection = db[self.collection_name]

        if checkpoint_id:
            # Get specific checkpoint
            doc = await collection.find_one(
                {"thread_id": thread_id, "checkpoint_id": checkpoint_id}
            )
        else:
            # Get latest checkpoint
            doc = await collection.find_one(
                {"thread_id": thread_id}, sort=[("created_at", -1)]
            )

        if not doc:
            return None

        return CheckpointTuple(
            config={
                "configurable": {
                    "thread_id": thread_id,
                    "checkpoint_id": doc["checkpoint_id"],
                }
            },
            checkpoint=self.serde.loads(doc["checkpoint"]),
            metadata=CheckpointMetadata(**doc.get("metadata", {})),
            parent_config=(
                {
                    "configurable": {
                        "thread_id": thread_id,
                        "checkpoint_id": doc.get("parent_checkpoint_id"),
                    }
                }
                if doc.get("parent_checkpoint_id")
                else None
            ),
        )

    async def alist(
        self,
        config: Dict[str, Any],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[CheckpointTuple]:
        """List checkpoints for a thread."""
        thread_id = config.get("configurable", {}).get("thread_id")
        if not thread_id:
            return []

        db = await self._get_db()
        collection = db[self.collection_name]

        query = {"thread_id": thread_id}
        if before:
            query["checkpoint_id"] = {"$lt": before}
        if filter:
            query.update(filter)

        cursor = collection.find(query).sort([("created_at", -1)])
        if limit:
            cursor = cursor.limit(limit)

        checkpoints = []
        async for doc in cursor:
            checkpoints.append(
                CheckpointTuple(
                    config={
                        "configurable": {
                            "thread_id": thread_id,
                            "checkpoint_id": doc["checkpoint_id"],
                        }
                    },
                    checkpoint=self.serde.loads(doc["checkpoint"]),
                    metadata=CheckpointMetadata(**doc.get("metadata", {})),
                    parent_config=(
                        {
                            "configurable": {
                                "thread_id": thread_id,
                                "checkpoint_id": doc.get("parent_checkpoint_id"),
                            }
                        }
                        if doc.get("parent_checkpoint_id")
                        else None
                    ),
                )
            )

        return checkpoints

    async def aput(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Save a checkpoint."""
        thread_id = config.get("configurable", {}).get("thread_id")
        if not thread_id:
            raise ValueError("thread_id is required in config")

        checkpoint_id = str(uuid.uuid4())
        parent_checkpoint_id = config.get("configurable", {}).get("checkpoint_id")

        db = await self._get_db()
        collection = db[self.collection_name]

        # Clean metadata to ensure MongoDB compatibility
        metadata_dict = (
            metadata.model_dump() if hasattr(metadata, "model_dump") else dict(metadata)
        )

        # Convert any ObjectId instances to strings in metadata
        clean_metadata = self._clean_for_mongodb(metadata_dict)

        doc = {
            "thread_id": thread_id,
            "checkpoint_id": checkpoint_id,
            "parent_checkpoint_id": parent_checkpoint_id,
            "checkpoint": self.serde.dumps(checkpoint),
            "metadata": clean_metadata,
            "created_at": datetime.now(),
        }

        try:
            await collection.insert_one(doc)
        except DuplicateKeyError:
            logger.warning(f"Checkpoint {checkpoint_id} already exists")

        return {
            "configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}
        }

    async def aput_writes(
        self, config: Dict[str, Any], writes: List[tuple], task_id: str
    ) -> None:
        """Save writes (for compatibility with newer LangGraph versions)."""
        # This method is required by newer LangGraph versions
        # For now, we'll just log that writes were received
        logger.debug(f"Received writes for task {task_id}: {len(writes)} writes")

    def get_next_version(self, current: Optional[str], channel: str) -> str:
        """Get next version for a channel (required by LangGraph)."""
        if current is None:
            return "1"
        try:
            return str(int(current) + 1)
        except ValueError:
            return str(uuid.uuid4())

    # Synchronous methods for compatibility
    def get_tuple(self, config: Dict[str, Any]) -> Optional[CheckpointTuple]:
        """Sync version of aget_tuple."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.aget_tuple(config))
        except RuntimeError:
            # No event loop running, create a new one
            return asyncio.run(self.aget_tuple(config))

    def list(
        self,
        config: Dict[str, Any],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[CheckpointTuple]:
        """Sync version of alist."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.alist(config, filter=filter, before=before, limit=limit)
            )
        except RuntimeError:
            return asyncio.run(
                self.alist(config, filter=filter, before=before, limit=limit)
            )

    def put(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Sync version of aput."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.aput(config, checkpoint, metadata, new_versions)
            )
        except RuntimeError:
            return asyncio.run(self.aput(config, checkpoint, metadata, new_versions))

    def put_writes(
        self, config: Dict[str, Any], writes: List[tuple], task_id: str
    ) -> None:
        """Sync version of aput_writes."""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.aput_writes(config, writes, task_id))
        except RuntimeError:
            asyncio.run(self.aput_writes(config, writes, task_id))


class MongoMemoryStore:
    """
    MongoDB-based long-term memory store with semantic search capabilities.

    Stores and retrieves memories by namespace and key, with text-based search.
    """

    def __init__(
        self,
        connection_string: str,
        database: str = "athlea_coaching",
        collection: str = "memories",
    ):
        self.connection_string = connection_string
        self.database_name = database
        self.collection_name = collection
        self._client: Optional[AsyncIOMotorClient] = None
        self._db: Optional[AsyncIOMotorDatabase] = None

    async def _get_db(self) -> AsyncIOMotorDatabase:
        """Get or create database connection."""
        if self._client is None:
            self._client = AsyncIOMotorClient(self.connection_string)
            self._db = self._client[self.database_name]

            # Create text index for search
            collection = self._db[self.collection_name]
            try:
                await collection.create_index(
                    [("namespace", 1), ("key", 1)], unique=True
                )
            except Exception:
                # Index may already exist, ignore
                pass
            try:
                await collection.create_index(
                    [("$**", "text")]
                )  # Full-text search index
            except Exception:
                # Index may already exist or conflict with existing, ignore
                pass

        return self._db

    def _clean_for_mongodb(self, obj: Any) -> Any:
        """Clean object to ensure MongoDB compatibility by converting ObjectId and LangChain objects to strings."""
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, BaseMessage):
            # Use LangChain's built-in serialization for message objects
            try:
                return dumpd(obj)
            except Exception as e:
                logger.warning(f"Failed to serialize LangChain message: {e}")
                # Fallback to basic dict representation
                return {
                    "type": obj.__class__.__name__,
                    "content": getattr(obj, "content", ""),
                    "additional_kwargs": getattr(obj, "additional_kwargs", {}),
                    "response_metadata": getattr(obj, "response_metadata", {}),
                }
        elif isinstance(obj, dict):
            return {key: self._clean_for_mongodb(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._clean_for_mongodb(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj  # MongoDB handles datetime natively
        else:
            return obj

    async def get(self, namespace: str, key: str) -> Optional[Dict[str, Any]]:
        """Get a memory by namespace and key."""
        db = await self._get_db()
        collection = db[self.collection_name]

        doc = await collection.find_one({"namespace": namespace, "key": key})
        return doc["value"] if doc else None

    async def put(self, namespace: str, key: str, value: Dict[str, Any]) -> None:
        """Store a memory."""
        db = await self._get_db()
        collection = db[self.collection_name]

        # Clean value to ensure MongoDB compatibility
        clean_value = self._clean_for_mongodb(value)

        doc = {
            "namespace": namespace,
            "key": key,
            "value": clean_value,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

        await collection.replace_one(
            {"namespace": namespace, "key": key}, doc, upsert=True
        )

    async def delete(self, namespace: str, key: str) -> bool:
        """Delete a memory."""
        db = await self._get_db()
        collection = db[self.collection_name]

        result = await collection.delete_one({"namespace": namespace, "key": key})
        return result.deleted_count > 0

    async def search(
        self, namespace: str, query: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search memories using text search."""
        db = await self._get_db()
        collection = db[self.collection_name]

        # Use MongoDB's text search
        cursor = (
            collection.find(
                {"namespace": namespace, "$text": {"$search": query}},
                {"score": {"$meta": "textScore"}},
            )
            .sort([("score", {"$meta": "textScore"})])
            .limit(limit)
        )

        results = []
        async for doc in cursor:
            results.append(doc["value"])

        return results

    async def list_namespaces(self) -> List[str]:
        """List all namespaces."""
        db = await self._get_db()
        collection = db[self.collection_name]

        namespaces = await collection.distinct("namespace")
        return namespaces

    async def list_keys(self, namespace: str) -> List[str]:
        """List all keys in a namespace."""
        db = await self._get_db()
        collection = db[self.collection_name]

        cursor = collection.find({"namespace": namespace}, {"key": 1})
        keys = []
        async for doc in cursor:
            keys.append(doc["key"])

        return keys
