"""
Airtable MCP Tool Implementation

A hardened tool for interfacing with Airtable via Model Context Protocol (MCP).
Uses the official MCP Python SDK and pyairtable library for reliable Airtable operations.
"""

import asyncio
import json
import logging
import os
from ...config import config
import time
from typing import Any, Dict, List, Literal, Optional

from langchain_core.tools import BaseTool
from pyairtable import Api
from pyairtable.api.types import RecordDict
from pydantic import BaseModel, Field, ValidationError

from ...schemas.airtable_mcp_schemas import (
    AirtableMCPInput,
    AirtableMCPOutput,
    ListBasesOutput,
    ListRecordsOutput,
    ListTablesOutput,
    SearchRecordsOutput,
)
from ..circuit_breaker import CircuitBreaker

logger = logging.getLogger(__name__)


class AirtableMCPError(Exception):
    """Base exception for Airtable MCP operations."""

    pass


class AirtableMCPTool:
    """
    Hardened Airtable MCP tool implementation.

    Provides reliable access to Airtable bases, tables, and records via MCP protocol
    using the pyairtable library with comprehensive error handling and monitoring.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        circuit_breaker_failure_threshold: int = 5,
        circuit_breaker_recovery_timeout: int = 60,
        circuit_breaker_expected_exception: type = AirtableMCPError,
    ):
        """
        Initialize the Airtable MCP tool.

        Args:
            api_key: Airtable API key. If None, will try to get from AIRTABLE_API_KEY env var
            circuit_breaker_failure_threshold: Number of failures before circuit opens
            circuit_breaker_recovery_timeout: Seconds to wait before trying to close circuit
            circuit_breaker_expected_exception: Exception type that should trigger circuit breaker
        """
        if not api_key:
            from ...config import config

            self.api_key = config.airtable.api_key
        else:
            self.api_key = api_key
        if not self.api_key:
            raise ValueError(
                "Airtable API key is required. Set AIRTABLE_API_KEY environment variable or pass api_key parameter."
            )

        # Initialize Airtable API client
        try:
            self.api = Api(self.api_key)
            logger.info("Airtable API client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Airtable API client: {e}")
            raise AirtableMCPError(f"Failed to initialize Airtable API: {e}")

        # Initialize circuit breaker
        self.circuit_breaker = CircuitBreaker(
            name="airtable_mcp",
            failure_threshold=circuit_breaker_failure_threshold,
            recovery_timeout=circuit_breaker_recovery_timeout,
            expected_exception=circuit_breaker_expected_exception,
        )

        logger.info(
            f"AirtableMCPTool initialized with circuit breaker "
            f"(failure_threshold={circuit_breaker_failure_threshold}, "
            f"recovery_timeout={circuit_breaker_recovery_timeout})"
        )

    async def execute(self, tool_input: AirtableMCPInput) -> AirtableMCPOutput:
        """
        Execute an Airtable MCP operation.

        Args:
            tool_input: Validated input parameters

        Returns:
            AirtableMCPOutput: Results of the operation

        Raises:
            AirtableMCPError: If operation fails
        """
        start_time = time.time()
        operation = tool_input.operation

        logger.info(f"Executing Airtable MCP operation: {operation}")

        try:
            # Check circuit breaker
            if not self.circuit_breaker.can_execute():
                raise AirtableMCPError(
                    "Circuit breaker is open. Service temporarily unavailable."
                )

            # Route to appropriate operation
            if operation == "list_bases":
                result = await self._list_bases()
            elif operation == "list_tables":
                result = await self._list_tables(tool_input.baseId)
            elif operation == "list_records":
                result = await self._list_records(
                    tool_input.baseId, tool_input.tableId, tool_input.maxRecords
                )
            elif operation == "search_records":
                result = await self._search_records(
                    tool_input.baseId,
                    tool_input.tableId,
                    tool_input.filterByFormula,
                    tool_input.maxRecords,
                    tool_input.view,
                    tool_input.sort,
                )
            else:
                raise AirtableMCPError(f"Unknown operation: {operation}")

            # Record success
            self.circuit_breaker.record_success()
            execution_time = int((time.time() - start_time) * 1000)

            logger.info(
                f"Airtable MCP operation '{operation}' completed successfully in {execution_time}ms"
            )

            return AirtableMCPOutput(
                success=True,
                operation=operation,
                data=result.get("data"),
                base_id=result.get("base_id"),
                table_id=result.get("table_id"),
                filter_formula=result.get("filter_formula"),
                data_count=result.get("data_count", 0),
                message=result.get("message", "Operation completed successfully"),
                execution_time_ms=execution_time,
                mcp_server_status="connected",
            )

        except Exception as e:
            # Record failure
            self.circuit_breaker.record_failure()
            execution_time = int((time.time() - start_time) * 1000)
            error_type = type(e).__name__

            logger.error(f"Airtable MCP operation '{operation}' failed: {e}")

            return AirtableMCPOutput(
                success=False,
                operation=operation,
                error_type=error_type,
                message=str(e),
                execution_time_ms=execution_time,
                mcp_server_status="error",
            )

    async def _list_bases(self) -> Dict[str, Any]:
        """List all accessible Airtable bases."""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            bases = await loop.run_in_executor(None, self.api.bases)

            bases_data = []
            for base in bases:
                bases_data.append(
                    {
                        "id": base.id,
                        "name": base.name,
                        "permission_level": getattr(
                            base, "permission_level", "unknown"
                        ),
                    }
                )

            return {
                "data": bases_data,
                "data_count": len(bases_data),
                "message": f"Found {len(bases_data)} bases",
            }

        except Exception as e:
            logger.error(f"Error listing bases: {e}")
            raise AirtableMCPError(f"Failed to list bases: {e}")

    async def _list_tables(self, base_id: str) -> Dict[str, Any]:
        """List tables in a specific base."""
        try:
            # Get base
            base = self.api.base(base_id)

            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            tables = await loop.run_in_executor(None, lambda: base.tables())

            tables_data = []
            for table in tables:
                tables_data.append(
                    {
                        "id": table.table_id,
                        "name": table.table_name,
                        "primary_field_id": getattr(table, "primary_field_id", None),
                        "description": getattr(table, "description", None),
                    }
                )

            return {
                "data": tables_data,
                "base_id": base_id,
                "data_count": len(tables_data),
                "message": f"Found {len(tables_data)} tables in base {base_id}",
            }

        except Exception as e:
            logger.error(f"Error listing tables for base {base_id}: {e}")
            raise AirtableMCPError(f"Failed to list tables for base {base_id}: {e}")

    async def _list_records(
        self, base_id: str, table_id: str, max_records: Optional[int] = None
    ) -> Dict[str, Any]:
        """List records from a specific table."""
        try:
            # Get table
            table = self.api.table(base_id, table_id)

            # Prepare parameters
            kwargs = {}
            if max_records:
                kwargs["max_records"] = max_records

            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            records = await loop.run_in_executor(None, lambda: table.all(**kwargs))

            records_data = []
            for record in records:
                records_data.append(
                    {
                        "id": record["id"],
                        "fields": record["fields"],
                        "created_time": record.get("createdTime"),
                    }
                )

            return {
                "data": records_data,
                "base_id": base_id,
                "table_id": table_id,
                "data_count": len(records_data),
                "message": f"Retrieved {len(records_data)} records from table {table_id}",
            }

        except Exception as e:
            logger.error(
                f"Error listing records for table {table_id} in base {base_id}: {e}"
            )
            raise AirtableMCPError(f"Failed to list records for table {table_id}: {e}")

    async def _search_records(
        self,
        base_id: str,
        table_id: str,
        filter_formula: str,
        max_records: Optional[int] = None,
        view: Optional[str] = None,
        sort: Optional[List[Dict[str, str]]] = None,
    ) -> Dict[str, Any]:
        """Search records in a table using a filter formula."""
        try:
            # Get table
            table = self.api.table(base_id, table_id)

            # Prepare parameters
            kwargs = {"formula": filter_formula}
            if max_records:
                kwargs["max_records"] = max_records
            if view:
                kwargs["view"] = view
            if sort:
                # Convert sort objects to pyairtable format
                kwargs["sort"] = [(s["field"], s.get("direction", "asc")) for s in sort]

            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            records = await loop.run_in_executor(None, lambda: table.all(**kwargs))

            records_data = []
            for record in records:
                records_data.append(
                    {
                        "id": record["id"],
                        "fields": record["fields"],
                        "created_time": record.get("createdTime"),
                    }
                )

            return {
                "data": records_data,
                "base_id": base_id,
                "table_id": table_id,
                "filter_formula": filter_formula,
                "data_count": len(records_data),
                "message": f"Found {len(records_data)} records matching filter in table {table_id}",
            }

        except Exception as e:
            logger.error(f"Error searching records in table {table_id}: {e}")
            raise AirtableMCPError(f"Failed to search records in table {table_id}: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the Airtable MCP tool."""
        return {
            "circuit_breaker_state": self.circuit_breaker.state.name,
            "failure_count": self.circuit_breaker.failure_count,
            "last_failure_time": self.circuit_breaker.last_failure_time,
            "api_key_configured": bool(self.api_key),
            "tool_name": "AirtableMCPTool",
        }


# Global instance for reuse
_airtable_mcp_tool = None


def get_airtable_mcp_tool() -> AirtableMCPTool:
    """Get or create the global Airtable MCP tool instance."""
    global _airtable_mcp_tool
    if _airtable_mcp_tool is None:
        _airtable_mcp_tool = AirtableMCPTool()
    return _airtable_mcp_tool


async def airtable_mcp_operation(tool_input: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main entry point for Airtable MCP operations.

    Args:
        tool_input: Raw input dictionary

    Returns:
        Dict containing operation results

    Raises:
        ValidationError: If input validation fails
        AirtableMCPError: If operation fails
    """
    try:
        # Validate input
        validated_input = AirtableMCPInput(**tool_input)

        # Get tool instance
        tool = get_airtable_mcp_tool()

        # Execute operation
        result = await tool.execute(validated_input)

        return result.model_dump()

    except ValidationError as e:
        logger.error(f"Input validation failed: {e}")
        return AirtableMCPOutput(
            success=False,
            operation=tool_input.get("operation", "unknown"),
            error_type="ValidationError",
            message=f"Input validation failed: {e}",
            execution_time_ms=0,
        ).model_dump()

    except Exception as e:
        logger.error(f"Unexpected error in airtable_mcp_operation: {e}")
        return AirtableMCPOutput(
            success=False,
            operation=tool_input.get("operation", "unknown"),
            error_type=type(e).__name__,
            message=f"Unexpected error: {e}",
            execution_time_ms=0,
        ).model_dump()


class AirtableToolInput(BaseModel):
    """Input schema for Airtable MCP tool."""

    operation: Literal[
        "list_bases", "list_tables", "list_records", "search_records"
    ] = Field(description="The operation to perform")
    baseId: Optional[str] = Field(
        default=None,
        description="Airtable base ID (required for list_tables, list_records, search_records)",
    )
    tableId: Optional[str] = Field(
        default=None,
        description="Airtable table ID (required for list_records, search_records)",
    )
    filterByFormula: Optional[str] = Field(
        default=None,
        description="Airtable filter formula (required for search_records)",
    )
    maxRecords: Optional[int] = Field(
        default=None, description="Maximum number of records to return"
    )
    view: Optional[str] = Field(
        default=None, description="View name to use for search_records"
    )


class AirtableMCPLangChainTool(BaseTool):
    """LangChain-compatible wrapper for AirtableMCPTool."""

    name: str = "airtable_mcp"
    description: str = """Access Airtable databases for fitness and coaching data.
    
    Operations available:
    - list_bases: List all accessible Airtable bases
    - list_tables: List tables in a specific base (requires baseId)
    - list_records: List records from a table (requires baseId and tableId)
    - search_records: Search records with filter formula (requires baseId, tableId, and filterByFormula)
    
    Use this tool to access exercise databases, nutrition information, training programs, and other fitness-related data stored in Airtable."""

    args_schema: type[BaseModel] = AirtableToolInput
    airtable_tool: AirtableMCPTool = Field(exclude=True)

    def __init__(self, airtable_tool: AirtableMCPTool):
        super().__init__(airtable_tool=airtable_tool)

    async def _arun(self, **kwargs) -> str:
        """Async implementation of the tool."""
        try:
            # Convert kwargs to the format expected by AirtableMCPInput
            input_dict = {
                "operation": kwargs.get("operation"),
                "baseId": kwargs.get("baseId"),
                "tableId": kwargs.get("tableId"),
                "filterByFormula": kwargs.get("filterByFormula"),
                "maxRecords": kwargs.get("maxRecords"),
                "view": kwargs.get("view"),
            }

            # Remove None values
            input_dict = {k: v for k, v in input_dict.items() if v is not None}

            # Validate and execute
            validated_input = AirtableMCPInput(**input_dict)
            result = await self.airtable_tool.execute(validated_input)

            # Return formatted result
            if result.success:
                return f"Operation '{result.operation}' completed successfully. Found {result.data_count} items. Data: {json.dumps(result.data, indent=2)}"
            else:
                return f"Operation '{result.operation}' failed: {result.message}"

        except Exception as e:
            return f"Error executing Airtable operation: {str(e)}"

    def _run(self, **kwargs) -> str:
        """Sync implementation (not recommended for async operations)."""
        import asyncio

        try:
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # If we're in an async context, we can't use run_until_complete
                # Return a placeholder response
                return f"Airtable operation '{kwargs.get('operation', 'unknown')}' requires async execution. Please use async context."
            except RuntimeError:
                # No running loop, we can create one
                return asyncio.run(self._arun(**kwargs))
        except Exception as e:
            return f"Error executing Airtable operation: {str(e)}"


def get_airtable_langchain_tool() -> AirtableMCPLangChainTool:
    """Get LangChain-compatible Airtable tool."""
    airtable_tool = get_airtable_mcp_tool()
    return AirtableMCPLangChainTool(airtable_tool=airtable_tool)
