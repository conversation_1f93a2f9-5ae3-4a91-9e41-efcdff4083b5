#!/usr/bin/env python3
"""
Test Pinecone Integration with Advanced Memory System

This script tests the Pinecone vector database integration with the
Phase 2 advanced memory system.
"""

import asyncio
import logging
import os
from typing import Any, Dict

from dotenv import load_dotenv

# Load environment variables from .env.local
load_dotenv(".env.local")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_pinecone_config():
    """Test Pinecone configuration."""
    print("🔧 Testing Pinecone Configuration")
    print("=" * 50)

    try:
        # Check environment variables
        pinecone_api_key = os.getenv("PINECONE_API_KEY")
        pinecone_env = os.getenv("PINECONE_ENVIRONMENT")

        print(f"Pinecone API Key: {'✅ Set' if pinecone_api_key else '❌ Missing'}")
        print(f"Pinecone Environment: {pinecone_env or '❌ Missing'}")

        if not pinecone_api_key:
            print("❌ Pinecone API key not found in environment")
            return False

        # Test mem0 config
        from athlea_langgraph.config.mem0_config import create_mem0_config_dict

        config = create_mem0_config_dict("development")
        vector_store = config.get("vector_store", {})

        print(f"Vector Store Provider: {vector_store.get('provider', 'Unknown')}")

        if vector_store.get("provider") == "pinecone":
            print("✅ Pinecone configured as vector store provider")
            pinecone_config = vector_store.get("config", {})
            print(f"   - Collection Name: {pinecone_config.get('collection_name')}")
            print(f"   - Environment: {pinecone_config.get('environment')}")
            print(f"   - Embedding Dims: {pinecone_config.get('embedding_model_dims')}")
        else:
            print(f"❌ Expected Pinecone, got: {vector_store.get('provider')}")
            return False

    except Exception as e:
        print(f"❌ Error in Pinecone config test: {e}")
        return False

    return True


async def test_pinecone_connection():
    """Test Pinecone connection."""
    print("\n🔌 Testing Pinecone Connection")
    print("=" * 50)

    try:
        from pinecone import Pinecone

        # Initialize Pinecone client
        pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))

        # List indexes
        indexes = pc.list_indexes()
        print(f"✅ Connected to Pinecone successfully")
        print(f"Available indexes: {[idx.name for idx in indexes]}")

        # Check if our index exists
        index_name = "athlea-memory"
        existing_indexes = [idx.name for idx in indexes]

        if index_name in existing_indexes:
            print(f"✅ Index '{index_name}' already exists")

            # Get index info
            index = pc.Index(index_name)
            stats = index.describe_index_stats()
            print(f"   - Total vectors: {stats.get('total_vector_count', 0)}")
            print(f"   - Dimension: {stats.get('dimension', 'Unknown')}")
        else:
            print(f"⚠️ Index '{index_name}' does not exist")
            print(
                "   This is normal for first-time setup - mem0 will create it automatically"
            )

    except Exception as e:
        print(f"❌ Error connecting to Pinecone: {e}")
        return False

    return True


async def test_advanced_memory_with_pinecone():
    """Test advanced memory system with Pinecone."""
    print("\n🧠 Testing Advanced Memory with Pinecone")
    print("=" * 50)

    try:
        from athlea_langgraph.graphs.coaching_graph import (
            ADVANCED_MEMORY_AVAILABLE,
            create_memory_enhanced_coaching_graph,
            get_memory_analytics,
        )

        if not ADVANCED_MEMORY_AVAILABLE:
            print("❌ Advanced memory system not available")
            return False

        print("✅ Advanced memory system available")

        # Test memory-enhanced graph creation
        user_id = "test_pinecone_user"

        print(f"Creating memory-enhanced graph for user: {user_id}")
        graph = create_memory_enhanced_coaching_graph(
            user_id=user_id,
            memory_config={
                "environment": "development",
                "enable_analytics": True,
                "enable_auto_maintenance": False,  # Disable for testing
            },
        )
        print("✅ Memory-enhanced coaching graph created successfully")

        # Test memory analytics
        print("Testing memory analytics...")
        analytics = await get_memory_analytics(
            user_id=user_id,
            memory_config={"environment": "development", "enable_analytics": True},
        )

        if "error" in analytics:
            print(f"⚠️ Analytics error (expected for new setup): {analytics['error']}")
        else:
            print("✅ Memory analytics retrieved successfully")
            print(f"   - User ID: {analytics.get('user_id')}")
            print(f"   - Timestamp: {analytics.get('timestamp')}")

    except Exception as e:
        print(f"❌ Error testing advanced memory: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


async def test_memory_storage_retrieval():
    """Test memory storage and retrieval with Pinecone."""
    print("\n💾 Testing Memory Storage and Retrieval")
    print("=" * 50)

    try:
        from athlea_langgraph.memory import AdvancedMemoryManager, MemoryType

        # Create memory manager
        memory_manager = AdvancedMemoryManager(
            environment="development",
            enable_analytics=True,
            enable_auto_maintenance=False,
        )

        user_id = "test_pinecone_storage"

        # Test memory storage
        print("Storing test memory...")
        memory_id = await memory_manager.store_memory(
            user_id=user_id,
            content="I want to improve my deadlift form and increase my max weight to 200kg",
            memory_type=MemoryType.CONVERSATION,
            metadata={"session_type": "coaching", "domain": "strength"},
        )
        print(f"✅ Memory stored with ID: {memory_id}")

        # Test memory retrieval
        print("Searching for stored memory...")
        memories = await memory_manager.search_memories(
            user_id=user_id, query="deadlift strength training", limit=5
        )
        print(f"✅ Found {len(memories)} memories")

        if memories:
            print("   Sample memory:")
            sample = memories[0]
            print(f"   - Content: {sample.get('text', 'N/A')[:100]}...")
            print(f"   - Metadata: {sample.get('metadata', {})}")

    except Exception as e:
        print(f"❌ Error testing memory storage/retrieval: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


async def main():
    """Run all Pinecone integration tests."""
    print("🎯 Pinecone Integration Tests")
    print("=" * 60)
    print("Testing Pinecone vector database integration with advanced memory")
    print()

    tests = [
        ("Pinecone Configuration", test_pinecone_config),
        ("Pinecone Connection", test_pinecone_connection),
        ("Advanced Memory with Pinecone", test_advanced_memory_with_pinecone),
        ("Memory Storage and Retrieval", test_memory_storage_retrieval),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🏆 All tests passed! Pinecone integration is working correctly.")
        print("\n💡 Key Features Verified:")
        print("  ✅ Pinecone vector database connectivity")
        print("  ✅ Advanced memory system with Pinecone backend")
        print("  ✅ Memory storage and retrieval operations")
        print("  ✅ Memory-enhanced coaching graph integration")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("  - Ensure PINECONE_API_KEY is set correctly")
        print("  - Check PINECONE_ENVIRONMENT is set to 'us-east-1'")
        print("  - Verify Pinecone account has sufficient quota")


if __name__ == "__main__":
    asyncio.run(main())
