"""
Domain schemas for multi-domain memory separation.

Defines coaching domains and domain-specific metadata structures.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class CoachingDomain(Enum):
    """Coaching domains for memory organization."""

    STRENGTH = "strength"
    CARDIO = "cardio"
    RUNNING = "running"
    CYCLING = "cycling"
    NUTRITION = "nutrition"
    RECOVERY = "recovery"
    MENTAL = "mental"
    GENERAL = "general"  # For cross-domain or unclassified content


@dataclass
class DomainClassification:
    """Result of domain classification for a memory."""

    primary_domain: CoachingDomain
    secondary_domains: List[CoachingDomain] = field(default_factory=list)
    confidence_scores: Dict[CoachingDomain, float] = field(default_factory=dict)
    classification_timestamp: datetime = field(default_factory=datetime.now)

    @property
    def all_domains(self) -> List[CoachingDomain]:
        """Get all relevant domains for this memory."""
        return [self.primary_domain] + self.secondary_domains


@dataclass
class MemoryDomainMetadata:
    """Domain-specific metadata for memories."""

    domain_classification: DomainClassification
    domain_specific_tags: List[str] = field(default_factory=list)
    cross_domain_references: List[str] = field(default_factory=list)  # Memory IDs
    domain_relevance_score: float = 1.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "primary_domain": self.domain_classification.primary_domain.value,
            "secondary_domains": [
                d.value for d in self.domain_classification.secondary_domains
            ],
            "confidence_scores": {
                d.value: score
                for d, score in self.domain_classification.confidence_scores.items()
            },
            "classification_timestamp": self.domain_classification.classification_timestamp.isoformat(),
            "domain_specific_tags": self.domain_specific_tags,
            "cross_domain_references": self.cross_domain_references,
            "domain_relevance_score": self.domain_relevance_score,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryDomainMetadata":
        """Create from dictionary data."""
        classification = DomainClassification(
            primary_domain=CoachingDomain(data["primary_domain"]),
            secondary_domains=[
                CoachingDomain(d) for d in data.get("secondary_domains", [])
            ],
            confidence_scores={
                CoachingDomain(d): score
                for d, score in data.get("confidence_scores", {}).items()
            },
            classification_timestamp=datetime.fromisoformat(
                data.get("classification_timestamp", datetime.now().isoformat())
            ),
        )

        return cls(
            domain_classification=classification,
            domain_specific_tags=data.get("domain_specific_tags", []),
            cross_domain_references=data.get("cross_domain_references", []),
            domain_relevance_score=data.get("domain_relevance_score", 1.0),
        )


# Domain-specific keywords for classification
DOMAIN_KEYWORDS = {
    CoachingDomain.STRENGTH: [
        "strength",
        "lifting",
        "weights",
        "deadlift",
        "squat",
        "bench press",
        "resistance",
        "muscle",
        "hypertrophy",
        "powerlifting",
        "bodybuilding",
        "sets",
        "reps",
        "progressive overload",
        "1RM",
        "PR",
    ],
    CoachingDomain.CARDIO: [
        "cardio",
        "cardiovascular",
        "endurance",
        "heart rate",
        "aerobic",
        "anaerobic",
        "HIIT",
        "interval",
        "steady state",
        "VO2",
        "zones",
    ],
    CoachingDomain.RUNNING: [
        "running",
        "run",
        "jog",
        "marathon",
        "5K",
        "10K",
        "half marathon",
        "pace",
        "miles",
        "kilometers",
        "trail",
        "track",
        "road running",
        "tempo",
        "speed work",
        "long run",
    ],
    CoachingDomain.CYCLING: [
        "cycling",
        "bike",
        "bicycle",
        "road bike",
        "mountain bike",
        "watts",
        "power",
        "cadence",
        "FTP",
        "cycling",
        "ride",
        "spinning",
    ],
    CoachingDomain.NUTRITION: [
        "nutrition",
        "diet",
        "food",
        "calories",
        "macros",
        "protein",
        "carbs",
        "fat",
        "meal",
        "eating",
        "supplements",
        "hydration",
        "weight loss",
        "weight gain",
        "meal prep",
        "vitamins",
    ],
    CoachingDomain.RECOVERY: [
        "recovery",
        "rest",
        "sleep",
        "massage",
        "stretching",
        "foam rolling",
        "mobility",
        "rest day",
        "deload",
        "active recovery",
        "injury",
        "rehabilitation",
        "therapy",
        "relaxation",
    ],
    CoachingDomain.MENTAL: [
        "mental",
        "mindset",
        "motivation",
        "confidence",
        "stress",
        "anxiety",
        "focus",
        "goals",
        "habits",
        "discipline",
        "mindfulness",
        "meditation",
        "positive thinking",
        "self-talk",
        "visualization",
    ],
    CoachingDomain.GENERAL: [
        "fitness",
        "health",
        "wellness",
        "coaching",
        "training",
        "exercise",
        "workout",
        "lifestyle",
        "progress",
        "goals",
        "plan",
        "routine",
        "schedule",
        "assessment",
        "consultation",
        "general",
    ],
}
