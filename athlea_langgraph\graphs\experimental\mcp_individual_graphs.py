"""
MCP-Enabled Individual Agent Graphs

This module provides individual graphs for testing and debugging
each specialized agent with full MCP tool integration and proper prompt loading.
"""

import logging
from typing import Any, Dict, List, Optional, Union

from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.prompts import PromptTemplate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from ...services.azure_openai_service import create_azure_chat_openai

# Import state and utilities
from ...states.state import AgentState
from ...utils.mcp_agent_integration import get_agent_tools
from ...utils.prompt_loader import PromptLoader

logger = logging.getLogger(__name__)


class MCPIndividualAgentState(AgentState):
    """Enhanced state for MCP-enabled individual agent testing."""

    current_agent: Optional[str] = None
    agent_response: Optional[str] = None
    tools_used: List[str] = []
    mcp_tools_available: List[str] = []
    debug_info: Dict[str, Any] = {}


def safe_get_content(message: Union[Dict[str, Any], BaseMessage]) -> str:
    """
    Safely extract content from either a dict or message object.
    """
    if isinstance(message, dict):
        return message.get("content", "")
    elif hasattr(message, "content"):
        return message.content
    else:
        return str(message) if message else ""


async def _experimental_create_mcp_strength_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled strength agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled strength agent test graph")

    async def mcp_strength_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled strength agent node with tools and proper prompts."""

        try:
            # Load strength coach prompt
            prompt_loader = PromptLoader()
            strength_prompt_config = prompt_loader.load_prompt("strength_coach")
            strength_prompt = strength_prompt_config.prompt.system

            if not strength_prompt:
                logger.warning("Could not load strength_coach prompt, using fallback")
                strength_prompt = "You are a professional strength training coach. Provide expert advice on strength training, powerlifting, and muscle building."

            # Get MCP and direct tools for strength agent
            tools = await get_agent_tools("strength", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            logger.info(f"Strength agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{strength_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need strength training advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="strength_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "strength_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "strength_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": strength_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP strength agent: {e}", exc_info=True)
            error_msg = f"MCP Strength agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="strength_agent")],
                "current_agent": "strength_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "strength_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("strength_agent", mcp_strength_node)
    builder.add_edge(START, "strength_agent")
    builder.add_edge("strength_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled strength agent test graph compiled successfully")
    return graph


async def _experimental_create_mcp_nutrition_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled nutrition agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled nutrition agent test graph")

    async def mcp_nutrition_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled nutrition agent node with tools and proper prompts."""

        try:
            # Load nutrition coach prompt
            prompt_loader = PromptLoader()
            nutrition_prompt_config = prompt_loader.load_prompt("nutrition_coach")
            nutrition_prompt = nutrition_prompt_config.prompt.system

            if not nutrition_prompt:
                logger.warning("Could not load nutrition_coach prompt, using fallback")
                nutrition_prompt = "You are a professional nutrition coach. Provide expert advice on diet, meal planning, and nutritional optimization."

            # Get MCP and direct tools for nutrition agent
            tools = await get_agent_tools("nutrition", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            logger.info(f"Nutrition agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{nutrition_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need nutrition advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="nutrition_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "nutrition_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "nutrition_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": nutrition_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP nutrition agent: {e}", exc_info=True)
            error_msg = f"MCP Nutrition agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="nutrition_agent")],
                "current_agent": "nutrition_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "nutrition_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("nutrition_agent", mcp_nutrition_node)
    builder.add_edge(START, "nutrition_agent")
    builder.add_edge("nutrition_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled nutrition agent test graph compiled successfully")
    return graph


async def _experimental_create_mcp_cardio_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled cardio agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled cardio agent test graph")

    async def mcp_cardio_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled cardio agent node with tools and proper prompts."""

        try:
            # Load cardio coach prompt
            prompt_loader = PromptLoader()
            cardio_prompt_config = prompt_loader.load_prompt("cardio_coach")
            cardio_prompt = cardio_prompt_config.prompt.system

            if not cardio_prompt:
                logger.warning("Could not load cardio_coach prompt, using fallback")
                cardio_prompt = "You are a professional cardio and endurance coach. Provide expert advice on cardio training, endurance building, and aerobic fitness."

            # Get MCP and direct tools for cardio agent
            tools = await get_agent_tools("cardio", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            logger.info(f"Cardio agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{cardio_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need cardio training advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="cardio_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "cardio_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "cardio_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": cardio_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP cardio agent: {e}", exc_info=True)
            error_msg = f"MCP Cardio agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="cardio_agent")],
                "current_agent": "cardio_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "cardio_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("cardio_agent", mcp_cardio_node)
    builder.add_edge(START, "cardio_agent")
    builder.add_edge("cardio_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled cardio agent test graph compiled successfully")
    return graph


async def _experimental_create_mcp_recovery_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled recovery agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled recovery agent test graph")

    async def mcp_recovery_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled recovery agent node with tools and proper prompts."""

        try:
            # Load recovery coach prompt
            prompt_loader = PromptLoader()
            recovery_prompt_config = prompt_loader.load_prompt("recovery_coach")
            recovery_prompt = recovery_prompt_config.prompt.system

            if not recovery_prompt:
                logger.warning("Could not load recovery_coach prompt, using fallback")
                recovery_prompt = "You are a professional recovery and wellness coach. Provide expert advice on recovery, sleep, stress management, and injury prevention."

            # Get MCP and direct tools for recovery agent
            tools = await get_agent_tools("recovery", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            logger.info(f"Recovery agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{recovery_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need recovery advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="recovery_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "recovery_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "recovery_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": recovery_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP recovery agent: {e}", exc_info=True)
            error_msg = f"MCP Recovery agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="recovery_agent")],
                "current_agent": "recovery_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "recovery_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("recovery_agent", mcp_recovery_node)
    builder.add_edge(START, "recovery_agent")
    builder.add_edge("recovery_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled recovery agent test graph compiled successfully")
    return graph


async def _experimental_create_mcp_mental_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled mental agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled mental agent test graph")

    async def mcp_mental_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled mental agent node with tools and proper prompts."""

        try:
            # Load mental coach prompt
            prompt_loader = PromptLoader()
            mental_prompt_config = prompt_loader.load_prompt("mental_coach")
            mental_prompt = mental_prompt_config.prompt.system

            if not mental_prompt:
                logger.warning("Could not load mental_coach prompt, using fallback")
                mental_prompt = "You are a professional mental performance coach. Provide expert advice on mental training, mindfulness, focus, and sports psychology."

            # Get MCP and direct tools for mental agent
            tools = await get_agent_tools("mental", use_mcp=True)
            tool_names = [tool.name for tool in tools]

            logger.info(f"Mental agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{mental_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need mental training advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="mental_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "mental_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "mental_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": mental_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP mental agent: {e}", exc_info=True)
            error_msg = f"MCP Mental agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="mental_agent")],
                "current_agent": "mental_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "mental_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("mental_agent", mcp_mental_node)
    builder.add_edge(START, "mental_agent")
    builder.add_edge("mental_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled mental agent test graph compiled successfully")
    return graph


async def _experimental_create_mcp_cycling_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create MCP-enabled cycling agent graph."""

    if config is None:
        config = {}

    logger.info("Creating MCP-enabled cycling agent test graph")

    async def mcp_cycling_node(state: MCPIndividualAgentState) -> Dict[str, Any]:
        """MCP-enabled cycling agent node with tools and proper prompts."""

        try:
            # Load cycling coach prompt
            prompt_loader = PromptLoader()
            cycling_prompt_config = prompt_loader.load_prompt("cycling_coach")
            cycling_prompt = cycling_prompt_config.prompt.system

            if not cycling_prompt:
                logger.warning("Could not load cycling_coach prompt, using fallback")
                cycling_prompt = "You are a professional cycling coach. Provide expert advice on cycling training, technique, and performance optimization."

            # Get MCP and direct tools for cycling agent (use cardio as fallback since they're similar)
            tools = await get_agent_tools(
                "cardio", use_mcp=True
            )  # Use cardio tools for cycling
            tool_names = [tool.name for tool in tools]

            logger.info(f"Cycling agent loaded {len(tools)} tools: {tool_names}")

            # Create ReAct prompt template
            react_prompt = PromptTemplate.from_template(
                f"""
{cycling_prompt}

You have access to the following tools:
{{tools}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {{input}}
Thought: {{agent_scratchpad}}
"""
            )

            # Get LLM
            llm = create_azure_chat_openai()

            # Create ReAct agent
            agent = create_react_agent(llm, tools, react_prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=config.get("max_iterations", 5),
                handle_parsing_errors=True,
            )

            # Get user input
            messages = list(state.get("messages", []))
            user_input = ""

            # Extract user input from various sources
            if messages:
                for msg in reversed(messages):
                    if isinstance(msg, HumanMessage):
                        user_input = msg.content
                        break

            if not user_input:
                user_input = (
                    state.get("input")
                    or state.get("user_query")
                    or state.get("query")
                    or state.get("text")
                    or "Hello, I need cycling advice."
                )

            # Execute agent with tools
            result = await agent_executor.ainvoke({"input": user_input})
            response = result.get("output", "No response generated")

            # Track tools used
            tools_used = []
            if hasattr(agent_executor, "intermediate_steps"):
                for step in agent_executor.intermediate_steps:
                    if hasattr(step, "tool"):
                        tools_used.append(step.tool)

            # Create response message
            ai_message = AIMessage(content=response, name="cycling_agent")

            return {
                "messages": messages + [ai_message],
                "current_agent": "cycling_agent",
                "agent_response": response,
                "tools_used": tools_used,
                "mcp_tools_available": tool_names,
                "debug_info": {
                    "agent": "cycling_agent",
                    "tools_count": len(tools),
                    "tools_available": tool_names,
                    "tools_used": tools_used,
                    "prompt_loaded": cycling_prompt is not None,
                    "user_input": user_input,
                    "mcp_enabled": True,
                },
            }

        except Exception as e:
            logger.error(f"Error in MCP cycling agent: {e}", exc_info=True)
            error_msg = f"MCP Cycling agent error: {str(e)}"

            return {
                "messages": state.get("messages", [])
                + [AIMessage(content=error_msg, name="cycling_agent")],
                "current_agent": "cycling_agent",
                "agent_response": error_msg,
                "tools_used": [],
                "mcp_tools_available": [],
                "debug_info": {
                    "agent": "cycling_agent",
                    "error": str(e),
                    "mcp_enabled": True,
                },
            }

    # Build graph
    builder = StateGraph(MCPIndividualAgentState)
    builder.add_node("cycling_agent", mcp_cycling_node)
    builder.add_edge(START, "cycling_agent")
    builder.add_edge("cycling_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("MCP-enabled cycling agent test graph compiled successfully")
    return graph
