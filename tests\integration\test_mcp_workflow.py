"""
Tests for the MCP workflow implementation.
"""

import pytest

from athlea_langgraph.workflows.mcp_workflow import (
    create_mcp_workflow,
    mock_search_running_sessions,
)


class TestMCPWorkflow:
    """Test cases for the MCP workflow."""

    @pytest.mark.asyncio
    async def test_create_workflow(self) -> None:
        """Test that the workflow can be created successfully."""
        workflow = await create_mcp_workflow()
        assert workflow is not None

    @pytest.mark.asyncio
    async def test_running_session_query(self) -> None:
        """Test workflow with a running session query."""
        workflow = await create_mcp_workflow()

        result = await workflow.ainvoke({"input": "I need to find a running session"})

        assert "mcpResult" in result
        assert result["mcpToolName"] == "search_running_sessions"
        assert "error" not in result

    @pytest.mark.asyncio
    async def test_unsupported_query(self) -> None:
        """Test workflow with an unsupported query."""
        workflow = await create_mcp_workflow()

        result = await workflow.ainvoke({"input": "What's the weather like?"})

        assert "error" in result
        assert result["error"] == "No suitable MCP tool found for input."
        assert "mcpResult" not in result

    @pytest.mark.asyncio
    async def test_mock_search_running_sessions(self) -> None:
        """Test the mock search running sessions tool."""
        result = await mock_search_running_sessions(
            {"sessionName": "TestSession", "limit": 3}
        )

        assert "sessions" in result
        assert len(result["sessions"]) == 3
        assert result["total"] == 3
        assert result["query"] == "TestSession"

        # Check session structure
        session = result["sessions"][0]
        assert "id" in session
        assert "name" in session
        assert "duration" in session
        assert "type" in session
        assert session["type"] == "running"

    @pytest.mark.asyncio
    async def test_workflow_streaming(self) -> None:
        """Test that the workflow can be streamed."""
        workflow = await create_mcp_workflow()

        chunks = []
        async for chunk in workflow.astream({"input": "Show me running session data"}):
            chunks.append(chunk)

        assert len(chunks) == 2  # decide_mcp_tool and execute_mcp_tool
        assert "decide_mcp_tool" in chunks[0]
        assert "execute_mcp_tool" in chunks[1]
