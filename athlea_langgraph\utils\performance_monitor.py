"""
Performance Monitoring

Utilities for monitoring and optimizing the coaching system's performance.
"""

import asyncio
import json
import logging
import time
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

import psutil

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""

    node_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_start: Optional[float] = None
    memory_end: Optional[float] = None
    memory_delta: Optional[float] = None
    token_count: Optional[int] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def complete(self):
        """Mark the metric as complete and calculate duration."""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time

        if self.memory_start and self.memory_end:
            self.memory_delta = self.memory_end - self.memory_start


class PerformanceMonitor:
    """Monitors performance metrics for the coaching system."""

    def __init__(
        self,
        enable_memory_tracking: bool = True,
        enable_token_tracking: bool = True,
        alert_threshold_seconds: float = 10.0,
        log_slow_operations: bool = True,
    ):
        """
        Initialize the performance monitor.

        Args:
            enable_memory_tracking: Track memory usage
            enable_token_tracking: Track token consumption
            alert_threshold_seconds: Threshold for slow operation alerts
            log_slow_operations: Log operations exceeding threshold
        """
        self.enable_memory_tracking = enable_memory_tracking
        self.enable_token_tracking = enable_token_tracking
        self.alert_threshold_seconds = alert_threshold_seconds
        self.log_slow_operations = log_slow_operations

        self.metrics: List[PerformanceMetrics] = []
        self.active_metrics: Dict[str, PerformanceMetrics] = {}
        self.node_statistics = defaultdict(
            lambda: {
                "count": 0,
                "total_duration": 0,
                "avg_duration": 0,
                "max_duration": 0,
                "min_duration": float("inf"),
                "errors": 0,
            }
        )

    def start_operation(
        self, node_name: str, metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Start tracking an operation.

        Returns:
            Operation ID for tracking
        """
        operation_id = f"{node_name}_{time.time()}"

        metric = PerformanceMetrics(
            node_name=node_name, start_time=time.time(), metadata=metadata or {}
        )

        if self.enable_memory_tracking:
            process = psutil.Process()
            metric.memory_start = process.memory_info().rss / 1024 / 1024  # MB

        self.active_metrics[operation_id] = metric
        logger.debug(f"Started tracking operation: {operation_id}")

        return operation_id

    def end_operation(
        self,
        operation_id: str,
        token_count: Optional[int] = None,
        error: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None,
    ):
        """End tracking an operation."""
        if operation_id not in self.active_metrics:
            logger.warning(f"Operation {operation_id} not found in active metrics")
            return

        metric = self.active_metrics.pop(operation_id)

        if self.enable_memory_tracking:
            process = psutil.Process()
            metric.memory_end = process.memory_info().rss / 1024 / 1024  # MB

        if self.enable_token_tracking and token_count:
            metric.token_count = token_count

        if error:
            metric.error = error

        if additional_metadata:
            metric.metadata.update(additional_metadata)

        metric.complete()
        self.metrics.append(metric)

        # Update statistics
        self._update_statistics(metric)

        # Check for slow operations
        if self.log_slow_operations and metric.duration > self.alert_threshold_seconds:
            logger.warning(
                f"Slow operation detected: {metric.node_name} "
                f"took {metric.duration:.2f}s (threshold: {self.alert_threshold_seconds}s)"
            )

    def _update_statistics(self, metric: PerformanceMetrics):
        """Update node statistics with completed metric."""
        stats = self.node_statistics[metric.node_name]

        stats["count"] += 1

        if metric.error:
            stats["errors"] += 1

        if metric.duration:
            stats["total_duration"] += metric.duration
            stats["avg_duration"] = stats["total_duration"] / stats["count"]
            stats["max_duration"] = max(stats["max_duration"], metric.duration)
            stats["min_duration"] = min(stats["min_duration"], metric.duration)

    def get_statistics(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            "node_statistics": dict(self.node_statistics),
            "total_operations": len(self.metrics),
            "active_operations": len(self.active_metrics),
            "memory_usage_mb": (
                psutil.Process().memory_info().rss / 1024 / 1024
                if self.enable_memory_tracking
                else None
            ),
        }

    def get_slow_operations(
        self, threshold: Optional[float] = None
    ) -> List[PerformanceMetrics]:
        """Get operations that exceeded the threshold."""
        threshold = threshold or self.alert_threshold_seconds
        return [m for m in self.metrics if m.duration and m.duration > threshold]

    def export_metrics(self, filepath: str):
        """Export metrics to a JSON file."""
        data = {
            "timestamp": datetime.now().isoformat(),
            "statistics": self.get_statistics(),
            "metrics": [
                {
                    "node_name": m.node_name,
                    "duration": m.duration,
                    "memory_delta": m.memory_delta,
                    "token_count": m.token_count,
                    "error": m.error,
                    "metadata": m.metadata,
                }
                for m in self.metrics
            ],
        }

        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Exported {len(self.metrics)} metrics to {filepath}")


class ConversationOptimizer:
    """Optimizes conversation state management for large conversations."""

    def __init__(
        self,
        max_messages: int = 50,
        summarization_threshold: int = 30,
        keep_recent_messages: int = 10,
    ):
        """
        Initialize the conversation optimizer.

        Args:
            max_messages: Maximum messages to keep in state
            summarization_threshold: When to trigger summarization
            keep_recent_messages: Number of recent messages to always keep
        """
        self.max_messages = max_messages
        self.summarization_threshold = summarization_threshold
        self.keep_recent_messages = keep_recent_messages

    async def optimize_messages(
        self, messages: List[Any], summarizer: Optional[Callable] = None
    ) -> List[Any]:
        """
        Optimize message list for performance.

        Args:
            messages: List of messages
            summarizer: Optional function to summarize old messages

        Returns:
            Optimized message list
        """
        if len(messages) <= self.max_messages:
            return messages

        logger.info(f"Optimizing {len(messages)} messages")

        # Keep system messages and recent messages
        system_messages = [
            m for m in messages if hasattr(m, "type") and m.type == "system"
        ]
        recent_messages = messages[-self.keep_recent_messages :]

        # Messages to potentially summarize
        middle_messages = messages[len(system_messages) : -self.keep_recent_messages]

        if summarizer and len(middle_messages) > self.summarization_threshold:
            # Summarize old messages
            summary = await summarizer(middle_messages)
            return system_messages + [summary] + recent_messages
        else:
            # Simple truncation
            keep_from_middle = (
                self.max_messages - len(system_messages) - len(recent_messages)
            )
            return (
                system_messages + middle_messages[-keep_from_middle:] + recent_messages
            )


class ResourceMonitor:
    """Monitors system resources during execution."""

    def __init__(
        self,
        check_interval_seconds: float = 5.0,
        cpu_threshold: float = 80.0,
        memory_threshold: float = 80.0,
    ):
        """
        Initialize the resource monitor.

        Args:
            check_interval_seconds: How often to check resources
            cpu_threshold: CPU usage threshold for alerts (percentage)
            memory_threshold: Memory usage threshold for alerts (percentage)
        """
        self.check_interval_seconds = check_interval_seconds
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.monitoring = False
        self.monitor_task = None

    async def start_monitoring(self):
        """Start resource monitoring."""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Started resource monitoring")

    async def stop_monitoring(self):
        """Stop resource monitoring."""
        self.monitoring = False
        if self.monitor_task:
            await self.monitor_task
        logger.info("Stopped resource monitoring")

    async def _monitor_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent

                if cpu_percent > self.cpu_threshold:
                    logger.warning(f"High CPU usage: {cpu_percent}%")

                if memory_percent > self.memory_threshold:
                    logger.warning(f"High memory usage: {memory_percent}%")

                await asyncio.sleep(self.check_interval_seconds)

            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                await asyncio.sleep(self.check_interval_seconds)

    def get_current_resources(self) -> Dict[str, Any]:
        """Get current resource usage."""
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_available_mb": psutil.virtual_memory().available / 1024 / 1024,
            "disk_usage_percent": psutil.disk_usage("/").percent,
        }


# Global performance monitor instance
_performance_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get or create the global performance monitor."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor
