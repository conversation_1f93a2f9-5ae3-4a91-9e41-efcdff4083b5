# 🧪 Testing Guide - Organized Test Architecture

## **🎯 New Organized Test Structure**

Following best practices, all tests are now properly organized by purpose and domain:

```
tests/
├── unit/                     # Fast, isolated component tests
├── integration/              # Multi-component workflow tests  
├── tools/                    # Tool-specific validation tests
├── agents/                   # Agent-specific unit tests
├── e2e/                      # End-to-end user journey tests
├── runners/                  # Test runner scripts (Phase 1-3)
├── fixtures/                 # Test data and utilities
└── synthetic/                # Automated health checks
```

## **🚀 Quick Test Commands**

### **Convenience Runner (Recommended)**
```bash
# 🚀 Run comprehensive test suite  
python run_tests_convenience.py main

# ⚡ Run modular agent tests (Phase 1)
python run_tests_convenience.py modular

# 🔧 Run tool organization tests (Phase 2)  
python run_tests_convenience.py tools

# 🎯 Run phase 3 tests with different modes
python run_tests_convenience.py phase3 fast        # Fast unit tests
python run_tests_convenience.py phase3 coverage    # Coverage report
python run_tests_convenience.py phase3 all         # All test modes
```

### **Direct Poetry Commands**
```bash
# ⚡ Fast unit tests only
poetry run pytest tests/unit/ -v

# 🔗 Integration tests only  
poetry run pytest tests/integration/ -v

# 🔧 Tool validation tests
poetry run pytest tests/tools/ -v

# 👥 Agent-specific tests
poetry run pytest tests/agents/ -v

# 🌐 End-to-end user journeys
poetry run pytest tests/e2e/ -v

# 📊 Tests with coverage report
poetry run pytest tests/unit/ tests/tools/ tests/integration/ --cov=athlea_langgraph --cov-report=html

# 🎯 Run specific test file
poetry run pytest tests/integration/test_onboarding_graph.py -v

# 🔍 Run tests matching pattern
poetry run pytest tests/ -k "onboarding" -v
```

---

## **🧪 Test Types & Purposes**

### **🔧 Unit Tests** (`tests/unit/`)
**What they test:** Individual components in isolation
- ✅ Single agent nodes
- ✅ Core functionality  
- ✅ State transformations
- ✅ Schema validations

**Characteristics:**
- **Speed:** < 1 second each
- **Dependencies:** All external services mocked
- **Purpose:** Catch logic errors early

### **👥 Agent Tests** (`tests/agents/`)
**What they test:** Agent-specific functionality
- ✅ Individual agent behavior
- ✅ Agent response formatting
- ✅ Agent tool usage
- ✅ Agent schema compliance

### **🔧 Tool Tests** (`tests/tools/`)
**What they test:** Domain-specific tool validation (Phase 2 aligned)
- ✅ Tool input/output contracts
- ✅ Schema validation
- ✅ Domain-specific logic
- ✅ Tool access control

### **🔗 Integration Tests** (`tests/integration/`)
**What they test:** How components work together
- ✅ Multi-agent workflows
- ✅ Graph execution flows
- ✅ State transitions
- ✅ Tool interactions

**Characteristics:**
- **Speed:** 5-30 seconds each
- **Dependencies:** Some real services, complex mocks
- **Purpose:** Verify component interactions

### **🌐 End-to-End Tests** (`tests/e2e/`)
**What they test:** Complete user journeys
- ✅ Full onboarding flow
- ✅ Multi-turn conversations
- ✅ Real API interactions
- ✅ Error handling scenarios

**Characteristics:**
- **Speed:** 30+ seconds each
- **Dependencies:** Real services and databases
- **Purpose:** Validate user experience

---

## **📂 Test Runner Organization** (`tests/runners/`)

All test runners are now organized by implementation phase:

```bash
# Phase 1: Modular Agents
python tests/runners/run_modular_agent_tests.py

# Phase 2: Tool Organization  
python tests/runners/run_tool_organization_tests.py

# Phase 3: Advanced Testing
python tests/runners/run_phase3_tests.py <mode>

# Comprehensive Suite
python tests/runners/run_tests.py
```

### **Phase 3 Test Modes:**
- `agents` - Agent-specific unit tests
- `tools` - Tool validation tests
- `integration` - End-to-end workflows
- `synthetic` - Automated health checks
- `fast` - Quick unit tests with fail-fast
- `coverage` - Full coverage analysis
- `health` - System health monitoring
- `performance` - Performance benchmarks
- `all` - All modes in sequence

---

## **🎯 When to Use Each Test Type**

| Scenario | Test Type | Command |
|----------|-----------|---------|
| **Quick feedback during development** | Unit | `python run_tests_convenience.py phase3 fast` |
| **Testing specific domains** | Tools | `poetry run pytest tests/tools/strength/ -v` |
| **Agent behavior validation** | Agents | `poetry run pytest tests/agents/ -v` |
| **Before committing code** | Integration | `poetry run pytest tests/integration/ -v` |
| **Before releasing features** | All Tests | `python run_tests_convenience.py main` |
| **System health monitoring** | Synthetic | `python run_tests_convenience.py phase3 health` |
| **Performance validation** | Benchmarks | `python run_tests_convenience.py phase3 performance` |

---

## **🚨 Test Best Practices (Following Multi-Agent Architecture)**

### **✅ Domain-Specific Testing**
```python
# ✅ GOOD - Test tools within their domain
def test_strength_exercise_database():
    from athlea_langgraph.tools.strength import exercise_database
    # Test strength domain tools
    
def test_nutrition_calorie_calculator():
    from athlea_langgraph.tools.nutrition import calorie_calculator
    # Test nutrition domain tools
```

### **✅ Contract Validation Testing**
```python
# ✅ GOOD - Test tool contracts are enforced
def test_tool_schema_validation():
    from athlea_langgraph.tools.contracts import validate_tool_input
    # Test that invalid inputs are rejected
    
def test_agent_tool_access_control():
    # Test that agents can only access allowed tools
```

### **✅ Phase-Aligned Testing**
```python
# Phase 1: Modular agent testing
def test_individual_agent_execution():
    # Test single agent in isolation
    
# Phase 2: Tool organization testing  
def test_domain_tool_organization():
    # Test tools are properly organized by domain
    
# Phase 3: Advanced testing patterns
def test_synthetic_coaching_session():
    # Test automated coaching session validation
```

---

## **📊 Coverage Analysis**

After running tests with coverage:
```bash
python run_tests_convenience.py phase3 coverage
# or
poetry run pytest tests/unit/ tests/tools/ tests/integration/ --cov=athlea_langgraph --cov-report=html
```

**View detailed report:**
```bash
open htmlcov/index.html  # macOS
```

**Coverage targets:**
- **Unit Tests:** 90%+ line coverage
- **Tool Tests:** 95%+ contract compliance
- **Integration Tests:** 80%+ workflow coverage  
- **Critical Paths:** 100% coverage

---

## **🔧 Debugging Failed Tests**

```bash
# Show full traceback
poetry run pytest tests/unit/test_failing.py -v --tb=long

# Stop on first failure
poetry run pytest tests/ -x

# Run only failed tests from last run
poetry run pytest tests/ --lf

# Drop into debugger on failure
poetry run pytest tests/ --pdb

# Run with print statements visible
poetry run pytest tests/ -s

# Debug specific test runner
python tests/runners/run_tool_organization_tests.py
```

---

## **🚀 CI/CD Integration**

For automated testing in GitHub Actions:
```yaml
# .github/workflows/test.yml
- name: Install Dependencies
  run: poetry install

- name: Run Fast Tests
  run: python run_tests_convenience.py phase3 fast

- name: Run Integration Tests  
  run: python run_tests_convenience.py phase3 integration

- name: Run Coverage Analysis
  run: python run_tests_convenience.py phase3 coverage

- name: Run Health Checks
  run: python run_tests_convenience.py phase3 health
```

---

## **📝 Migration from Old Structure**

**Files moved:**
- `run_*_tests.py` → `tests/runners/`
- `test_onboarding_*.py` → `tests/e2e/`
- Tool tests → `tests/tools/`
- Integration tests → `tests/integration/`
- Unit tests → `tests/unit/`

**New convenience runner:**
- `run_tests_convenience.py` provides easy access to all test runners

This follows the **Phase-Aligned Testing Architecture** from the implementation:
1. **Phase 1** → Modular agents testing
2. **Phase 2** → Tool organization & contract testing  
3. **Phase 3** → Advanced testing patterns & health monitoring