import os
import async<PERSON>
from langsmith import Client
from athlea_langgraph.utils.prompt_loader import get_prompt_loader


async def sync_prompts():
    """
    Synchronize local prompts with LangSmith.
    Deletes all existing prompts and uploads the local versions.
    """
    print("🚀 Starting prompt synchronization with LangSmith...")

    try:
        client = Client()
        project_name = os.environ.get("LANGCHAIN_PROJECT", "default")
        print(f"🎯 Targeting LangSmith project: {project_name}")

        # 1. Delete all existing prompts
        print("\n🗑️ Deleting existing prompts from LangSmith...")
        prompts = client.list_prompts()
        for prompt in prompts:
            client.delete_prompt(prompt.name)
            print(f"   - Deleted: {prompt.name}")
        print("✅ All existing prompts deleted.")

        # 2. Upload local prompts
        print("\n⬆️ Uploading local prompts to LangSmith...")
        prompt_loader = await get_prompt_loader("athlea_langgraph/prompts")

        # The prompt_loader should have a way to list all discovered prompts
        if hasattr(prompt_loader, "prompts") and prompt_loader.prompts:
            for prompt_name, prompt_data in prompt_loader.prompts.items():
                try:
                    client.upload_prompt(
                        prompt_name=prompt_name,
                        input_keys=list(prompt_data.prompt.input_variables),
                        template=prompt_data.prompt.template,
                        description=prompt_data.metadata.get("description", ""),
                    )
                    print(f"   - Uploaded: {prompt_name}")
                except Exception as e:
                    print(f"   - ⚠️ Failed to upload {prompt_name}: {e}")
            print("✅ All local prompts uploaded successfully.")
        else:
            print("   - ⚠️ No local prompts found to upload.")

    except Exception as e:
        print(f"\n❌ An error occurred during prompt synchronization: {e}")
        print(
            "   Please ensure your LANGCHAIN_API_KEY and LANGCHAIN_PROJECT environment variables are set."
        )


if __name__ == "__main__":
    asyncio.run(sync_prompts())
