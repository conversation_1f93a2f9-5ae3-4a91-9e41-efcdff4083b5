#!/usr/bin/env python3
"""
Phase 2 Advanced Memory System - Integration Test

End-to-end integration test that validates the Phase 2 memory system
works correctly with real coaching workflows and user interactions.

This test simulates a realistic coaching scenario with:
- Multiple coaching sessions across different domains
- Memory storage, classification, and retrieval
- Summarization of old conversations
- Analytics tracking and reporting
- Memory decay and cleanup operations
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Phase 2 components
from athlea_langgraph.memory import (
    AdvancedMemoryManager,
    CoachingDomain,
    DateRange,
    MemoryOperationType,
    MemoryType,
)


class Phase2MemoryIntegrationTest:
    """Integration test for Phase 2 advanced memory system."""

    def __init__(self):
        self.manager = None
        self.test_user_id = f"integration_test_user_{uuid.uuid4().hex[:8]}"
        self.stored_memory_ids = []

    async def setup(self):
        """Set up the test environment."""
        print("🔧 Setting up Phase 2 Memory Integration Test")

        # Create advanced memory manager
        self.manager = AdvancedMemoryManager(
            environment="test",
            enable_analytics=True,
            enable_auto_maintenance=False,  # Manual control for testing
        )

        print(f"✅ Created advanced memory manager for user: {self.test_user_id}")

    async def teardown(self):
        """Clean up test resources."""
        if self.manager:
            await self.manager.shutdown()
        print("✅ Test cleanup completed")

    async def simulate_coaching_sessions(self):
        """Simulate multiple coaching sessions across different domains."""
        print("\n👥 Simulating Realistic Coaching Sessions")

        # Session 1: Initial consultation (general domain)
        session1_memories = [
            {
                "content": "User is 28 years old, beginner level, wants to lose 15 pounds and build strength. "
                "Has no previous injuries but is concerned about proper form. "
                "Available for workouts 4 days per week, prefers morning sessions.",
                "type": MemoryType.GOAL,
                "domain": CoachingDomain.GENERAL,
            },
            {
                "content": "User prefers compound movements and is interested in both cardio and strength training. "
                "Dislikes running but enjoys cycling. Has access to a full gym.",
                "type": MemoryType.PREFERENCE,
                "domain": CoachingDomain.GENERAL,
            },
        ]

        # Session 2: Strength training focus
        session2_memories = [
            {
                "content": "Completed initial strength assessment: bodyweight squats (15 reps), "
                "push-ups (8 reps), plank (45 seconds). Starting with basic movements.",
                "type": MemoryType.WORKOUT_LOG,
                "domain": CoachingDomain.STRENGTH,
            },
            {
                "content": "Taught proper deadlift form with bodyweight, user picked it up quickly. "
                "Will progress to barbell next session. Focus on hip hinge movement.",
                "type": MemoryType.CONVERSATION,
                "domain": CoachingDomain.STRENGTH,
            },
        ]

        # Session 3: Nutrition planning
        session3_memories = [
            {
                "content": "User currently eats 1800-2000 calories per day, mostly processed foods. "
                "Drinks 2-3 cups of coffee, minimal water intake. Skips breakfast often.",
                "type": MemoryType.CONVERSATION,
                "domain": CoachingDomain.NUTRITION,
            },
            {
                "content": "Set nutrition goal: increase protein to 130g daily, drink 8 glasses of water, "
                "include vegetables in every meal. User will track for one week.",
                "type": MemoryType.GOAL,
                "domain": CoachingDomain.NUTRITION,
            },
        ]

        # Session 4: Progress check and cardio
        session4_memories = [
            {
                "content": "Week 2 progress: Lost 2 pounds, completed all strength workouts. "
                "Deadlift progressed to 95 lbs, squats improved to 20 reps. "
                "User feeling more confident with form.",
                "type": MemoryType.PROGRESS,
                "domain": CoachingDomain.STRENGTH,
            },
            {
                "content": "Added 20-minute cycling sessions twice per week. User enjoys the bike "
                "and prefers it over treadmill. Heart rate target: 140-150 bpm.",
                "type": MemoryType.WORKOUT_LOG,
                "domain": CoachingDomain.CARDIO,
            },
        ]

        # Store all memories
        all_sessions = [
            ("Initial Consultation", session1_memories),
            ("Strength Focus", session2_memories),
            ("Nutrition Planning", session3_memories),
            ("Progress & Cardio", session4_memories),
        ]

        for session_name, memories in all_sessions:
            print(f"\n  📝 Storing {session_name} memories...")

            for memory in memories:
                memory_id = await self.manager.store_memory(
                    user_id=self.test_user_id,
                    content=memory["content"],
                    memory_type=memory["type"],
                    domain=memory["domain"],
                )

                self.stored_memory_ids.append(memory_id)
                print(
                    f"    ✅ Stored {memory['type'].value} in {memory['domain'].value}"
                )

        print(
            f"\n✅ Stored {len(self.stored_memory_ids)} memories across {len(all_sessions)} sessions"
        )

    async def test_domain_specific_retrieval(self):
        """Test domain-specific memory retrieval."""
        print("\n🔍 Testing Domain-Specific Retrieval")

        # Test strength domain search
        strength_memories = await self.manager.search_memories(
            user_id=self.test_user_id,
            query="deadlift form progression",
            domains=[CoachingDomain.STRENGTH],
            session_type="coaching",
            limit=5,
        )

        print(f"  💪 Strength domain search: found {len(strength_memories)} memories")

        # Verify strength-related content
        for memory in strength_memories:
            content = memory.get("content", "").lower()
            assert any(
                term in content for term in ["strength", "deadlift", "squat", "workout"]
            ), "Should contain strength-related terms"

        # Test nutrition domain search
        nutrition_memories = await self.manager.search_memories(
            user_id=self.test_user_id,
            query="protein calories nutrition goals",
            domains=[CoachingDomain.NUTRITION],
            session_type="planning",
            limit=5,
        )

        print(f"  🥗 Nutrition domain search: found {len(nutrition_memories)} memories")

        # Test cross-domain search
        general_memories = await self.manager.search_memories(
            user_id=self.test_user_id,
            query="goals progress user preferences",
            # No domain specified - should search all
            session_type="check_in",
            limit=10,
        )

        print(f"  🎯 Cross-domain search: found {len(general_memories)} memories")

        assert len(strength_memories) > 0, "Should find strength memories"
        assert len(nutrition_memories) > 0, "Should find nutrition memories"
        assert len(general_memories) > 0, "Should find general memories"

    async def test_contextual_retrieval(self):
        """Test context-aware retrieval for different session types."""
        print("\n🎪 Testing Contextual Retrieval")

        session_types = [
            ("coaching", "workout plan deadlift"),
            ("check_in", "progress goals achievement"),
            ("planning", "future goals nutrition"),
        ]

        for session_type, query in session_types:
            memories = await self.manager.search_memories(
                user_id=self.test_user_id,
                query=query,
                session_type=session_type,
                use_advanced_ranking=True,
                limit=5,
            )

            print(
                f"  📋 {session_type} session: found {len(memories)} contextual memories"
            )

            # Verify ranking factors are present
            for memory in memories[:2]:  # Check top 2
                assert "ranking_factors" in memory, "Should have ranking factors"
                factors = memory["ranking_factors"]
                assert "semantic" in factors, "Should have semantic score"

    async def test_conversation_summarization(self):
        """Test automatic conversation summarization."""
        print("\n📝 Testing Conversation Summarization")

        # Create some older conversation memories for summarization
        old_conversations = []
        for i in range(3):
            memory_id = await self.manager.store_memory(
                user_id=self.test_user_id,
                content=f"Detailed conversation {i} about workout modifications, "
                f"discussing form improvements, weight progressions, and recovery strategies. "
                f"User asked about sleep importance and protein timing after workouts.",
                memory_type=MemoryType.CONVERSATION,
            )
            old_conversations.append(memory_id)

        # Test summarization
        summary_result = await self.manager.summarize_user_history(
            user_id=self.test_user_id,
            time_threshold=1,  # Very short for testing
        )

        if summary_result.get("summary"):
            print(
                f"  ✅ Generated summary: {len(summary_result['summary'])} characters"
            )
            print(f"  🔑 Key insights: {len(summary_result.get('key_insights', []))}")

            metadata = summary_result.get("metadata", {})
            compression = metadata.get("compression_ratio", 0)
            print(f"  📊 Compression ratio: {compression:.2f}")
        else:
            print(
                "  ⚠️  No conversations found for summarization (expected for recent memories)"
            )

    async def test_memory_lifecycle_management(self):
        """Test memory decay and lifecycle management."""
        print("\n🔄 Testing Memory Lifecycle Management")

        # Run memory maintenance
        maintenance_result = await self.manager.run_memory_maintenance(
            user_id=self.test_user_id
        )

        print(f"  🧹 Maintenance completed:")
        print(f"    - Archived: {len(maintenance_result.get('archived_memories', []))}")
        print(f"    - Deleted: {maintenance_result.get('deleted_count', 0)}")
        print(
            f"    - Processing time: {maintenance_result.get('processing_time_ms', 0):.1f}ms"
        )

        # Get recommendations
        recommendations = await self.manager.get_optimization_recommendations(
            user_id=self.test_user_id
        )

        print(f"  💡 Generated {len(recommendations)} optimization recommendations")
        for i, rec in enumerate(recommendations[:3]):  # Show first 3
            print(f"    {i+1}. {rec}")

    async def test_analytics_and_monitoring(self):
        """Test analytics and monitoring features."""
        print("\n📊 Testing Analytics and Monitoring")

        # Generate analytics report
        report = await self.manager.get_analytics_report(
            days_back=1, user_id=self.test_user_id
        )

        if "error" not in report:
            print("  ✅ Analytics report generated successfully")

            # Check report structure
            retrieval = report.get("retrieval_metrics", {})
            performance = report.get("performance_metrics", {})

            print(f"    - Total queries: {retrieval.get('total_queries', 0)}")
            print(
                f"    - Average latency: {retrieval.get('average_latency_ms', 0):.1f}ms"
            )
            print(f"    - Error rate: {performance.get('error_rate', 0):.1%}")
        else:
            print(f"  ⚠️  Analytics limited: {report['error']}")

        # Check system health
        health = await self.manager.get_system_health()

        if "error" not in health:
            status = health.get("status_level", "unknown")
            alerts = len(health.get("active_alerts", []))
            print(f"  🏥 System health: {status} ({alerts} alerts)")
        else:
            print(f"  ⚠️  Health monitoring limited: {health['error']}")

        # Get user statistics
        stats = await self.manager.get_user_memory_stats(self.test_user_id)

        if "error" not in stats:
            basic_stats = stats.get("basic_stats", {})
            domain_dist = stats.get("domain_distribution", {})

            total_memories = basic_stats.get("total_memories", 0)
            most_active_domain = domain_dist.get("most_active_domain")

            print(f"  📈 User stats: {total_memories} total memories")
            if most_active_domain:
                domain_name, count = most_active_domain
                print(f"    Most active domain: {domain_name} ({count} memories)")
        else:
            print(f"  ⚠️  User stats limited: {stats['error']}")

    async def run_integration_test(self):
        """Run the complete integration test."""
        print("🚀 Phase 2 Advanced Memory System - Integration Test")
        print("=" * 60)
        print("Simulating end-to-end coaching workflow with advanced memory features")
        print()

        try:
            # Setup
            await self.setup()

            # Run integration test steps
            test_steps = [
                ("Simulate Coaching Sessions", self.simulate_coaching_sessions),
                ("Domain-Specific Retrieval", self.test_domain_specific_retrieval),
                ("Contextual Retrieval", self.test_contextual_retrieval),
                ("Conversation Summarization", self.test_conversation_summarization),
                ("Memory Lifecycle Management", self.test_memory_lifecycle_management),
                ("Analytics and Monitoring", self.test_analytics_and_monitoring),
            ]

            results = []
            for step_name, step_func in test_steps:
                try:
                    print(f"\n{'='*20} {step_name} {'='*20}")
                    await step_func()
                    results.append((step_name, True, None))
                    print(f"✅ {step_name} completed successfully")
                except Exception as e:
                    results.append((step_name, False, str(e)))
                    print(f"❌ {step_name} failed: {e}")
                    logger.error(
                        f"Integration test step {step_name} failed: {e}", exc_info=True
                    )

            # Summary
            print(f"\n{'='*60}")
            print("📊 PHASE 2 INTEGRATION TEST RESULTS")
            print(f"{'='*60}")

            passed = sum(1 for _, success, _ in results if success)
            total = len(results)

            for step_name, success, error in results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"{status} {step_name}")
                if error:
                    print(f"    Error: {error}")

            print(
                f"\nSummary: {passed}/{total} integration steps passed ({passed/total*100:.1f}%)"
            )

            if passed == total:
                print("\n🎉 PHASE 2 INTEGRATION TEST - COMPLETE SUCCESS!")
                print("\n🚀 End-to-End Workflow Validated:")
                print("  ✅ Multi-session coaching workflow")
                print("  ✅ Domain-aware memory organization")
                print("  ✅ Advanced search and retrieval")
                print("  ✅ Contextual memory ranking")
                print("  ✅ Memory summarization and compression")
                print("  ✅ Intelligent lifecycle management")
                print("  ✅ Real-time analytics and monitoring")

                print("\n🏆 Phase 2 Advanced Memory System Ready for Production!")
                return True
            else:
                print("\n⚠️  Some integration steps failed.")
                print(
                    "The Phase 2 system may need additional configuration or debugging."
                )
                return False

        except Exception as e:
            print(f"\n❌ Integration test failed with error: {e}")
            logger.error(f"Integration test failed: {e}", exc_info=True)
            return False

        finally:
            # Cleanup
            await self.teardown()


async def test_phase2_memory_integration():
    """Pytest-compatible integration test function."""
    test = Phase2MemoryIntegrationTest()
    success = await test.run_integration_test()
    assert success, "Phase 2 memory system integration test failed"


if __name__ == "__main__":

    async def main():
        """Run the integration test as a standalone script."""
        print("🎯 Phase 2 Advanced Memory System - Integration Test")
        print("Testing realistic coaching workflow with advanced memory features")
        print()

        # Environment check
        required_vars = ["OPENAI_API_KEY"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            print(f"❌ Missing required environment variables: {missing_vars}")
            print("Please ensure all required variables are set.")
            return False

        print("🔧 Environment: All required variables present")
        print()

        # Run integration test
        test = Phase2MemoryIntegrationTest()
        return await test.run_integration_test()

    # Run the test
    result = asyncio.run(main())
    exit(0 if result else 1)
