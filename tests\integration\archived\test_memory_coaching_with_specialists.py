"""
Focused Test: Memory Coaching with Specialized Coaches

This script demonstrates how the example_memory_coaching.py integrates
with the specialized coaches to provide domain-specific expertise
while maintaining memory and personalization.

Run this to see how the memory-enhanced coaching system routes
queries to appropriate specialists and maintains context.
"""

import asyncio
import logging
import os
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_memory_coaching_with_specialists():
    """
    Test the memory coaching example with specialized coach integration.
    """
    # Get MongoDB URI from environment
    mongodb_uri = os.getenv("MONGODB_URI")

    if not mongodb_uri:
        print("❌ Error: MONGODB_URI environment variable not set")
        print("Please set your MongoDB connection string:")
        print("export MONGODB_URI='your_mongodb_connection_string'")
        return

    print("🏃‍♂️ Memory Coaching with Specialized Coaches Demo")
    print("=" * 60)

    try:
        # Import the memory coaching demo
        from athlea_langgraph.example_memory_coaching import MemoryCoachingDemo

        # Create demo instance
        demo = MemoryCoachingDemo(mongodb_uri)

        # Test 1: Run the original memory features demo
        print("\n1️⃣ Running Original Memory Features Demo")
        print("-" * 40)
        await demo.demonstrate_memory_features()

        # Test 2: Enhanced demo with specialist-specific queries
        print("\n2️⃣ Enhanced Demo: Specialist-Specific Queries")
        print("-" * 40)
        await demonstrate_specialist_routing(demo)

        # Test 3: Cross-session specialist memory
        print("\n3️⃣ Cross-Session Specialist Memory")
        print("-" * 40)
        await demonstrate_cross_session_specialists(demo)

        print("\n✅ Memory coaching with specialists demo completed!")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")


async def demonstrate_specialist_routing(demo):
    """
    Demonstrate how queries get routed to appropriate specialists.
    """
    user_id = "specialist_routing_demo"
    thread_id = f"routing_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # Setup user profile
    coaching_graph = await demo.get_coaching_graph(user_id)

    specialist_profile = {
        "name": "Alex Chen",
        "age": 29,
        "fitness_level": "intermediate",
        "goals": ["strength training", "marathon training", "better nutrition"],
        "preferences": {
            "workout_time": "morning",
            "workout_duration": "60-90 minutes",
            "preferred_activities": ["weight training", "running", "cycling"],
        },
        "restrictions": {
            "injuries": ["previous ankle sprain"],
            "dietary": ["lactose intolerant"],
            "time_constraints": ["travel frequently for work"],
        },
        "created_at": datetime.now().isoformat(),
    }

    await coaching_graph.update_user_profile(specialist_profile)
    print(f"✓ User profile created for {user_id}")

    # Specialist-specific queries
    specialist_queries = [
        {
            "query": "I want to deadlift safely but I'm worried about my form. Can you help me with proper technique?",
            "expected_specialist": "strength_coach",
            "description": "Strength training technique question",
        },
        {
            "query": "I'm training for my first marathon. How should I structure my weekly running schedule?",
            "expected_specialist": "running_coach",
            "description": "Marathon training plan question",
        },
        {
            "query": "I want to add cycling to my training. What type of bike should I get for road cycling?",
            "expected_specialist": "cycling_coach",
            "description": "Cycling equipment and training question",
        },
        {
            "query": "I'm lactose intolerant and need protein sources for muscle building. What foods should I focus on?",
            "expected_specialist": "nutrition_coach",
            "description": "Nutrition question with dietary restriction",
        },
        {
            "query": "I travel a lot for work and struggle to maintain consistent training. How can I stay motivated?",
            "expected_specialist": "mental_coach",
            "description": "Mental coaching for consistency",
        },
        {
            "query": "I've been training hard and feel fatigued. What recovery strategies should I implement?",
            "expected_specialist": "recovery_coach",
            "description": "Recovery and fatigue management",
        },
    ]

    for i, query_info in enumerate(specialist_queries, 1):
        print(f"\n📝 Query {i}: {query_info['description']}")
        print(f"User: {query_info['query']}")
        print("-" * 50)

        try:
            # Run coaching session
            result = await coaching_graph.run_coaching_session(
                user_message=query_info["query"], thread_id=thread_id
            )

            # Display results
            print(f"🤖 Coach Response:")
            if result.get("aggregated_response"):
                response = result["aggregated_response"]
                print(f"{response[:250]}...")
            elif result.get("clarification_output"):
                response = result["clarification_output"]
                print(f"{response[:250]}...")
            else:
                print("Processing your request...")

            # Check which specialists were involved
            specialist_responses = []
            for specialist in [
                "strength",
                "running",
                "cardio",
                "cycling",
                "nutrition",
                "recovery",
                "mental",
            ]:
                if result.get(f"{specialist}_response"):
                    specialist_responses.append(specialist)

            if specialist_responses:
                print(f"👥 Specialists consulted: {', '.join(specialist_responses)}")

                # Check if expected specialist was involved
                expected = query_info["expected_specialist"].replace("_coach", "")
                if expected in specialist_responses:
                    print(f"✅ Expected specialist ({expected}) was consulted")
                else:
                    print(f"⚠️ Expected specialist ({expected}) was not consulted")
                    print(f"   Instead consulted: {', '.join(specialist_responses)}")
            else:
                print("ℹ️ No specialist responses detected")

            # Show memory context
            if result.get("memory_context"):
                memory_info = result["memory_context"]
                print(
                    f"🧠 Memory: {memory_info.get('memory_count', 0)} relevant memories"
                )

        except Exception as e:
            logger.error(f"Error in query {i}: {e}")
            print(f"❌ Error: {e}")

        print()


async def demonstrate_cross_session_specialists(demo):
    """
    Demonstrate how specialists maintain context across sessions.
    """
    user_id = "cross_session_specialist_demo"

    # Setup user
    coaching_graph = await demo.get_coaching_graph(user_id)

    cross_session_profile = {
        "name": "Jordan Smith",
        "age": 26,
        "fitness_level": "beginner",
        "goals": ["lose weight", "build strength", "improve health"],
        "preferences": {
            "workout_time": "evening",
            "workout_duration": "45 minutes",
            "preferred_activities": ["weight training", "walking"],
        },
        "restrictions": {
            "injuries": ["knee sensitivity"],
            "dietary": ["vegetarian"],
            "time_constraints": ["busy work schedule"],
        },
        "created_at": datetime.now().isoformat(),
    }

    await coaching_graph.update_user_profile(cross_session_profile)
    print(f"✓ User profile created for {user_id}")

    # Session 1: Initial strength consultation
    print("\n📅 Session 1: Initial Strength Training Consultation")
    session1_thread = f"session_1_{datetime.now().strftime('%H%M%S')}"

    result1 = await coaching_graph.run_coaching_session(
        user_message="I'm a complete beginner to strength training and have sensitive knees. Can you help me start safely?",
        thread_id=session1_thread,
    )

    print(
        "User: I'm a complete beginner to strength training and have sensitive knees. Can you help me start safely?"
    )
    if result1.get("aggregated_response"):
        print(f"Coach: {result1['aggregated_response'][:200]}...")
    else:
        print("Coach: Processing your request...")

    # Show specialists involved
    specialists_s1 = [
        s
        for s in [
            "strength",
            "running",
            "cardio",
            "cycling",
            "nutrition",
            "recovery",
            "mental",
        ]
        if result1.get(f"{s}_response")
    ]
    if specialists_s1:
        print(f"👥 Session 1 specialists: {', '.join(specialists_s1)}")

    # Small delay
    await asyncio.sleep(2)

    # Session 2: Nutrition follow-up
    print("\n📅 Session 2: Nutrition Follow-up")
    session2_thread = f"session_2_{datetime.now().strftime('%H%M%S')}"

    result2 = await coaching_graph.run_coaching_session(
        user_message="Based on the strength training plan we discussed, what should I eat as a vegetarian to support my goals?",
        thread_id=session2_thread,
    )

    print(
        "User: Based on the strength training plan we discussed, what should I eat as a vegetarian to support my goals?"
    )
    if result2.get("aggregated_response"):
        print(f"Coach: {result2['aggregated_response'][:200]}...")
    else:
        print("Coach: Processing your request...")

    # Show specialists involved
    specialists_s2 = [
        s
        for s in [
            "strength",
            "running",
            "cardio",
            "cycling",
            "nutrition",
            "recovery",
            "mental",
        ]
        if result2.get(f"{s}_response")
    ]
    if specialists_s2:
        print(f"👥 Session 2 specialists: {', '.join(specialists_s2)}")

    # Small delay
    await asyncio.sleep(2)

    # Session 3: Progress check and adjustment
    print("\n📅 Session 3: Progress Check and Adjustment")
    session3_thread = f"session_3_{datetime.now().strftime('%H%M%S')}"

    result3 = await coaching_graph.run_coaching_session(
        user_message="I've been following the beginner strength plan for 2 weeks. My knees feel good, but I want to progress. What's next?",
        thread_id=session3_thread,
    )

    print(
        "User: I've been following the beginner strength plan for 2 weeks. My knees feel good, but I want to progress. What's next?"
    )
    if result3.get("aggregated_response"):
        print(f"Coach: {result3['aggregated_response'][:200]}...")
    else:
        print("Coach: Processing your request...")

    # Show specialists involved
    specialists_s3 = [
        s
        for s in [
            "strength",
            "running",
            "cardio",
            "cycling",
            "nutrition",
            "recovery",
            "mental",
        ]
        if result3.get(f"{s}_response")
    ]
    if specialists_s3:
        print(f"👥 Session 3 specialists: {', '.join(specialists_s3)}")

    print("\n🧠 Cross-Session Analysis:")
    print("- Session 2 should reference the strength plan from Session 1")
    print("- Session 3 should reference both previous sessions and show progression")
    print("- Each session maintains context about knee sensitivity and vegetarian diet")
    print("- Different specialists contribute while maintaining continuity")
    print(
        f"- Specialist involvement: S1({', '.join(specialists_s1)}), S2({', '.join(specialists_s2)}), S3({', '.join(specialists_s3)})"
    )


async def quick_specialist_test():
    """
    Quick test to verify specialist coaches are working.
    """
    print("🔧 Quick Specialist Test")
    print("=" * 30)

    try:
        # Test individual coach imports
        from athlea_langgraph.agents.specialized_coaches import (
            get_tools_manager,
            nutrition_coach_node,
            running_coach_node,
            strength_coach_node,
        )

        print("✅ Specialist coaches imported successfully")

        # Test tools manager
        tools_manager = await get_tools_manager()
        print("✅ Tools manager initialized")

        # Test tool availability
        strength_tools = tools_manager.get_strength_coach_tools()
        print(f"✅ Strength coach tools: {len(strength_tools)} available")

        nutrition_tools = tools_manager.get_nutrition_coach_tools()
        print(f"✅ Nutrition coach tools: {len(nutrition_tools)} available")

        print("✅ Quick test passed - specialists are ready!")

    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        logger.error(f"Quick test error: {e}")


if __name__ == "__main__":
    # Run quick test first
    print("Running quick specialist test...")
    asyncio.run(quick_specialist_test())

    print("\n" + "=" * 60)

    # Run full demo
    asyncio.run(test_memory_coaching_with_specialists())
