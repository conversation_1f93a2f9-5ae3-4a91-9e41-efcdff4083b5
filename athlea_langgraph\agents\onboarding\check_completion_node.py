import logging
from typing import Any, Dict

from langchain_core.messages import BaseMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import OnboardingState
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class CheckCompletionNode:
    """Node for checking if enough information has been gathered"""

    def __init__(self):
        # Low temperature for deterministic check
        self.llm = create_azure_chat_openai(temperature=0.0)
        self.completion_check_prompt = None  # Will be loaded lazily

        # Fallback prompt in case loading fails
        self.fallback_prompt = """Analyze the provided conversation history, focusing ONLY on information explicitly stated by the USER.

**Your Task:** Verify if the USER has provided information covering ALL of the following categories:
1.  **Specific Fitness Goal(s):** At least one clear goal mentioned for their primary sport(s).
2.  **Experience Level:** Some statement about their experience (overall or per sport).
3.  **Time Commitment:** Details on days/week, duration, or time of day.
4.  **Equipment Access:** Mention of available equipment or workout location relevant to their goals.
5.  **Priorities/Connections/Seasonality:** Statement on how goals relate, primary focus, or relevant time constraints.

**Additional Check:** Examine the VERY LAST user message. Does it clearly indicate readiness to proceed or explicitly ask for the plan (e.g., "Okay", "Yes", "Let's do it", "Generate the plan", "Sounds good", confirming the last piece of info)?

**Response Rules:**
- Respond ONLY with the word "true" IF AND ONLY IF:
    - There is clear evidence from the USER for **ALL 5 required categories** listed above.
    - AND the **last user message** indicates readiness.
- Respond ONLY with the word "false" otherwise (if any category is missing OR the user isn't ready).

Do not provide any explanation or other text."""

    async def _get_completion_check_prompt(self) -> str:
        """Load the completion check prompt lazily"""
        if self.completion_check_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/completion_check"
                )
                self.completion_check_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding completion check prompt from file"
                )
            except Exception as e:
                logger.error(f"Failed to load onboarding completion check prompt: {e}")
                self.completion_check_prompt = self.fallback_prompt

        return self.completion_check_prompt

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Check if onboarding information gathering is complete"""
        logger.info("[Node: checkCompletionNode] Entering node.")

        messages = state.get("messages", [])

        if not messages:
            logger.info("[Node: checkCompletionNode] No messages, cannot be complete.")
            return {"has_enough_info": False}

        # Get the completion check prompt (loads lazily if needed)
        completion_prompt = await self._get_completion_check_prompt()

        # Prepare history for the check LLM
        history_for_check = [
            SystemMessage(content=completion_prompt),
            *[
                msg for msg in messages if not isinstance(msg, SystemMessage)
            ],  # Exclude other system prompts
        ]

        logger.info(
            f"[Node: checkCompletionNode] Calling LLM for completion check with history length: {len(history_for_check) - 1}"
        )

        try:
            response = await self.llm.ainvoke(history_for_check)
            content = (
                response.content.strip().lower()
                if isinstance(response.content, str)
                else ""
            )
            logger.info(f"[Node: checkCompletionNode] Raw LLM Response: {content}")

            is_complete = content == "true"
            logger.info(
                f"[Node: checkCompletionNode] Determined completion: {is_complete}"
            )

            return {"has_enough_info": is_complete}

        except Exception as error:
            logger.error(f"[Node: checkCompletionNode] Error calling LLM: {error}")
            return {"has_enough_info": False}  # Default to false on error


# Create the node instance
check_completion_node = CheckCompletionNode()
