"""
Azure Search Retriever Tool Schemas

Pydantic models for validating inputs and outputs of the Azure Search Retriever tool.
"""

from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class SearchResultItem(BaseModel):
    """Represents a single search result item."""

    content: str = Field(..., description="Main content of the search result.")
    title: Optional[str] = Field(
        None, description="Title of the search result document."
    )
    source: Optional[str] = Field(
        None, description="Original source of the content (e.g., filename, URL base)."
    )
    url: Optional[str] = Field(
        None, description="Direct URL to the source document, if available."
    )
    score: Optional[float] = Field(
        None,
        description="Relevance score of the search result (e.g., BM25 or vector similarity score).",
    )
    highlights: Optional[Dict[str, List[str]]] = Field(
        None, description="Highlighted snippets from the content matching the query."
    )
    chunk_id: Optional[str] = Field(
        None, description="Identifier for the specific chunk if content is chunked."
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Other metadata associated with the result."
    )


class AzureSearchRetrieverInput(BaseModel):
    """Input model for the Azure Search Retriever tool."""

    query: str = Field(..., description="The search query string.")
    index_name: Optional[str] = Field(
        None,
        description="Optional Azure Cognitive Search index name. Uses default if not provided.",
    )
    top_k: int = Field(
        default=5,
        ge=1,
        le=100,
        description="The maximum number of search results to return.",
    )
    vector_search: bool = Field(
        default=False,
        description="Whether to perform a vector similarity search. Requires embedding model configuration.",
    )
    hybrid_search: bool = Field(
        default=True,
        description="If vector_search is True, setting this to True performs a hybrid (vector + keyword) search. False for pure vector search.",
    )
    # Future considerations: filters, faceting, semantic search options
    filter_expression: Optional[str] = Field(
        None, description="OData filter expression to apply to the search query."
    )
    select_fields: Optional[List[str]] = Field(
        None, description="Specific fields to retrieve from the search index."
    )
    query_type: Literal["simple", "full"] = Field(
        "simple", description="Query language to use (simple or full Lucene)."
    )
    highlight_fields: Optional[List[str]] = Field(
        default=["content"], description="Fields to generate highlights for."
    )
    semantic_configuration_name: Optional[str] = Field(
        None, description="Name of the semantic configuration for semantic search."
    )


class AzureSearchRetrieverOutput(BaseModel):
    """Output model for the Azure Search Retriever tool."""

    success: bool = Field(
        ..., description="Indicates if the search operation was successful."
    )
    query: str = Field(..., description="The original search query used.")
    results: List[SearchResultItem] = Field(
        default_factory=list, description="List of search result items."
    )
    result_count: int = Field(..., description="Number of results returned.")
    error_type: Optional[str] = Field(
        None,
        description="Type of error if success is false (e.g., 'validation_error', 'search_api_error', 'embedding_error').",
    )
    message: str = Field(
        ...,
        description="A descriptive message about the outcome (success or error details).",
    )
    request_id: str = Field(
        ..., description="Unique identifier for this request/response cycle."
    )
    execution_time_ms: int = Field(
        ..., description="Total execution time in milliseconds."
    )
    fallback_response: Optional[Dict[str, Any]] = Field(
        None,
        description="Fallback data if available (e.g., from a cache or simpler search).",
    )
