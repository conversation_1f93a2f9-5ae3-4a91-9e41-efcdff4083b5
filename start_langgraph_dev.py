#!/usr/bin/env python3
"""
Start the LangGraph development server with proper configuration.

This script uses the LangGraph CLI to start the server, which should
avoid the multiprocessing and logging conflicts we've been seeing.
"""

import os
import sys
import subprocess
import warnings
from pathlib import Path

# Suppress warnings early
warnings.filterwarnings("ignore", message=".*PydanticDeprecatedSince20.*")
warnings.filterwarnings("ignore", message=".*The `schema` method is deprecated.*")
warnings.filterwarnings("ignore", category=UserWarning, module="pydantic.*")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="pydantic.*")


def check_environment():
    """Check if required environment variables are set."""
    required_vars = [
        "AZURE_OPENAI_API_KEY",
        "AZURE_OPENAI_ENDPOINT", 
        "AZURE_DEPLOYMENT_NAME",
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file or environment configuration.")
        return False
    
    return True


def start_langgraph_dev():
    """Start the LangGraph development server."""
    try:
        print("🚀 Starting LangGraph Development Server")
        print("=" * 50)
        
        # Check environment
        if not check_environment():
            sys.exit(1)
        
        # Check if langgraph.json exists
        if not Path("langgraph.json").exists():
            print("❌ langgraph.json not found. Make sure you're in the project root.")
            sys.exit(1)
        
        # Set default port
        port = os.getenv("PORT", "8123")
        host = os.getenv("HOST", "127.0.0.1")
        
        print(f"🌐 Server will start at: http://{host}:{port}")
        print("📝 LangGraph Studio will be available for graph visualization")
        print("⏰ Starting server... (this may take a moment)")
        print("")
        
        # Start the LangGraph development server
        cmd = [
            "langgraph", "dev",
            "--port", str(port),
            "--host", host
        ]
        
        print(f"🔧 Running command: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down LangGraph server...")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ LangGraph server failed to start: {e}")
        print("💡 Make sure you have langgraph installed: pip install langgraph")
        sys.exit(1)
    except FileNotFoundError:
        print("\n❌ LangGraph CLI not found.")
        print("💡 Make sure you have langgraph installed: pip install langgraph")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    start_langgraph_dev() 