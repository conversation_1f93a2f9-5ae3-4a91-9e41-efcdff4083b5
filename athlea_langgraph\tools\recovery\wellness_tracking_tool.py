"""
Wellness Tracking Tool

Provides daily wellness assessment, recovery scoring, and training readiness
indicators with personalized recommendations based on wellness metrics.
"""

import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from ..base_tool import BaseDomainTool
from ..schemas.recovery_schemas import WellnessAssessmentInput, WellnessTrackingOutput
from langchain_core.tools import BaseTool as LangChainBaseTool, tool
from pydantic import BaseModel, Field as PydanticField

logger = logging.getLogger(__name__)


@dataclass
class WellnessTrend:
    """Wellness trend data structure."""

    metric: str
    current_value: float
    seven_day_average: float
    fourteen_day_average: float
    trend_direction: str  # "improving", "declining", "stable"
    z_score: float  # How many standard deviations from personal baseline


class WellnessTrackingTool(BaseDomainTool):
    """
    Advanced wellness tracking with sport-specific protocols, HRV analysis,
    trend monitoring, and predictive recovery recommendations.
    """

    domain: str = "recovery"
    name: str = "wellness_tracking_tool"  # Ensure this matches the original tool name
    description: str = (
        "Advanced wellness tracking with sport-specific protocols, HRV integration, "
        "trend analysis, dynamic baselines, and predictive recovery modeling"
    )

    def __init__(self):
        super().__init__()

        # Sport-specific recovery protocols
        self.sport_specific_protocols = {
            "endurance_sports": {
                "sports": ["running", "cycling", "swimming", "triathlon", "rowing"],
                "key_metrics": ["hrv", "resting_hr", "sleep_quality", "leg_fatigue"],
                "recovery_emphasis": {
                    "aerobic_base": "Focus on easy pace recovery sessions",
                    "glycogen_replenishment": "Prioritize carbohydrate intake within 30 minutes",
                    "cardiac_drift": "Monitor heart rate drift during easy efforts",
                },
                "warning_thresholds": {
                    "hrv_decline": 15,  # % below baseline
                    "resting_hr_elevation": 10,  # bpm above baseline
                    "sleep_efficiency": 80,  # Below this percentage
                    "energy_level": 4,  # Below this rating
                },
                "recovery_interventions": [
                    "Reduce training intensity by 20-30% for 2-3 days",
                    "Increase easy aerobic volume proportion",
                    "Focus on sleep extension (additional 30-60 minutes)",
                    "Consider massage or compression therapy",
                    "Monitor hydration status more closely",
                    "Add electrolyte replacement strategies",
                ],
            },
            "strength_power_sports": {
                "sports": ["weightlifting", "powerlifting", "crossfit", "gymnastics"],
                "key_metrics": [
                    "muscle_soreness",
                    "motivation",
                    "power_output",
                    "joint_stiffness",
                ],
                "recovery_emphasis": {
                    "protein_synthesis": "Ensure 1.6-2.2g protein per kg body weight",
                    "cns_recovery": "Monitor central nervous system fatigue",
                    "joint_health": "Prioritize mobility and soft tissue work",
                },
                "warning_thresholds": {
                    "muscle_soreness": 7,
                    "motivation_level": 4,
                    "energy_level": 4,
                    "joint_stiffness": 6,
                },
                "recovery_interventions": [
                    "Reduce training volume by 25-40% while maintaining intensity",
                    "Increase inter-set rest periods",
                    "Add deload week if fatigue persists >5 days",
                    "Focus on mobility and soft tissue work",
                    "Consider contrast therapy (hot/cold exposure)",
                    "Prioritize protein timing around workouts",
                ],
            },
            "team_sports": {
                "sports": ["soccer", "basketball", "hockey", "rugby", "volleyball"],
                "key_metrics": [
                    "reaction_time",
                    "mood",
                    "sleep_quality",
                    "muscle_soreness",
                ],
                "recovery_emphasis": {
                    "multidirectional_fatigue": "Address multiple movement patterns",
                    "cognitive_load": "Account for decision-making fatigue",
                    "contact_stress": "Monitor impact and collision effects",
                },
                "warning_thresholds": {
                    "reaction_time_decline": 10,  # % slower than baseline
                    "mood_rating": 5,
                    "sleep_quality": 5,
                    "muscle_soreness": 6,
                },
                "recovery_interventions": [
                    "Adjust training to focus on skill vs. fitness",
                    "Reduce contact and high-intensity drills",
                    "Increase active recovery with light movement",
                    "Address any lingering minor injuries",
                    "Consider sports psychology support if mood affected",
                    "Use game simulation at reduced intensity",
                ],
            },
            "skill_sports": {
                "sports": ["tennis", "golf", "martial_arts", "climbing"],
                "key_metrics": [
                    "focus",
                    "fine_motor_control",
                    "confidence",
                    "technical_consistency",
                ],
                "recovery_emphasis": {
                    "neural_efficiency": "Maintain movement quality over quantity",
                    "precision_fatigue": "Monitor fine motor skill degradation",
                    "mental_focus": "Address cognitive and emotional state",
                },
                "warning_thresholds": {
                    "focus_rating": 5,
                    "confidence_level": 5,
                    "stress_level": 7,
                    "sleep_quality": 5,
                },
                "recovery_interventions": [
                    "Reduce technical session volume",
                    "Focus on fundamental movement patterns",
                    "Incorporate mental training and visualization",
                    "Use shorter, more frequent practice sessions",
                    "Address any performance anxiety",
                    "Consider meditation or mindfulness training",
                ],
            },
        }

        # HRV interpretation and recommendations
        self.hrv_analysis = {
            "interpretation_ranges": {
                "very_high": {"z_score": 2.0, "status": "exceptional_recovery"},
                "high": {"z_score": 1.0, "status": "good_recovery"},
                "normal": {"z_score": 0.0, "status": "baseline_recovery"},
                "low": {"z_score": -1.0, "status": "reduced_recovery"},
                "very_low": {"z_score": -2.0, "status": "poor_recovery"},
            },
            "recommendations_by_status": {
                "exceptional_recovery": [
                    "Excellent recovery state - consider progressive overload",
                    "This is an optimal time for challenging training",
                    "Maintain current recovery protocols",
                ],
                "good_recovery": [
                    "Good recovery - proceed with planned training",
                    "Consider moderate intensity increases if applicable",
                    "Monitor for any declining trends",
                ],
                "baseline_recovery": [
                    "Normal recovery state - maintain current approach",
                    "Continue with planned training load",
                    "Focus on consistency in recovery habits",
                ],
                "reduced_recovery": [
                    "Below-optimal recovery - reduce training intensity 15-25%",
                    "Prioritize sleep and stress management",
                    "Consider additional recovery modalities",
                ],
                "poor_recovery": [
                    "Significant recovery deficit - consider rest day",
                    "Reduce training intensity by 30-50%",
                    "Investigate potential stressors or illness",
                ],
            },
            "trend_analysis": {
                "declining_3_day": "Short-term fatigue - adjust next 1-2 sessions",
                "declining_7_day": "Accumulated fatigue - consider deload week",
                "declining_14_day": "Overreaching risk - implement recovery phase",
                "improving_trend": "Positive adaptation - maintain current approach",
            },
        }

        # Advanced recovery modality recommendations
        self.recovery_modalities = {
            "active_recovery": {
                "low_intensity_cardio": {
                    "description": "20-40 minutes at 60-70% max HR",
                    "benefits": [
                        "improved circulation",
                        "metabolite clearance",
                        "mental relaxation",
                    ],
                    "best_for": ["endurance athletes", "general fitness"],
                },
                "yoga_flow": {
                    "description": "30-60 minutes gentle yoga focusing on breath",
                    "benefits": ["flexibility", "stress reduction", "body awareness"],
                    "best_for": ["all sports", "high stress periods"],
                },
                "mobility_work": {
                    "description": "15-30 minutes targeted stretching and mobility",
                    "benefits": [
                        "range of motion",
                        "muscle tension relief",
                        "injury prevention",
                    ],
                    "best_for": ["strength sports", "desk workers"],
                },
            },
            "passive_recovery": {
                "massage_therapy": {
                    "description": "Professional or self-massage for 20-60 minutes",
                    "benefits": ["muscle tension relief", "circulation", "relaxation"],
                    "frequency": "1-2x per week for athletes",
                },
                "contrast_therapy": {
                    "description": "Alternating hot (3-4 min) and cold (1 min) exposure",
                    "benefits": [
                        "reduced inflammation",
                        "improved circulation",
                        "pain relief",
                    ],
                    "protocol": "3-4 cycles ending with cold",
                },
                "compression_therapy": {
                    "description": "Pneumatic compression or garments for 20-30 minutes",
                    "benefits": [
                        "lymphatic drainage",
                        "reduced swelling",
                        "faster recovery",
                    ],
                    "timing": "within 2 hours post-exercise",
                },
            },
            "sleep_optimization": {
                "sleep_extension": {
                    "description": "Increase sleep duration by 30-60 minutes",
                    "benefits": [
                        "enhanced recovery",
                        "improved performance",
                        "better mood",
                    ],
                    "implementation": "Earlier bedtime or later wake time",
                },
                "nap_protocols": {
                    "description": "20-30 minute naps between 1-3 PM",
                    "benefits": ["alertness", "performance", "recovery acceleration"],
                    "guidelines": "Avoid if sleep quality is already poor",
                },
            },
            "nutrition_recovery": {
                "post_workout_nutrition": {
                    "description": "Carbs + protein within 30 minutes post-exercise",
                    "ratios": "3:1 or 4:1 carb:protein for endurance, 2:1 for strength",
                    "benefits": [
                        "glycogen replenishment",
                        "protein synthesis",
                        "faster recovery",
                    ],
                },
                "hydration_strategies": {
                    "description": "Replace 150% of fluid losses from exercise",
                    "electrolytes": "Include sodium for sessions >60 minutes",
                    "monitoring": "Urine color should be pale yellow",
                },
                "anti_inflammatory_foods": {
                    "description": "Omega-3 rich foods, antioxidants, polyphenols",
                    "examples": [
                        "fatty fish",
                        "berries",
                        "leafy greens",
                        "tart cherry juice",
                    ],
                    "timing": "Include daily, especially post-training days",
                },
            },
        }

        # Predictive modeling factors
        self.predictive_factors = {
            "injury_risk_indicators": [
                "consecutive_days_high_soreness",
                "sleep_debt_accumulation",
                "training_load_spikes",
                "mood_decline_trend",
                "hrv_decline_pattern",
            ],
            "performance_readiness_factors": [
                "hrv_above_baseline",
                "sleep_quality_trend",
                "motivation_level",
                "energy_stability",
                "stress_management",
            ],
        }

    def _determine_sport_category(self, sport: str) -> str:
        """Determine sport category for specific protocols."""
        sport_lower = sport.lower()

        for category, data in self.sport_specific_protocols.items():
            if any(s in sport_lower for s in data["sports"]):
                return category

        # Default to general fitness protocols
        return "endurance_sports"  # Default assumption

    def _analyze_hrv_trend(
        self,
        current_hrv: float,
        baseline_hrv: float,
        historical_data: Optional[List[float]] = None,
    ) -> Dict[str, Any]:
        """Analyze HRV trend and provide interpretation."""
        if not historical_data:
            historical_data = [baseline_hrv] * 7  # Dummy baseline data

        # Calculate z-score
        import statistics

        mean_hrv = statistics.mean(historical_data)
        std_hrv = statistics.stdev(historical_data) if len(historical_data) > 1 else 1.0
        z_score = (current_hrv - mean_hrv) / std_hrv if std_hrv > 0 else 0.0

        # Determine status
        status = "baseline_recovery"
        for range_name, range_data in self.hrv_analysis[
            "interpretation_ranges"
        ].items():
            if z_score >= range_data["z_score"]:
                status = range_data["status"]
                break

        return {
            "z_score": z_score,
            "status": status,
            "recommendations": self.hrv_analysis["recommendations_by_status"][status],
            "trend": "stable",  # Would calculate from historical data
        }

    def _calculate_dynamic_thresholds(
        self, historical_assessments: List[WellnessAssessmentInput]
    ) -> Dict[str, float]:
        """Calculate personalized thresholds based on historical data."""
        if not historical_assessments or len(historical_assessments) < 7:
            # Return default thresholds if insufficient data
            return {
                "energy_level": 4.0,
                "mood_rating": 4.0,
                "stress_level": 7.0,
                "muscle_soreness": 7.0,
                "sleep_quality": 4.0,
            }

        # Calculate personalized baselines (simplified - would use more sophisticated methods)
        import statistics

        energy_values = [a.energy_level for a in historical_assessments]
        mood_values = [a.mood_rating for a in historical_assessments]
        stress_values = [a.stress_level for a in historical_assessments]
        soreness_values = [a.muscle_soreness for a in historical_assessments]
        sleep_values = [a.sleep_quality for a in historical_assessments]

        return {
            "energy_level": statistics.mean(energy_values)
            - statistics.stdev(energy_values),
            "mood_rating": statistics.mean(mood_values) - statistics.stdev(mood_values),
            "stress_level": statistics.mean(stress_values)
            + statistics.stdev(stress_values),
            "muscle_soreness": statistics.mean(soreness_values)
            + statistics.stdev(soreness_values),
            "sleep_quality": statistics.mean(sleep_values)
            - statistics.stdev(sleep_values),
        }

    def _generate_sport_specific_recommendations(
        self, assessment: WellnessAssessmentInput, sport: str
    ) -> List[str]:
        """Generate recommendations specific to sport type."""
        sport_category = self._determine_sport_category(sport)
        protocol = self.sport_specific_protocols[sport_category]

        recommendations = []
        thresholds = protocol["warning_thresholds"]
        interventions = protocol["recovery_interventions"]

        # Check sport-specific warning signs
        warning_count = 0

        if hasattr(
            assessment, "energy_level"
        ) and assessment.energy_level <= thresholds.get("energy_level", 4):
            warning_count += 1

        if hasattr(
            assessment, "muscle_soreness"
        ) and assessment.muscle_soreness >= thresholds.get("muscle_soreness", 7):
            warning_count += 1

        if hasattr(
            assessment, "motivation_level"
        ) and assessment.motivation_level <= thresholds.get("motivation_level", 4):
            warning_count += 1

        if hasattr(
            assessment, "sleep_quality"
        ) and assessment.sleep_quality <= thresholds.get("sleep_quality", 5):
            warning_count += 1

        # Provide sport-specific interventions based on warning count
        if warning_count >= 3:
            recommendations.extend(interventions[:3])
        elif warning_count >= 2:
            recommendations.extend(interventions[:2])
        elif warning_count >= 1:
            recommendations.extend(interventions[:1])
        else:
            # Maintenance recommendations
            recommendations.extend(
                [
                    f"Continue current {sport_category.replace('_', ' ')} training approach",
                    "Focus on consistency in recovery practices",
                    "Monitor trends for any emerging patterns",
                ]
            )

        return recommendations

    def _select_recovery_modalities(
        self, assessment: WellnessAssessmentInput, sport_category: str
    ) -> List[str]:
        """Select appropriate recovery modalities based on assessment."""
        modality_recommendations = []

        # Based on energy level
        if assessment.energy_level <= 4:
            modality_recommendations.extend(
                [
                    "Consider gentle active recovery (20-30 min walk)",
                    "Prioritize passive recovery modalities",
                    "Extend sleep by 30-60 minutes if possible",
                ]
            )
        elif assessment.energy_level <= 6:
            modality_recommendations.extend(
                [
                    "Light active recovery session recommended",
                    "Include mobility work or gentle yoga",
                ]
            )
        else:
            modality_recommendations.extend(
                ["Normal training can proceed", "Maintain current recovery routine"]
            )

        # Based on muscle soreness
        if assessment.muscle_soreness >= 7:
            modality_recommendations.extend(
                [
                    "Consider massage therapy or self-massage",
                    "Apply contrast therapy (hot/cold)",
                    "Use compression garments for 20-30 minutes",
                ]
            )

        # Based on stress level
        if assessment.stress_level >= 7:
            modality_recommendations.extend(
                [
                    "Practice stress-reduction techniques",
                    "Consider meditation or deep breathing exercises",
                    "Prioritize activities you enjoy outside training",
                ]
            )

        return modality_recommendations[:4]

    def _calculate_comprehensive_wellness_score(
        self, assessment: WellnessAssessmentInput
    ) -> int:
        """Calculate wellness score with sport-specific weighting."""
        # Enhanced scoring that considers individual metric importance
        base_score = (
            assessment.energy_level * 0.25
            + assessment.mood_rating * 0.15
            + (11 - assessment.stress_level) * 0.15  # Inverted
            + (11 - assessment.muscle_soreness) * 0.15  # Inverted
            + assessment.sleep_quality * 0.20
            + assessment.motivation_level * 0.10
        )

        # Adjust for hydration (bonus/penalty)
        hydration_factor = 1.0
        if assessment.hydration_liters:
            if assessment.hydration_liters < 2.0:
                hydration_factor = 0.95  # 5% penalty
            elif assessment.hydration_liters >= 3.0:
                hydration_factor = 1.05  # 5% bonus

        return int((base_score * 10 * hydration_factor))

    async def assess_wellness(  # Ensure this method name matches the original tool
        self,
        assessment: WellnessAssessmentInput,
        sport: str = "general_fitness",
        historical_data: Optional[List[WellnessAssessmentInput]] = None,
        hrv_data: Optional[Dict[str, float]] = None,
    ) -> WellnessTrackingOutput:
        """Perform comprehensive wellness assessment with advanced analytics."""
        try:
            # Calculate comprehensive scores
            wellness_score = self._calculate_comprehensive_wellness_score(assessment)

            # Dynamic threshold calculation
            if historical_data:
                thresholds = self._calculate_dynamic_thresholds(historical_data)
            else:
                # Use sport-specific defaults
                sport_category = self._determine_sport_category(sport)
                thresholds = self.sport_specific_protocols[sport_category][
                    "warning_thresholds"
                ]

            # Generate sport-specific recommendations
            sport_recommendations = self._generate_sport_specific_recommendations(
                assessment, sport
            )

            # HRV analysis if data available
            hrv_recommendations = []
            if hrv_data and "current_hrv" in hrv_data:
                hrv_analysis = self._analyze_hrv_trend(
                    hrv_data["current_hrv"],
                    hrv_data.get("baseline_hrv", hrv_data["current_hrv"]),
                )
                hrv_recommendations = hrv_analysis["recommendations"]

            # Select recovery modalities
            sport_category = self._determine_sport_category(sport)
            recovery_modalities = self._select_recovery_modalities(
                assessment, sport_category
            )

            # Combine all recommendations
            comprehensive_recommendations = []
            comprehensive_recommendations.extend(sport_recommendations[:2])
            comprehensive_recommendations.extend(hrv_recommendations[:2])
            comprehensive_recommendations.extend(recovery_modalities[:2])

            # Enhanced warning signs with thresholds
            warning_signs = []
            if assessment.energy_level <= thresholds.get("energy_level", 4):
                warning_signs.append(
                    f"Energy level below personal threshold ({thresholds['energy_level']:.1f})"
                )
            if assessment.stress_level >= thresholds.get("stress_level", 7):
                warning_signs.append(
                    f"Stress level above personal threshold ({thresholds['stress_level']:.1f})"
                )
            if assessment.muscle_soreness >= thresholds.get("muscle_soreness", 7):
                warning_signs.append(
                    f"Muscle soreness above threshold - consider recovery focus"
                )
            if len(assessment.pain_areas) >= 2:
                warning_signs.append("Multiple pain areas reported - monitor carefully")

            # Calculate training readiness with sport-specific factors
            readiness_base = wellness_score
            if hrv_data and "current_hrv" in hrv_data:
                hrv_factor = max(
                    0.8,
                    min(
                        1.2,
                        hrv_data["current_hrv"]
                        / hrv_data.get("baseline_hrv", hrv_data["current_hrv"]),
                    ),
                )
                readiness_base = int(readiness_base * hrv_factor)

            training_readiness = min(100, max(20, readiness_base))

            # Enhanced training modifications
            training_modifications = []
            if training_readiness < 60:
                training_modifications.extend(
                    [
                        "Consider complete rest day or very light active recovery",
                        f"If training, reduce intensity by 40-50% for {sport_category.replace('_', ' ')}",
                        "Focus on mobility, breathing, and relaxation techniques",
                    ]
                )
            elif training_readiness < 75:
                training_modifications.extend(
                    [
                        f"Reduce {sport_category.replace('_', ' ')} training intensity by 20-30%",
                        "Maintain technique focus over performance goals",
                        "Add extra recovery time between sessions",
                    ]
                )
            else:
                training_modifications.extend(
                    [
                        "Proceed with planned training approach",
                        f"Good readiness for {sport_category.replace('_', ' ')} training",
                        "Monitor for any declining trends",
                    ]
                )

            # Follow-up actions
            follow_up_actions = []
            if wellness_score < 50:
                follow_up_actions.extend(
                    [
                        "Re-assess wellness daily for next 3-5 days",
                        "Consider consulting sports medicine professional",
                        "Track sleep and nutrition more closely",
                    ]
                )
            elif len(warning_signs) >= 2:
                follow_up_actions.extend(
                    [
                        "Monitor trends over next 2-3 days",
                        "Implement targeted recovery strategies",
                        "Consider reducing training load temporarily",
                    ]
                )
            else:
                follow_up_actions.extend(
                    [
                        "Continue current wellness monitoring",
                        "Weekly trend analysis recommended",
                    ]
                )

            return WellnessTrackingOutput(
                overall_wellness_score=wellness_score,
                training_readiness_score=training_readiness,
                recovery_recommendations=comprehensive_recommendations[:6],
                warning_signs=warning_signs[:4],
                suggested_modifications=training_modifications[:4],
                follow_up_actions=follow_up_actions[:3],
            )

        except Exception as e:
            logger.error(f"Error in comprehensive wellness assessment: {e}")
            raise


class WellnessTrackingLangChainTool(LangChainBaseTool):
    """LangChain-compatible tool for tracking wellness."""

    name: str = "assess_wellness"
    description: str = WellnessTrackingTool.description
    args_schema: type[BaseModel] = WellnessAssessmentInput
    wellness_tool: WellnessTrackingTool = PydanticField(
        default_factory=WellnessTrackingTool, exclude=True
    )

    async def _arun(self, **kwargs: Any) -> str:
        """Async implementation of the tool."""
        try:
            # The tool's method signature is `assess_wellness(self, assessment, sport, ...)`
            # We need to extract the 'assessment' object and other optional params from kwargs
            assessment_input = WellnessAssessmentInput(**kwargs)
            sport = kwargs.get("sport", "general_fitness")
            historical_data = kwargs.get("historical_data")
            hrv_data = kwargs.get("hrv_data")

            result = await self.wellness_tool.assess_wellness(
                assessment=assessment_input,
                sport=sport,
                historical_data=historical_data,
                hrv_data=hrv_data,
            )
            return result.model_dump_json(indent=2)
        except Exception as e:
            logger.error(f"Error assessing wellness: {e}")
            return f'{{"error": "Failed to assess wellness: {str(e)}"}}'

    def _run(self, **kwargs: Any) -> str:
        """Sync implementation of the tool."""
        import asyncio

        try:
            return asyncio.run(self._arun(**kwargs))
        except RuntimeError:
            return '{"error": "Async execution required for wellness assessment."}'


# Create a standalone instance of the tool
wellness_tracking_tool = WellnessTrackingTool()


@tool(args_schema=WellnessAssessmentInput)
async def assess_wellness(
    energy_level: int,
    mood_rating: int,
    stress_level: int,
    muscle_soreness: int,
    sleep_quality: int,
    motivation_level: int,
    hydration_liters: Optional[float] = 2.0,
    training_readiness: Optional[int] = None,
    pain_areas: List[str] = [],
    sport: str = "general_fitness",
    historical_data: Optional[Dict[str, Any]] = None,
    hrv_data: Optional[Dict[str, Any]] = None,
) -> str:
    """
    Assesses overall wellness and training readiness based on user-reported metrics. Use this when a user wants to check their recovery status or mentions fatigue, stress, or soreness.
    """
    try:
        input_data = WellnessAssessmentInput(
            energy_level=energy_level,
            mood_rating=mood_rating,
            stress_level=stress_level,
            muscle_soreness=muscle_soreness,
            sleep_quality=sleep_quality,
            motivation_level=motivation_level,
            hydration_liters=hydration_liters,
            training_readiness=training_readiness,
            pain_areas=pain_areas,
        )
        # Note: The 'sport', 'historical_data', and 'hrv_data' are not part of the Pydantic model
        # but are passed to the tool's execution logic.
        result = await wellness_tracking_tool.assess_wellness(
            assessment=input_data,
            sport=sport,
            historical_data=historical_data,
            hrv_data=hrv_data,
        )
        return result.model_dump_json(indent=2)
    except Exception as e:
        logger.error(f"Error in assess_wellness tool: {e}")
        return f'{{"error": "Failed to assess wellness: {str(e)}"}}'
