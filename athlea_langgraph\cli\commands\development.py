"""
Development and testing commands for prompt management.
"""

import json
import sys
from pathlib import Path
from typing import Any, Dict

import click

from athlea_langgraph.utils import PromptLoader
from athlea_langgraph.utils.prompt_migration import run_migration

from ..utils.formatters import format_stats


@click.group()
def dev():
    """Development and testing tools."""
    pass


@dev.command()
@click.option(
    "--fix", is_flag=True, help="Attempt to fix validation errors automatically"
)
@click.pass_context
def validate(ctx, fix: bool):
    """Validate all prompts in the system."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    prompts = loader.list_prompts()
    total_prompts = len(prompts)
    valid_prompts = 0
    invalid_prompts = []

    click.echo(f"🔍 Validating {total_prompts} prompts...")
    click.echo("=" * 50)

    for prompt_name in prompts:
        try:
            config = loader.load_prompt(prompt_name)
            is_valid = loader.validate_prompt(config)

            if is_valid:
                valid_prompts += 1
                if cli_ctx.verbose:
                    click.echo(f"✅ {prompt_name}")
            else:
                invalid_prompts.append(prompt_name)
                click.echo(f"❌ {prompt_name} - Validation failed")

        except Exception as e:
            invalid_prompts.append(prompt_name)
            click.echo(f"💥 {prompt_name} - Error: {e}")

    # Summary
    click.echo("\n" + "=" * 50)
    click.echo(f"📊 VALIDATION RESULTS")
    click.echo(f"Total prompts: {total_prompts}")
    click.echo(f"Valid prompts: {valid_prompts}")
    click.echo(f"Invalid prompts: {len(invalid_prompts)}")

    if invalid_prompts:
        click.echo(f"\n❌ Invalid prompts: {', '.join(invalid_prompts)}")

        if fix:
            click.echo("\n🔧 Attempting to fix issues...")
            # Basic fix attempts could go here
            click.echo("Auto-fix functionality not implemented yet.")

        sys.exit(1)
    else:
        click.echo("\n✅ All prompts are valid!")


@dev.command()
@click.argument("name")
@click.option("--context", help="JSON context for template rendering")
@click.option("--variables", help="JSON variables for rendering")
@click.pass_context
def test(ctx, name: str, context: str, variables: str):
    """Test prompt rendering with different contexts."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    try:
        # Parse context and variables
        render_context = {}
        if context:
            try:
                render_context.update(json.loads(context))
            except json.JSONDecodeError as e:
                click.echo(f"Error parsing context JSON: {e}", err=True)
                sys.exit(1)

        if variables:
            try:
                var_dict = json.loads(variables)
                render_context.update(var_dict)
            except json.JSONDecodeError as e:
                click.echo(f"Error parsing variables JSON: {e}", err=True)
                sys.exit(1)

        # Load and render prompt
        config = loader.load_prompt(name)

        click.echo(f"🧪 Testing prompt: {name}")
        click.echo("=" * 50)

        # Show basic info
        click.echo(f"Type: {config.metadata.prompt_type.value}")
        click.echo(f"Version: {config.metadata.version}")
        click.echo(f"Tags: {', '.join(config.metadata.tags)}")

        # Test rendering
        if render_context:
            click.echo(f"\nUsing context: {json.dumps(render_context, indent=2)}")
            rendered = loader.render_prompt(name, render_context)
        else:
            click.echo("\nNo context provided, rendering without variables")
            rendered = loader.render_prompt(name)

        click.echo("\n📋 RENDERED PROMPT:")
        click.echo("-" * 30)
        click.echo(rendered)

        click.echo(f"\n📊 Rendered length: {len(rendered)} characters")
        click.echo(f"📊 Line count: {len(rendered.split(chr(10)))}")

        # Validation test
        is_valid = loader.validate_prompt(config)
        validation_status = "✅ VALID" if is_valid else "❌ INVALID"
        click.echo(f"🔍 Validation: {validation_status}")

    except Exception as e:
        click.echo(f"Error testing prompt '{name}': {e}", err=True)
        sys.exit(1)


@dev.command()
@click.option(
    "--source-dir",
    default="athlea_langgraph",
    help="Source directory to scan for prompts",
)
@click.pass_context
def migrate(ctx, source_dir: str):
    """Run prompt migration from source code."""
    cli_ctx = ctx.find_root().obj

    click.echo(f"🔄 Running prompt migration from {source_dir}")
    click.echo("=" * 50)

    try:
        # Run the migration
        run_migration(source_dir, cli_ctx.prompts_dir)
        click.echo("✅ Migration completed successfully!")

        # Show updated stats
        loader = cli_ctx.get_loader()
        loader.initialize()  # Refresh after migration
        prompts = loader.list_prompts()
        click.echo(f"📊 Total prompts now: {len(prompts)}")

    except Exception as e:
        click.echo(f"❌ Migration failed: {e}", err=True)
        sys.exit(1)


@dev.command()
@click.option("--detailed", is_flag=True, help="Show detailed statistics")
@click.pass_context
def stats(ctx, detailed: bool):
    """Show prompt system statistics."""
    cli_ctx = ctx.find_root().obj
    loader = cli_ctx.get_loader()

    # Gather statistics
    all_prompts = loader.list_prompts()
    cache_stats = loader.get_cache_stats()

    # Analyze prompts
    stats_data = {
        "total_prompts": len(all_prompts),
        "cached_prompts": cache_stats["cached_prompts"],
        "tracked_files": cache_stats["tracked_files"],
    }

    # Group by type and tags
    type_counts = {}
    tag_counts = {}
    author_counts = {}
    total_content_length = 0

    for prompt_name in all_prompts:
        try:
            config = loader.load_prompt(prompt_name)

            # Type counts
            prompt_type = config.metadata.prompt_type.value
            type_counts[prompt_type] = type_counts.get(prompt_type, 0) + 1

            # Tag counts
            for tag in config.metadata.tags:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1

            # Author counts
            author = config.metadata.author
            author_counts[author] = author_counts.get(author, 0) + 1

            # Content length
            total_content_length += len(config.prompt.system)

        except Exception:
            continue  # Skip problematic prompts

    # Display basic stats
    click.echo("📊 PROMPT SYSTEM STATISTICS")
    click.echo("=" * 50)
    click.echo(f"Total Prompts: {stats_data['total_prompts']}")
    click.echo(f"Cached Prompts: {stats_data['cached_prompts']}")
    click.echo(f"Tracked Files: {stats_data['tracked_files']}")
    click.echo(
        f"Average Content Length: {total_content_length // max(len(all_prompts), 1)} characters"
    )

    if detailed:
        click.echo("\n🏷️  PROMPTS BY TYPE:")
        for ptype, count in sorted(type_counts.items()):
            click.echo(f"  {ptype}: {count}")

        click.echo("\n🏷️  TOP TAGS:")
        sorted_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
        for tag, count in sorted_tags[:10]:  # Top 10 tags
            click.echo(f"  {tag}: {count}")

        click.echo("\n👥 AUTHORS:")
        for author, count in sorted(author_counts.items()):
            click.echo(f"  {author}: {count}")

    click.echo(f"\n📁 Prompts Directory: {cli_ctx.prompts_dir}")


@dev.command()
@click.option(
    "--backup-dir", default="prompt_backups", help="Directory to store backups"
)
@click.option("--compress", is_flag=True, help="Compress backup files")
@click.pass_context
def backup(ctx, backup_dir: str, compress: bool):
    """Create a backup of all prompts."""
    cli_ctx = ctx.find_root().obj

    import shutil
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"prompts_backup_{timestamp}"

    backup_path = Path(backup_dir) / backup_name
    backup_path.mkdir(parents=True, exist_ok=True)

    try:
        # Copy prompts directory
        prompts_source = Path(cli_ctx.prompts_dir)
        prompts_dest = backup_path / "prompts"

        shutil.copytree(prompts_source, prompts_dest)

        # Create metadata file
        loader = cli_ctx.get_loader()
        all_prompts = loader.list_prompts()

        metadata = {
            "backup_timestamp": timestamp,
            "source_directory": str(prompts_source),
            "total_prompts": len(all_prompts),
            "prompt_list": all_prompts,
        }

        metadata_file = backup_path / "backup_metadata.json"
        with open(metadata_file, "w") as f:
            json.dump(metadata, f, indent=2)

        if compress:
            # Create compressed archive
            archive_path = f"{backup_path}.tar.gz"
            shutil.make_archive(
                str(backup_path), "gztar", str(backup_path.parent), backup_name
            )
            shutil.rmtree(backup_path)  # Remove uncompressed version
            click.echo(f"✅ Created compressed backup: {archive_path}")
        else:
            click.echo(f"✅ Created backup: {backup_path}")

        click.echo(f"📊 Backed up {len(all_prompts)} prompts")

    except Exception as e:
        click.echo(f"❌ Backup failed: {e}", err=True)
        sys.exit(1)


@dev.command()
@click.option("--watch", is_flag=True, help="Watch for file changes and auto-reload")
@click.pass_context
def monitor(ctx, watch: bool):
    """Monitor prompt system health and changes."""
    cli_ctx = ctx.find_root().obj

    if watch:
        click.echo("👀 Watching for prompt file changes... (Press Ctrl+C to stop)")
        click.echo(f"📁 Monitoring directory: {cli_ctx.prompts_dir}")

        try:
            import time

            from watchdog.events import FileSystemEventHandler
            from watchdog.observers import Observer

            class PromptFileHandler(FileSystemEventHandler):
                def on_modified(self, event):
                    if event.src_path.endswith(".json"):
                        click.echo(f"🔄 Prompt file changed: {event.src_path}")
                        # Could trigger auto-validation here

                def on_created(self, event):
                    if event.src_path.endswith(".json"):
                        click.echo(f"✨ New prompt file: {event.src_path}")

                def on_deleted(self, event):
                    if event.src_path.endswith(".json"):
                        click.echo(f"🗑️  Prompt file deleted: {event.src_path}")

            event_handler = PromptFileHandler()
            observer = Observer()
            observer.schedule(event_handler, cli_ctx.prompts_dir, recursive=True)
            observer.start()

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                observer.stop()
                click.echo("\n👋 Stopped monitoring")
            observer.join()

        except ImportError:
            click.echo(
                "❌ watchdog package not installed. Install with: pip install watchdog",
                err=True,
            )
            sys.exit(1)
    else:
        # One-time health check
        loader = cli_ctx.get_loader()
        prompts = loader.list_prompts()

        click.echo("🏥 PROMPT SYSTEM HEALTH CHECK")
        click.echo("=" * 40)

        issues = []

        for prompt_name in prompts:
            try:
                config = loader.load_prompt(prompt_name)
                is_valid = loader.validate_prompt(config)
                if not is_valid:
                    issues.append(f"Validation failed: {prompt_name}")
            except Exception as e:
                issues.append(f"Load error: {prompt_name} - {e}")

        if issues:
            click.echo(f"❌ Found {len(issues)} issues:")
            for issue in issues:
                click.echo(f"  • {issue}")
            sys.exit(1)
        else:
            click.echo("✅ All prompts are healthy!")
            click.echo(f"📊 Checked {len(prompts)} prompts")
