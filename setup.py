"""
Setup configuration for athlea-langgraph package.
This ensures proper installation for both development and deployment.
"""

from setuptools import find_packages, setup

setup(
    name="athlea-langgraph",
    version="0.1.0",
    description="Python LangGraph implementation for Athlea coaching",
    author="Athlea Team",
    packages=find_packages(),
    python_requires=">=3.11",
    install_requires=[
        "langgraph>=0.4.7",
        "langchain>=0.3.25",
        "langchain-openai>=0.3.18",
        "python-dotenv>=1.1.0",
        "aiohttp>=3.10.0",
        "pydantic>=2.10.0",
        "pymongo>=4.10.0",
        "motor>=3.6.0",
        "azure-identity>=1.19.0",
        "requests>=2.32.0",
        "typing-extensions>=4.12.0",
        "langgraph-checkpoint-postgres>=2.0.21",
        "fastapi>=0.115.12",
        "uvicorn>=0.34.2",
        "httpx>=0.28.1",
        "prometheus-client>=0.21.1",
        "azure-search-documents>=11.5.2",
        "azure-core>=1.34.0",
        "openai>=1.82.0",
        "mcp>=1.9.1",
        "pyairtable>=3.1.1",
        "psutil>=7.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=8.0.0",
            "pytest-asyncio>=0.24.0",
            "black>=24.0.0",
            "isort>=5.13.0",
            "mypy>=1.8.0",
            "pytest-cov>=6.1.1",
        ]
    },
    include_package_data=True,
    zip_safe=False,
)
