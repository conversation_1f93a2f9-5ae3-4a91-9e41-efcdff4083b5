# Data and State Management

This document provides a comprehensive overview of the data and state management architecture for the Athlea coaching system. It covers how we handle user data, session state, long-term memory, and performance caching.

## Architecture Overview

Our system uses a hybrid data architecture designed for performance, scalability, and intelligence. It combines multiple technologies, each serving a specific purpose:

- **MongoDB**: Serves as the primary database for persistent storage of **user profiles**, **training plans**, and **session state checkpoints**.
- **Redis**: An in-memory data store used as a high-speed **caching layer** to dramatically accelerate user data retrieval.
- **Mem0 & Pinecone**: A specialized, semantic memory system that gives the agents **long-term, intelligent memory**, allowing for highly contextual and personalized conversations.

This diagram illustrates how the components work together:

```mermaid
graph TD
    A[Coaching Agent] --> B{Need User Data};
    B --> C[Memory Cache (in-memory)];
    C -->|Miss| D[Redis Cache];
    D -->|Miss| E[MongoDB Database];
    E --> F[Populate Caches];
    F --> A;
    C -->|Hit| A;
    D -->|Hit| A;

    A --> G{Need Long-Term Memory};
    G --> H[Mem0 Service];
    H --> I[Pinecone Vector Search];
    I --> H;
    H --> A;
```

---

## 1. Persistent Data Layer: MongoDB

MongoDB is our source of truth for core user data and session state.

### User Profile and Plan Storage

We use MongoDB to store two critical data objects: the user's `TrainingProfile` and their `TrainingPlan`.

- **Location**: `athlea_langgraph/utils/user_data_utils.py`
- **Database**: `AthleaUserData`
- **Collection**: `users`

**Data Flow**:
1.  An API request is received with a `userId`.
2.  The `UserDataManager` fetches the corresponding user document from MongoDB.
3.  The data is cleaned, validated, and formatted for the LLM.
4.  This data is injected into the agent's prompt, providing essential context for personalized coaching.

This ensures that every coaching session is informed by the user's actual, up-to-date training profile and current plan.

### Session State Checkpointing (`MongoSaver`)

To ensure conversations can be paused and resumed, LangGraph's state is persisted to MongoDB using a checkpointer.

- **Location**: `athlea_langgraph/memory/mongo_memory.py`
- **How it Works**: The `MongoSaver` class implements LangGraph's checkpointer interface, automatically saving the state of a conversation thread to a MongoDB collection. When a user returns to a conversation, the checkpointer loads the state, allowing the agent to pick up exactly where it left off.

---

## 2. High-Speed Caching Layer: Redis

To ensure ultra-fast response times, we've implemented a multi-level caching system in front of MongoDB, using Redis for shared caching and an in-memory TTL cache for instantaneous hits.

- **Guide**: `docs/ADVANCED_CACHING_GUIDE.md` (original document)
- **Class**: `AdvancedUserDataCache`

### Multi-Level Caching Strategy

1.  **Level 1: Memory Cache (`TTLCache`)**: An in-memory cache with a very short TTL (e.g., 300ms) for immediate, sub-millisecond data retrieval for requests that arrive in quick succession.
2.  **Level 2: Redis Cache (Azure)**: A shared cache with a longer TTL (e.g., 30 minutes to 2 hours). This provides very fast (1-5ms) data retrieval for active users without hitting the database.
3.  **Level 3: MongoDB Fallback**: If data is not in either cache, we query the MongoDB database, which is slower but reliable. The result is then populated back into the Redis and memory caches.

This architecture achieves a **90%+ cache hit rate** for active users, resulting in a **10-100x performance improvement** over direct database queries. The system also includes features like connection pooling, data compression for large payloads, and health checks.

---

## 3. Intelligent Memory System: Mem0 and Pinecone

While MongoDB stores structured user data, our intelligent memory system, powered by **Mem0**, captures the nuances of conversations to provide a truly personalized coaching experience.

### How it Works

Mem0 is not a separate graph node. Instead, it's a service integrated at specific points in the agent's reasoning process:

1.  **Memory Retrieval (Before Reasoning)**: When a user sends a message, the `enhanced_reasoning_node` first queries the `Mem0MemoryService`. This service performs a semantic search (via Pinecone) on the user's past conversations to find relevant memories. This context is then injected into the prompt, equipping the agent with a "memory" of past interactions.

2.  **Memory Storage (After Aggregation)**: After a coaching interaction is complete, the `enhanced_aggregation_node` sends a summary of the conversation (user query, final response, coaches involved) to the `Mem0MemoryService`.

### Automatic Intelligence

Mem0 uses its own LLM to automatically extract and structure memories. When provided with conversation text and domain-specific metadata (e.g., `"domain": "fitness_coaching"`), it intelligently identifies and stores key information without needing explicit instructions:

- **User Goals**: "I want to run a marathon."
- **Preferences**: "I prefer to work out in the morning."
- **Constraints**: "I only have access to dumbbells."
- **Experience**: "I'm new to lifting but have been running for years."

These memories are converted into vector embeddings (using Azure OpenAI's `text-embedding-3-large`) and stored in **Pinecone**, a vector database. This allows for fast, semantic search to find the most relevant memories for any given query.

This three-tiered data architecture provides a robust, high-performance, and intelligent foundation for the Athlea coaching system. 