#!/usr/bin/env python3
"""
Clear Prompts Script

Safely removes all prompt files from the athlea_langgraph/prompts directory.
Provides backup options and confirmation prompts to prevent accidental deletion.
"""

import os
import shutil
import sys
from pathlib import Path
from datetime import datetime


def backup_prompts(prompts_dir: Path, backup_dir: Path = None):
    """Create a backup of all prompts before deletion."""
    if backup_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = prompts_dir.parent / f"prompts_backup_{timestamp}"

    if prompts_dir.exists():
        shutil.copytree(prompts_dir, backup_dir)
        print(f"✅ Backup created: {backup_dir}")
        return backup_dir
    return None


def count_prompt_files(prompts_dir: Path):
    """Count all files in the prompts directory."""
    if not prompts_dir.exists():
        return 0, []

    files = []
    for root, dirs, filenames in os.walk(prompts_dir):
        for filename in filenames:
            if filename.endswith((".json", ".yaml", ".yml", ".txt")):
                files.append(Path(root) / filename)

    return len(files), files


def remove_prompt_files(prompts_dir: Path, keep_structure: bool = True):
    """
    Remove all prompt files.

    Args:
        prompts_dir: Path to prompts directory
        keep_structure: If True, keeps directories but removes files.
                       If False, removes entire directory structure.
    """
    removed_count = 0

    if not prompts_dir.exists():
        print(f"❌ Directory not found: {prompts_dir}")
        return 0

    if keep_structure:
        # Remove only files, keep directory structure
        for root, dirs, filenames in os.walk(prompts_dir):
            for filename in filenames:
                file_path = Path(root) / filename
                if filename.endswith((".json", ".yaml", ".yml", ".txt")):
                    try:
                        file_path.unlink()
                        print(f"   🗑️  Removed: {file_path.relative_to(prompts_dir)}")
                        removed_count += 1
                    except Exception as e:
                        print(f"   ❌ Failed to remove {file_path}: {e}")
    else:
        # Remove entire directory and recreate empty one
        try:
            shutil.rmtree(prompts_dir)
            prompts_dir.mkdir(parents=True, exist_ok=True)
            print(f"   🗑️  Removed entire directory: {prompts_dir}")
            removed_count = "all"
        except Exception as e:
            print(f"   ❌ Failed to remove directory {prompts_dir}: {e}")

    return removed_count


def main():
    """Main function with interactive prompts."""
    # Define paths
    project_root = Path(__file__).parent.parent
    prompts_dir = project_root / "athlea_langgraph" / "prompts"

    print("🗑️  Prompt Cleanup Utility")
    print("=" * 40)
    print(f"Target directory: {prompts_dir}")

    # Check if directory exists
    if not prompts_dir.exists():
        print(f"❌ Prompts directory not found: {prompts_dir}")
        sys.exit(1)

    # Count files
    file_count, files = count_prompt_files(prompts_dir)

    if file_count == 0:
        print("ℹ️  No prompt files found to remove.")
        sys.exit(0)

    print(f"📊 Found {file_count} prompt files:")

    # Show first few files as examples
    for i, file_path in enumerate(files[:5]):
        rel_path = file_path.relative_to(prompts_dir)
        print(f"   - {rel_path}")

    if len(files) > 5:
        print(f"   ... and {len(files) - 5} more files")

    print()

    # Confirmation prompts
    choice = input(
        "Choose an option:\n"
        "1. Create backup and remove all files (keep directories)\n"
        "2. Create backup and remove everything (including directories)\n"
        "3. Remove files without backup (DANGER!)\n"
        "4. Cancel\n"
        "Enter choice (1-4): "
    ).strip()

    if choice == "4" or choice.lower() in ["cancel", "c", "n", "no"]:
        print("❌ Operation cancelled.")
        sys.exit(0)

    if choice not in ["1", "2", "3"]:
        print("❌ Invalid choice. Operation cancelled.")
        sys.exit(1)

    # Create backup if requested
    backup_created = None
    if choice in ["1", "2"]:
        print("\n📦 Creating backup...")
        backup_created = backup_prompts(prompts_dir)

    # Final confirmation
    if choice == "3":
        confirm = input(
            "\n⚠️  WARNING: This will permanently delete all prompt files without backup!\n"
            "Type 'DELETE' to confirm: "
        ).strip()
        if confirm != "DELETE":
            print("❌ Operation cancelled.")
            sys.exit(0)
    else:
        confirm = (
            input(f"\n🗑️  Proceed with removing {file_count} prompt files? (y/N): ")
            .strip()
            .lower()
        )
        if confirm not in ["y", "yes"]:
            print("❌ Operation cancelled.")
            sys.exit(0)

    # Perform deletion
    print("\n🚀 Removing prompt files...")

    keep_structure = choice == "1"
    removed_count = remove_prompt_files(prompts_dir, keep_structure)

    # Summary
    print(f"\n✅ Operation completed!")
    if backup_created:
        print(f"📦 Backup available at: {backup_created}")

    if isinstance(removed_count, int):
        print(f"🗑️  Removed {removed_count} files")
    else:
        print(f"🗑️  Removed entire directory structure")

    print(f"📁 Target directory: {prompts_dir}")

    # Show what's left
    remaining_count, _ = count_prompt_files(prompts_dir)
    print(f"📊 Remaining prompt files: {remaining_count}")


if __name__ == "__main__":
    main()
