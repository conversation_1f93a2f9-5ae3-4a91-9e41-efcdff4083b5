"""
SpecializedCoachState for Issue #26: Phase 3 Individual Specialized Coach ReWOO+Reflection Implementation

This state extends the base AgentState to support sophisticated individual coach capabilities:
- Complexity assessment and routing
- ReWOO worker execution tracking
- Reflection and quality improvement
- Performance and debug monitoring
"""

from typing import Any, Dict, List, Optional
from typing_extensions import TypedDict
from .state import AgentState


class SpecializedCoachState(AgentState):
    """Enhanced state for individual specialized coach with ReWOO+Reflection capabilities."""

    # Complexity Assessment Fields
    complexity_level: Optional[str]  # "simple", "moderate", "complex"
    complexity_score: Optional[float]  # 0.0-1.0
    complexity_reasoning: Optional[str]
    complexity_assessment_time: Optional[float]
    complexity_assessment_method: Optional[str]  # "llm_based", "fallback"

    # Coach Domain Information
    coach_domain: Optional[str]  # strength, nutrition, cardio, etc.
    coach_specialization: Optional[str]

    # Execution Path Tracking
    execution_path: Optional[str]  # "simple", "complex", "complex_fallback"
    routing_decision: Optional[str]

    # Simple Path Results
    simple_response: Optional[str]
    simple_execution_time: Optional[float]
    simple_success: bool

    # Complex Path Results (ReWOO-based)
    rewoo_plan: Optional[Dict[str, Any]]
    worker_results: Dict[str, Any]
    synthesis_result: Optional[str]
    complex_execution_time: Optional[float]
    complex_success: bool
    workers_executed: int
    successful_workers: int

    # Reflection Results
    reflection_applied: bool
    reflection_improvements: List[str]
    reflection_score: Optional[float]
    quality_assessment: Optional[Dict[str, Any]]

    # Performance Metrics
    total_execution_time: Optional[float]
    llm_calls_made: int
    tokens_used: Optional[int]

    # Debug and Monitoring
    execution_trace: List[Dict[str, Any]]
    error_log: List[str]
    warning_log: List[str]

    # Feature Flags and Configuration
    rewoo_enabled: bool
    reflection_enabled: bool
    complexity_threshold_simple: float
    complexity_threshold_complex: float


# Helper functions for state management (since TypedDict doesn't support methods)


def log_execution_step(
    state: SpecializedCoachState, step_name: str, step_data: Dict[str, Any]
) -> None:
    """Log an execution step for debugging and monitoring."""
    if "execution_trace" not in state:
        state["execution_trace"] = []
    state["execution_trace"].append(
        {"step": step_name, "timestamp": step_data.get("timestamp"), "data": step_data}
    )


def log_error(state: SpecializedCoachState, error_message: str) -> None:
    """Log an error message."""
    if "error_log" not in state:
        state["error_log"] = []
    state["error_log"].append(error_message)


def log_warning(state: SpecializedCoachState, warning_message: str) -> None:
    """Log a warning message."""
    if "warning_log" not in state:
        state["warning_log"] = []
    state["warning_log"].append(warning_message)


def get_execution_summary(state: SpecializedCoachState) -> Dict[str, Any]:
    """Get a summary of the execution for monitoring and analytics."""
    return {
        "coach_domain": state.get("coach_domain"),
        "complexity_level": state.get("complexity_level"),
        "complexity_score": state.get("complexity_score"),
        "execution_path": state.get("execution_path"),
        "total_execution_time": state.get("total_execution_time"),
        "complexity_assessment_time": state.get("complexity_assessment_time"),
        "simple_execution_time": state.get("simple_execution_time"),
        "complex_execution_time": state.get("complex_execution_time"),
        "workers_executed": state.get("workers_executed", 0),
        "successful_workers": state.get("successful_workers", 0),
        "reflection_applied": state.get("reflection_applied", False),
        "llm_calls_made": state.get("llm_calls_made", 0),
        "success": state.get("simple_success", False)
        or state.get("complex_success", False),
        "errors": len(state.get("error_log", [])),
        "warnings": len(state.get("warning_log", [])),
    }


def is_complex_query(state: SpecializedCoachState) -> bool:
    """Determine if this query should use complex ReWOO execution."""
    complexity_score = state.get("complexity_score")
    if complexity_score is None:
        return True  # Default to complex if unknown
    threshold = state.get("complexity_threshold_complex", 0.7)
    return complexity_score >= threshold


def is_simple_query(state: SpecializedCoachState) -> bool:
    """Determine if this query should use simple execution."""
    complexity_score = state.get("complexity_score")
    if complexity_score is None:
        return False  # Default to complex if unknown
    threshold = state.get("complexity_threshold_simple", 0.3)
    return complexity_score <= threshold


def should_use_rewoo(state: SpecializedCoachState) -> bool:
    """Determine if ReWOO should be used based on complexity and configuration."""
    if not state.get("rewoo_enabled", True):
        return False
    return not is_simple_query(state)


def get_performance_metrics(state: SpecializedCoachState) -> Dict[str, Any]:
    """Get detailed performance metrics for monitoring."""
    metrics = {
        "execution_efficiency": None,
        "complexity_accuracy": None,
        "worker_success_rate": None,
        "response_quality": None,
    }

    # Calculate execution efficiency (time vs complexity)
    total_time = state.get("total_execution_time")
    complexity_score = state.get("complexity_score")
    if total_time and complexity_score:
        expected_time = 5 if is_simple_query(state) else 30
        metrics["execution_efficiency"] = expected_time / max(total_time, 1)

    # Calculate worker success rate for complex queries
    workers_executed = state.get("workers_executed", 0)
    if workers_executed > 0:
        successful_workers = state.get("successful_workers", 0)
        metrics["worker_success_rate"] = successful_workers / workers_executed

    # Include reflection score as quality metric
    reflection_score = state.get("reflection_score")
    if reflection_score:
        metrics["response_quality"] = reflection_score

    return metrics


def create_specialized_coach_state(**kwargs) -> SpecializedCoachState:
    """Create a SpecializedCoachState with proper defaults."""
    defaults = {
        # Core AgentState fields
        "messages": [],
        "user_query": None,
        "user_profile": None,
        "routing_decision": None,
        "pending_agents": None,
        "plan": None,
        "current_step": None,
        "domain_contributions": {},
        "required_domains": [],
        "completed_domains": [],
        "aggregated_plan": None,
        "proceed_to_generation": False,
        "current_plan": None,
        "is_onboarding": False,
        "strength_response": None,
        "running_response": None,
        "cardio_response": None,
        "cycling_response": None,
        "nutrition_response": None,
        "recovery_response": None,
        "mental_response": None,
        "reasoning_output": None,
        "clarification_output": None,
        "aggregated_response": None,
        # SpecializedCoachState fields
        "complexity_level": None,
        "complexity_score": None,
        "complexity_reasoning": None,
        "complexity_assessment_time": None,
        "complexity_assessment_method": None,
        "coach_domain": None,
        "coach_specialization": None,
        "execution_path": None,
        "simple_response": None,
        "simple_execution_time": None,
        "simple_success": False,
        "rewoo_plan": None,
        "worker_results": {},
        "synthesis_result": None,
        "complex_execution_time": None,
        "complex_success": False,
        "workers_executed": 0,
        "successful_workers": 0,
        "reflection_applied": False,
        "reflection_improvements": [],
        "reflection_score": None,
        "quality_assessment": None,
        "total_execution_time": None,
        "llm_calls_made": 0,
        "tokens_used": None,
        "execution_trace": [],
        "error_log": [],
        "warning_log": [],
        "rewoo_enabled": True,
        "reflection_enabled": True,
        "complexity_threshold_simple": 0.3,
        "complexity_threshold_complex": 0.7,
    }

    # Update with provided kwargs
    defaults.update(kwargs)
    return defaults
