"""
Azure Maps Tool Schemas

Pydantic models for validating inputs and outputs of the Azure Maps tool.
Supports geocoding, weather, nearby search, and route calculations.
"""

from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator

# --- Common Models ---


class Position(BaseModel):
    """Represents a geographical position (latitude, longitude)."""

    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lon: float = Field(..., ge=-180, le=180, description="Longitude")


class Address(BaseModel):
    """Represents a postal address."""

    streetNumber: Optional[str] = Field(None, description="Street number")
    streetName: Optional[str] = Field(None, description="Street name")
    municipalitySubdivision: Optional[str] = Field(
        None, description="Neighborhood, district"
    )
    municipality: Optional[str] = Field(None, description="City, town")
    countrySecondarySubdivision: Optional[str] = Field(None, description="County")
    countrySubdivision: Optional[str] = Field(None, description="State, province")
    postalCode: Optional[str] = Field(None, description="ZIP code, postal code")
    country: Optional[str] = Field(None, description="Country name")
    countryCode: str = Field(
        ..., description="Two-letter country code (ISO 3166-1 alpha-2)"
    )
    freeformAddress: Optional[str] = Field(None, description="Full address string")


# --- Geocoding Models ---


class GeocodeResult(BaseModel):
    """Result of a geocoding operation."""

    type: Optional[str] = Field(
        None, description="Type of a GEOMETRY object, e.g., 'Point'"
    )
    position: Position = Field(..., description="Coordinates of the geocoded location")
    address: Address = Field(..., description="Detailed address information")
    match_type: Optional[str] = Field(
        None, description="Match type (e.g., 'Good', 'Rooftop')"
    )
    confidence: Optional[str] = Field(None, description="Confidence level of the match")


# --- Weather Models ---


class WeatherMetric(BaseModel):
    """Represents a weather metric with value and unit."""

    value: float = Field(..., description="Numeric value of the metric")
    unit: str = Field(..., description="Unit of measurement (e.g., 'C', '%')")
    unit_type: Optional[int] = Field(None, description="Unit type identifier")


class WeatherResult(BaseModel):
    """Current weather conditions."""

    date_time: str = Field(
        ..., description="Date and time of the observation (ISO 8601)"
    )
    phrase: str = Field(..., description="Descriptive weather phrase (e.g., 'Cloudy')")
    temperature: WeatherMetric = Field(..., description="Air temperature")
    real_feel_temperature: WeatherMetric = Field(
        ..., description="Apparent temperature"
    )
    relative_humidity: int = Field(
        ..., ge=0, le=100, description="Relative humidity in percent"
    )
    wind_speed: WeatherMetric = Field(..., description="Wind speed")
    uv_index: int = Field(..., ge=0, description="UV index")
    cloud_cover: int = Field(..., ge=0, le=100, description="Cloud cover in percent")
    pressure: WeatherMetric = Field(..., description="Atmospheric pressure")
    precipitation_summary: Optional[Dict[str, WeatherMetric]] = Field(
        None, description="Precipitation summary for past hours"
    )


# --- Nearby Search Models ---


class POIInfo(BaseModel):
    """Point of Interest (POI) information."""

    id: str = Field(..., description="POI identifier")
    name: str = Field(..., description="POI name")
    position: Position = Field(..., description="POI coordinates")
    address: Optional[str] = Field(None, description="Formatted POI address")
    category: Optional[str] = Field(
        None, description="POI category (e.g., 'Restaurant')"
    )
    phone: Optional[str] = Field(None, description="POI phone number")
    url: Optional[str] = Field(None, description="POI website URL")
    distance_meters: Optional[float] = Field(
        None, description="Distance to POI in meters"
    )


class NearbyResult(BaseModel):
    """Results of a nearby search."""

    summary: Dict[str, Any] = Field(
        ..., description="Summary of the search query and results"
    )
    results: List[POIInfo] = Field(..., description="List of found POIs")
    location_count: int = Field(..., description="Total number of locations found")


# --- Routes Models ---


class RouteSummary(BaseModel):
    """Summary of a calculated route."""

    length_in_meters: int = Field(..., description="Route length in meters")
    travel_time_in_seconds: int = Field(
        ..., description="Estimated travel time in seconds"
    )
    traffic_delay_in_seconds: Optional[int] = Field(
        0, description="Traffic delay in seconds"
    )
    departure_time: Optional[str] = Field(None, description="Departure time (ISO 8601)")
    arrival_time: Optional[str] = Field(None, description="Arrival time (ISO 8601)")


class RouteLeg(BaseModel):
    """A leg of a calculated route."""

    summary: RouteSummary = Field(..., description="Summary for this leg")
    points: List[Position] = Field(
        ..., description="List of points defining the leg geometry"
    )


class RouteResult(BaseModel):
    """Result of a route calculation."""

    format_version: Optional[str] = Field(
        None, description="Format version of the response"
    )
    routes: List[Dict[str, Any]] = Field(
        ..., description="List of calculated routes"
    )  # Simplified for now
    # To be more specific, we could define Route object with legs, sections etc.
    # For now, keeping it flexible as the tool focuses on running trails which might have simpler structure.
    trail_count: int = Field(..., description="Number of suitable running trails found")
    deduplicated_trails: int = Field(
        ..., description="Number of trails after deduplication"
    )


# --- Input/Output Models for the Tool ---


class AzureMapsInput(BaseModel):
    """Input model for the AzureMapsTool."""

    command: Literal["geocode", "weather", "nearby", "routes"] = Field(
        ..., description="Azure Maps command to execute"
    )

    # Geocoding specific
    address: Optional[str] = Field(
        None, description="Address to geocode (e.g., '1 Microsoft Way, Redmond, WA')"
    )

    # Weather, Nearby, Routes specific
    lat: Optional[float] = Field(
        None, ge=-90, le=90, description="Latitude for weather, nearby, or route search"
    )
    lon: Optional[float] = Field(
        None,
        ge=-180,
        le=180,
        description="Longitude for weather, nearby, or route search",
    )

    # Nearby specific
    query: Optional[str] = Field(
        None, description="Search query for nearby places (e.g., 'coffee', 'park')"
    )
    radius: Optional[int] = Field(
        None, gt=0, description="Search radius in meters for nearby search (max 50000)"
    )
    limit: Optional[int] = Field(
        None, ge=1, le=100, description="Maximum number of results for nearby search"
    )

    # Routes specific
    route_type: Optional[Literal["running", "walking", "cycling"]] = Field(
        "running", description="Type of route to calculate (running, walking, cycling)"
    )
    max_distance_km: Optional[float] = Field(
        10.0,
        gt=0,
        description="Maximum desired trail distance in kilometers for routes search",
    )

    @model_validator(mode="after")
    def validate_command_specific_fields(self) -> "AzureMapsInput":
        """Validate that required fields are present for each command type."""
        if self.command == "geocode":
            if not self.address:
                raise ValueError("Address is required for geocode command")
        elif self.command in ["weather", "nearby", "routes"]:
            if self.lat is None or self.lon is None:
                raise ValueError(
                    f"Latitude and longitude are required for {self.command} command"
                )
            if self.command == "nearby":
                if not self.query:
                    raise ValueError("Search query is required for nearby command")
                if self.radius and self.radius > 50000:
                    raise ValueError("Search radius cannot exceed 50,000 meters")
            elif self.command == "routes":
                if self.max_distance_km and self.max_distance_km <= 0:
                    raise ValueError("Max distance for routes must be positive")
        return self


class AzureMapsOutput(BaseModel):
    """Output model for the AzureMapsTool."""

    success: bool = Field(
        ..., description="Indicates if the command execution was successful"
    )
    command: str = Field(..., description="The command that was executed")
    error_type: Optional[str] = Field(
        None,
        description="Type of error if success is false (e.g., 'validation_error', 'api_error')",
    )
    message: str = Field(
        ...,
        description="A descriptive message about the outcome (success or error details)",
    )
    request_id: str = Field(
        ..., description="Unique identifier for this request/response cycle"
    )
    execution_time_ms: int = Field(
        ..., description="Total execution time in milliseconds"
    )

    # Command-specific results (only one will be populated)
    geocode_result: Optional[GeocodeResult] = Field(
        None, description="Result of geocoding operation"
    )
    weather_result: Optional[WeatherResult] = Field(
        None, description="Result of weather lookup"
    )
    nearby_result: Optional[NearbyResult] = Field(
        None, description="Result of nearby search"
    )
    routes_result: Optional[RouteResult] = Field(
        None, description="Result of route calculation"
    )

    # Fallback data if primary operation fails but circuit breaker provides a stale/default response
    fallback_response: Optional[Dict[str, Any]] = Field(
        None, description="Fallback data if available"
    )
    retry_after: Optional[int] = Field(
        None,
        description="Suggested seconds to wait before retrying (for rate limiting)",
    )

    @model_validator(mode="after")
    def validate_result_population(self) -> "AzureMapsOutput":
        """Ensure that for a successful operation, the correct result field is populated."""
        if self.success:
            populated_fields = 0
            if self.geocode_result:
                populated_fields += 1
            if self.weather_result:
                populated_fields += 1
            if self.nearby_result:
                populated_fields += 1
            if self.routes_result:
                populated_fields += 1

            if populated_fields == 0 and not self.fallback_response:
                # This case could be valid if the command doesn't produce a specific typed result but was successful.
                # For now, assume one of the typed results or a fallback should be present on success.
                pass  # Or raise ValueError("Successful operation must populate a result field or provide fallback data")
            elif populated_fields > 1:
                raise ValueError(
                    "Only one result field should be populated for a successful operation"
                )
        return self
