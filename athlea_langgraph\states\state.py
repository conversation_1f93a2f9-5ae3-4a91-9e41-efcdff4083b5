"""
Core state definitions for the Athlea LangGraph coaching system.

This module contains the base state classes and reducers used throughout
the coaching graph implementation.
"""

from typing import Annotated, Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
from typing_extensions import TypedDict


# Define the reducer for messages
def messages_reducer(
    left: List[BaseMessage], right: List[BaseMessage]
) -> List[BaseMessage]:
    """Reducer for combining message lists."""
    return add_messages(left, right)


# Core state definition
class AgentState(TypedDict):
    """
    Core state for the Athlea coaching system.

    This is the base state that all other states extend from.
    """

    # Core conversation fields
    messages: Annotated[List[BaseMessage], messages_reducer]
    user_query: Optional[str]
    user_profile: Optional[Dict[str, Any]]

    # Routing and planning fields
    routing_decision: Optional[Union[str, List[str]]]
    pending_agents: Optional[List[str]]
    plan: Optional[List[str]]
    current_step: Optional[int]

    # Domain coordination fields
    domain_contributions: Dict[str, str]
    required_domains: List[str]
    completed_domains: List[str]
    aggregated_plan: Optional[str]
    proceed_to_generation: bool
    current_plan: Optional[Any]

    # System flags
    is_onboarding: bool

    # Specialist coach responses
    strength_response: Optional[str]
    running_response: Optional[str]
    cardio_response: Optional[str]
    cycling_response: Optional[str]
    nutrition_response: Optional[str]
    recovery_response: Optional[str]
    mental_response: Optional[str]

    # Other node outputs
    reasoning_output: Optional[str]
    clarification_output: Optional[str]
    aggregated_response: Optional[str]


# Additional reducers for specific fields
def user_profile_reducer(
    left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    """Reducer for user profile updates."""
    if right is None:
        return left
    if left is None:
        return right
    return {**left, **right}


def routing_decision_reducer(
    left: Optional[Union[str, List[str]]], right: Optional[Union[str, List[str]]]
) -> Optional[Union[str, List[str]]]:
    """Reducer for routing decisions."""
    return right if right is not None else left


def pending_agents_reducer(
    left: Optional[List[str]], right: Optional[List[str]]
) -> Optional[List[str]]:
    """Reducer for pending agents list."""
    return right if right is not None else left


def plan_reducer(
    left: Optional[List[str]], right: Optional[List[str]]
) -> Optional[List[str]]:
    """Reducer for plan updates."""
    return right if right is not None else left


def current_step_reducer(left: Optional[int], right: Optional[int]) -> Optional[int]:
    """Reducer for current step updates."""
    return right if right is not None else left


def domain_contributions_reducer(
    left: Dict[str, str], right: Dict[str, str]
) -> Dict[str, str]:
    """Reducer for domain contributions."""
    return {**left, **right}


def required_domains_reducer(left: List[str], right: List[str]) -> List[str]:
    """Reducer for required domains list."""
    return right if right else left


def completed_domains_reducer(left: List[str], right: List[str]) -> List[str]:
    """Reducer for completed domains list."""
    return right if right else left


def aggregated_plan_reducer(left: Optional[str], right: Optional[str]) -> Optional[str]:
    """Reducer for aggregated plan updates."""
    return right if right is not None else left


def proceed_to_generation_reducer(left: bool, right: bool) -> bool:
    """Reducer for proceed to generation flag."""
    return right


def current_plan_reducer(left: Optional[Any], right: Optional[Any]) -> Optional[Any]:
    """Reducer for current plan updates."""
    return right if right is not None else left


def is_onboarding_reducer(left: bool, right: bool) -> bool:
    """Reducer for onboarding flag."""
    return right


# Alias for backward compatibility
GraphState = AgentState


__all__ = [
    "AgentState",
    "GraphState",
    "messages_reducer",
    "user_profile_reducer",
    "routing_decision_reducer",
    "pending_agents_reducer",
    "plan_reducer",
    "current_step_reducer",
    "domain_contributions_reducer",
    "required_domains_reducer",
    "completed_domains_reducer",
    "aggregated_plan_reducer",
    "proceed_to_generation_reducer",
    "current_plan_reducer",
    "is_onboarding_reducer",
]
