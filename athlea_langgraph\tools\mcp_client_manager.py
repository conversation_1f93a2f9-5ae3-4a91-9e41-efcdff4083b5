"""
MCP Client Manager

Manages connections to MCP servers and provides tools to agents.
Uses the official langchain-mcp-adapters for seamless integration.
"""

import asyncio
import logging
import sys
from typing import Any, Dict, List, Optional

from langchain_core.tools import BaseTool

# Official LangChain MCP integration
try:
    from langchain_mcp_adapters.client import MultiServerMCPClient
    from langchain_mcp_adapters.tools import load_mcp_tools

    MCP_AVAILABLE = True
except ImportError:
    logging.warning(
        "Official LangChain MCP adapters not available. Install with: pip install langchain-mcp-adapters"
    )
    MCP_AVAILABLE = False

logger = logging.getLogger(__name__)


class MCPClientManager:
    """
    Manages MCP server connections and tool retrieval using official LangChain adapters.

    Provides a centralized way to connect to multiple MCP servers
    and retrieve tools for different coaching domains.
    """

    def __init__(self):
        self.client: Optional[MultiServerMCPClient] = None
        self.tools_cache: Dict[str, List[BaseTool]] = {}
        self.initialized = False

        # Correctly configure the stdio-based MCP servers to be launched by the client.
        self.server_config = {
            "strength": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.strength_mcp.server"],
                "transport": "stdio",
            },
            "cardio": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.cardio_mcp.server"],
                "transport": "stdio",
            },
            "nutrition": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.nutrition_mcp.server"],
                "transport": "stdio",
            },
            "recovery": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.recovery_mcp.server"],
                "transport": "stdio",
            },
            "mental": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.mental_mcp.server"],
                "transport": "stdio",
            },
            "external": {
                "command": sys.executable,
                "args": ["-m", "mcp_servers.external_mcp.server"],
                "transport": "stdio",
            },
        }

    async def initialize(self) -> bool:
        """Initialize MCP client connections using official adapters."""
        if not MCP_AVAILABLE:
            logger.warning(
                "MCP adapters not available - skipping MCP tool initialization"
            )
            return False

        try:
            logger.info("🔌 Initializing official LangChain MCP client connections...")

            # Create MultiServerMCPClient with our server configuration
            self.client = MultiServerMCPClient(self.server_config)

            # Load tools from all configured servers
            await self._load_all_tools()

            self.initialized = True
            logger.info(
                f"✅ Official MCP client initialized with {len(self.tools_cache)} tool categories"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to initialize official MCP client: {e}")
            return False

    async def _load_all_tools(self, max_retries: int = 3, base_delay: float = 1.0):
        """Load tools from all configured MCP servers using official adapters with retry mechanism."""
        if not self.client:
            return

        # Retry mechanism with exponential backoff
        for attempt in range(max_retries + 1):
            try:
                logger.info(
                    f"🔧 Loading MCP tools (attempt {attempt + 1}/{max_retries + 1})..."
                )

                # Add delay between attempts (except first)
                if attempt > 0:
                    delay = base_delay * (2 ** (attempt - 1))
                    logger.info(f"⏱️ Waiting {delay}s before retry...")
                    await asyncio.sleep(delay)

                # Get all tools from all configured servers with timeout
                all_tools = await asyncio.wait_for(
                    self.client.get_tools(), timeout=15.0
                )
                self.tools_cache["all"] = all_tools

                # Categorize tools based on their names and descriptions
                location_tools = []
                data_tools = []
                external_tools = []

                for tool in all_tools:
                    tool_name = tool.name.lower()
                    tool_desc = tool.description.lower() if tool.description else ""

                    # Categorize based on tool names and descriptions
                    if any(
                        keyword in tool_name or keyword in tool_desc
                        for keyword in [
                            "location",
                            "map",
                            "route",
                            "direction",
                            "facility",
                            "search_location",
                        ]
                    ):
                        location_tools.append(tool)
                    elif any(
                        keyword in tool_name or keyword in tool_desc
                        for keyword in [
                            "airtable",
                            "search",
                            "query",
                            "web",
                            "weather",
                            "wikipedia",
                        ]
                    ):
                        data_tools.append(tool)
                    else:
                        external_tools.append(tool)

                # Store categorized tools
                self.tools_cache["location"] = location_tools
                self.tools_cache["data"] = data_tools
                self.tools_cache["external"] = external_tools

                logger.info(f"✅ Successfully loaded {len(all_tools)} total MCP tools:")
                logger.info(f"   📍 Location tools: {len(location_tools)}")
                logger.info(f"   📊 Data tools: {len(data_tools)}")
                logger.info(f"   🔌 External tools: {len(external_tools)}")

                return  # Success, exit retry loop

            except asyncio.TimeoutError:
                logger.warning(f"⏱️ Timeout loading MCP tools on attempt {attempt + 1}")
                if attempt == max_retries:
                    logger.error("❌ All attempts to load MCP tools timed out")
                    break
                continue

            except Exception as e:
                logger.warning(f"⚠️ MCP tool loading attempt {attempt + 1} failed: {e}")
                if attempt == max_retries:
                    logger.error(
                        f"❌ All {max_retries + 1} attempts to load MCP tools failed"
                    )
                    break
                continue

        # Initialize empty caches on failure
        logger.info("🔄 Initializing with empty tool caches due to connection failures")
        self.tools_cache = {"all": [], "location": [], "data": [], "external": []}

    async def get_all_tools(self) -> List[BaseTool]:
        """Get all tools from all MCP servers."""
        if not self.initialized:
            await self.initialize()
        return self.tools_cache.get("all", [])

    async def get_external_tools(self) -> List[BaseTool]:
        """Get external service tools (Maps, Airtable, Search, etc.)"""
        if not self.initialized:
            await self.initialize()
        return self.tools_cache.get("external", [])

    async def get_location_tools(self) -> List[BaseTool]:
        """Get location and mapping tools for route planning."""
        if not self.initialized:
            await self.initialize()
        return self.tools_cache.get("location", [])

    async def get_data_tools(self) -> List[BaseTool]:
        """Get data query tools (Airtable, Search, etc.)"""
        if not self.initialized:
            await self.initialize()
        return self.tools_cache.get("data", [])

    async def get_tools_for_agent(self, agent_domain: str) -> List[BaseTool]:
        """
        Get MCP tools relevant to a specific agent domain.

        Args:
            agent_domain: The agent's domain (strength, nutrition, cardio, etc.)

        Returns:
            List of relevant MCP tools for the agent
        """
        # Ensure tools are loaded
        if not self.initialized:
            await self.initialize()

        domain_tool_mapping = {
            "strength": await self.get_location_tools(),  # Gym finding, route planning
            "nutrition": await self.get_data_tools(),  # Recipe search, nutrition data
            "cardio": [
                *await self.get_location_tools(),  # Route planning, elevation
                *[
                    tool
                    for tool in await self.get_data_tools()
                    if "weather" in tool.name.lower()
                ],  # Weather for outdoor training
            ],
            "recovery": await self.get_data_tools(),  # Research tools
            "mental": await self.get_data_tools(),  # Research and resource tools
        }

        tools = domain_tool_mapping.get(agent_domain, [])
        logger.info(f"🎯 Retrieved {len(tools)} MCP tools for {agent_domain} agent")
        return tools

    def is_available(self) -> bool:
        """Check if official MCP integration is available and initialized."""
        return MCP_AVAILABLE and self.initialized

    async def close(self):
        """Clean up MCP client connections."""
        if self.client:
            try:
                # The official client handles cleanup automatically
                # but we can explicitly close if needed
                pass
            except Exception as e:
                logger.error(f"Error closing official MCP client: {e}")


# Global MCP client manager instance
_mcp_client_manager = MCPClientManager()


async def get_mcp_client_manager() -> MCPClientManager:
    """Get the global MCP client manager instance using official adapters."""
    if not _mcp_client_manager.initialized:
        await _mcp_client_manager.initialize()
    return _mcp_client_manager
