#!/usr/bin/env python3
"""
Test LangSmith API Methods

Quick test to understand the exact signature of LangSmith's push_prompt method.
"""

import os
import sys
import inspect

try:
    from langsmith import Client
    from langchain_core.prompts import PromptTemplate
except ImportError as e:
    print(f"❌ Error: {e}")
    sys.exit(1)


def main():
    api_key = os.getenv("LANGSMITH_API_KEY")
    if not api_key:
        print("❌ Error: LANGSMITH_API_KEY environment variable not set.")
        sys.exit(1)

    try:
        client = Client()
        print("✅ Connected to LangSmith")

        # Inspect the push_prompt method signature
        push_prompt_method = client.push_prompt
        signature = inspect.signature(push_prompt_method)

        print(f"\n🔍 push_prompt method signature:")
        print(f"   {push_prompt_method.__name__}{signature}")

        # Get the docstring
        docstring = push_prompt_method.__doc__
        if docstring:
            print(f"\n📖 Documentation:")
            print(f"   {docstring[:300]}...")

        # Test with a simple prompt
        test_prompt = PromptTemplate.from_template("Hello {name}!")

        print(f"\n🧪 Testing simple push_prompt call...")
        try:
            # Try the simplest possible call
            result = client.push_prompt(test_prompt)
            print(f"✅ Simple push_prompt worked: {result}")
        except Exception as e:
            print(f"❌ Simple push_prompt failed: {e}")

        print(f"\n🧪 Testing push_prompt with repo_handle...")
        try:
            # Try with repo_handle
            result = client.push_prompt("test_prompt_name", test_prompt)
            print(f"✅ push_prompt with repo_handle worked: {result}")
        except Exception as e:
            print(f"❌ push_prompt with repo_handle failed: {e}")

    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
