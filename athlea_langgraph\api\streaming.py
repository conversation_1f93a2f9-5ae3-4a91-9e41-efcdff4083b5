print("🍓🍓🍓 EXECUTING LATEST STREAMING.PY 🍓🍓🍓")
"""
Streaming implementation for Comprehensive Coaching Graph

This module provides Server-Sent Events (SSE) streaming capabilities
that work with the Next.js frontend expectations, including proper
agent identification and streaming patterns.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional
import os
import time

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.graph.state import CompiledStateGraph

from ..graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_coaching_graph as create_comprehensive_coaching_graph,
    ComprehensiveCoachingConfig,
)
from ..graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_coaching_graph,
)
from ..states.state import AgentState

logger = logging.getLogger(__name__)


class StreamingEventType:
    """Constants for streaming event types that match frontend expectations."""

    AGENT_START = "agent_start"
    TOKEN = "token"
    TOOL_RESULT = "tool_result"
    NEED_INPUT = "need_input"
    COMPLETE = "complete"
    ERROR = "error"
    SIDEBAR_UPDATE = "sidebar_update"


class ComprehensiveCoachingStreamer:
    """
    Handles streaming of comprehensive coaching graph execution to match frontend expectations.

    Provides proper SSE formatting with agent identification for each token.
    """

    def __init__(self, graph: CompiledStateGraph):
        self.graph = graph
        self.current_agent = None

        # Nodes that should never stream to frontend (internal processing only)
        self.internal_nodes = {
            "complexity_assessment",  # Internal routing decision node
            "simple_router",  # Internal routing node
            "complex_router",  # Internal routing node
        }

        # Agent mappings for nodes that should stream to frontend
        self.agent_mappings = {
            # Core reasoning and planning
            "reasoning": "reasoning",
            "planning": "planner",
            "intelligence_hub": "Athlea",  # New optimized node - maps to Athlea for analysis
            # Remove complexity_assessment from streaming - this should be internal only
            # "complexity_assessment": "Athlea",  # REMOVED - this should not be streamed to frontend
            # Remove simple_router from streaming - this should be internal only
            # "simple_router": "Athlea",  # REMOVED - this should not be streamed to frontend
            "complex_planner": "planner",
            "complex_executor": "Athlea",
            "complex_router": "Athlea",
            "smart_executor": "Athlea",  # New optimized node for consolidated coach execution
            "optimized_greeting": "Athlea",  # New optimized greeting node
            # Sport-specific coaches - map to exact domain names expected by frontend
            # Frontend expects: strength, running, cycling, nutrition, recovery, mental
            "cycling_coach": "cycling",
            "running_coach": "running",  # Maps to "running"
            "cardio_coach": "cardio",  # Maps to "cardio"
            "strength_coach": "strength",
            "nutrition_coach": "nutrition",
            "recovery_coach": "recovery",
            "mental_coach": "mental",
            # Additional mappings for any other agents
            "general": "general",
            "finalizer": "finalizer",
            "head": "head",
            # Enable aggregation streaming as Athlea Coach (synthesis response)
            "aggregation": "Athlea",  # Enabled - streams synthesized response as Athlea Coach
            "clarification": "Athlea",
            "reflection": "Athlea",
        }

    def format_sse_event(self, event_type: str, data: Dict[str, Any]) -> str:
        """Format data as Server-Sent Events."""
        json_data = json.dumps(data)
        return f"event: {event_type}\ndata: {json_data}\n\n"

    async def stream_coaching_response(
        self, user_message: str, thread_id: str, user_id: str, config=None
    ) -> AsyncGenerator[str, None]:
        """Stream coaching response with proper LangGraph token streaming."""
        logger.info(f"🚀 BACKEND: [STREAM_START] Starting coaching stream")
        logger.info(
            f"📊 BACKEND: [STREAM_START] Request details: user_id={user_id}, thread_id={thread_id}, message_length={len(user_message)}"
        )
        logger.info(
            f"💬 BACKEND: [STREAM_START] User message preview: {user_message[:100]}{'...' if len(user_message) > 100 else ''}"
        )

        # Log agent mappings for debugging
        logger.info(f"🗺️ BACKEND: [STREAM_MAPPINGS] Available agent mappings:")
        for node_name, agent_domain in self.agent_mappings.items():
            logger.info(f"  📌 {node_name} -> {agent_domain}")

        # Add timeout for the entire streaming operation
        timeout_seconds = 120  # 2 minutes timeout
        start_time = time.time()

        try:
            # Create input state
            input_state = {
                "messages": [HumanMessage(content=user_message)],
                "user_id": user_id,
                "thread_id": thread_id,
                "enable_human_feedback": False,  # Disable interrupts for streaming
            }

            logger.info(
                f"🔧 BACKEND: [STREAM] Created input state with {len(input_state['messages'])} messages"
            )

            # Configure RunnableConfig for the graph
            graph_config = {"configurable": {"thread_id": thread_id}}
            if config:
                graph_config.update(config)

            logger.info(f"⚙️ BACKEND: [STREAM] Graph config: {graph_config}")

            # Use astream_events with version="v2" for proper token streaming
            # This is the key to getting individual tokens instead of buffered responses
            logger.info(
                f"🎯 BACKEND: [STREAM] Starting graph execution with token streaming..."
            )
            logger.info(
                f"🔍 BACKEND: [STREAM] Watching for events from these mapped nodes: {list(self.agent_mappings.keys())}"
            )

            event_count = 0
            agent_starts_sent = set()
            nodes_processed = set()
            token_counts_by_node = {}
            event_types_seen = set()
            last_event_time = time.time()
            stall_timeout = 30  # 30 seconds without events is considered a stall

            # Send initial heartbeat
            yield f"event: heartbeat\ndata: {json.dumps({'status': 'starting'})}\n\n"

            # Use astream_events v2 for proper token streaming
            async for event in self.graph.astream_events(
                input_state, graph_config, version="v2"
            ):
                # Check for overall timeout
                elapsed_time = time.time() - start_time
                if elapsed_time > timeout_seconds:
                    logger.error(
                        f"❌ BACKEND: [TIMEOUT] Stream exceeded {timeout_seconds}s timeout"
                    )
                    yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Request timeout - please try again'})}\n\n"
                    break

                # Check for stall (no events for too long)
                time_since_last_event = time.time() - last_event_time
                if time_since_last_event > stall_timeout:
                    logger.error(f"❌ BACKEND: [STALL] No events for {stall_timeout}s")
                    yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Processing stalled - please try again'})}\n\n"
                    break

                last_event_time = time.time()
                event_count += 1
                event_type = event.get("event", "unknown")
                event_name = event.get("name", "unknown")
                event_types_seen.add(event_type)

                # Send periodic heartbeats
                if event_count % 50 == 0:
                    yield f"event: heartbeat\ndata: {json.dumps({'status': 'processing', 'events': event_count})}\n\n"

                if event_count % 10 == 0:  # Log every 10th event to avoid spam
                    logger.info(
                        f"📈 BACKEND: [STREAM] Processed {event_count} events so far. Event types seen: {sorted(event_types_seen)}"
                    )

                try:
                    # Log event details
                    event_type = event.get("event", "unknown")
                    event_name = event.get("name", "unknown")

                    # Enhanced event logging
                    logger.debug(
                        f"🔄 BACKEND: [EVENT] #{event_count} Type: {event_type}, Name: {event_name}"
                    )

                    # Log ALL events that mention mapped node names (even non-streaming ones)
                    if event_name in self.agent_mappings:
                        logger.info(
                            f"🎪 BACKEND: [NODE_ACTIVITY] Event '{event_type}' from mapped node: '{event_name}' -> '{self.agent_mappings[event_name]}'"
                        )
                    elif event_name in self.internal_nodes:
                        logger.debug(
                            f"⚫ BACKEND: [INTERNAL_ACTIVITY] Event '{event_type}' from internal node: '{event_name}' (not streamed)"
                        )

                    # Also check metadata for node names
                    metadata = event.get("metadata", {})
                    metadata_node = metadata.get("langgraph_node") or metadata.get(
                        "name"
                    )
                    if metadata_node and metadata_node != event_name:
                        if metadata_node in self.agent_mappings:
                            logger.info(
                                f"📋 BACKEND: [NODE_ACTIVITY] Event '{event_type}' has metadata node: '{metadata_node}' -> '{self.agent_mappings[metadata_node]}'"
                            )
                        elif metadata_node in self.internal_nodes:
                            logger.debug(
                                f"⚫ BACKEND: [INTERNAL_ACTIVITY] Event '{event_type}' has metadata node: '{metadata_node}' (internal, not streamed)"
                            )

                    # Handle on_chat_model_stream events - this is where individual tokens come from
                    if event_type == "on_chat_model_stream":
                        # This is the key event type for token streaming
                        chunk_data = event.get("data", {})
                        chunk = chunk_data.get("chunk")

                        # Get the node name to determine which agent is streaming
                        tags = event.get("tags", [])
                        metadata = event.get("metadata", {})

                        # Enhanced node detection logging
                        logger.debug(f"🏷️ BACKEND: [NODE_DETECTION] Event tags: {tags}")
                        logger.debug(
                            f"📋 BACKEND: [NODE_DETECTION] Event metadata: {metadata}"
                        )

                        # Priority order for node detection:
                        # 1. First try metadata node detection (most reliable for graph nodes)
                        # 2. Then try tags
                        # 3. Finally fall back to event name (least reliable, usually just LLM class names)
                        node_name = None
                        detection_method = None

                        # Method 1: Check metadata for langgraph node
                        if metadata.get("langgraph_node"):
                            node_name = metadata["langgraph_node"]
                            detection_method = "metadata/langgraph_node"

                        # Method 2: Check other metadata fields
                        elif metadata.get("node"):
                            node_name = metadata["node"]
                            detection_method = "metadata/node"

                        # Method 3: Check tags for node names
                        elif tags:
                            for tag in tags:
                                if tag in self.agent_mappings:
                                    node_name = tag
                                    detection_method = "tags"
                                    break

                        # Method 4: Fall back to event name (usually just LLM class name)
                        if not node_name:
                            node_name = event.get("name", "unknown")
                            detection_method = "event_name (fallback)"

                        logger.info(
                            f"🎯 BACKEND: [NODE_DETECTION] Detected node: '{node_name}' via {detection_method}"
                        )

                        # First check: Is this an internal node? If yes, skip entirely
                        if node_name in self.internal_nodes:
                            logger.debug(
                                f"⚫ BACKEND: [INTERNAL_SKIP] Skipping internal node: '{node_name}'"
                            )
                            continue

                        # Second check: Do we have a mapping for this node?
                        agent_domain = None
                        if node_name in self.agent_mappings:
                            # Map to agent domain
                            agent_domain = self.agent_mappings[node_name]
                            logger.info(
                                f"✅ BACKEND: [NODE_MAPPING] '{node_name}' -> '{agent_domain}' (mapped)"
                            )
                        else:
                            # Not an internal node and not mapped - fall back to general
                            agent_domain = "general"
                            logger.warning(
                                f"⚠️ BACKEND: [NODE_MAPPING] '{node_name}' -> '{agent_domain}' (fallback to general)"
                            )

                        # Track first token for agent start events
                        if node_name not in agent_starts_sent:
                            agent_starts_sent.add(node_name)
                            logger.info(
                                f"🆕 BACKEND: [NODE_TRACKING] First token from node: '{node_name}' -> agent: '{agent_domain}'"
                            )
                            yield f"data: {json.dumps({'type': 'agent_start', 'agent': agent_domain})}\n\n"
                            logger.info(
                                f"📤 BACKEND: [SSE_SEND] Agent start: '{agent_domain}' (triggered by node: '{node_name}')"
                            )

                        # Stream the token if we have chunk content
                        if chunk and hasattr(chunk, "content") and chunk.content:
                            yield f"data: {json.dumps({'type': 'token', 'content': chunk.content, 'agent': agent_domain})}\n\n"

                            # Update token tracking
                            if node_name not in token_counts_by_node:
                                token_counts_by_node[node_name] = 0
                            token_counts_by_node[node_name] += 1

                            # Log progress every 10 tokens
                            if token_counts_by_node[node_name] % 10 == 0:
                                logger.info(
                                    f"📊 BACKEND: [TOKEN_PROGRESS] Node: '{node_name}' has sent {token_counts_by_node[node_name]} tokens to agent: '{agent_domain}'"
                                )

                    # Handle on_chain_start events for agent routing (backup method)
                    elif (
                        event_type == "on_chain_start"
                        and event_name in self.agent_mappings
                    ):
                        agent_domain = self.agent_mappings[event_name]

                        logger.info(
                            f"🚀 BACKEND: [AGENT_START] Chain start detected - Agent starting: '{event_name}' -> '{agent_domain}'"
                        )

                        if agent_domain not in agent_starts_sent:
                            agent_starts_sent.add(agent_domain)
                            sse_event = f'event: agent_start\ndata: {{"agent": "{agent_domain}"}}\n\n'
                            logger.info(
                                f"📤 BACKEND: [SSE_SEND] Agent start: '{agent_domain}' (via chain_start for '{event_name}')"
                            )
                            yield sse_event

                    # Handle completion events
                    elif event_type == "on_chain_end" and event_name in [
                        "comprehensive_coaching_graph",
                        "optimized_comprehensive_coaching_graph",
                        "aggregation",
                        "smart_executor",
                        "optimized_greeting",
                    ]:
                        logger.info(
                            f"✅ BACKEND: [COMPLETE] Graph execution completed: '{event_name}'"
                        )

                        sse_event = f'event: complete\ndata: {{"message": "Response completed"}}\n\n'
                        logger.info(f"📤 BACKEND: [SSE_SEND] Completion event sent")
                        yield sse_event
                        break

                    # Log other notable events
                    elif event_type in ["on_chain_start", "on_chain_end"]:
                        logger.debug(
                            f"🔗 BACKEND: [CHAIN_EVENT] {event_type}: '{event_name}'"
                        )

                    # Log any events with unmapped node names that could be relevant
                    elif (
                        event_name
                        and event_name not in self.agent_mappings
                        and event_type == "on_chat_model_stream"
                    ):
                        logger.warning(
                            f"🔍 BACKEND: [UNMAPPED_NODE] Found streaming event from unmapped node: '{event_name}' (type: {event_type})"
                        )

                except Exception as e:
                    logger.error(
                        f"❌ BACKEND: [EVENT_ERROR] Error processing event #{event_count}: {e}"
                    )
                    logger.error(f"🔍 BACKEND: [EVENT_ERROR] Event data: {event}")
                    # Don't break on individual event errors - continue processing
                    continue

            # Final summary logging
            logger.info(
                f"🏁 BACKEND: [STREAM_END] Completed streaming, total events processed: {event_count}"
            )
            logger.info(
                f"📊 BACKEND: [STREAM_SUMMARY] Agent starts sent: {list(agent_starts_sent)}"
            )
            logger.info(
                f"🎭 BACKEND: [STREAM_SUMMARY] Nodes that produced tokens: {list(nodes_processed)}"
            )

            # Log token counts per node
            if token_counts_by_node:
                logger.info(f"📈 BACKEND: [TOKEN_SUMMARY] Token counts by node:")
                for node_name, count in token_counts_by_node.items():
                    agent_domain = self.agent_mappings.get(node_name, "general")
                    logger.info(f"  🎯 {node_name} -> {agent_domain}: {count} tokens")
            else:
                logger.warning(
                    f"⚠️ BACKEND: [TOKEN_SUMMARY] No tokens were produced by any nodes!"
                )

            # If no tokens were produced, send a fallback response
            if not token_counts_by_node:
                logger.warning(
                    "⚠️ BACKEND: [FALLBACK] No response generated, sending fallback"
                )
                fallback_response = await self._fallback_coach_response(user_message)
                yield f"data: {json.dumps({'type': 'agent_start', 'agent': 'Athlea'})}\n\n"
                yield f"data: {json.dumps({'type': 'token', 'content': fallback_response, 'agent': 'Athlea'})}\n\n"
                yield f"event: complete\ndata: {json.dumps({'message': 'Response completed (fallback)'})}\n\n"

        except asyncio.CancelledError:
            logger.warning("⚠️ BACKEND: [STREAM_CANCELLED] Stream was cancelled")
            yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Stream cancelled'})}\n\n"
            raise

        except Exception as e:
            logger.error(f"❌ BACKEND: [STREAM_ERROR] Fatal error in streaming: {e}")
            logger.exception("Full traceback:")

            # Send error event
            error_event = f'event: error\ndata: {{"message": "Backend streaming error: {str(e)}"}}\n\n'
            yield error_event

        finally:
            # Always send a final heartbeat to indicate stream is closing
            total_time = time.time() - start_time
            logger.info(
                f"⏱️ BACKEND: [STREAM_COMPLETE] Total streaming time: {total_time:.2f}s"
            )
            yield f"event: heartbeat\ndata: {json.dumps({'status': 'completed', 'total_time': total_time})}\n\n"

    def _get_agent_for_node(self, node_name: str) -> Optional[str]:
        """Map node names to agent names."""
        clean_name = node_name.split(":")[-1] if ":" in node_name else node_name
        return self.agent_mappings.get(clean_name)

    def _extract_response_content(
        self, state_update: Dict[str, Any], node_name: str
    ) -> Optional[str]:
        """Extract response content from state update."""
        if not state_update or not isinstance(state_update, dict):
            return None

        # Check for final_response first (comprehensive graph)
        if "final_response" in state_update and state_update["final_response"]:
            content = state_update["final_response"]
            if isinstance(content, str) and content.strip():
                return content

        # Check for coach-specific responses
        coach_response_fields = [
            f"{node_name}_response",
            "clarification_response",
            "aggregated_response",
        ]

        try:
            for field in coach_response_fields:
                if field in state_update and state_update[field]:
                    content = state_update[field]
                    if isinstance(content, str) and content.strip():
                        return content

            # Check coach_responses dict
            if "coach_responses" in state_update and state_update["coach_responses"]:
                coach_responses = state_update["coach_responses"]
                if isinstance(coach_responses, dict):
                    for coach, response in coach_responses.items():
                        if response and isinstance(response, str) and response.strip():
                            return response

        except (TypeError, AttributeError, KeyError):
            pass

        return None

    def _determine_agent_from_message(self, user_message: str) -> str:
        """Determine appropriate agent based on user message content."""
        if not user_message or not isinstance(user_message, str):
            return "Athlea"

        message_lower = user_message.lower()

        try:
            if any(
                word in message_lower
                for word in ["strength", "muscle", "lift", "weights"]
            ):
                return "strength"
            elif any(
                word in message_lower for word in ["nutrition", "diet", "food", "eat"]
            ):
                return "nutrition"
            elif any(word in message_lower for word in ["cardio", "running", "run"]):
                return "cardio"
            elif any(word in message_lower for word in ["cycle", "cycling", "bike"]):
                return "cycling"
            elif any(word in message_lower for word in ["recovery", "rest", "sleep"]):
                return "recovery"
            elif any(
                word in message_lower for word in ["mental", "mindset", "motivation"]
            ):
                return "mental"
            else:
                return "Athlea"
        except (TypeError, AttributeError):
            return "Athlea"

    async def _fallback_coach_response(self, user_message: str) -> str:
        """Provide a fallback response when the main graph fails."""
        agent_type = self._determine_agent_from_message(user_message)

        fallback_responses = {
            "strength": f"Great question about strength training! For building strength, focus on compound movements like squats, deadlifts, and bench presses. Start with proper form, then gradually increase weight. What's your current experience level with strength training?",
            "nutrition": f"Excellent question about nutrition! A balanced approach with lean proteins, whole grains, healthy fats, and plenty of fruits and vegetables is key. Stay hydrated and consider your activity level when planning meals. What are your specific nutrition goals?",
            "cardio": f"Good question about cardio! The best cardio depends on your goals - HIIT for efficiency, steady-state for endurance, or activities you enjoy for consistency. What's your current fitness level and what do you enjoy doing?",
            "cycling": f"Great interest in cycling! Whether for fitness or transport, cycling is excellent cardio. Focus on proper bike fit, gradual distance increases, and consistent training. What type of cycling interests you most?",
            "recovery": f"Recovery is crucial for progress! Focus on quality sleep (7-9 hours), proper hydration, gentle stretching, and managing stress. Listen to your body and don't underestimate rest days. How's your current recovery routine?",
            "mental": f"Mental training is as important as physical! Visualization, goal setting, positive self-talk, and mindfulness can boost performance. Consistency in mental practices builds mental strength. What mental challenges are you facing?",
            "Athlea": f"I'm here to help with your fitness journey! Whether it's strength training, nutrition, cardio, or any other aspect of health and fitness, I can provide personalized guidance. What specific area would you like to focus on today?",
        }

        return fallback_responses.get(agent_type, fallback_responses["Athlea"])


async def create_coaching_streamer(
    mongodb_uri: str, user_id: str
) -> ComprehensiveCoachingStreamer:
    """
    Factory function to create a coaching streamer with the comprehensive graph.

    Args:
        mongodb_uri: MongoDB connection string
        user_id: User identifier

    Returns:
        Configured ComprehensiveCoachingStreamer instance
    """
    # Check if optimized graph is enabled
    use_optimized = os.getenv("USE_OPTIMIZED_GRAPH", "true").lower() == "true"
    graph_type = "optimized" if use_optimized else "original"

    logger.info(f"🏗️ Creating {graph_type} coaching graph for user: {user_id}")

    # Create comprehensive coaching config
    config = ComprehensiveCoachingConfig(
        user_id=user_id,
        mongodb_uri=mongodb_uri,
        enable_memory=True,
        use_react_agents=True,
        max_iterations=5,
        enable_human_feedback=False,  # Disable for streaming
        enable_reflexion=True,
        max_reflexion_iterations=2,
        complexity_threshold=0.6,
    )

    # Create the appropriate coaching graph
    if use_optimized:
        logger.info("🚀 Using OPTIMIZED coaching graph with Intelligence Hub")
        graph = await create_optimized_coaching_graph(config)
    else:
        logger.info("📊 Using ORIGINAL comprehensive coaching graph")
        graph = await create_comprehensive_coaching_graph(config)

    logger.info(
        f"✅ {graph_type.title()} graph created successfully with {len(graph.nodes)} nodes"
    )

    return ComprehensiveCoachingStreamer(graph)


# Convenience functions for common streaming patterns
async def stream_coaching_response(
    user_message: str,
    thread_id: str,
    user_id: str,
    mongodb_uri: str,
    user_data: Optional[Dict[str, Any]] = None,
    user_input: Optional[str] = None,
) -> AsyncGenerator[str, None]:
    """
    Convenience function for streaming a single coaching response with enhanced logging.

    Args:
        user_message: The user's message/query
        thread_id: Unique thread ID for session continuity
        user_id: User identifier
        mongodb_uri: MongoDB connection string
        user_data: User training profile and plan data from MongoDB
        user_input: Resume input for continuing interrupted sessions

    Yields:
        SSE-formatted strings with coaching responses
    """
    logger.info(
        f"🎯 BACKEND: [MAIN_STREAM] Processing coaching request for user: {user_id}"
    )
    logger.info(
        f"📊 BACKEND: [MAIN_STREAM] Request details: thread_id={thread_id}, message_length={len(user_message)}"
    )
    logger.info(
        f"💬 BACKEND: [MAIN_STREAM] User message preview: {user_message[:100]}{'...' if len(user_message) > 100 else ''}"
    )

    # Check if optimized graph is enabled
    use_optimized = os.getenv("USE_OPTIMIZED_GRAPH", "true").lower() == "true"
    graph_type = "optimized" if use_optimized else "original"

    logger.info(f"🚀 BACKEND: [MAIN_STREAM] Using {graph_type} coaching graph")

    # Create the comprehensive coaching graph
    config = ComprehensiveCoachingConfig(
        user_id=user_id,
        mongodb_uri=mongodb_uri,
        thread_id=thread_id,
        enable_memory=True,
        use_react_agents=True,
        max_iterations=5,
        enable_human_feedback=False,  # Ensure this is False for non-interactive streaming
        enable_reflexion=True,
    )

    # Create the appropriate coaching graph
    if use_optimized:
        graph = await create_optimized_coaching_graph(config)
    else:
        graph = await create_comprehensive_coaching_graph(config)
    streamer = ComprehensiveCoachingStreamer(graph)

    logger.info(
        f"🔧 BACKEND: [MAIN_STREAM] Created graph and streamer for user: {user_id}"
    )

    # Prepare user profile context
    user_profile = {}
    if user_data and (
        user_data.get("training_profile") or user_data.get("current_plan")
    ):
        if user_data.get("training_profile"):
            user_profile.update(user_data["training_profile"])
            logger.info(
                f"✅ BACKEND: Using MongoDB training profile data for userId: {user_id}"
            )

        if user_data.get("current_plan"):
            user_profile["current_plan"] = user_data["current_plan"]
            logger.info(
                f"✅ BACKEND: Using MongoDB current plan data for userId: {user_id}"
            )

        # Add formatted versions for prompt consumption
        if user_data.get("training_profile_formatted"):
            user_profile["training_profile_formatted"] = user_data[
                "training_profile_formatted"
            ]

        if user_data.get("current_plan_formatted"):
            user_profile["current_plan_formatted"] = user_data["current_plan_formatted"]
    else:
        logger.warning(f"⚠️ BACKEND: No user data from MongoDB for userId: {user_id}")
        user_profile = {"user_id": user_id}

    # Handle resume input for interrupted sessions
    final_message = user_message
    if user_input:
        logger.info(f"🔄 BACKEND: Resuming session with user input: {user_input}")
        final_message = f"RESUME_INPUT: {user_input}"

    # Create initial state
    initial_state = {
        "user_query": user_message,
        "messages": [HumanMessage(content=user_message)],
        "user_id": user_id,
        "user_profile": user_profile,
        "current_plan": user_profile.get("current_plan"),
        "thread_id": thread_id,
        "enable_human_feedback": False,  # Disable for streaming
    }

    # Stream the response
    async for event in streamer.stream_coaching_response(
        user_message=final_message,
        thread_id=thread_id,
        user_id=user_id,
        config=user_profile,
    ):
        yield event


async def stream_specialized_coach(
    user_message: str,
    coach_type: str,
    thread_id: str,
    user_id: str,
    mongodb_uri: str,
    user_data: Optional[Dict[str, Any]] = None,
    user_input: Optional[str] = None,
) -> AsyncGenerator[str, None]:
    """
    Stream response from a specific specialized coach with enhanced logging.

    Args:
        user_message: The user's message/query
        coach_type: Type of coach (strength, nutrition, etc.)
        thread_id: Unique thread ID for session continuity
        user_id: User identifier
        mongodb_uri: MongoDB connection string
        user_data: User training profile and plan data from MongoDB
        user_input: Resume input for continuing interrupted sessions

    Yields:
        SSE-formatted strings with specialized coach responses
    """
    logger.info(
        f"🎯 BACKEND: [SPECIALIZED_STREAM] Processing specialized request for coach: {coach_type}"
    )
    logger.info(
        f"📊 BACKEND: [SPECIALIZED_STREAM] Request details: user_id={user_id}, thread_id={thread_id}"
    )

    # Normalize coach_type - strip "_coach" suffix if present
    normalized_coach_type = coach_type
    if coach_type.endswith("_coach"):
        normalized_coach_type = coach_type[:-6]  # Remove "_coach" suffix
        logger.info(
            f"🔄 BACKEND: [SPECIALIZED_STREAM] Normalized coach type from '{coach_type}' to '{normalized_coach_type}'"
        )

    # Check if optimized graph is enabled
    use_optimized = os.getenv("USE_OPTIMIZED_GRAPH", "true").lower() == "true"
    graph_type = "optimized" if use_optimized else "original"

    logger.info(
        f"🚀 BACKEND: [SPECIALIZED_STREAM] Using {graph_type} coaching graph for {normalized_coach_type}"
    )

    # Create the comprehensive coaching graph
    config = ComprehensiveCoachingConfig(
        user_id=user_id,
        mongodb_uri=mongodb_uri,
        thread_id=thread_id,
        enable_memory=True,
        use_react_agents=True,
        max_iterations=5,
        enable_human_feedback=False,  # Disable for specialized requests
        enable_reflexion=False,  # Disable for specialized requests
    )

    # Create the appropriate coaching graph
    if use_optimized:
        graph = await create_optimized_coaching_graph(config)
    else:
        graph = await create_comprehensive_coaching_graph(config)
    streamer = ComprehensiveCoachingStreamer(graph)

    logger.info(
        f"🔧 BACKEND: [SPECIALIZED_STREAM] Created specialized graph for coach: {normalized_coach_type}"
    )

    # Prepare user profile context
    user_profile = {}
    if user_data and (
        user_data.get("training_profile") or user_data.get("current_plan")
    ):
        if user_data.get("training_profile"):
            user_profile.update(user_data["training_profile"])
            logger.info(
                f"✅ BACKEND: Using MongoDB training profile data for specialized coach ({normalized_coach_type}) - userId: {user_id}"
            )

            # Extract domain-specific data if available
            domain_data = user_data["training_profile"].get(normalized_coach_type, {})
            if domain_data:
                user_profile[f"{normalized_coach_type}_specific"] = domain_data
                logger.info(
                    f"✅ BACKEND: Found {normalized_coach_type}-specific training data for userId: {user_id}"
                )

        if user_data.get("current_plan"):
            user_profile["current_plan"] = user_data["current_plan"]
            logger.info(
                f"✅ BACKEND: Using MongoDB current plan data for specialized coach ({normalized_coach_type}) - userId: {user_id}"
            )

        # Add formatted versions for prompt consumption
        if user_data.get("training_profile_formatted"):
            user_profile["training_profile_formatted"] = user_data[
                "training_profile_formatted"
            ]

        if user_data.get("current_plan_formatted"):
            user_profile["current_plan_formatted"] = user_data["current_plan_formatted"]
    else:
        logger.warning(
            f"⚠️ BACKEND: No user data from MongoDB for specialized coach ({normalized_coach_type}) - userId: {user_id}"
        )
        user_profile = {"user_id": user_id}

    # Handle resume input for interrupted sessions
    if user_input:
        logger.info(
            f"🔄 BACKEND: Resuming specialized coach session ({normalized_coach_type}) with user input: {user_input}"
        )
        targeted_message = f"RESUME_INPUT: {user_input}"
    else:
        # Create targeted message for specialized coach - use the original coach_type with _coach suffix
        targeted_message = f"[COACH_OVERRIDE:{coach_type}] {user_message}"

    # Stream the response
    async for event in streamer.stream_coaching_response(
        user_message=targeted_message,
        thread_id=thread_id,
        user_id=user_id,
        config=user_profile,
    ):
        # Override agent name to match the coach type for specialized requests
        if '"agent":' in event and normalized_coach_type:
            # Map coach types to frontend domains - use normalized type
            coach_domain_map = {
                "strength": "strength",
                "cardio": "cardio",
                "running": "running",
                "cycling": "cycling",
                "nutrition": "nutrition",
                "recovery": "recovery",
                "mental": "mental",
            }
            if normalized_coach_type in coach_domain_map:
                domain = coach_domain_map[normalized_coach_type]
                # Replace any occurrence of "agent": "X" with the correct domain
                import re

                event = re.sub(r'"agent":\s*"[^"]*"', f'"agent": "{domain}"', event)
                logger.debug(
                    f"🔄 BACKEND: [SPECIALIZED_STREAM] Overrode agent name to '{domain}' for coach type '{normalized_coach_type}'"
                )

        yield event
