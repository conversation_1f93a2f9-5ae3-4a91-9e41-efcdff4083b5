#!/usr/bin/env python3
"""
Test script for mem0 integration with LangGraph

This script tests the basic functionality of the mem0 integration including:
- Configuration setup
- Memory adapter initialization
- Basic memory operations
- User profile management
"""

import asyncio
import logging
import os
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_mem0_config():
    """Test mem0 configuration creation."""
    logger.info("=== Testing mem0 Configuration ===")

    try:
        from athlea_langgraph.config.mem0_config import create_mem0_config_dict

        config_dict = create_mem0_config_dict("development")
        logger.info(f"Config created: {config_dict['vector_store']['provider']}")
        logger.info(f"Config dict keys: {config_dict.keys()}")

        # Test that required keys are present
        assert "vector_store" in config_dict
        assert "embedder" in config_dict
        assert "llm" in config_dict
        assert "version" in config_dict

        # Test vector store config
        vs_config = config_dict["vector_store"]
        assert vs_config["provider"] in ["qdrant", "pinecone"]
        logger.info(f"Vector store provider: {vs_config['provider']}")

        logger.info("✅ Configuration tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


async def test_mem0_adapter():
    """Test mem0 adapter basic functionality."""
    logger.info("=== Testing mem0 Adapter ===")

    try:
        from athlea_langgraph.memory.mem0_adapter import Mem0Adapter, MemoryType

        # Initialize adapter
        adapter = Mem0Adapter(environment="development")
        logger.info("✅ Mem0 adapter initialized")

        # Test user ID
        test_user_id = "test_user_123"

        # Test adding memories
        memory_id = await adapter.add_memory(
            user_id=test_user_id,
            content="I prefer morning workouts and hate burpees",
            memory_type=MemoryType.PREFERENCE,
            metadata={"test": True},
        )
        logger.info(f"✅ Added memory: {memory_id}")

        # Test searching memories
        memories = await adapter.search_memories(
            user_id=test_user_id, query="workout preferences", limit=5
        )
        logger.info(f"✅ Found {len(memories)} memories")

        # Test user profile
        profile = await adapter.get_user_profile(test_user_id)
        logger.info(
            f"✅ Retrieved profile: {len(profile.get('preferences', []))} preferences"
        )

        # Test relevant context
        context = await adapter.get_relevant_context(
            user_id=test_user_id, query="What workout should I do today?"
        )
        logger.info(
            f"✅ Retrieved context: {len(context.get('relevant_memories', []))} relevant memories"
        )

        logger.info("✅ Adapter tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ Adapter test failed: {e}")
        return False


async def test_enhanced_coaching_graph():
    """Test the mem0-enhanced coaching graph."""
    logger.info("=== Testing Enhanced Coaching Graph ===")

    try:
        from athlea_langgraph.graphs.coaching_graph_with_mem0 import (
            Mem0EnhancedCoachingGraph,
        )

        # Initialize graph
        test_user_id = "test_user_456"
        graph = Mem0EnhancedCoachingGraph(
            user_id=test_user_id, environment="development"
        )
        logger.info("✅ Enhanced coaching graph initialized")

        # Test user profile initialization
        profile_data = {
            "goals": ["lose weight", "build strength"],
            "preferences": ["morning workouts", "no burpees"],
            "fitness_level": "beginner",
            "injuries": ["lower back issues"],
        }

        success = await graph.initialize_user_profile(profile_data)
        logger.info(f"✅ Profile initialization: {success}")

        # Test personalized context
        context = await graph.get_personalized_context("I want a workout plan")
        logger.info(
            f"✅ Personalized context: {len(context.get('relevant_memories', []))} memories"
        )

        # Test memory stats
        stats = await graph.get_user_memory_stats()
        logger.info(f"✅ Memory stats: {stats.get('total_memories', 0)} total memories")

        logger.info("✅ Enhanced coaching graph tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ Enhanced coaching graph test failed: {e}")
        return False


async def test_full_workflow():
    """Test a full workflow with mem0 integration."""
    logger.info("=== Testing Full Workflow ===")

    try:
        from athlea_langgraph.graphs.coaching_graph_with_mem0 import (
            get_mem0_enhanced_coaching_graph,
        )

        # Create enhanced graph
        test_user_id = "test_user_workflow"
        graph = await get_mem0_enhanced_coaching_graph(
            user_id=test_user_id,
            environment="development",
            enable_performance_monitoring=False,  # Disable for testing
        )
        logger.info("✅ Full graph created")

        # Initialize with profile
        profile_data = {
            "goals": ["marathon training"],
            "preferences": ["running", "outdoor workouts"],
            "fitness_level": "intermediate",
        }

        await graph.initialize_user_profile(profile_data)
        logger.info("✅ Profile initialized")

        # Test a coaching session
        response = await graph.run_coaching_session(
            user_message="I want to start training for a marathon. What should I do?",
            thread_id="test_thread_001",
        )

        # Check response structure
        required_keys = ["aggregated_response", "session_metadata"]
        for key in required_keys:
            if key not in response:
                raise ValueError(f"Missing required key in response: {key}")

        logger.info(f"✅ Coaching session completed")
        logger.info(f"Response length: {len(response.get('aggregated_response', ''))}")

        # Test memory stats after session
        stats = await graph.get_user_memory_stats()
        logger.info(
            f"✅ Final memory stats: {stats.get('total_memories', 0)} total memories"
        )

        logger.info("✅ Full workflow test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Full workflow test failed: {e}")
        return False


async def main():
    """Run all mem0 integration tests."""
    logger.info("🚀 Starting mem0 Integration Tests")

    results = {}

    # Test 1: Configuration
    logger.info("\n--- Running Configuration Test ---")
    results["config"] = test_mem0_config()  # Changed from test_config

    # Test 2: Adapter
    logger.info("\n--- Running Adapter Test ---")
    results["adapter"] = await test_mem0_adapter()

    # Test 3: Enhanced Graph
    logger.info("\n--- Running Enhanced Graph Test ---")
    results["enhanced_graph"] = await test_enhanced_coaching_graph()

    # Test 4: Full Workflow
    logger.info("\n--- Running Full Workflow Test ---")
    results["full_workflow"] = await test_full_workflow()

    # Summary
    logger.info("\n🏁 Test Results Summary:")
    passed = 0
    total = len(results)

    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        logger.info(f"  {test_name.replace('_', ' ').title()}: {status}")
        if passed_test:
            passed += 1

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed < total:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
    else:
        logger.info("🎉 All tests passed successfully!")

    return passed == total


if __name__ == "__main__":
    # Set up environment for testing
    os.environ.setdefault("ENVIRONMENT", "development")

    # Run tests
    success = asyncio.run(main())
    exit(0 if success else 1)
