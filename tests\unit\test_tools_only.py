#!/usr/bin/env python3
"""
Simple Tools Test - Focus on Tool Integration Without Memory

This script tests the specialized coaches and their tools without memory persistence
to isolate and fix the tool binding issues.
"""

import asyncio
import logging
from typing import Any, Dict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_tools_only():
    """Test tools integration without memory persistence."""
    print("🔧 Testing Tools Integration Only")
    print("=" * 50)

    try:
        # Import tools manager
        from athlea_langgraph.agents.specialized_coaches import get_tools_manager

        # Initialize tools manager
        print("Initializing tools manager...")
        tools_manager = await get_tools_manager()

        print(f"✅ Tools manager initialized")

        # Test individual tool access
        print("\n🔍 Testing Individual Tool Access:")
        print("-" * 30)

        # Test strength coach tools
        strength_tools = tools_manager.get_strength_coach_tools()
        print(f"Strength coach tools: {len(strength_tools)} available")
        for tool in strength_tools:
            print(f"  - {tool.__class__.__name__}")

        # Test nutrition coach tools
        nutrition_tools = tools_manager.get_nutrition_coach_tools()
        print(f"Nutrition coach tools: {len(nutrition_tools)} available")
        for tool in nutrition_tools:
            print(f"  - {tool.__class__.__name__}")

        # Test tool binding format
        print("\n🔗 Testing Tool Binding Format:")
        print("-" * 30)

        if strength_tools:
            tool = strength_tools[0]
            print(f"Tool type: {type(tool)}")
            print(f"Tool class: {tool.__class__.__name__}")

            # Check if tool has required methods
            if hasattr(tool, "name"):
                print(f"Tool name: {tool.name}")
            if hasattr(tool, "description"):
                print(f"Tool description: {tool.description}")
            if hasattr(tool, "args_schema"):
                print(f"Tool args schema: {tool.args_schema}")

        # Test simple coach node without memory
        print("\n🏃‍♂️ Testing Simple Coach Node:")
        print("-" * 30)

        from athlea_langgraph.agents.specialized_coaches import strength_coach_node
        from athlea_langgraph.state import AgentState

        # Create simple test state
        test_state = AgentState(
            messages=[],
            user_query="What's the best way to deadlift safely?",
            user_profile={
                "name": "Test User",
                "fitness_level": "beginner",
                "goals": ["strength training"],
                "restrictions": {"injuries": ["knee sensitivity"]},
            },
        )

        print("Running strength coach node...")
        try:
            result = await strength_coach_node(test_state)
            print("✅ Strength coach node executed successfully")
            if result.get("strength_response"):
                response = result["strength_response"]
                print(f"Response preview: {response[:100]}...")
            else:
                print("⚠️ No strength_response in result")
                print(f"Result keys: {list(result.keys())}")

        except Exception as e:
            print(f"❌ Error in strength coach node: {e}")
            import traceback

            traceback.print_exc()

    except Exception as e:
        print(f"❌ Error in tools test: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_tools_only())
