"""
Pydantic schemas for Google Maps Elevation API tool.
"""

from typing import List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class ElevationPoint(BaseModel):
    """Single point for elevation lookup."""

    lat: float = Field(..., ge=-90, le=90, description="Latitude in decimal degrees")
    lon: float = Field(..., ge=-180, le=180, description="Longitude in decimal degrees")


class ElevationInput(BaseModel):
    """Input schema for Google Maps Elevation API calls."""

    mode: Literal["point", "path", "locations"] = Field(
        ..., description="Type of elevation lookup to perform"
    )

    # For single point lookups
    lat: Optional[float] = Field(
        None, ge=-90, le=90, description="Latitude for single point"
    )
    lon: Optional[float] = Field(
        None, ge=-180, le=180, description="Longitude for single point"
    )

    # For multiple points or path
    points: Optional[List[ElevationPoint]] = Field(
        None,
        max_length=500,  # Google Maps API limit
        description="List of coordinates for path or locations lookup",
    )

    # Optional parameters
    samples: Optional[int] = Field(
        None,
        ge=2,
        le=512,
        description="Number of sample points along path (path mode only)",
    )

    @field_validator("points")
    @classmethod
    def validate_points_input(cls, v, info):
        """Validate points based on mode."""
        mode = info.data.get("mode")

        if mode == "point":
            # Point mode requires lat/lon, not points
            lat = info.data.get("lat")
            lon = info.data.get("lon")
            if lat is None or lon is None:
                raise ValueError("Point mode requires lat and lon parameters")
            return None

        elif mode in ["path", "locations"]:
            # Path/locations mode requires points
            if not v or len(v) < 2:
                raise ValueError(f"{mode} mode requires at least 2 points")
            return v

        return v


class ElevationResult(BaseModel):
    """Single elevation result point."""

    lat: float
    lon: float
    elevation: float = Field(..., description="Elevation in meters")
    resolution: Optional[float] = Field(None, description="Data resolution in meters")


class ElevationOutput(BaseModel):
    """Output schema for Google Maps Elevation API responses."""

    # For single point results
    elevation: Optional[float] = Field(
        None, description="Single elevation value in meters"
    )

    # For path analysis
    min_elevation: Optional[float] = Field(
        None, description="Minimum elevation along path"
    )
    max_elevation: Optional[float] = Field(
        None, description="Maximum elevation along path"
    )
    elevation_gain: Optional[float] = Field(None, description="Total elevation gain")
    elevation_loss: Optional[float] = Field(None, description="Total elevation loss")

    # For all modes
    points: Optional[List[ElevationResult]] = Field(
        None, description="All elevation points returned"
    )

    # Metadata
    api_status: str = Field(..., description="Google API response status")
    total_points: int = Field(..., description="Number of points processed")
    resolution_range: Optional[str] = Field(None, description="Data resolution info")


class ElevationError(BaseModel):
    """Error response schema for elevation API failures."""

    error_type: str = Field(..., description="Type of error encountered")
    message: str = Field(..., description="Human-readable error message")
    api_status: Optional[str] = Field(
        None, description="Google API status if available"
    )
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retry")
