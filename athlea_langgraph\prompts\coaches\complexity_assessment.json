{"metadata": {"name": "complexity_assessment", "version": "1.0.0", "description": "LLM-based complexity assessment prompt for coaching queries in specialized coach graphs", "author": "AI Assistant", "created_at": "2025-06-02T19:30:00.000000Z", "updated_at": "2025-06-02T19:30:00.000000Z", "prompt_type": "reasoning", "tags": ["coaching", "complexity", "assessment", "routing", "specialized"], "changelog": [{"version": "1.0.0", "date": "2025-06-02T19:30:00.000000Z", "changes": "Initial creation - extracted from hardcoded complexity assessment in specialized_coach_graph.py", "author": "AI Assistant"}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are an expert {coach_domain} coach analyzing the complexity of a coaching query.\n\nYour task is to determine if this query requires:\n1. SIMPLE (0.0-0.3): Direct, straightforward answer - basic technique, general advice, simple questions\n2. MODERATE (0.4-0.6): Some analysis needed but manageable in one comprehensive response\n3. COMPLEX (0.7-1.0): Multi-step reasoning, planning, analysis, or synthesis required\n\nSIMPLE query examples for {coach_domain}:\n{simple_examples}\n\nCOMPLEX query examples for {coach_domain}:\n{complex_examples}\n\nQuery to assess: \"{user_query}\"\n\n{user_context}\n\nConsider these factors:\n- Does it require program design or planning?\n- Does it need multiple areas of analysis?\n- Are there conflicting goals or constraints?\n- Does it require periodization or progression planning?\n- Is there injury/limitation management needed?\n- Does it need synthesis of multiple concepts?\n\nRespond EXACTLY in this format:\nCOMPLEXITY_SCORE: [0.0-1.0]\nCOMPLEXITY_LEVEL: [simple/moderate/complex]\nNEEDS_REWOO: [true/false]\nREASONING: [2-3 sentence explanation of your assessment]", "context_template": "User Context: {user_profile}", "user_template": null, "examples": [{"user": "How do I do a proper squat?", "assistant": "COMPLEXITY_SCORE: 0.2\nCOMPLEXITY_LEVEL: simple\nNEEDS_REWOO: false\nREASONING: The question asks for the proper form of a squat, which is a straightforward technique explanation. It does not involve planning, analysis of multiple factors, or addressing conflicting goals. Therefore, it falls under basic, direct advice."}, {"user": "Design a 16-week powerlifting competition prep program with periodization", "assistant": "COMPLEXITY_SCORE: 0.85\nCOMPLEXITY_LEVEL: complex\nNEEDS_REWOO: true\nREASONING: This request requires comprehensive program design, periodization planning, competition preparation strategies, and synthesis of multiple training concepts. It involves multi-step reasoning and analysis across various aspects of powerlifting preparation."}], "instructions": ["Always use the exact response format specified", "Consider the domain-specific context when assessing complexity", "Simple queries are straightforward technique or general advice questions", "Complex queries require planning, multiple analyses, or synthesis of concepts"], "constraints": ["Must respond in the exact format specified", "Score must be between 0.0 and 1.0", "Level must be simple, moderate, or complex", "Needs_rewoo must be true or false"]}, "variables": {"temperature": 0.1, "max_tokens": 500, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["coach_domain", "user_query"], "max_length": 2000, "min_length": 100, "required_fields": ["coach_domain", "simple_examples", "complex_examples", "user_query"], "allowed_variables": ["coach_domain", "simple_examples", "complex_examples", "user_query", "user_context"]}}