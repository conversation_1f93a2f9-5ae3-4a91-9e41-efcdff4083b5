"""
Logging configuration for Athlea LangGraph.
"""

import logging
import sys
from typing import Dict, Any
from pydantic import Field, ConfigDict
from pydantic_settings import BaseSettings

from .base import Environment


class LoggingConfig(BaseSettings):
    """Logging configuration."""

    model_config = ConfigDict(env_file=".env", case_sensitive=False, extra="allow")

    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT"
    )


def setup_logging(environment: Environment = Environment.DEVELOPMENT) -> None:
    """Setup logging configuration based on environment."""

    logging_config = LoggingConfig()

    if environment == Environment.DEVELOPMENT:
        level = logging.DEBUG
    elif environment == Environment.STAGING:
        level = logging.INFO
    else:
        level = logging.WARNING

    if logging_config.level:
        level = getattr(logging, logging_config.level.upper(), level)

    logging.basicConfig(
        level=level,
        format=logging_config.format,
        handlers=[logging.StreamHandler(sys.stdout)],
    )
