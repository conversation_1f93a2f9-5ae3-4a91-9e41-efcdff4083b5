import pytest
from unittest.mock import patch

from athlea_langgraph.config import config
from athlea_langgraph.services.azure_openai_service import (
    create_azure_chat_openai,
    get_azure_openai_config,
    validate_azure_openai_config,
)
from athlea_langgraph.utils.embedding_util import get_embedding_config_for_mem0
from athlea_langgraph.config.mem0_config import create_mem0_config_dict


class TestConfigIntegration:
    @patch.dict(
        "os.environ",
        {
            "AZURE_OPENAI_API_KEY": "test-key",
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_DEPLOYMENT_NAME": "test-deployment",
            "AZURE_MAPS_SUBSCRIPTION_KEY": "test-maps-key",
            "AZURE_SEARCH_SERVICE_NAME": "test-search",
            "AZURE_SEARCH_API_KEY": "test-search-key",
            "EMBEDDING_API_KEY": "test-embedding-key",
            "EMBEDDING_ENDPOINT": "https://test-embedding.com/",
            "MONGODB_URI": "mongodb://localhost:27017/test",
            "AIRTABLE_API_KEY": "test-airtable-key",
        },
    )
    def test_azure_openai_service_integration(self):
        azure_config = get_azure_openai_config()
        assert azure_config["endpoint"] == "https://test.openai.azure.com/"
        assert azure_config["deployment_name"] == "test-deployment"
        assert azure_config["has_api_key"] is True

        assert validate_azure_openai_config() is True

    @patch.dict(
        "os.environ",
        {
            "EMBEDDING_API_KEY": "test-embedding-key",
            "EMBEDDING_ENDPOINT": "https://test-embedding.com/",
            "AZURE_OPENAI_API_KEY": "test-openai-key",
        },
    )
    def test_embedding_config_integration(self):
        embedding_config = get_embedding_config_for_mem0()
        assert embedding_config is not None
        assert "provider" in embedding_config

    @patch.dict(
        "os.environ",
        {
            "AZURE_OPENAI_API_KEY": "test-key",
            "AZURE_OPENAI_ENDPOINT": "https://test.openai.azure.com/",
            "AZURE_OPENAI_CHAT_DEPLOYMENT": "test-chat-deployment",
            "PINECONE_API_KEY": "test-pinecone-key",
        },
    )
    def test_mem0_config_integration(self):
        mem0_config = create_mem0_config_dict("development")
        assert "vector_store" in mem0_config
        assert "embedder" in mem0_config
        assert "llm" in mem0_config
        assert mem0_config["version"] == "v1.1"

    def test_centralized_config_access(self):
        assert hasattr(config, "azure_openai")
        assert hasattr(config, "azure_maps")
        assert hasattr(config, "azure_search")
        assert hasattr(config, "embedding")
        assert hasattr(config, "database")
        assert hasattr(config, "airtable")
        assert hasattr(config, "mem0")
