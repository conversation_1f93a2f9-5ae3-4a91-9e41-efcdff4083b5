#!/usr/bin/env python3
"""
Debug Tool Input Parsing

Simple script to test and debug the tool input parsing logic.
"""

import json
import logging

from athlea_langgraph.agents.react_coach_executor import ReActCoachExecutor
from athlea_langgraph.agents.specialized_coaches import (
    STRENGTH_COACH_PROMPT,
    get_tools_manager,
)

# Set up debug logging
logging.basicConfig(level=logging.DEBUG)


async def debug_tool_input():
    """Debug tool input parsing."""
    print("🔍 Debugging Tool Input Parsing")
    print("=" * 50)

    # Get tools
    tools_manager = await get_tools_manager()
    strength_tools = tools_manager.get_strength_coach_tools()

    # Create executor
    executor = ReActCoachExecutor(
        coach_name="debug_coach",
        coach_prompt=STRENGTH_COACH_PROMPT,
        tools=strength_tools,
        max_iterations=1,
    )

    # Test input that should work
    test_input = """
    {
      "command": "strength",
      "duration_minutes": 60,
      "intensity": "moderate",
      "equipment": ["dumbbells", "barbell", "bench"],
      "goals": ["build muscle", "increase strength"],
      "experience_level": "intermediate",
      "preferences": {"focus_areas": ["chest", "back"], "rest_time": "60-90 seconds"}
    }
    """

    print("Test Input:")
    print(test_input)
    print()

    # Test parsing
    parsed = executor._parse_and_preprocess_input("session_generation", test_input)
    print("Parsed Result:")
    print(json.dumps(parsed, indent=2))
    print()

    # Test with problematic input (what LLM might generate)
    problematic_input = """
    {
      "command": "strength",
      "duration_minutes": 60,
      "equipment": "dumbbells, barbell, bench",
      "preferences": []
    }
    """

    print("Problematic Input:")
    print(problematic_input)
    print()

    parsed_problematic = executor._parse_and_preprocess_input(
        "session_generation", problematic_input
    )
    print("Parsed Problematic Result:")
    print(json.dumps(parsed_problematic, indent=2))
    print()

    # Test Action parsing (this is where the issue might be)
    print("🔍 Testing Action Parsing")
    print("=" * 30)

    # Simulate LLM response with nested JSON
    llm_response = """
    Thought: I need to generate a strength training session for this user.
    Action: session_generation
    Action Input: {"command": "strength", "duration_minutes": 60, "intensity": "moderate", "equipment": ["dumbbells", "barbell"], "preferences": {"focus_areas": ["chest", "back"]}}
    """

    print("LLM Response:")
    print(llm_response)
    print()

    action_result = executor._parse_action(llm_response)
    if action_result:
        action_name, action_input = action_result
        print(f"✅ Action parsing result:")
        print(f"  Action: {action_name}")
        print(f"  Raw Input: {repr(action_input)}")
        print()

        # Test the full parsing pipeline
        print("🔍 Testing Full Parsing Pipeline")
        print("=" * 30)

        final_parsed = executor._parse_and_preprocess_input(
            "session_generation", action_input
        )
        print("Final Parsed Input:")
        print(json.dumps(final_parsed, indent=2))

    else:
        print("❌ Action parsing failed")

    # Test with already wrapped input (the problematic case)
    print("\n🔍 Testing Pre-wrapped Input")
    print("=" * 30)

    wrapped_input = (
        '{"query": "{\\"command\\": \\"strength\\", \\"duration_minutes\\": 60}"}'
    )
    print(f"Wrapped Input: {wrapped_input}")

    unwrapped_result = executor._parse_and_preprocess_input(
        "session_generation", wrapped_input
    )
    print("Unwrapped Result:")
    print(json.dumps(unwrapped_result, indent=2))

    # Test actual tool execution
    print("\n🔍 Testing Actual Tool Execution")
    print("=" * 30)

    # Create a valid input for the session_generation tool
    tool_input = {
        "command": "strength",
        "date": "2024-01-15",  # Required field
        "duration_minutes": 60,
        "intensity": "moderate",
        "equipment": ["dumbbells", "barbell"],
        "goals": ["build muscle"],
        "experience_level": "intermediate",
        "preferences": {"focus_areas": ["chest"]},
    }

    try:
        # Test the tool execution with our parsed input
        result = await executor._execute_tool(
            "session_generation", json.dumps(tool_input)
        )
        print("✅ Tool execution successful!")
        print(
            f"Result: {result[:200]}..." if len(result) > 200 else f"Result: {result}"
        )
    except Exception as e:
        print(f"❌ Tool execution failed: {e}")
        print(f"Error type: {type(e)}")

    # Test direct tool invocation to see full error
    print("\n🔍 Testing Direct Tool Invocation")
    print("=" * 30)

    from athlea_langgraph.tools.external.session_generation import SessionGenerationTool

    direct_tool = SessionGenerationTool()
    direct_input = {
        "command": "strength",
        "date": "2024-01-15",
        "duration": 60,
        "intensity": "moderate",
    }

    try:
        direct_result = await direct_tool.invoke(direct_input)
        print("✅ Direct tool execution successful!")
        print(f"Success: {direct_result.success}")
        if not direct_result.success:
            print(f"Error: {direct_result.message}")
    except Exception as e:
        print(f"❌ Direct tool execution failed: {e}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(debug_tool_input())
