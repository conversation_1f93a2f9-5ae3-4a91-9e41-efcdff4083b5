import logging
from typing import Any, Dict

from langgraph.types import interrupt

from athlea_langgraph.states.onboarding_state import OnboardingState

logger = logging.getLogger(__name__)


class NeedInputNode:
    """Node for handling when more user input is needed"""

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Signal that user input is needed and interrupt the graph"""
        logger.info(
            "[Node: needInputNode] Entering node - interrupting for user input."
        )

        # Set flags to indicate we need input
        updates = {
            "needs_input": True,
            "requires_input": True,
            "input_prompt": "Please provide more information to continue with your personalized plan.",
            "current_task_description": "Waiting for additional user information to complete onboarding.",
        }

        # Interrupt the graph execution to wait for user input
        # This will pause execution until the user provides more input
        interrupt("More information needed from user")

        logger.info("[Node: needInputNode] Interrupted for user input.")

        return updates


# Create the node instance
need_input_node = NeedInputNode()
