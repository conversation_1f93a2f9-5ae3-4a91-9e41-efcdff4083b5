"""
Strength Agent - Modular ReAct Implementation

Specialized agent for strength training, resistance training, and muscle building.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.strength import (
    StrengthExerciseDatabase,
    StrengthAssessmentTool,
)
from ..tools.strength.exercise_database import (
    search_strength_exercises,
)
from ..tools.strength import (
    get_exercise_progression,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class StrengthAgent(BaseReActAgent):
    """Specialized agent for strength training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the strength agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Strength Coach. Your primary function is to provide precise, evidence-based strength training advice by leveraging your specialized tools.

**CRITICAL INSTRUCTIONS:**
1.  **Assess the User's Query:** Analyze the user's question to determine if one of your tools can provide a precise, data-driven answer.
2.  **Prioritize Tool Usage:** For any request related to exercise searching, creating progressions, performing assessments, or retrieving scientific research, you MUST use the appropriate tool. Do not answer from general knowledge if a tool is available.
3.  **Use Tools Correctly:**
    - For exercise searches, use `search_strength_exercises`.
    - To create an exercise progression, use `get_exercise_progression`.
    - For research questions, use `azure_search_retriever`.
    - For current news or product reviews, use `web_search`.
4.  **Engage in Conversation:** If a tool is not required, respond naturally and conversationally. If you need more information to use a tool effectively, ask the user clarifying questions.

**PARAMETER GUIDELINES:**
- `search_strength_exercises` expects `muscle_groups` and `equipment` as lists of strings (e.g., `["chest", "shoulders"]`).
- `search_strength_exercises` expects `difficulty_level` as a single string (e.g., `"beginner"`).

Your goal is to be a helpful and accurate strength coach, using your tools to provide the best possible guidance."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="strength_agent",
            domain="strength",
            system_prompt=fallback_prompt,
            tools=[],
            permissions=[
                "exercise_search",
                "progression_planning",
                "strength_assessment",
            ],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("strength_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded strength coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load strength coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the strength agent.
        This makes the agent self-contained and independent of a central tool manager.
        """
        if not self._tools_loaded:
            # Domain-specific strength tools - use the correct LangChain wrapped tools
            strength_tools = [
                search_strength_exercises,  # This uses ExerciseSearchInput schema correctly
                get_exercise_progression,
            ]

            # Add shared tools that all agents should have

            # 1. GraphRAG tool for research
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                strength_tools.append(create_graphrag_tool())
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 2. Session Generation tool
            try:
                from ..tools.external.session_generation import SessionGenerationTool
                from langchain_core.tools import BaseTool

                class SessionGenerationLangChainTool(BaseTool):
                    name: str = "session_generation"
                    description: str = """Generate structured strength training sessions and workout programs.
                    Use this tool to create detailed training programs, workout sessions, and strength protocols.
                    Input should be a JSON string with session parameters including goals, duration, and equipment."""

                    def _run(self, session_params: str) -> str:
                        try:
                            import json

                            session_tool = SessionGenerationTool()
                            params = json.loads(session_params)
                            result = session_tool.generate_session(params)
                            return json.dumps(result, indent=2)
                        except Exception as e:
                            return f"Session generation failed: {str(e)}"

                    async def _arun(self, session_params: str) -> str:
                        return self._run(session_params)

                strength_tools.append(SessionGenerationLangChainTool())
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # 3. Azure Search tool for research and knowledge retrieval
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain_tool = azure_search_tool.to_langchain_tool()
                strength_tools.append(azure_search_langchain_tool)
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # 4. Web Search tool for current information
            try:
                from langchain_community.tools import DuckDuckGoSearchRun

                web_search_tool = DuckDuckGoSearchRun(
                    name="web_search",
                    description="Search the web for current strength training research, techniques, and equipment reviews. Use for recent studies, training methods, or product reviews.",
                )
                strength_tools.append(web_search_tool)
            except Exception as e:
                logger.warning(f"Failed to load web search tool: {e}")

            # Update the agent's tools
            self.tools = strength_tools

            # Tools loaded successfully for direct tool calling
            logger.info(
                f"✅ Loaded {len(strength_tools)} tools for direct calling: {[t.name for t in strength_tools]}"
            )

            logger.info(
                f"Loaded {len(strength_tools)} tools for strength agent: {[t.name for t in strength_tools]}"
            )
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for strength coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process strength training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)


# Create the strength agent instance
strength_agent = StrengthAgent()


async def strength_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for strength training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with strength agent response
    """
    logger.info("--- Running Strength Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not strength_agent.tools:
            await strength_agent.get_domain_tools()

        # Process the request
        result = await strength_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "strength_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Strength agent completed with {len(strength_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in strength agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your strength training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "strength_agent",
            "error": str(e),
        }
