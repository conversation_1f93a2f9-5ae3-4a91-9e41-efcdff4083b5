"""Mental Training Tools Package

Evidence-based mental health and wellness tools for comprehensive mental training.
"""

from langchain_core.tools import tool
import json

# Import the original classes
from .goal_tracker import GoalTracker
from .mental_state_assessment import MentalStateAssessmentTool
from .mood_pattern_analyzer import <PERSON>od<PERSON>atternAnal<PERSON><PERSON>
from .stress_tracker import StressLevelTracker


@tool("mental_state_assessment")
def mental_state_assessment(assessment_data: str) -> str:
    """Conduct comprehensive mental state assessment evaluating mood, stress, motivation, and overall well-being.

    Args:
        assessment_data: JSON string containing user assessment data with fields like user_id, mood_rating, stress_level, etc.

    Returns:
        JSON string with detailed assessment results and recommendations
    """
    try:
        data = (
            json.loads(assessment_data)
            if isinstance(assessment_data, str)
            else assessment_data
        )

        tool = MentalStateAssessmentTool()
        result = tool.invoke(assessment_data)
        return result
    except Exception as e:
        return json.dumps(
            {"error": f"Mental state assessment failed: {str(e)}", "status": "failed"}
        )


@tool("stress_level_tracker")
def stress_level_tracker(stress_data: str) -> str:
    """Track and analyze stress levels, identify triggers and patterns over time.

    Args:
        stress_data: JSON string containing stress tracking data

    Returns:
        JSON string with stress analysis and recommendations
    """
    try:
        data = json.loads(stress_data) if isinstance(stress_data, str) else stress_data

        tool = StressLevelTracker()
        result = tool.invoke(stress_data)
        return result
    except Exception as e:
        return json.dumps(
            {"error": f"Stress tracking failed: {str(e)}", "status": "failed"}
        )


@tool("mood_pattern_analyzer")
def mood_pattern_analyzer(mood_data: str) -> str:
    """Track daily mood entries and analyze patterns over time, identify trends and triggers.

    Args:
        mood_data: JSON string containing mood tracking data

    Returns:
        JSON string with mood pattern analysis
    """
    try:
        data = json.loads(mood_data) if isinstance(mood_data, str) else mood_data

        tool = MoodPatternAnalyzer()
        result = tool.invoke(mood_data)
        return result
    except Exception as e:
        return json.dumps(
            {"error": f"Mood analysis failed: {str(e)}", "status": "failed"}
        )


@tool("goal_tracker")
def goal_tracker(goal_data: str) -> str:
    """Create SMART mental health goals, track progress systematically and provide motivation support.

    Args:
        goal_data: JSON string containing goal tracking data

    Returns:
        JSON string with goal tracking results
    """
    try:
        data = json.loads(goal_data) if isinstance(goal_data, str) else goal_data

        tool = GoalTracker()
        result = tool.invoke(goal_data)
        return result
    except Exception as e:
        return json.dumps(
            {"error": f"Goal tracking failed: {str(e)}", "status": "failed"}
        )


__all__ = [
    "mental_state_assessment",
    "stress_level_tracker",
    "mood_pattern_analyzer",
    "goal_tracker",
    # Keep the original classes for backward compatibility
    "MentalStateAssessmentTool",
    "StressLevelTracker",
    "MoodPatternAnalyzer",
    "GoalTracker",
]
