"""
Production-Ready Redis Client for Azure Redis Cache

This module provides a robust, async Redis client implementation following
Azure Redis Cache best practices with proper connection management,
retry strategies, health monitoring, and authentication.

Key Features:
- Async/await throughout (no sync blocking)
- Proper SSL/TLS configuration for Azure
- Connection pooling and reuse
- Exponential backoff retry strategy
- Health monitoring and reconnection logic
- Thread-safe singleton pattern with SemaphoreSlim
- Comprehensive error handling and logging
"""

import asyncio
import ssl
import time
import logging
import math
from typing import Optional, Dict, Any, Union, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from contextlib import asynccontextmanager

import redis.asyncio as aioredis
from redis.asyncio.retry import Retry
from redis.exceptions import (
    ConnectionError,
    TimeoutError,
    RedisError,
    AuthenticationError,
    BusyLoadingError,
)

logger = logging.getLogger(__name__)


def exponential_backoff(failures: int, base: float = 1.0, cap: float = 60.0) -> float:
    """Exponential backoff calculation for retries."""
    return min(cap, base * (2**failures))


@dataclass
class RedisConfig:
    """Redis connection configuration optimized for Azure Redis Cache."""

    # Azure Redis Cache settings
    host: str = "athlea-redis-standard.redis.cache.windows.net"
    port: int = 6380
    password: str = (
        "IrFtfY8rnuyt80nGrrRdsKbaw2jeueUEUAzCaG123Vo="  # Primary access key for athlea-redis-standard
    )

    # SSL/TLS settings (required for Azure)
    ssl: bool = True
    ssl_check_hostname: bool = False  # Important for Azure Redis Cache
    ssl_cert_reqs: str = "none"  # Azure Redis Cache specific

    # Connection settings
    decode_responses: bool = True
    socket_timeout: float = 30.0
    socket_connect_timeout: float = 30.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[int, int] = None

    # Connection pool settings
    max_connections: int = 50
    connection_pool_kwargs: Dict[str, Any] = None

    # Retry settings
    retry_on_timeout: bool = True
    retry_on_error: tuple = (ConnectionError, TimeoutError, BusyLoadingError)
    max_retries: int = 5
    retry_delay_base: float = 1.0
    retry_delay_cap: float = 60.0

    # Health check settings
    health_check_interval: int = 30
    ping_timeout: float = 5.0

    # Reconnection settings
    reconnect_min_interval: timedelta = timedelta(seconds=60)
    reconnect_error_threshold: timedelta = timedelta(seconds=30)
    max_reconnect_attempts: int = 3

    def __post_init__(self):
        if self.socket_keepalive_options is None:
            # TCP keepalive settings optimized for Azure
            self.socket_keepalive_options = {
                1: 1,  # TCP_KEEPIDLE
                2: 3,  # TCP_KEEPINTVL
                3: 5,  # TCP_KEEPCNT
            }

        if self.connection_pool_kwargs is None:
            self.connection_pool_kwargs = {}


class RedisConnectionState:
    """Track Redis connection state and health."""

    def __init__(self):
        self.connected: bool = False
        self.last_ping: Optional[datetime] = None
        self.last_error: Optional[datetime] = None
        self.first_error: Optional[datetime] = None
        self.last_reconnect: Optional[datetime] = None
        self.connection_attempts: int = 0
        self.successful_operations: int = 0
        self.failed_operations: int = 0

    def record_success(self):
        """Record a successful operation."""
        self.successful_operations += 1
        self.last_ping = datetime.now()
        # Reset error tracking on success
        if self.first_error:
            self.first_error = None
            self.last_error = None

    def record_error(self):
        """Record a failed operation."""
        self.failed_operations += 1
        now = datetime.now()
        self.last_error = now
        if not self.first_error:
            self.first_error = now

    def should_reconnect(self, config: RedisConfig) -> bool:
        """Determine if we should attempt reconnection."""
        if not self.first_error or not self.last_error:
            return False

        now = datetime.now()

        # Check if we've been failing long enough
        error_duration = now - self.first_error
        time_since_last_error = now - self.last_error
        time_since_last_reconnect = (
            now - self.last_reconnect if self.last_reconnect else timedelta.max
        )

        return (
            error_duration >= config.reconnect_error_threshold
            and time_since_last_error <= config.reconnect_error_threshold
            and time_since_last_reconnect >= config.reconnect_min_interval
        )

    def record_reconnect(self):
        """Record a reconnection attempt."""
        self.last_reconnect = datetime.now()
        self.connection_attempts += 1


class RedisClient:
    """
    Production-ready async Redis client with connection management.

    Features:
    - Thread-safe singleton pattern
    - Async connection management
    - Automatic reconnection with backoff
    - Health monitoring and metrics
    - Comprehensive error handling
    """

    _instance: Optional["RedisClient"] = None
    _lock = asyncio.Lock()

    def __init__(self, config: Optional[RedisConfig] = None):
        self.config = config or RedisConfig()
        self._redis: Optional[aioredis.Redis] = None
        self._connection_semaphore = asyncio.Semaphore(1)
        self._state = RedisConnectionState()
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown = False

    @classmethod
    async def get_instance(cls, config: Optional[RedisConfig] = None) -> "RedisClient":
        """Get or create singleton Redis client instance."""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls(config)
                    await cls._instance.initialize()
        return cls._instance

    async def initialize(self) -> None:
        """Initialize the Redis client and start health monitoring."""
        try:
            await self._create_connection()
            self._start_health_monitoring()
            logger.info("✅ Redis client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Redis client: {e}")
            raise

    async def _create_connection(self) -> aioredis.Redis:
        """Create a new Redis connection with proper configuration."""
        try:
            # Connection configuration for Azure Redis Cache - simplified to working parameters only
            connection_kwargs = {
                "host": self.config.host,
                "port": self.config.port,
                "password": self.config.password,
                "ssl": self.config.ssl,
                "ssl_cert_reqs": self.config.ssl_cert_reqs,
                "ssl_check_hostname": self.config.ssl_check_hostname,
                "decode_responses": self.config.decode_responses,
                "socket_timeout": self.config.socket_timeout,
                "socket_connect_timeout": self.config.socket_connect_timeout,
                # Removed problematic parameters that cause Error 22:
                # - health_check_interval
                # - max_connections
                # - socket_keepalive
                # - socket_keepalive_options
                # - retry_on_timeout
                # - connection_pool_kwargs
            }

            # Create connection
            redis_client = aioredis.Redis(**connection_kwargs)

            # Test connection with ping
            await asyncio.wait_for(
                redis_client.ping(), timeout=self.config.ping_timeout
            )

            self._redis = redis_client
            self._state.connected = True
            self._state.record_success()

            logger.info(
                f"🔗 Redis connection established to {self.config.host}:{self.config.port}"
            )
            return redis_client

        except Exception as e:
            self._state.record_error()
            logger.error(f"❌ Failed to create Redis connection: {e}")
            raise

    async def _reconnect_if_needed(self) -> None:
        """Reconnect if connection is unhealthy and conditions are met."""
        if not self._state.should_reconnect(self.config):
            return

        async with self._connection_semaphore:
            # Double-check after acquiring semaphore
            if not self._state.should_reconnect(self.config):
                return

            logger.warning("🔄 Attempting Redis reconnection due to persistent errors")
            self._state.record_reconnect()

            try:
                # Close existing connection
                if self._redis:
                    try:
                        await self._redis.aclose()
                    except Exception as e:
                        logger.warning(f"Error closing old Redis connection: {e}")

                # Create new connection
                await self._create_connection()
                logger.info("✅ Redis reconnection successful")

            except Exception as e:
                logger.error(f"❌ Redis reconnection failed: {e}")
                self._state.connected = False
                raise

    async def _execute_with_retry(self, operation, *args, **kwargs):
        """Execute Redis operation with automatic retry and reconnection."""
        max_attempts = self.config.max_reconnect_attempts

        for attempt in range(max_attempts):
            try:
                # Check if we need to reconnect
                await self._reconnect_if_needed()

                # Ensure we have a connection
                if not self._redis:
                    await self._create_connection()

                # Execute operation
                result = await operation(*args, **kwargs)
                self._state.record_success()
                return result

            except (ConnectionError, TimeoutError, AuthenticationError) as e:
                self._state.record_error()
                self._state.connected = False

                if attempt == max_attempts - 1:
                    logger.error(
                        f"❌ Redis operation failed after {max_attempts} attempts: {e}"
                    )
                    raise

                logger.warning(
                    f"⚠️ Redis operation failed (attempt {attempt + 1}/{max_attempts}): {e}"
                )
                await asyncio.sleep(
                    exponential_backoff(attempt, self.config.retry_delay_base)
                )

            except Exception as e:
                self._state.record_error()
                logger.error(f"❌ Unexpected Redis error: {e}")
                raise

    def _start_health_monitoring(self) -> None:
        """Start background health monitoring task."""
        if self._health_check_task and not self._health_check_task.done():
            return

        self._health_check_task = asyncio.create_task(self._health_monitor())

    async def _health_monitor(self) -> None:
        """Background task to monitor Redis connection health."""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.config.health_check_interval)

                if self._redis and self._state.connected:
                    try:
                        await asyncio.wait_for(
                            self._redis.ping(), timeout=self.config.ping_timeout
                        )
                        self._state.record_success()
                    except Exception as e:
                        logger.warning(f"⚠️ Redis health check failed: {e}")
                        self._state.record_error()
                        self._state.connected = False

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Health monitor error: {e}")

    @asynccontextmanager
    async def get_connection(self):
        """Get Redis connection with automatic management."""
        await self._execute_with_retry(lambda: None)  # Ensure connection
        try:
            yield self._redis
        except Exception as e:
            self._state.record_error()
            raise

    async def ping(self) -> bool:
        """Test Redis connection."""
        try:
            result = await self._execute_with_retry(self._redis.ping)
            return result
        except Exception:
            return False

    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        return await self._execute_with_retry(self._redis.get, key)

    async def set(
        self,
        key: str,
        value: Union[str, bytes],
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False,
    ) -> bool:
        """Set key-value with optional expiration."""
        return await self._execute_with_retry(
            self._redis.set, key, value, ex=ex, px=px, nx=nx, xx=xx
        )

    async def delete(self, *keys: str) -> int:
        """Delete keys."""
        return await self._execute_with_retry(self._redis.delete, *keys)

    async def exists(self, *keys: str) -> int:
        """Check if keys exist."""
        return await self._execute_with_retry(self._redis.exists, *keys)

    async def expire(self, key: str, time: int) -> bool:
        """Set key expiration."""
        return await self._execute_with_retry(self._redis.expire, key, time)

    async def ttl(self, key: str) -> int:
        """Get key time to live."""
        return await self._execute_with_retry(self._redis.ttl, key)

    async def mget(self, keys: list) -> list:
        """Get multiple values."""
        return await self._execute_with_retry(self._redis.mget, keys)

    async def mset(self, mapping: dict) -> bool:
        """Set multiple key-value pairs."""
        return await self._execute_with_retry(self._redis.mset, mapping)

    async def pipeline(self):
        """Get Redis pipeline."""
        await self._execute_with_retry(lambda: None)  # Ensure connection
        return self._redis.pipeline()

    async def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "connected": self._state.connected,
            "last_ping": (
                self._state.last_ping.isoformat() if self._state.last_ping else None
            ),
            "last_error": (
                self._state.last_error.isoformat() if self._state.last_error else None
            ),
            "connection_attempts": self._state.connection_attempts,
            "successful_operations": self._state.successful_operations,
            "failed_operations": self._state.failed_operations,
            "error_rate": (
                self._state.failed_operations
                / max(
                    1, self._state.successful_operations + self._state.failed_operations
                )
            ),
            "config": {
                "host": self.config.host,
                "port": self.config.port,
                "ssl": self.config.ssl,
                "max_connections": self.config.max_connections,
            },
        }

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        try:
            start_time = time.time()
            ping_result = await self.ping()
            ping_latency = (time.time() - start_time) * 1000

            return {
                "status": "healthy" if ping_result else "unhealthy",
                "ping_success": ping_result,
                "ping_latency_ms": round(ping_latency, 2),
                "connected": self._state.connected,
                "stats": await self.get_stats(),
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connected": False,
                "timestamp": datetime.now().isoformat(),
            }

    async def close(self) -> None:
        """Close Redis connection and cleanup."""
        self._shutdown = True

        # Cancel health monitoring
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        # Close Redis connection
        if self._redis:
            try:
                await self._redis.aclose()
            except Exception as e:
                logger.warning(f"Error closing Redis connection: {e}")

        self._state.connected = False
        logger.info("🔒 Redis client closed successfully")

    @classmethod
    async def close_instance(cls) -> None:
        """Close singleton instance."""
        if cls._instance:
            await cls._instance.close()
            cls._instance = None


# Convenience functions for easy usage
async def get_redis_client(config: Optional[RedisConfig] = None) -> RedisClient:
    """Get Redis client instance."""
    return await RedisClient.get_instance(config)


async def test_redis_connection(config: Optional[RedisConfig] = None) -> bool:
    """Test Redis connection."""
    try:
        client = await get_redis_client(config)
        return await client.ping()
    except Exception as e:
        logger.error(f"Redis connection test failed: {e}")
        return False


# Example usage and testing
if __name__ == "__main__":

    async def main():
        # Test the Redis client
        config = RedisConfig()
        client = await get_redis_client(config)

        try:
            # Test basic operations
            print("Testing Redis connection...")
            health = await client.health_check()
            print(f"Health check: {health}")

            # Test set/get
            await client.set("test_key", "test_value", ex=60)
            value = await client.get("test_key")
            print(f"Retrieved value: {value}")

            # Get stats
            stats = await client.get_stats()
            print(f"Stats: {stats}")

        finally:
            await client.close()

    asyncio.run(main())
