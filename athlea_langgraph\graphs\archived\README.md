# Archived Graphs

This directory contains experimental and testing graph implementations that are no longer actively used in production but are preserved for reference.

## Archived Files

### Memory Testing Graphs
- `coaching_graph_with_mem0.py` - Experimental graph with mem0 memory integration
- `coaching_graph_with_memory.py` - Testing graph for memory functionality

### Pattern Experiments
- `reflection_enhanced_coaching_graph.py` - Experimental reflection pattern implementation
- `rewoo_reflection_coaching_graph.py` - Experimental ReWOO (Reasoning + Acting) pattern

### Legacy Versions
- `coaching_graph.py` - Earlier version of the coaching graph
- `individual_graphs.py` - Testing file for individual agent graphs

## Note

These files are preserved for historical reference and potential future use. The memory functionality has been integrated into the main production graphs, and the experimental patterns may be revisited in future iterations.

For current production graphs, see the parent directory:
- `comprehensive_coaching_graph.py` - Main production coaching graph
- `onboarding_graph.py` - User onboarding flow
- `studio_graph.py` - LangGraph Studio integration 