#!/usr/bin/env python3
"""
Modular Agent Test Runner

Simple script to run the comprehensive modular agent tests for GitHub Issue #8.
This tests the planning node routing to individual specialized agents following
multi-agent best practices.

Usage:
    python run_modular_agent_tests.py

Requirements:
    - All dependencies installed (pip install -r requirements.txt)
    - Environment variables set (see .env.local)
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path and change working directory
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)


async def main():
    """Run the modular agent test suite."""
    print("🎯 Modular Agent Test Suite")
    print("=" * 50)
    print("Testing GitHub Issue #8: Break Up Monolithic Specialized Coaches")
    print("Focus: Planning Node → Individual Agent → ReAct Execution")
    print()

    try:
        # Import and run the test suite
        from tests.integration.test_modular_agents import run_modular_agent_tests

        await run_modular_agent_tests()

        print("\n🏆 Test Suite Completed Successfully!")
        print("\n💡 Key Validations:")
        print(
            "✅ Planning node dynamically routes to correct agents (no keyword matching)"
        )
        print("✅ Individual agents execute ReAct pattern properly")
        print("✅ Schema validation and error handling work")
        print("✅ Integration with existing graph structure")
        print("\n🔗 Related Files:")
        print("- athlea_langgraph/agents/base_agent.py")
        print("- athlea_langgraph/agents/strength_agent.py")
        print("- athlea_langgraph/agents/nutrition_agent.py")
        print("- athlea_langgraph/agents/cardio_agent.py")
        print("- athlea_langgraph/agents/recovery_agent.py")
        print("- athlea_langgraph/agents/mental_agent.py")

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the python-langgraph directory")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Check that all agent files exist in athlea_langgraph/agents/")
        print(f"4. Current working directory: {os.getcwd()}")
        sys.exit(1)

    except Exception as e:
        print(f"❌ Test Error: {e}")
        print("\n🔧 This might be expected if:")
        print("- Azure OpenAI credentials are not set up")
        print("- Some import dependencies are missing")
        print("- Tools are temporarily disabled")
        print(
            "\nThe tests are designed to show the modular architecture even with some failures."
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
