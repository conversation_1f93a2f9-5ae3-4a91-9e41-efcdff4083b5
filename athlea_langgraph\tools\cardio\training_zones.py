"""
Comprehensive Training Zones Calculator Tool

This tool calculates personalized training zones for cardiovascular exercise including:
- Heart rate zones (multiple methodologies)
- Running pace zones based on performance
- Cycling power zones and FTP calculations
- Swimming pace zones
- RPE (Rate of Perceived Exertion) correlations
- Zone-specific training recommendations
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator

from ..base_tool import BaseDomainTool

logger = logging.getLogger(__name__)


class HeartRateZonesInput(BaseModel):
    """Input schema for heart rate zones calculation."""

    age: int = Field(..., ge=13, le=100, description="User age")
    resting_heart_rate: Optional[int] = Field(
        None, ge=30, le=120, description="Resting heart rate in BPM"
    )
    max_heart_rate: Optional[int] = Field(
        None, ge=120, le=220, description="Maximum heart rate in BPM (if known)"
    )
    lactate_threshold_hr: Optional[int] = Field(
        None, ge=100, le=200, description="Lactate threshold heart rate"
    )
    method: str = Field(
        "karvonen",
        description="Calculation method (karvonen, percentage_max, lactate_threshold)",
    )


class RunningPaceZonesInput(BaseModel):
    """Input schema for running pace zones calculation."""

    five_k_time_seconds: Optional[int] = Field(
        None, ge=600, le=3600, description="5K race time in seconds"
    )
    ten_k_time_seconds: Optional[int] = Field(
        None, ge=1200, le=7200, description="10K race time in seconds"
    )
    half_marathon_time_seconds: Optional[int] = Field(
        None, ge=3600, le=14400, description="Half marathon time in seconds"
    )
    marathon_time_seconds: Optional[int] = Field(
        None, ge=7200, le=28800, description="Marathon time in seconds"
    )
    recent_threshold_pace: Optional[int] = Field(
        None, ge=180, le=900, description="Recent threshold pace in seconds per mile"
    )
    preferred_distance: str = Field(
        "10k", description="Preferred race distance for zone calculation"
    )


class CyclingPowerZonesInput(BaseModel):
    """Input schema for cycling power zones calculation."""

    ftp_watts: Optional[int] = Field(
        None, ge=50, le=600, description="Functional Threshold Power in watts"
    )
    twenty_min_avg_power: Optional[int] = Field(
        None, ge=50, le=600, description="20-minute average power in watts"
    )
    one_hour_avg_power: Optional[int] = Field(
        None, ge=50, le=600, description="1-hour average power in watts"
    )
    weight_kg: float = Field(
        ..., gt=0, description="User weight in kg for power-to-weight calculations"
    )
    cycling_experience: str = Field(
        "recreational",
        description="Experience level (beginner/recreational/competitive/elite)",
    )


class SwimmingPaceZonesInput(BaseModel):
    """Input schema for swimming pace zones calculation."""

    hundred_meter_time_seconds: Optional[int] = Field(
        None, ge=60, le=300, description="100m swim time in seconds"
    )
    four_hundred_meter_time_seconds: Optional[int] = Field(
        None, ge=240, le=1200, description="400m swim time in seconds"
    )
    fifteen_hundred_meter_time_seconds: Optional[int] = Field(
        None, ge=900, le=3600, description="1500m swim time in seconds"
    )
    stroke_type: str = Field(
        "freestyle",
        description="Primary stroke (freestyle/backstroke/breaststroke/butterfly)",
    )
    pool_length: int = Field(25, description="Pool length in meters (25 or 50)")


class TrainingZonesInput(BaseModel):
    """Complete training zones input schema."""

    user_id: str = Field(..., description="Unique user identifier")
    sport_type: str = Field(
        ..., description="Primary sport (running/cycling/swimming/triathlon)"
    )
    heart_rate_zones: HeartRateZonesInput
    running_pace_zones: Optional[RunningPaceZonesInput] = None
    cycling_power_zones: Optional[CyclingPowerZonesInput] = None
    swimming_pace_zones: Optional[SwimmingPaceZonesInput] = None
    training_focus: str = Field(
        "general_fitness",
        description="Training focus (endurance/speed/general_fitness/competition)",
    )
    calculation_date: datetime = Field(default_factory=datetime.now)


class HeartRateZonesOutput(BaseModel):
    """Heart rate zones calculation output."""

    zone_1_recovery: Dict[str, int] = Field(
        ..., description="Recovery zone (50-60% max HR)"
    )
    zone_2_aerobic: Dict[str, int] = Field(
        ..., description="Aerobic base zone (60-70% max HR)"
    )
    zone_3_threshold: Dict[str, int] = Field(
        ..., description="Aerobic threshold zone (70-80% max HR)"
    )
    zone_4_lactate: Dict[str, int] = Field(
        ..., description="Lactate threshold zone (80-90% max HR)"
    )
    zone_5_neuromuscular: Dict[str, int] = Field(
        ..., description="Neuromuscular power zone (90-100% max HR)"
    )
    calculation_method: str = Field(..., description="Method used for calculation")
    max_hr_used: int = Field(..., description="Maximum heart rate used in calculations")
    resting_hr_used: Optional[int] = Field(
        None, description="Resting heart rate used (if applicable)"
    )


class RunningPaceZonesOutput(BaseModel):
    """Running pace zones calculation output."""

    zone_1_recovery: Dict[str, str] = Field(..., description="Recovery pace zone")
    zone_2_aerobic: Dict[str, str] = Field(..., description="Aerobic base pace zone")
    zone_3_tempo: Dict[str, str] = Field(..., description="Tempo/threshold pace zone")
    zone_4_lactate: Dict[str, str] = Field(
        ..., description="Lactate threshold pace zone"
    )
    zone_5_vo2max: Dict[str, str] = Field(..., description="VO2 max pace zone")
    zone_6_anaerobic: Dict[str, str] = Field(
        ..., description="Anaerobic/neuromuscular pace zone"
    )
    reference_time_used: Dict[str, int] = Field(
        ..., description="Reference race time used for calculations"
    )
    pace_format: str = Field("minutes_per_mile", description="Pace format used")


class CyclingPowerZonesOutput(BaseModel):
    """Cycling power zones calculation output."""

    zone_1_recovery: Dict[str, int] = Field(
        ..., description="Recovery power zone (<55% FTP)"
    )
    zone_2_endurance: Dict[str, int] = Field(
        ..., description="Endurance power zone (55-75% FTP)"
    )
    zone_3_tempo: Dict[str, int] = Field(
        ..., description="Tempo power zone (75-90% FTP)"
    )
    zone_4_threshold: Dict[str, int] = Field(
        ..., description="Lactate threshold zone (90-105% FTP)"
    )
    zone_5_vo2max: Dict[str, int] = Field(
        ..., description="VO2 max power zone (105-120% FTP)"
    )
    zone_6_anaerobic: Dict[str, int] = Field(
        ..., description="Anaerobic capacity zone (120%+ FTP)"
    )
    ftp_used: int = Field(..., description="FTP value used for calculations")
    power_to_weight_ratio: float = Field(
        ..., description="Power-to-weight ratio (watts/kg)"
    )


class SwimmingPaceZonesOutput(BaseModel):
    """Swimming pace zones calculation output."""

    zone_1_recovery: Dict[str, str] = Field(..., description="Recovery pace zone")
    zone_2_aerobic: Dict[str, str] = Field(
        ..., description="Aerobic endurance pace zone"
    )
    zone_3_threshold: Dict[str, str] = Field(..., description="Threshold pace zone")
    zone_4_vo2max: Dict[str, str] = Field(..., description="VO2 max pace zone")
    zone_5_anaerobic: Dict[str, str] = Field(..., description="Anaerobic pace zone")
    reference_time_used: Dict[str, int] = Field(
        ..., description="Reference time used for calculations"
    )
    pace_format: str = Field("minutes_per_100m", description="Pace format used")


class ZoneRecommendationsOutput(BaseModel):
    """Training zone recommendations and guidelines."""

    time_distribution: Dict[str, float] = Field(
        ..., description="Recommended time distribution across zones"
    )
    training_focus_recommendations: Dict[str, List[str]] = Field(
        ..., description="Zone-specific training recommendations"
    )
    session_examples: Dict[str, Dict[str, Any]] = Field(
        ..., description="Example training sessions for each zone"
    )
    progression_guidelines: List[str] = Field(
        ..., description="Guidelines for zone-based progression"
    )
    monitoring_recommendations: List[str] = Field(
        ..., description="Recommendations for monitoring training zones"
    )


class TrainingZonesOutput(BaseModel):
    """Complete training zones calculation output."""

    user_id: str
    calculation_id: str
    calculation_date: datetime
    sport_type: str
    heart_rate_zones: HeartRateZonesOutput
    running_pace_zones: Optional[RunningPaceZonesOutput] = None
    cycling_power_zones: Optional[CyclingPowerZonesOutput] = None
    swimming_pace_zones: Optional[SwimmingPaceZonesOutput] = None
    zone_recommendations: ZoneRecommendationsOutput
    rpe_correlations: Dict[str, int] = Field(
        ..., description="RPE correlations for each zone"
    )
    next_recalculation_date: datetime = Field(
        ..., description="Recommended date for zone recalculation"
    )


class TrainingZonesCalculator(BaseDomainTool):
    """
    Comprehensive training zones calculator providing personalized heart rate,
    pace, and power zones for various sports with training recommendations.
    """

    domain: str = "cardiovascular_training"
    name: str = "training_zones_calculator"
    description: str = (
        "Calculate personalized training zones for heart rate, pace, and power"
    )

    def __init__(self):
        super().__init__()
        self.pace_equivalency_factors = self._load_pace_equivalency_factors()
        self.rpe_zone_mapping = self._load_rpe_zone_mapping()

    def _load_pace_equivalency_factors(self) -> Dict[str, Dict[str, float]]:
        """Load pace equivalency factors for different distances."""
        return {
            "5k_to_other": {
                "mile": 0.95,  # 5K pace is typically 5% faster than mile pace
                "10k": 1.05,  # 10K pace is typically 5% slower than 5K pace
                "half_marathon": 1.15,  # Half marathon pace is ~15% slower than 5K
                "marathon": 1.25,  # Marathon pace is ~25% slower than 5K
                "threshold": 1.08,  # Threshold pace is ~8% slower than 5K
            },
            "10k_to_other": {
                "5k": 0.95,
                "mile": 0.90,
                "half_marathon": 1.10,
                "marathon": 1.20,
                "threshold": 1.03,
            },
        }

    def _load_rpe_zone_mapping(self) -> Dict[str, int]:
        """Load RPE (Rate of Perceived Exertion) mapping for training zones."""
        return {
            "zone_1": 3,  # Very light
            "zone_2": 5,  # Light
            "zone_3": 6,  # Moderate
            "zone_4": 8,  # Hard
            "zone_5": 9,  # Very hard
        }

    def calculate_heart_rate_zones(
        self, hr_input: HeartRateZonesInput
    ) -> HeartRateZonesOutput:
        """Calculate heart rate training zones using specified method."""

        # Determine maximum heart rate
        if hr_input.max_heart_rate:
            max_hr = hr_input.max_heart_rate
        else:
            # Use age-based formula (220 - age)
            max_hr = 220 - hr_input.age

        resting_hr = hr_input.resting_heart_rate or 70  # Default if not provided

        if hr_input.method == "karvonen":
            # Karvonen method uses heart rate reserve
            hr_reserve = max_hr - resting_hr

            zones = {
                "zone_1_recovery": {
                    "min_hr": int(resting_hr + (hr_reserve * 0.50)),
                    "max_hr": int(resting_hr + (hr_reserve * 0.60)),
                },
                "zone_2_aerobic": {
                    "min_hr": int(resting_hr + (hr_reserve * 0.60)),
                    "max_hr": int(resting_hr + (hr_reserve * 0.70)),
                },
                "zone_3_threshold": {
                    "min_hr": int(resting_hr + (hr_reserve * 0.70)),
                    "max_hr": int(resting_hr + (hr_reserve * 0.80)),
                },
                "zone_4_lactate": {
                    "min_hr": int(resting_hr + (hr_reserve * 0.80)),
                    "max_hr": int(resting_hr + (hr_reserve * 0.90)),
                },
                "zone_5_neuromuscular": {
                    "min_hr": int(resting_hr + (hr_reserve * 0.90)),
                    "max_hr": max_hr,
                },
            }

        elif hr_input.method == "lactate_threshold" and hr_input.lactate_threshold_hr:
            # Lactate threshold-based zones
            lt_hr = hr_input.lactate_threshold_hr

            zones = {
                "zone_1_recovery": {
                    "min_hr": int(lt_hr * 0.68),
                    "max_hr": int(lt_hr * 0.78),
                },
                "zone_2_aerobic": {
                    "min_hr": int(lt_hr * 0.78),
                    "max_hr": int(lt_hr * 0.88),
                },
                "zone_3_threshold": {
                    "min_hr": int(lt_hr * 0.88),
                    "max_hr": int(lt_hr * 0.95),
                },
                "zone_4_lactate": {
                    "min_hr": int(lt_hr * 0.95),
                    "max_hr": int(lt_hr * 1.05),
                },
                "zone_5_neuromuscular": {"min_hr": int(lt_hr * 1.05), "max_hr": max_hr},
            }

        else:
            # Simple percentage of max HR method
            zones = {
                "zone_1_recovery": {
                    "min_hr": int(max_hr * 0.50),
                    "max_hr": int(max_hr * 0.60),
                },
                "zone_2_aerobic": {
                    "min_hr": int(max_hr * 0.60),
                    "max_hr": int(max_hr * 0.70),
                },
                "zone_3_threshold": {
                    "min_hr": int(max_hr * 0.70),
                    "max_hr": int(max_hr * 0.80),
                },
                "zone_4_lactate": {
                    "min_hr": int(max_hr * 0.80),
                    "max_hr": int(max_hr * 0.90),
                },
                "zone_5_neuromuscular": {
                    "min_hr": int(max_hr * 0.90),
                    "max_hr": max_hr,
                },
            }

        return HeartRateZonesOutput(
            **zones,
            calculation_method=hr_input.method,
            max_hr_used=max_hr,
            resting_hr_used=resting_hr if hr_input.resting_heart_rate else None,
        )

    def calculate_running_pace_zones(
        self, pace_input: RunningPaceZonesInput
    ) -> RunningPaceZonesOutput:
        """Calculate running pace zones based on race performances."""

        # Determine reference pace (prefer 10K, fall back to other distances)
        reference_time = None
        reference_distance = None

        if pace_input.ten_k_time_seconds:
            reference_time = pace_input.ten_k_time_seconds
            reference_distance = "10k"
        elif pace_input.five_k_time_seconds:
            reference_time = pace_input.five_k_time_seconds
            reference_distance = "5k"
        elif pace_input.half_marathon_time_seconds:
            reference_time = pace_input.half_marathon_time_seconds
            reference_distance = "half_marathon"
        elif pace_input.marathon_time_seconds:
            reference_time = pace_input.marathon_time_seconds
            reference_distance = "marathon"

        if not reference_time:
            # Use threshold pace if provided
            if pace_input.recent_threshold_pace:
                threshold_pace_per_mile = pace_input.recent_threshold_pace
            else:
                raise ValueError(
                    "No race times or threshold pace provided for pace zone calculation"
                )
        else:
            # Calculate threshold pace from race time
            threshold_pace_per_mile = self._calculate_threshold_pace_from_race(
                reference_time, reference_distance
            )

        # Calculate pace zones based on threshold pace
        zones = self._calculate_pace_zones_from_threshold(threshold_pace_per_mile)

        return RunningPaceZonesOutput(
            **zones,
            reference_time_used={
                reference_distance
                or "threshold": reference_time
                or threshold_pace_per_mile
            },
            pace_format="minutes_per_mile",
        )

    def _calculate_threshold_pace_from_race(
        self, race_time_seconds: int, distance: str
    ) -> int:
        """Calculate threshold pace from race performance."""

        distance_miles = {
            "5k": 3.107,
            "10k": 6.214,
            "half_marathon": 13.109,
            "marathon": 26.218,
        }

        race_pace_per_mile = race_time_seconds / distance_miles[distance]

        # Convert race pace to threshold pace using established relationships
        pace_adjustments = {
            "5k": 1.08,  # Threshold is ~8% slower than 5K pace
            "10k": 1.03,  # Threshold is ~3% slower than 10K pace
            "half_marathon": 0.95,  # Threshold is ~5% faster than half marathon pace
            "marathon": 0.85,  # Threshold is ~15% faster than marathon pace
        }

        threshold_pace = race_pace_per_mile * pace_adjustments[distance]
        return int(threshold_pace)

    def _calculate_pace_zones_from_threshold(
        self, threshold_pace_seconds: int
    ) -> Dict[str, Dict[str, str]]:
        """Calculate all pace zones from threshold pace."""

        def seconds_to_pace_string(seconds: float) -> str:
            """Convert seconds per mile to MM:SS format."""
            minutes = int(seconds // 60)
            seconds = int(seconds % 60)
            return f"{minutes}:{seconds:02d}"

        # Zone calculations based on threshold pace
        zones = {
            "zone_1_recovery": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 1.20),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 1.35),
                "description": "Easy recovery pace",
            },
            "zone_2_aerobic": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 1.10),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 1.20),
                "description": "Aerobic base building pace",
            },
            "zone_3_tempo": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 1.03),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 1.10),
                "description": "Tempo/comfortably hard pace",
            },
            "zone_4_lactate": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 0.98),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 1.03),
                "description": "Lactate threshold pace",
            },
            "zone_5_vo2max": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 0.90),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 0.98),
                "description": "VO2 max interval pace",
            },
            "zone_6_anaerobic": {
                "min_pace": seconds_to_pace_string(threshold_pace_seconds * 0.80),
                "max_pace": seconds_to_pace_string(threshold_pace_seconds * 0.90),
                "description": "Anaerobic/neuromuscular pace",
            },
        }

        return zones

    def calculate_cycling_power_zones(
        self, power_input: CyclingPowerZonesInput
    ) -> CyclingPowerZonesOutput:
        """Calculate cycling power zones based on FTP."""

        # Determine FTP
        if power_input.ftp_watts:
            ftp = power_input.ftp_watts
        elif power_input.twenty_min_avg_power:
            # FTP is typically 95% of 20-minute power
            ftp = int(power_input.twenty_min_avg_power * 0.95)
        elif power_input.one_hour_avg_power:
            # 1-hour power is approximately FTP
            ftp = power_input.one_hour_avg_power
        else:
            raise ValueError("No power data provided for power zone calculation")

        # Calculate power zones based on FTP
        zones = {
            "zone_1_recovery": {
                "min_watts": 0,
                "max_watts": int(ftp * 0.55),
                "description": "Active recovery",
            },
            "zone_2_endurance": {
                "min_watts": int(ftp * 0.55),
                "max_watts": int(ftp * 0.75),
                "description": "Endurance/aerobic base",
            },
            "zone_3_tempo": {
                "min_watts": int(ftp * 0.75),
                "max_watts": int(ftp * 0.90),
                "description": "Tempo/aerobic threshold",
            },
            "zone_4_threshold": {
                "min_watts": int(ftp * 0.90),
                "max_watts": int(ftp * 1.05),
                "description": "Lactate threshold",
            },
            "zone_5_vo2max": {
                "min_watts": int(ftp * 1.05),
                "max_watts": int(ftp * 1.20),
                "description": "VO2 max",
            },
            "zone_6_anaerobic": {
                "min_watts": int(ftp * 1.20),
                "max_watts": 9999,  # No upper limit
                "description": "Anaerobic capacity",
            },
        }

        # Calculate power-to-weight ratio
        power_to_weight = ftp / power_input.weight_kg

        return CyclingPowerZonesOutput(
            **zones, ftp_used=ftp, power_to_weight_ratio=round(power_to_weight, 2)
        )

    def calculate_swimming_pace_zones(
        self, swim_input: SwimmingPaceZonesInput
    ) -> SwimmingPaceZonesOutput:
        """Calculate swimming pace zones based on test times."""

        # Determine reference pace (prefer 400m)
        reference_time = None
        reference_distance = None

        if swim_input.four_hundred_meter_time_seconds:
            reference_time = swim_input.four_hundred_meter_time_seconds
            reference_distance = "400m"
            threshold_pace_per_100m = reference_time / 4  # 400m time divided by 4
        elif swim_input.hundred_meter_time_seconds:
            reference_time = swim_input.hundred_meter_time_seconds
            reference_distance = "100m"
            threshold_pace_per_100m = (
                reference_time * 1.05
            )  # Threshold is ~5% slower than 100m pace
        elif swim_input.fifteen_hundred_meter_time_seconds:
            reference_time = swim_input.fifteen_hundred_meter_time_seconds
            reference_distance = "1500m"
            threshold_pace_per_100m = (
                reference_time / 15
            ) * 0.98  # Threshold is ~2% faster than 1500m pace
        else:
            raise ValueError("No swimming times provided for pace zone calculation")

        # Calculate pace zones
        def seconds_to_swim_pace(seconds: float) -> str:
            """Convert seconds per 100m to MM:SS format."""
            minutes = int(seconds // 60)
            seconds = int(seconds % 60)
            return f"{minutes}:{seconds:02d}"

        zones = {
            "zone_1_recovery": {
                "min_pace": seconds_to_swim_pace(threshold_pace_per_100m * 1.15),
                "max_pace": seconds_to_swim_pace(threshold_pace_per_100m * 1.25),
                "description": "Easy recovery swimming",
            },
            "zone_2_aerobic": {
                "min_pace": seconds_to_swim_pace(threshold_pace_per_100m * 1.05),
                "max_pace": seconds_to_swim_pace(threshold_pace_per_100m * 1.15),
                "description": "Aerobic endurance pace",
            },
            "zone_3_threshold": {
                "min_pace": seconds_to_swim_pace(threshold_pace_per_100m * 0.98),
                "max_pace": seconds_to_swim_pace(threshold_pace_per_100m * 1.05),
                "description": "Threshold pace",
            },
            "zone_4_vo2max": {
                "min_pace": seconds_to_swim_pace(threshold_pace_per_100m * 0.90),
                "max_pace": seconds_to_swim_pace(threshold_pace_per_100m * 0.98),
                "description": "VO2 max pace",
            },
            "zone_5_anaerobic": {
                "min_pace": seconds_to_swim_pace(threshold_pace_per_100m * 0.80),
                "max_pace": seconds_to_swim_pace(threshold_pace_per_100m * 0.90),
                "description": "Anaerobic/sprint pace",
            },
        }

        return SwimmingPaceZonesOutput(
            **zones,
            reference_time_used={reference_distance: reference_time},
            pace_format="minutes_per_100m",
        )

    def generate_zone_recommendations(
        self, zones_input: TrainingZonesInput
    ) -> ZoneRecommendationsOutput:
        """Generate training recommendations based on zones and training focus."""

        # Time distribution recommendations based on training focus
        if zones_input.training_focus == "endurance":
            time_distribution = {
                "zone_1": 0.15,
                "zone_2": 0.65,
                "zone_3": 0.15,
                "zone_4": 0.04,
                "zone_5": 0.01,
            }
        elif zones_input.training_focus == "speed":
            time_distribution = {
                "zone_1": 0.20,
                "zone_2": 0.50,
                "zone_3": 0.15,
                "zone_4": 0.10,
                "zone_5": 0.05,
            }
        elif zones_input.training_focus == "competition":
            time_distribution = {
                "zone_1": 0.20,
                "zone_2": 0.55,
                "zone_3": 0.15,
                "zone_4": 0.07,
                "zone_5": 0.03,
            }
        else:  # general_fitness
            time_distribution = {
                "zone_1": 0.25,
                "zone_2": 0.50,
                "zone_3": 0.20,
                "zone_4": 0.04,
                "zone_5": 0.01,
            }

        # Training focus recommendations for each zone
        focus_recommendations = {
            "zone_1": [
                "Active recovery sessions",
                "Easy aerobic maintenance",
                "Movement quality focus",
                "Stress reduction and regeneration",
            ],
            "zone_2": [
                "Aerobic base building",
                "Fat oxidation development",
                "Capillary density improvement",
                "Long steady-state sessions",
            ],
            "zone_3": [
                "Tempo workouts",
                "Aerobic threshold development",
                "Moderate sustained efforts",
                "Rhythm and pace practice",
            ],
            "zone_4": [
                "Lactate threshold intervals",
                "Time trial efforts",
                "Sustained hard efforts",
                "Metabolic conditioning",
            ],
            "zone_5": [
                "VO2 max intervals",
                "Short high-intensity efforts",
                "Neuromuscular power development",
                "Peak power training",
            ],
        }

        # Example training sessions for each zone
        session_examples = {
            "zone_1": {
                "duration": "45-90 minutes",
                "example": "Easy 60-minute continuous session with focus on form and relaxation",
                "frequency": "2-4 times per week",
            },
            "zone_2": {
                "duration": "60-180 minutes",
                "example": "2-hour steady aerobic session maintaining conversational pace",
                "frequency": "3-5 times per week",
            },
            "zone_3": {
                "duration": "20-60 minutes",
                "example": "30-minute tempo session or 3x10min intervals with 2min recovery",
                "frequency": "1-2 times per week",
            },
            "zone_4": {
                "duration": "20-40 minutes total",
                "example": "4x8min intervals at threshold pace with 3min recovery",
                "frequency": "1-2 times per week",
            },
            "zone_5": {
                "duration": "15-30 minutes total",
                "example": "6x3min intervals at VO2 max intensity with 3min recovery",
                "frequency": "1 time per week",
            },
        }

        # Progression guidelines
        progression_guidelines = [
            "Start with 80% time in zones 1-2 for first 4-6 weeks",
            "Gradually introduce zone 3 work after aerobic base is established",
            "Add zone 4 intervals only after consistent zone 2-3 training",
            "Zone 5 work should be limited and only for advanced athletes",
            "Monitor weekly training stress and adjust intensity distribution accordingly",
            "Allow 48-72 hours recovery between high-intensity zone 4-5 sessions",
        ]

        # Monitoring recommendations
        monitoring_recommendations = [
            "Use heart rate monitor for zones 1-3 training",
            "Use pace/power for zones 4-5 when precision is critical",
            "Track RPE (Rate of Perceived Exertion) for all sessions",
            "Monitor resting heart rate for overtraining indicators",
            "Reassess zones every 6-8 weeks or after significant fitness changes",
            "Pay attention to heart rate drift during long zone 2 sessions",
        ]

        return ZoneRecommendationsOutput(
            time_distribution=time_distribution,
            training_focus_recommendations=focus_recommendations,
            session_examples=session_examples,
            progression_guidelines=progression_guidelines,
            monitoring_recommendations=monitoring_recommendations,
        )

    async def calculate_training_zones(
        self, zones_input: TrainingZonesInput
    ) -> TrainingZonesOutput:
        """
        Calculate comprehensive training zones for specified sport.

        Args:
            zones_input: Complete zones calculation input data

        Returns:
            TrainingZonesOutput: Complete training zones and recommendations
        """
        try:
            logger.info(f"Calculating training zones for user {zones_input.user_id}")

            # Calculate heart rate zones (always required)
            hr_zones = self.calculate_heart_rate_zones(zones_input.heart_rate_zones)

            # Calculate sport-specific zones based on input
            running_zones = None
            cycling_zones = None
            swimming_zones = None

            if zones_input.running_pace_zones:
                try:
                    running_zones = self.calculate_running_pace_zones(
                        zones_input.running_pace_zones
                    )
                except ValueError as e:
                    logger.warning(f"Could not calculate running zones: {str(e)}")

            if zones_input.cycling_power_zones:
                try:
                    cycling_zones = self.calculate_cycling_power_zones(
                        zones_input.cycling_power_zones
                    )
                except ValueError as e:
                    logger.warning(f"Could not calculate cycling zones: {str(e)}")

            if zones_input.swimming_pace_zones:
                try:
                    swimming_zones = self.calculate_swimming_pace_zones(
                        zones_input.swimming_pace_zones
                    )
                except ValueError as e:
                    logger.warning(f"Could not calculate swimming zones: {str(e)}")

            # Generate recommendations
            zone_recommendations = self.generate_zone_recommendations(zones_input)

            # Create RPE correlations
            rpe_correlations = self.rpe_zone_mapping.copy()

            # Calculate next recalculation date (6-8 weeks)
            next_recalc = zones_input.calculation_date + timedelta(weeks=7)

            # Generate unique calculation ID
            import uuid

            calculation_id = str(uuid.uuid4())

            logger.info(
                f"Completed training zones calculation for user {zones_input.user_id}"
            )

            return TrainingZonesOutput(
                user_id=zones_input.user_id,
                calculation_id=calculation_id,
                calculation_date=zones_input.calculation_date,
                sport_type=zones_input.sport_type,
                heart_rate_zones=hr_zones,
                running_pace_zones=running_zones,
                cycling_power_zones=cycling_zones,
                swimming_pace_zones=swimming_zones,
                zone_recommendations=zone_recommendations,
                rpe_correlations=rpe_correlations,
                next_recalculation_date=next_recalc,
            )

        except Exception as e:
            logger.error(f"Error calculating training zones: {str(e)}")
            raise
