"""
Mem0 configuration for Athlea LangGraph integration.
Provides configuration for memory management using various vector stores.
"""

from typing import Any, Dict
from . import config
from athlea_langgraph.utils.embedding_util import get_embedding_config_for_mem0


def create_mem0_config_dict(environment: str = "development") -> Dict[str, Any]:
    """
    Create mem0 configuration dictionary with supported providers.

    Args:
        environment: Environment name (development, staging, production)

    Returns:
        Dictionary containing mem0 configuration
    """

    pinecone_api_key = config.mem0.pinecone_api_key

    # Re-enable Pinecone with correct configuration format
    # Based on Mem0 documentation: https://docs.mem0.ai/components/vectordbs/dbs/pinecone

    if pinecone_api_key:
        # Use Pinecone with serverless configuration (recommended for new indexes)
        vector_store_config = {
            "provider": "pinecone",
            "config": {
                "collection_name": "athlea-memory",  # Use the existing index
                "embedding_model_dims": 3072,  # Match text-embedding-3-large dimensions
                "api_key": pinecone_api_key,
                "serverless_config": {
                    "cloud": "aws",
                    "region": config.mem0.pinecone_environment,
                },
                "metric": "cosine",  # Default similarity metric
                "batch_size": 100,  # Default batch size
            },
        }
    elif environment == "production":
        # Fallback to local Qdrant for production if no Pinecone
        vector_store_config = {
            "provider": "qdrant",
            "config": {
                "url": "http://localhost:6333",
                "collection_name": "athlea_memories",
                "embedding_model_dims": 3072,  # text-embedding-3-large dimensions
            },
        }
    else:
        # Use local Qdrant for development/staging (fallback)
        vector_store_config = {
            "provider": "qdrant",
            "config": {
                "url": "http://localhost:6333",
                "collection_name": "athlea_memories",
                "embedding_model_dims": 3072,  # text-embedding-3-large dimensions
            },
        }

    # Embedding configuration using our frontend-compatible utility
    embedding_config = get_embedding_config_for_mem0()

    # LLM configuration (use Azure OpenAI)
    llm_config = {
        "provider": "azure_openai",
        "config": {
            "model": config.azure_openai.chat_deployment,
            "temperature": 0.1,
            "max_tokens": 1000,
            "azure_kwargs": {
                "azure_deployment": config.azure_openai.chat_deployment,
                "api_version": config.azure_openai.api_version,
                "azure_endpoint": config.azure_openai.endpoint,
                "api_key": config.azure_openai.api_key,
            },
        },
    }

    return {
        "vector_store": vector_store_config,
        "embedder": embedding_config,
        "llm": llm_config,
        "version": "v1.1",
    }


# Environment-specific configurations
DEV_CONFIG = create_mem0_config_dict("development")
STAGING_CONFIG = create_mem0_config_dict("staging")
PROD_CONFIG = create_mem0_config_dict("production")


def get_mem0_config(environment: str = "development") -> Dict[str, Any]:
    """
    Get mem0 configuration for specific environment.

    Args:
        environment: Environment name

    Returns:
        Configuration dictionary
    """
    configs = {
        "development": DEV_CONFIG,
        "staging": STAGING_CONFIG,
        "production": PROD_CONFIG,
    }

    return configs.get(environment, DEV_CONFIG)
