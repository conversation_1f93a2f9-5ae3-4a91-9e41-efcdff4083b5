import os
import sys
from langsmith import Client, __version__ as langsmith_version
from langsmith.utils import LangSmithError
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
# Paste the FULL Lang<PERSON>mith trace URL here
TRACE_URL = "https://smith.langchain.com/o/33b93de5-f986-4c9b-b36e-dbe83a0b433a/projects/p/5d919fce-f4c8-4ca6-adcf-7cee54291c7a/r/1f04485f-273b-6680-9f5b-0442d2391a8d?trace_id=1f04485f-273b-6680-9f5b-0442d2391a8d&start_time=2025-06-08T16%3A30%3A54.899938"
# ---------------------

def analyze_trace(trace_url: str):
    """Fetches and prints a detailed analysis of a <PERSON><PERSON><PERSON> trace."""
    
    if os.getenv("LANGSMITH_API_KEY") is None:
        print("❌ Error: LANGSMITH_API_KEY environment variable not set or found in .env file.")
        print("Please get your key from https://smith.langchain.com/settings")
        return

    if "YOUR_TRACE_URL_HERE" in trace_url:
        print("❌ Error: Please replace 'YOUR_TRACE_URL_HERE' with an actual LangSmith trace URL in the script.")
        return

    try:
        print("🔍 Initializing LangSmith client...")
        client = Client()
        print("✅ Client initialized.")
        
        run_id = trace_url.split("/r/")[1].split("?")[0]
        print(f"🔎 Fetching trace for Run ID: {run_id}...")
        
        # Get the top-level run
        print("  - Reading parent run...")
        parent_run = client.read_run(run_id)
        print("  - Parent run successfully read.")
        
        print("\n" + "="*50)
        print("📊 TRACE SUMMARY")
        print("="*50)
        print(f"  - Run ID: {parent_run.id}")
        print(f"  - Status: {'✅ SUCCESS' if parent_run.error is None else '❌ FAILED'}")
        if parent_run.error:
            print(f"  - Error: {parent_run.error}")
        if parent_run.end_time and parent_run.start_time:
            latency = (parent_run.end_time - parent_run.start_time).total_seconds()
            print(f"  - Latency: {latency:.2f}s")
        print("\n📝 Inputs:")
        print(parent_run.inputs)
        
        print("\n" + "="*50)
        print("🏃 EXECUTION STEPS")
        print("="*50)

        # Get all child runs (the steps)
        print("  - Listing child runs...")
        steps = list(client.list_runs(trace_id=parent_run.trace_id, execution_order=1))
        print(f"  - Found {len(steps)} steps.")
        
        if not steps:
            print("No detailed steps found for this run.")
        
        for step in steps:
            status = '❌ Error' if step.error else '✅ OK'
            print(f"\n▶️ Node: {step.name} ({step.run_type}) | Status: {status}")
            
            if step.inputs:
                print("  Inputs:")
                print(f"    {step.inputs}")
            
            if step.outputs:
                print("  Outputs:")
                print(f"    {step.outputs}")
            
            if step.error:
                print("  Error Details:")
                print(f"    {step.error}")
        
        print("\n" + "="*50)
        print("✅ Trace analysis complete.")

    except LangSmithError as e:
        print(f"❌ LangSmith API Error: {e}")
        print("Please ensure your API key and the trace URL are correct.")
    except (IndexError, TypeError):
        print(f"❌ Invalid URL format. Please paste the full LangSmith trace URL.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def main():
    """Main function to run the trace analysis."""
    print("--- Script Starting ---")
    print(f"Python version: {sys.version}")
    print(f"LangSmith client version: {langsmith_version}")
    
    try:
        analyze_trace(TRACE_URL)
    except Exception as e:
        print(f"❌ A critical, unhandled exception occurred in main: {e}")
        import traceback
        traceback.print_exc()
        
    print("--- Script Finished ---")

if __name__ == "__main__":
    main() 