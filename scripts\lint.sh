#!/bin/bash
# lint.sh - Comprehensive linting for python-langgraph

# Add Python 3.9 user bin to PATH for linting tools (macOS compatibility)
export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"

echo "🔍 Running Python linting suite..."

echo "📝 Checking code formatting with Black..."
if poetry run black --check .; then
    echo "✅ Black formatting check passed"
else
    echo "❌ Black formatting issues found. Run 'poetry run black .' to fix"
    exit 1
fi

echo "📦 Checking import sorting with isort..."
if poetry run isort --check-only .; then
    echo "✅ isort check passed"
else
    echo "❌ Import sorting issues found. Run 'poetry run isort .' to fix"
    exit 1
fi

echo "🔧 Running type checking with MyPy..."
if poetry run mypy athlea_langgraph/; then
    echo "✅ MyPy type checking passed"
else
    echo "❌ Type checking issues found"
    exit 1
fi

echo "🎉 All linting checks passed!" 