"""
Tests for Goal Tracker Tool

Comprehensive test suite covering goal creation, tracking functionality,
SMART validation, and progress analysis.
"""

import pytest
from datetime import datetime, timedelta

from athlea_langgraph.tools.mental.goal_tracker import GoalTracker
from athlea_langgraph.tools.schemas.mental_schemas import (
    GoalCategory,
    GoalProgressInput,
    GoalStatus,
    GoalSummaryOutput,
    GoalTrackingOutput,
    MentalHealthGoalInput,
)


class TestGoalTracker:
    """Test suite for Goal Tracker Tool."""

    @pytest.fixture
    def goal_tracker(self):
        """Create goal tracker instance."""
        return GoalTracker()

    @pytest.fixture
    def well_structured_goal(self):
        """Well-structured SMART goal for testing."""
        return MentalHealthGoalInput(
            user_id="user_123",
            goal_category=GoalCategory.HABIT_FORMATION,
            specific_outcome="Establish a daily 20-minute meditation practice to improve focus and reduce stress",
            measurement_method="Track completed meditation sessions daily",
            difficulty_level=5,
            target_completion_date=datetime.now() + timedelta(days=60),
            motivation_reasons=[
                "Improve mental clarity and focus",
                "Reduce daily stress and anxiety",
                "Better sleep quality",
            ],
            daily_actions=[
                "Meditate for 20 minutes after waking up",
                "Use meditation app for guidance",
                "Create peaceful meditation space",
            ],
            weekly_milestones=[
                "Complete 7 consecutive days of meditation",
                "Notice improved focus during work",
                "Feel more relaxed in the evenings",
            ],
            potential_obstacles=[
                "Forgetting to meditate in the morning",
                "Lack of motivation on difficult days",
                "Time constraints with busy schedule",
            ],
            support_strategies=[
                "Set phone reminder for meditation time",
                "Join online meditation community",
                "Start with shorter sessions if needed",
            ],
            accountability_methods=[
                "Daily check-in with meditation app",
                "Weekly progress review",
            ],
        )

    @pytest.fixture
    def poorly_structured_goal(self):
        """Poorly structured goal for testing validation."""
        return MentalHealthGoalInput(
            user_id="user_456",
            goal_category=GoalCategory.MENTAL_HEALTH,
            specific_outcome="Be happier",
            difficulty_level=8,
            target_completion_date=datetime.now() + timedelta(days=1),
            motivation_reasons=["Want to feel better"],
            daily_actions=[],
            weekly_milestones=[],
            potential_obstacles=[],
            support_strategies=[],
        )

    @pytest.fixture
    def excellent_goal(self):
        """Excellently structured goal for testing."""
        return MentalHealthGoalInput(
            user_id="user_789",
            goal_category=GoalCategory.STRESS_MANAGEMENT,
            specific_outcome="Develop comprehensive stress management skills including breathing techniques, time management, and boundary setting to maintain work-life balance",
            measurement_method="Weekly stress level ratings and coping strategy usage tracking",
            difficulty_level=6,
            target_completion_date=datetime.now() + timedelta(days=90),
            motivation_reasons=[
                "Improve work performance and satisfaction",
                "Better relationships with family and friends",
                "Long-term health and wellbeing",
                "Career advancement opportunities",
            ],
            daily_actions=[
                "Practice 5-minute breathing exercises",
                "Set clear work boundaries and stick to them",
                "Review and prioritize daily tasks",
                "Take regular breaks throughout workday",
            ],
            weekly_milestones=[
                "Implement all daily stress management practices",
                "Successfully set boundaries in work situations",
                "Notice reduced stress levels in evening",
                "Improved sleep quality and energy",
            ],
            potential_obstacles=[
                "Work pressure and deadlines",
                "Colleagues expecting immediate responses",
                "Old habits of overcommitting",
            ],
            support_strategies=[
                "Discuss boundaries with supervisor",
                "Use calendar blocks for focus time",
                "Practice saying no to non-essential tasks",
                "Regular check-ins with stress management coach",
            ],
            accountability_methods=[
                "Weekly review with accountability partner",
                "Daily stress tracking in journal",
                "Monthly professional check-in",
            ],
        )

    @pytest.fixture
    def good_progress_input(self):
        """Good progress tracking input."""
        return GoalProgressInput(
            user_id="user_123",
            goal_id="goal_123",
            progress_percentage=70,
            motivation_level=8,
            actions_completed=[
                "Daily meditation for 3 weeks",
                "Set up dedicated meditation space",
                "Joined online meditation community",
            ],
            challenges_encountered=["Missed sessions during travel week"],
            strategies_used=[
                "Morning routine integration",
                "Accountability partner check-ins",
                "Guided meditation apps",
            ],
            mood_during_progress=7,
            insights_learned=[
                "Consistency is more important than session length",
                "Morning meditation sets positive tone for day",
            ],
            goal_adjustment_needed=False,
        )

    @pytest.fixture
    def struggling_progress_input(self):
        """Struggling progress tracking input."""
        return GoalProgressInput(
            user_id="user_456",
            goal_id="goal_456",
            progress_percentage=25,
            motivation_level=4,
            actions_completed=[
                "Read about stress management",
                "Tried breathing exercises twice",
            ],
            challenges_encountered=[
                "Work too demanding for practice",
                "Forgetting to use techniques",
                "Feeling overwhelmed by multiple strategies",
            ],
            strategies_used=["Reading stress management books"],
            mood_during_progress=4,
            insights_learned=[],
            goal_adjustment_needed=True,
        )

    @pytest.mark.asyncio
    async def test_create_well_structured_goal(
        self, goal_tracker, well_structured_goal
    ):
        """Test creating a well-structured goal."""
        result = await goal_tracker.create_goal(well_structured_goal)

        assert result["status"] == "created"
        assert result["goal_id"]
        assert (
            "high likelihood" in result["success_likelihood"].lower()
            or "good likelihood" in result["success_likelihood"].lower()
        )
        assert len(result["smart_validation"]) > 0
        assert result["creation_timestamp"]

    @pytest.mark.asyncio
    async def test_create_poorly_structured_goal(
        self, goal_tracker, poorly_structured_goal
    ):
        """Test creating a poorly structured goal."""
        result = await goal_tracker.create_goal(poorly_structured_goal)

        assert result["status"] == "created"
        assert "lower likelihood" in result["success_likelihood"].lower()
        assert any(
            "⚠" in validation for validation in result["smart_validation"].split("; ")
        )

    @pytest.mark.asyncio
    async def test_create_excellent_goal(self, goal_tracker, excellent_goal):
        """Test creating an excellently structured goal."""
        result = await goal_tracker.create_goal(excellent_goal)

        assert result["status"] == "created"
        assert "high likelihood" in result["success_likelihood"].lower()
        assert most_validations_positive(result["smart_validation"])

    @pytest.mark.asyncio
    async def test_track_good_progress(self, goal_tracker, good_progress_input):
        """Test tracking good goal progress."""
        result = await goal_tracker.track_goal_progress(good_progress_input)

        assert isinstance(result, GoalTrackingOutput)
        assert result.goal_status == GoalStatus.IN_PROGRESS
        assert (
            "strong" in result.progress_trend.lower()
            or "excellent" in result.progress_trend.lower()
        )
        assert "high likelihood" in result.completion_likelihood.lower()
        assert result.consistency_score >= 6
        assert len(result.most_effective_strategies) > 0
        assert len(result.celebration_milestones) > 0

    @pytest.mark.asyncio
    async def test_track_struggling_progress(
        self, goal_tracker, struggling_progress_input
    ):
        """Test tracking struggling goal progress."""
        result = await goal_tracker.track_goal_progress(struggling_progress_input)

        assert isinstance(result, GoalTrackingOutput)
        assert result.consistency_score <= 5
        assert len(result.biggest_challenges) > 0
        assert len(result.adjustment_recommendations) > 0
        assert len(result.motivation_boosters) > 0

    @pytest.mark.asyncio
    async def test_generate_goal_summary(self, goal_tracker):
        """Test generating goal summary."""
        result = await goal_tracker.generate_goal_summary("user_123")

        assert isinstance(result, GoalSummaryOutput)
        assert result.total_goals > 0
        assert result.active_goals >= 0
        assert result.completed_goals >= 0
        assert 0.0 <= result.goal_completion_rate <= 1.0
        assert len(result.most_successful_categories) > 0
        assert len(result.recommendations) > 0

    def test_validate_smart_criteria(self, goal_tracker, well_structured_goal):
        """Test SMART criteria validation."""
        validations = goal_tracker._validate_smart_criteria(well_structured_goal)

        assert isinstance(validations, list)
        assert len(validations) == 5  # Should validate all SMART criteria
        assert any("specific" in validation.lower() for validation in validations)
        assert any("measurable" in validation.lower() for validation in validations)
        assert any("achievable" in validation.lower() for validation in validations)
        assert any("relevant" in validation.lower() for validation in validations)
        assert any("time-bound" in validation.lower() for validation in validations)

    def test_generate_goal_optimization_suggestions(
        self, goal_tracker, poorly_structured_goal
    ):
        """Test goal optimization suggestions."""
        suggestions = goal_tracker._generate_goal_optimization_suggestions(
            poorly_structured_goal
        )

        assert isinstance(suggestions, list)
        assert len(suggestions) <= 4  # Should be limited
        assert any(suggestion for suggestion in suggestions)

    def test_assess_goal_success_likelihood(
        self, goal_tracker, excellent_goal, poorly_structured_goal
    ):
        """Test goal success likelihood assessment."""
        # Test excellent goal
        excellent_likelihood = goal_tracker._assess_goal_success_likelihood(
            excellent_goal
        )
        assert "high" in excellent_likelihood.lower()

        # Test poor goal
        poor_likelihood = goal_tracker._assess_goal_success_likelihood(
            poorly_structured_goal
        )
        assert (
            "lower" in poor_likelihood.lower() or "moderate" in poor_likelihood.lower()
        )

    def test_determine_goal_status(self, goal_tracker):
        """Test goal status determination."""
        # Test completed goal
        completed_input = GoalProgressInput(
            user_id="test", goal_id="test", progress_percentage=100, motivation_level=8
        )
        assert (
            goal_tracker._determine_goal_status(completed_input) == GoalStatus.COMPLETED
        )

        # Test in-progress goal
        progress_input = GoalProgressInput(
            user_id="test", goal_id="test", progress_percentage=50, motivation_level=6
        )
        assert (
            goal_tracker._determine_goal_status(progress_input)
            == GoalStatus.IN_PROGRESS
        )

        # Test paused goal
        paused_input = GoalProgressInput(
            user_id="test",
            goal_id="test",
            progress_percentage=20,
            motivation_level=2,
            challenges_encountered=["too many challenges", "overwhelmed", "no time"],
        )
        assert goal_tracker._determine_goal_status(paused_input) == GoalStatus.PAUSED

    def test_analyze_progress_trend(
        self, goal_tracker, good_progress_input, struggling_progress_input
    ):
        """Test progress trend analysis."""
        # Test good progress
        good_trend = goal_tracker._analyze_progress_trend(good_progress_input)
        assert "excellent" in good_trend.lower() or "strong" in good_trend.lower()

        # Test struggling progress
        struggling_trend = goal_tracker._analyze_progress_trend(
            struggling_progress_input
        )
        assert (
            "slowing" in struggling_trend.lower()
            or "variable" in struggling_trend.lower()
        )

    def test_assess_completion_likelihood(self, goal_tracker, good_progress_input):
        """Test completion likelihood assessment."""
        likelihood = goal_tracker._assess_completion_likelihood(good_progress_input)
        assert "high likelihood" in likelihood.lower()

    def test_estimate_completion_time(self, goal_tracker):
        """Test completion time estimation."""
        # Test high progress
        high_progress = GoalProgressInput(
            user_id="test", goal_id="test", progress_percentage=85, motivation_level=8
        )
        time_estimate = goal_tracker._estimate_completion_time(high_progress)
        assert "week" in time_estimate.lower()

        # Test low progress
        low_progress = GoalProgressInput(
            user_id="test", goal_id="test", progress_percentage=15, motivation_level=5
        )
        time_estimate = goal_tracker._estimate_completion_time(low_progress)
        assert "month" in time_estimate.lower()

    def test_calculate_consistency_score(self, goal_tracker, good_progress_input):
        """Test consistency score calculation."""
        score = goal_tracker._calculate_consistency_score(good_progress_input)
        assert 1 <= score <= 10
        assert score >= 7  # Should be high for good progress

    def test_identify_effective_strategies(self, goal_tracker, good_progress_input):
        """Test effective strategy identification."""
        strategies = goal_tracker._identify_effective_strategies(good_progress_input)

        assert isinstance(strategies, list)
        assert len(strategies) <= 4  # Should be limited
        assert any("strategy" in strategy.lower() for strategy in strategies)

    def test_identify_biggest_challenges(self, goal_tracker, struggling_progress_input):
        """Test biggest challenge identification."""
        challenges = goal_tracker._identify_biggest_challenges(
            struggling_progress_input
        )

        assert isinstance(challenges, list)
        assert len(challenges) <= 4  # Should be limited
        assert len(challenges) > 0

    def test_generate_adjustment_recommendations(
        self, goal_tracker, struggling_progress_input
    ):
        """Test adjustment recommendation generation."""
        recommendations = goal_tracker._generate_adjustment_recommendations(
            struggling_progress_input
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) <= 4  # Should be limited
        assert len(recommendations) > 0

    def test_generate_motivation_boosters(
        self, goal_tracker, struggling_progress_input
    ):
        """Test motivation booster generation."""
        boosters = goal_tracker._generate_motivation_boosters(struggling_progress_input)

        assert isinstance(boosters, list)
        assert len(boosters) <= 4  # Should be limited
        assert any(
            "motivation" in booster.lower() or "why" in booster.lower()
            for booster in boosters
        )

    def test_generate_support_recommendations(
        self, goal_tracker, struggling_progress_input
    ):
        """Test support recommendation generation."""
        support = goal_tracker._generate_support_recommendations(
            struggling_progress_input
        )

        assert isinstance(support, list)
        assert len(support) <= 3  # Should be limited
        assert len(support) > 0

    def test_identify_celebration_milestones(self, goal_tracker, good_progress_input):
        """Test celebration milestone identification."""
        milestones = goal_tracker._identify_celebration_milestones(good_progress_input)

        assert isinstance(milestones, list)
        assert len(milestones) > 0
        assert any("celebrate" in milestone.lower() for milestone in milestones)

    def test_simulate_goal_summary(self, goal_tracker):
        """Test goal summary simulation."""
        summary = goal_tracker._simulate_goal_summary("test_user")

        assert isinstance(summary, GoalSummaryOutput)
        assert summary.total_goals > 0
        assert summary.average_progress > 0
        assert 0.0 <= summary.goal_completion_rate <= 1.0

    @pytest.mark.asyncio
    async def test_edge_case_completed_goal_tracking(self, goal_tracker):
        """Test tracking a completed goal."""
        completed_input = GoalProgressInput(
            user_id="user_completed",
            goal_id="goal_completed",
            progress_percentage=100,
            motivation_level=9,
            actions_completed=["all actions completed"],
            mood_during_progress=9,
        )

        result = await goal_tracker.track_goal_progress(completed_input)
        assert result.goal_status == GoalStatus.COMPLETED
        assert "Goal already completed" in result.time_to_completion_estimate

    @pytest.mark.asyncio
    async def test_edge_case_very_difficult_goal(self, goal_tracker):
        """Test creating a very difficult goal."""
        difficult_goal = MentalHealthGoalInput(
            user_id="user_difficult",
            goal_category=GoalCategory.MENTAL_HEALTH,
            specific_outcome="Completely transform my mental health and life perspective",
            difficulty_level=9,
            target_completion_date=datetime.now() + timedelta(days=30),
            motivation_reasons=["Need major change"],
            daily_actions=["Everything at once"],
            weekly_milestones=["Be completely different"],
            potential_obstacles=["Everything"],
            support_strategies=["Figure it out"],
        )

        result = await goal_tracker.create_goal(difficult_goal)
        assert (
            "high difficulty" in result["smart_validation"]
            or "ensure adequate support" in result["smart_validation"]
        )

    def test_goal_category_guidance(self, goal_tracker):
        """Test goal category specific guidance."""
        # Test that all categories have guidance
        for category in GoalCategory:
            guidance = goal_tracker.category_guidance.get(category)
            assert guidance is not None
            assert "focus" in guidance
            assert "examples" in guidance
            assert "success_factors" in guidance

    def test_difficulty_level_descriptions(self, goal_tracker):
        """Test difficulty level descriptions."""
        assert len(goal_tracker.difficulty_levels) == 10
        for level in range(1, 11):
            assert level in goal_tracker.difficulty_levels
            assert isinstance(goal_tracker.difficulty_levels[level], str)

    def test_achievement_strategies(self, goal_tracker):
        """Test achievement strategy categories."""
        strategy_categories = [
            "planning",
            "motivation",
            "persistence",
            "behavior_change",
        ]
        for category in strategy_categories:
            assert category in goal_tracker.achievement_strategies
            assert len(goal_tracker.achievement_strategies[category]) > 0

    def test_validation_constraints(self):
        """Test input validation constraints."""
        # Test valid goal input
        valid_goal = MentalHealthGoalInput(
            user_id="test",
            goal_category=GoalCategory.HABIT_FORMATION,
            specific_outcome="Test goal",
            difficulty_level=5,
            target_completion_date=datetime.now() + timedelta(days=30),
            motivation_reasons=["test reason"],
            daily_actions=["test action"],
            weekly_milestones=["test milestone"],
            potential_obstacles=["test obstacle"],
            support_strategies=["test strategy"],
        )
        assert valid_goal.difficulty_level == 5

        # Test invalid difficulty level (should raise validation error)
        with pytest.raises(ValueError):
            MentalHealthGoalInput(
                user_id="test",
                goal_category=GoalCategory.HABIT_FORMATION,
                specific_outcome="Test goal",
                difficulty_level=11,  # Invalid: > 10
                target_completion_date=datetime.now() + timedelta(days=30),
                motivation_reasons=["test reason"],
            )

    def test_tool_properties(self, goal_tracker):
        """Test tool properties and configuration."""
        assert goal_tracker.domain == "mental_training"
        assert goal_tracker.name == "goal_tracker"
        assert goal_tracker.description
        assert hasattr(goal_tracker, "difficulty_levels")
        assert hasattr(goal_tracker, "achievement_strategies")
        assert hasattr(goal_tracker, "category_guidance")
        assert hasattr(goal_tracker, "progress_insights")


def most_validations_positive(validation_string: str) -> bool:
    """Helper function to check if most validations are positive."""
    validations = validation_string.split("; ")
    positive_count = sum(1 for v in validations if v.startswith("✓"))
    return positive_count > len(validations) / 2
