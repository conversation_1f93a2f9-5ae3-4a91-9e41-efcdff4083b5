#!/usr/bin/env python3
"""
Basic test with gpt-4.1-nano to isolate the issue.
Testing simple function calling without complex ReAct.
"""

import asyncio
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai


# Simple test tool
@tool
def calculate_daily_calories(
    age: int,
    gender: str,
    weight_kg: float,
    height_cm: float,
    activity_level: str,
    goal: str,
) -> str:
    """Calculate daily caloric needs based on user profile and goals."""

    # Mifflin-St Jeor equation
    if gender.lower() == "male":
        bmr = (10 * weight_kg) + (6.25 * height_cm) - (5 * age) + 5
    else:
        bmr = (10 * weight_kg) + (6.25 * height_cm) - (5 * age) - 161

    # Activity multipliers
    activity_multipliers = {
        "sedentary": 1.2,
        "lightly_active": 1.375,
        "moderately_active": 1.55,
        "very_active": 1.725,
        "extra_active": 1.9,
    }

    multiplier = activity_multipliers.get(activity_level.lower(), 1.55)
    tdee = bmr * multiplier

    return f"Your estimated daily calories: {int(tdee)} kcal (BMR: {int(bmr)}, Activity: {activity_level})"


async def test_basic_function_calling():
    """Test basic function calling with gpt-4.1-nano."""

    print("🧪 Testing Basic Function Calling with GPT-4.1-nano")
    print("=" * 60)

    # Create LLM with function calling
    llm = create_azure_chat_openai(
        node_type="coaching",
        streaming=False,
        temperature=0.1,  # Lower temperature for more deterministic behavior
    )

    # Bind the tool to the LLM
    llm_with_tools = llm.bind_tools([calculate_daily_calories])

    # Test message
    message = HumanMessage(
        content="I am a 30 year old male, 180cm tall, weigh 80kg, and I'm moderately active. I want to maintain my weight. Please calculate my daily calories."
    )

    print(f"Query: {message.content}")
    print("-" * 60)

    try:
        # Call the LLM
        response = await llm_with_tools.ainvoke([message])

        print("✅ LLM Response:")
        print(f"Content: {response.content}")

        if response.tool_calls:
            print(f"\n🛠️ Tool Calls Found: {len(response.tool_calls)}")
            for i, tool_call in enumerate(response.tool_calls):
                print(f"  {i+1}. Tool: {tool_call['name']}")
                print(f"     Args: {tool_call['args']}")

                # Execute the tool
                tool_result = calculate_daily_calories.invoke(tool_call["args"])
                print(f"     Result: {tool_result}")

            return True
        else:
            print("\n❌ No tool calls found")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


async def test_simple_conversation():
    """Test simple conversation without tools."""

    print("\n🗣️ Testing Simple Conversation (No Tools)")
    print("=" * 60)

    llm = create_azure_chat_openai(
        node_type="coaching", streaming=False, temperature=0.1
    )

    message = HumanMessage(content="Hello! Can you help me with fitness?")

    try:
        response = await llm.ainvoke([message])
        print(f"Query: {message.content}")
        print(f"Response: {response.content}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


async def main():
    """Run all tests."""

    # Test 1: Simple conversation
    conv_success = await test_simple_conversation()

    # Test 2: Function calling
    func_success = await test_basic_function_calling()

    print("\n" + "=" * 60)
    print("📊 RESULTS:")
    print(f"  Simple Conversation: {'✅ PASS' if conv_success else '❌ FAIL'}")
    print(f"  Function Calling: {'✅ PASS' if func_success else '❌ FAIL'}")

    if conv_success and func_success:
        print("\n🎉 Both tests passed! GPT-4.1-nano is working correctly.")
        print("   The issue is likely in the ReAct implementation complexity.")
    elif conv_success and not func_success:
        print("\n⚠️  Conversation works but function calling fails.")
        print("   This suggests a tool binding or parameter issue.")
    else:
        print("\n🚨 Basic functionality is broken.")
        print("   Check your Azure OpenAI configuration.")


if __name__ == "__main__":
    asyncio.run(main())
