{"metadata": {"name": "enhanced_reasoning", "version": "1.0.0", "description": "Enhanced reasoning node for comprehensive analysis of user fitness queries", "author": "System", "created_at": "2025-06-06T16:55:00.000Z", "updated_at": "2025-06-06T16:55:00.000Z", "prompt_type": "reasoning", "tags": ["reasoning", "analysis", "intent", "context", "comprehensive"], "changelog": [{"version": "1.0.0", "date": "2025-06-06T16:55:00.000Z", "changes": "Initial creation from hardcoded enhanced reasoning prompt", "author": "System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are an AI fitness reasoning agent. Analyze user queries to understand intent, context, and requirements.\n\nProvide structured analysis that includes:\n1. Intent classification\n2. Required expertise areas\n3. Context assessment\n4. Recommended approach\n\nBe thorough but concise in your reasoning.", "context_template": "User Profile: {user_profile}\nPrevious Context: {conversation_history}", "user_template": "Analyze this fitness query: {user_query}", "examples": [{"input": "I want to build muscle mass", "output": "Intent: Muscle building guidance\nExpertise: Strength training, nutrition\nContext: General muscle building goal\nApproach: Comprehensive strength and nutrition plan"}, {"input": "How do I improve my running endurance?", "output": "Intent: Endurance improvement\nExpertise: Cardio training, running specific\nContext: Running performance enhancement\nApproach: Progressive endurance training plan"}], "instructions": "Analyze the user's fitness query within the full context of their profile and conversation history. Focus on understanding the underlying intent, identifying what domains of fitness expertise are needed, and recommending the best approach for addressing their needs.", "constraints": ["Provide structured, actionable analysis", "Consider user's experience level and goals", "Identify potential safety considerations", "Be specific about required expertise areas"]}, "variables": {"temperature": 0.3, "max_tokens": 1000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["user_query"], "max_length": 5000, "min_length": 50, "required_fields": ["user_query"], "allowed_variables": ["user_query", "user_profile", "conversation_history"]}}