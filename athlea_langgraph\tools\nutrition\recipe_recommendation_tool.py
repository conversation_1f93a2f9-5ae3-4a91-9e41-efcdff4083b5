"""
Recipe Recommendation Tool

Python implementation of recipe recommendation using Edamam Recipe Search API.
Provides recipe search, nutritional analysis, and meal suggestions.
"""

import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

import aiohttp
from pydantic import BaseModel, Field, ConfigDict

from ..base_tool import BaseDomainTool

logger = logging.getLogger(__name__)


class Recipe(BaseModel):
    """Individual recipe information."""

    label: str  # Recipe name
    source: str  # Source website/blog
    url: str  # Recipe URL
    yield_servings: int = Field(
        default=1
    )  # Number of servings (default to 1 if missing)
    calories: float  # Total calories
    total_time: Optional[int] = None  # Cooking time in minutes
    cuisine_type: List[str] = []  # Cuisine types
    meal_type: List[str] = []  # Meal types (breakfast, lunch, dinner)
    dish_type: List[str] = []  # Dish types (main course, side dish, etc.)
    ingredients: List[str] = []  # Ingredient list
    health_labels: List[str] = []  # Health labels (vegetarian, vegan, etc.)
    diet_labels: List[str] = []  # Diet labels (balanced, low-carb, etc.)
    image_url: Optional[str] = None  # Recipe image

    # Nutritional information per serving
    calories_per_serving: Optional[float] = None
    protein_per_serving: Optional[float] = None
    carbs_per_serving: Optional[float] = None
    fat_per_serving: Optional[float] = None
    fiber_per_serving: Optional[float] = None

    model_config = ConfigDict(validate_by_name=True)

    def __init__(self, **data):
        if "yield" in data and "yield_servings" not in data:
            data["yield_servings"] = data["yield"]
        super().__init__(**data)


class RecipeSearchInput(BaseModel):
    """Input parameters for recipe search."""

    query: str = Field(
        description="Search query for recipes (e.g., 'chicken breast', 'high protein breakfast')"
    )
    diet_type: Optional[List[str]] = Field(
        default=None,
        description="Diet labels (balanced, high-protein, low-carb, low-fat, etc.)",
    )
    health_labels: Optional[List[str]] = Field(
        default=None,
        description="Health labels (vegetarian, vegan, gluten-free, dairy-free, etc.)",
    )
    cuisine_type: Optional[List[str]] = Field(
        default=None, description="Cuisine types (American, Asian, Mediterranean, etc.)"
    )
    meal_type: Optional[List[str]] = Field(
        default=None, description="Meal types (breakfast, lunch, dinner, snack)"
    )
    max_calories: Optional[int] = Field(
        default=None, description="Maximum calories per serving"
    )
    min_protein: Optional[int] = Field(
        default=None, description="Minimum protein grams per serving"
    )
    max_results: Optional[int] = Field(
        default=10, description="Maximum number of results to return"
    )


class RecipeSearchOutput(BaseModel):
    """Output from recipe search."""

    recipes: List[Recipe]
    total_found: int
    search_query: str
    applied_filters: Dict[str, Any]


class NutritionRecommendationInput(BaseModel):
    """Input for personalized nutrition recommendations."""

    calorie_target: int = Field(description="Target calories per meal")
    diet_preferences: Optional[List[str]] = Field(
        default=None, description="Diet preferences"
    )
    health_restrictions: Optional[List[str]] = Field(
        default=None, description="Health restrictions/allergies"
    )
    meal_type: str = Field(description="Type of meal (breakfast, lunch, dinner, snack)")
    cuisine_preference: Optional[str] = Field(
        default=None, description="Preferred cuisine type"
    )


class NutritionRecipeRecommendationTool(BaseDomainTool):
    """
    Recipe recommendation tool for nutrition domain.

    Provides:
    - Recipe search using Edamam API
    - Nutritional analysis of recipes
    - Personalized meal recommendations
    - Diet-specific recipe filtering
    - Health label filtering
    """

    domain: str = "nutrition"
    name: str = "nutrition_recipe_recommendation"
    description: str = (
        "Search and recommend recipes based on nutritional requirements and dietary preferences"
    )

    def __init__(self):
        super().__init__()
        # Edamam API credentials - fallback to os.getenv for now until config is extended
        try:
            from ...config import config

            self.app_id = getattr(config, "edamam", {}).get("app_id") or os.getenv(
                "EDAMAM_RECIPE_APP_ID", ""
            )
            self.app_key = getattr(config, "edamam", {}).get("app_key") or os.getenv(
                "EDAMAM_RECIPE_APP_KEY", ""
            )
        except (ImportError, AttributeError):
            # Fallback to environment variables if config not available
            self.app_id = os.getenv("EDAMAM_RECIPE_APP_ID", "")
            self.app_key = os.getenv("EDAMAM_RECIPE_APP_KEY", "")
        self.base_url = "https://api.edamam.com/api/recipes/v2"

        # Fallback recipe database if API is not available
        self.fallback_recipes = self._load_fallback_recipes()

    async def search_recipes(
        self, search_input: RecipeSearchInput
    ) -> RecipeSearchOutput:
        """Search for recipes using Edamam API or fallback database."""
        try:
            if self.app_id and self.app_key:
                # Use Edamam API
                return await self._search_edamam_api(search_input)
            else:
                # Use fallback database
                logger.warning(
                    "Edamam API credentials not found, using fallback database"
                )
                return self._search_fallback_database(search_input)

        except Exception as e:
            logger.error(f"Recipe search failed: {str(e)}")
            # Fallback to local database
            return self._search_fallback_database(search_input)

    async def get_personalized_recommendations(
        self, recommendation_input: NutritionRecommendationInput
    ) -> RecipeSearchOutput:
        """Get personalized recipe recommendations based on nutrition goals."""
        try:
            # Build search query based on meal type and preferences
            query_parts = []

            # Add base meal type
            query_parts.append(recommendation_input.meal_type)

            # Add cuisine preference
            if recommendation_input.cuisine_preference:
                query_parts.append(recommendation_input.cuisine_preference)

            # Build search input
            search_input = RecipeSearchInput(
                query=" ".join(query_parts),
                diet_type=recommendation_input.diet_preferences,
                health_labels=recommendation_input.health_restrictions,
                meal_type=[recommendation_input.meal_type],
                max_calories=recommendation_input.calorie_target
                + 100,  # Allow 100 calorie buffer
                max_results=8,
            )

            results = await self.search_recipes(search_input)

            # Filter and sort by calorie proximity to target
            filtered_recipes = []
            for recipe in results.recipes:
                if recipe.calories_per_serving:
                    calorie_diff = abs(
                        recipe.calories_per_serving
                        - recommendation_input.calorie_target
                    )
                    if calorie_diff <= 200:  # Within 200 calories of target
                        filtered_recipes.append((recipe, calorie_diff))

            # Sort by calorie proximity
            filtered_recipes.sort(key=lambda x: x[1])

            # Return top matches
            top_recipes = [recipe for recipe, _ in filtered_recipes[:5]]

            return RecipeSearchOutput(
                recipes=top_recipes,
                total_found=len(top_recipes),
                search_query=f"Personalized {recommendation_input.meal_type} recommendations",
                applied_filters={
                    "calorie_target": recommendation_input.calorie_target,
                    "diet_preferences": recommendation_input.diet_preferences,
                    "health_restrictions": recommendation_input.health_restrictions,
                },
            )

        except Exception as e:
            logger.error(f"Personalized recommendation failed: {str(e)}")
            raise Exception(f"Failed to get personalized recommendations: {str(e)}")

    async def _search_edamam_api(
        self, search_input: RecipeSearchInput
    ) -> RecipeSearchOutput:
        """Search recipes using Edamam API."""
        params = {
            "type": "public",
            "q": search_input.query,
            "app_id": self.app_id,
            "app_key": self.app_key,
            "from": 0,
            "to": search_input.max_results or 10,
        }

        # Add filters
        if search_input.diet_type:
            params["diet"] = search_input.diet_type

        if search_input.health_labels:
            params["health"] = search_input.health_labels

        if search_input.cuisine_type:
            params["cuisineType"] = search_input.cuisine_type

        if search_input.meal_type:
            params["mealType"] = search_input.meal_type

        if search_input.max_calories:
            params["calories"] = f"0-{search_input.max_calories}"

        async with aiohttp.ClientSession() as session:
            async with session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    recipes = self._parse_edamam_response(data)

                    return RecipeSearchOutput(
                        recipes=recipes,
                        total_found=len(recipes),
                        search_query=search_input.query,
                        applied_filters={
                            "diet_type": search_input.diet_type,
                            "health_labels": search_input.health_labels,
                            "cuisine_type": search_input.cuisine_type,
                            "meal_type": search_input.meal_type,
                            "max_calories": search_input.max_calories,
                        },
                    )
                else:
                    raise Exception(f"Edamam API error: {response.status}")

    def _parse_edamam_response(self, data: Dict) -> List[Recipe]:
        """Parse Edamam API response into Recipe objects."""
        recipes = []

        for hit in data.get("hits", []):
            recipe_data = hit.get("recipe", {})

            # Calculate per-serving nutrition
            total_calories = recipe_data.get("calories", 0)
            servings = recipe_data.get("yield", 1)  # Default to 1 serving if missing
            if servings == 0:  # Avoid division by zero
                servings = 1
            calories_per_serving = total_calories / servings

            # Extract nutrition per serving
            nutrients = recipe_data.get("totalNutrients", {})
            protein_per_serving = (
                nutrients.get("PROCNT", {}).get("quantity", 0) / servings
                if servings > 0
                else 0
            )
            carbs_per_serving = (
                nutrients.get("CHOCDF", {}).get("quantity", 0) / servings
                if servings > 0
                else 0
            )
            fat_per_serving = (
                nutrients.get("FAT", {}).get("quantity", 0) / servings
                if servings > 0
                else 0
            )
            fiber_per_serving = (
                nutrients.get("FIBTG", {}).get("quantity", 0) / servings
                if servings > 0
                else 0
            )

            try:
                recipe = Recipe(
                    label=recipe_data.get("label", "Unknown Recipe"),
                    source=recipe_data.get("source", "Unknown Source"),
                    url=recipe_data.get("url", ""),
                    yield_servings=servings,
                    calories=total_calories,
                    total_time=recipe_data.get("totalTime"),
                    cuisine_type=recipe_data.get("cuisineType", []),
                    meal_type=recipe_data.get("mealType", []),
                    dish_type=recipe_data.get("dishType", []),
                    ingredients=[
                        line for line in recipe_data.get("ingredientLines", [])
                    ],
                    health_labels=recipe_data.get("healthLabels", []),
                    diet_labels=recipe_data.get("dietLabels", []),
                    image_url=recipe_data.get("image"),
                    calories_per_serving=calories_per_serving,
                    protein_per_serving=protein_per_serving,
                    carbs_per_serving=carbs_per_serving,
                    fat_per_serving=fat_per_serving,
                    fiber_per_serving=fiber_per_serving,
                )
                recipes.append(recipe)
            except Exception as e:
                logger.warning(
                    f"Failed to parse recipe: {recipe_data.get('label', 'Unknown')}, error: {str(e)}"
                )
                continue

        return recipes

    def _search_fallback_database(
        self, search_input: RecipeSearchInput
    ) -> RecipeSearchOutput:
        """Search fallback recipe database when API is unavailable."""
        query_lower = search_input.query.lower()
        matching_recipes = []

        for recipe_data in self.fallback_recipes:
            # Simple text matching - Include diet_labels and health_labels in the searchable text
            recipe_text = (
                f"{recipe_data['label']} {' '.join(recipe_data['ingredients'])} "
                f"{' '.join(recipe_data.get('diet_labels', []))} "
                f"{' '.join(recipe_data.get('health_labels', []))}".lower()
            )

            if any(word in recipe_text for word in query_lower.split()):
                # Apply filters
                if (
                    search_input.max_calories
                    and recipe_data.get("calories_per_serving", 0)
                    > search_input.max_calories
                ):
                    continue

                if search_input.health_labels:
                    if not any(
                        label in recipe_data.get("health_labels", [])
                        for label in search_input.health_labels
                    ):
                        continue

                recipe = Recipe(**recipe_data)
                matching_recipes.append(recipe)

        # Limit results
        max_results = search_input.max_results or 10
        matching_recipes = matching_recipes[:max_results]

        return RecipeSearchOutput(
            recipes=matching_recipes,
            total_found=len(matching_recipes),
            search_query=search_input.query,
            applied_filters={"source": "fallback_database"},
        )

    def _load_fallback_recipes(self) -> List[Dict]:
        """Load fallback recipe database."""
        return [
            {
                "label": "Grilled Chicken Breast with Vegetables",
                "source": "Athlea Nutrition",
                "url": "",
                "yield": 1,
                "calories": 350,
                "total_time": 25,
                "cuisine_type": ["American"],
                "meal_type": ["lunch", "dinner"],
                "dish_type": ["main course"],
                "ingredients": [
                    "150g chicken breast",
                    "100g broccoli",
                    "50g bell peppers",
                    "10ml olive oil",
                    "Salt and pepper to taste",
                ],
                "health_labels": ["gluten-free", "dairy-free"],
                "diet_labels": ["high-protein", "low-carb"],
                "image_url": None,
                "calories_per_serving": 350,
                "protein_per_serving": 45,
                "carbs_per_serving": 8,
                "fat_per_serving": 12,
                "fiber_per_serving": 4,
            },
            {
                "label": "Overnight Oats with Berries",
                "source": "Athlea Nutrition",
                "url": "",
                "yield": 1,
                "calories": 320,
                "total_time": 480,  # Overnight prep
                "cuisine_type": ["American"],
                "meal_type": ["breakfast"],
                "dish_type": ["main course"],
                "ingredients": [
                    "50g rolled oats",
                    "150ml almond milk",
                    "50g mixed berries",
                    "15g chia seeds",
                    "1 tbsp honey",
                ],
                "health_labels": ["vegetarian", "gluten-free"],
                "diet_labels": ["balanced"],
                "image_url": None,
                "calories_per_serving": 320,
                "protein_per_serving": 12,
                "carbs_per_serving": 45,
                "fat_per_serving": 8,
                "fiber_per_serving": 12,
            },
            {
                "label": "Quinoa Buddha Bowl",
                "source": "Athlea Nutrition",
                "url": "",
                "yield": 1,
                "calories": 450,
                "total_time": 30,
                "cuisine_type": ["Mediterranean"],
                "meal_type": ["lunch", "dinner"],
                "dish_type": ["main course"],
                "ingredients": [
                    "80g quinoa",
                    "100g chickpeas",
                    "50g avocado",
                    "100g mixed greens",
                    "50g cherry tomatoes",
                    "30g tahini dressing",
                ],
                "health_labels": ["vegetarian", "vegan", "gluten-free"],
                "diet_labels": ["balanced", "high-fiber"],
                "image_url": None,
                "calories_per_serving": 450,
                "protein_per_serving": 18,
                "carbs_per_serving": 52,
                "fat_per_serving": 16,
                "fiber_per_serving": 14,
            },
        ]

    # Legacy method for backward compatibility
    async def recommend_recipes(self, recommendation_input):
        """Recommend recipes (legacy compatibility method)."""
        if isinstance(recommendation_input, dict):
            # Convert dict input to appropriate input model
            if "calorie_target" in recommendation_input:
                input_data = NutritionRecommendationInput(**recommendation_input)
                return await self.get_personalized_recommendations(input_data)
            else:
                input_data = RecipeSearchInput(**recommendation_input)
                return await self.search_recipes(input_data)
        else:
            if isinstance(recommendation_input, NutritionRecommendationInput):
                return await self.get_personalized_recommendations(recommendation_input)
            else:
                return await self.search_recipes(recommendation_input)


import json

# LangChain Tool Wrappers
from langchain_core.tools import tool


@tool
def search_recipes(search_data: str) -> str:
    """
    Search for recipes based on query and dietary filters.

    Use this tool for:
    - Finding recipes by ingredient or dish name
    - Filtering by dietary restrictions
    - Searching by cuisine type or meal type
    - Finding recipes within calorie limits

    Input should be JSON string:
    {
        "query": "chicken breast",
        "diet_type": ["high-protein"],
        "health_labels": ["gluten-free"],
        "cuisine_type": ["American"],
        "meal_type": ["dinner"],
        "max_calories": 500,
        "max_results": 10
    }
    """
    try:
        data = json.loads(search_data)

        search_input = RecipeSearchInput(**data)
        recipe_tool = NutritionRecipeRecommendationTool()

        # Return fallback database results for demo
        fallback_results = recipe_tool._search_fallback_database(search_input)

        # Format results for better readability
        formatted_recipes = []
        for recipe in fallback_results.recipes:
            formatted_recipe = {
                "name": recipe.label,
                "calories_per_serving": recipe.calories_per_serving,
                "protein_per_serving": recipe.protein_per_serving,
                "ingredients": recipe.ingredients[:5],  # First 5 ingredients
                "health_labels": recipe.health_labels,
                "diet_labels": recipe.diet_labels,
                "meal_type": recipe.meal_type,
                "cuisine_type": recipe.cuisine_type,
            }
            formatted_recipes.append(formatted_recipe)

        result = {
            "total_found": fallback_results.total_found,
            "search_query": fallback_results.search_query,
            "recipes": formatted_recipes,
            "applied_filters": fallback_results.applied_filters,
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        return f"Recipe search failed: {str(e)}"


@tool
def get_recipe_recommendations(recommendation_data: str) -> str:
    """
    Get personalized recipe recommendations based on nutritional goals and preferences.

    Use this tool for:
    - Finding recipes that match specific calorie targets
    - Getting meal-specific recommendations
    - Finding recipes that fit dietary preferences
    - Getting nutrition-optimized recipe suggestions

    Input should be JSON string:
    {
        "calorie_target": 400,
        "diet_preferences": ["high-protein", "low-carb"],
        "health_restrictions": ["gluten-free"],
        "meal_type": "lunch",
        "cuisine_preference": "Mediterranean"
    }
    """
    try:
        data = json.loads(recommendation_data)

        recommendation_input = NutritionRecommendationInput(**data)
        recipe_tool = NutritionRecipeRecommendationTool()

        # Use fallback database for demo - filter by calorie target
        search_input = RecipeSearchInput(
            query=recommendation_input.meal_type,
            diet_type=recommendation_input.diet_preferences,
            health_labels=recommendation_input.health_restrictions,
            meal_type=[recommendation_input.meal_type],
            max_calories=recommendation_input.calorie_target + 100,
            max_results=5,
        )

        fallback_results = recipe_tool._search_fallback_database(search_input)

        # Filter by calorie proximity
        filtered_recipes = []
        for recipe in fallback_results.recipes:
            if recipe.calories_per_serving:
                calorie_diff = abs(
                    recipe.calories_per_serving - recommendation_input.calorie_target
                )
                if calorie_diff <= 150:  # Within 150 calories
                    formatted_recipe = {
                        "name": recipe.label,
                        "calories_per_serving": recipe.calories_per_serving,
                        "calorie_difference": calorie_diff,
                        "protein_per_serving": recipe.protein_per_serving,
                        "carbs_per_serving": recipe.carbs_per_serving,
                        "fat_per_serving": recipe.fat_per_serving,
                        "ingredients": recipe.ingredients[:5],
                        "health_labels": recipe.health_labels,
                        "diet_labels": recipe.diet_labels,
                        "suitability_score": max(
                            0, 100 - (calorie_diff / 5)
                        ),  # Score based on calorie closeness
                    }
                    filtered_recipes.append(formatted_recipe)

        # Sort by suitability score
        filtered_recipes.sort(key=lambda x: x["suitability_score"], reverse=True)

        result = {
            "calorie_target": recommendation_input.calorie_target,
            "meal_type": recommendation_input.meal_type,
            "total_recommendations": len(filtered_recipes),
            "recommendations": filtered_recipes[:3],  # Top 3 recommendations
            "applied_filters": {
                "diet_preferences": recommendation_input.diet_preferences,
                "health_restrictions": recommendation_input.health_restrictions,
                "cuisine_preference": recommendation_input.cuisine_preference,
            },
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        return f"Recipe recommendations failed: {str(e)}"
