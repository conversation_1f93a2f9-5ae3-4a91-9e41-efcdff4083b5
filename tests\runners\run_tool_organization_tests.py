#!/usr/bin/env python3
"""
Phase 2: Tool Organization Test Runner

Simple script to run the tool organization tests for Phase 2 implementation:
- Proposal 2A: Domain-specific tool organization
- Proposal 2B: Tool-agent contract enforcement

Usage:
    python run_tool_organization_tests.py

Requirements:
    - All dependencies installed (pip install -r requirements.txt)
    - Environment variables set (see .env.local)
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path and change working directory
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)


async def main():
    """Run the tool organization test suite."""
    print("🎯 Phase 2: Tool Layer Organization Test Suite")
    print("=" * 50)
    print("Testing Proposal 2A: Domain-specific tool organization")
    print("Testing Proposal 2B: Tool-agent contract enforcement")
    print("Focus: Schema validation + Access control + Integration")
    print()

    try:
        # Import and run the test suite
        from tests.integration.test_tool_organization import run_tool_organization_tests

        await run_tool_organization_tests()

        print("\n🏆 Test Suite Completed Successfully!")
        print("\n💡 Key Validations:")
        print("✅ Domain-specific tools organized by expertise area")
        print("✅ Tool-agent contracts enforce schema validation")
        print("✅ Access control prevents cross-domain tool usage")
        print("✅ Integration with existing modular agents")
        print("✅ Backward compatibility maintained")

        print("\n🔗 Related Files:")
        print("- athlea_langgraph/tools/strength/")
        print("- athlea_langgraph/tools/nutrition/")
        print("- athlea_langgraph/tools/cardio/")
        print("- athlea_langgraph/tools/recovery/")
        print("- athlea_langgraph/tools/mental/")
        print("- athlea_langgraph/tools/contracts/")
        print("- athlea_langgraph/tools/schemas/")

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the python-langgraph directory")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Check that all tool files exist in athlea_langgraph/tools/")
        print(f"4. Current working directory: {os.getcwd()}")
        sys.exit(1)

    except Exception as e:
        print(f"❌ Test Error: {e}")
        print("\n🔧 This might be expected if:")
        print("- Some domain tools are still placeholders")
        print("- Import dependencies are missing")
        print("- Schema validation needs refinement")
        print(
            "\nThe tests are designed to show the tool organization even with some failures."
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
