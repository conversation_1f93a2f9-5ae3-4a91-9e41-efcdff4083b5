"""
Enhanced Session State Models

Extended state models for the intelligent session generation system that integrate
with existing session schemas and onboarding state.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage

# Import existing schemas to extend them
from ..schemas.session_generation_schemas import (
    StrengthSession,
    RunningSession,
    CyclingSession,
    RecoverySession,
    SessionGenerationInput,
    SessionGenerationOutput,
)

from ..states.onboarding_state import (
    OnboardingState,
    PlanDetails,
    ExampleSession,
    SidebarStateData,
)


class FitnessProfile(BaseModel):
    """Enhanced user fitness profile based on research best practices"""

    user_id: str

    # Physiological Metrics (from Trisystems research)
    training_zones: Optional[Dict[str, Dict[str, Union[int, str]]]] = (
        None  # zone -> {hr_range, power_range, pace_range}
    )
    aerobic_threshold: Optional[Dict[str, Union[int, str]]] = (
        None  # {heart_rate, power, pace}
    )
    anaerobic_threshold: Optional[Dict[str, Union[int, str]]] = (
        None  # {heart_rate, power, pace}
    )
    vo2_max: Optional[float] = None
    resting_heart_rate: Optional[int] = None
    max_heart_rate: Optional[int] = None
    functional_threshold_power: Optional[int] = None  # For cycling (watts)
    lactate_threshold_pace: Optional[str] = None  # For running (min/km)

    # Recovery and Health Metrics
    recovery_metrics: Optional[Dict[str, Any]] = None  # HRV, sleep quality, etc.
    injury_history: List[str] = Field(default_factory=list)
    current_limitations: List[str] = Field(default_factory=list)

    # Equipment and Environment
    equipment_available: List[str] = Field(default_factory=list)
    preferred_locations: List[str] = Field(
        default_factory=list
    )  # indoor, outdoor, gym, home

    # Training History and Preferences
    training_experience_years: Optional[int] = None
    preferred_training_days: List[str] = Field(
        default_factory=list
    )  # monday, tuesday, etc.
    time_constraints: Optional[Dict[str, Any]] = (
        None  # daily_minutes, weekly_sessions, etc.
    )

    # Adaptation Parameters (learned over time)
    adaptation_rate: Optional[float] = None  # how quickly user adapts to training
    recovery_rate: Optional[float] = None  # how quickly user recovers
    injury_risk_profile: Optional[str] = None  # low, moderate, high
    motivation_patterns: Optional[Dict[str, Any]] = None


class TrainingSession(BaseModel):
    """Enhanced training session that extends existing schemas"""

    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    date: datetime
    domain: str  # "strength", "running", "cycling", "recovery"
    session_type: str  # "base", "tempo", "intervals", "recovery", etc.

    # Core Session Data (matches existing schemas)
    duration_minutes: int
    intensity_level: Union[int, str]  # Supports both numeric and string formats
    difficulty_level: int = Field(ge=1, le=10)

    # Session Content (populated from existing schema objects)
    session_data: Optional[
        Union[StrengthSession, RunningSession, CyclingSession, RecoverySession]
    ] = None

    # Coach Intelligence
    generating_coach: str  # which coach created this session
    coach_rationale: str  # why this session was chosen
    expected_benefits: List[str] = Field(default_factory=list)
    prerequisites: List[str] = Field(default_factory=list)

    # User Experience
    estimated_exertion: Optional[int] = Field(None, ge=1, le=10)  # coach's prediction
    equipment_needed: List[str] = Field(default_factory=list)
    location_type: Optional[str] = None  # indoor, outdoor, gym, home

    # Adaptation and Learning
    adaptation_focus: List[str] = Field(
        default_factory=list
    )  # what this session is meant to improve
    coordination_notes: Optional[str] = None  # how this fits with other sessions

    # Status
    status: str = Field(default="planned")  # planned, completed, skipped, modified
    user_modifications: List[Dict[str, Any]] = Field(default_factory=list)


class SessionFeedback(BaseModel):
    """User feedback on completed sessions with enhanced learning data"""

    session_id: str
    user_id: str
    completed: bool
    completion_date: Optional[datetime] = None

    # User Ratings
    difficulty_rating: Optional[int] = Field(None, ge=1, le=10)  # actual vs predicted
    enjoyment_rating: Optional[int] = Field(None, ge=1, le=10)
    perceived_exertion: Optional[int] = Field(None, ge=1, le=10)

    # Detailed Feedback
    notes: Optional[str] = None
    completion_time_minutes: Optional[int] = None
    modifications_made: List[str] = Field(default_factory=list)

    # Learning Data
    felt_too_easy: Optional[bool] = None
    felt_too_hard: Optional[bool] = None
    would_repeat: Optional[bool] = None
    next_session_preferences: Optional[Dict[str, Any]] = None


class WeeklyPlan(BaseModel):
    """Enhanced weekly training plan with coach coordination"""

    week_number: int
    start_date: datetime
    end_date: datetime

    # Session Organization
    sessions: Dict[str, List[TrainingSession]] = Field(
        default_factory=dict
    )  # day -> sessions
    daily_focuses: Dict[str, str] = Field(
        default_factory=dict
    )  # day -> focus description

    # Load Management
    weekly_volume: Dict[str, int] = Field(
        default_factory=dict
    )  # domain -> total minutes
    weekly_intensity_distribution: Dict[str, int] = Field(
        default_factory=dict
    )  # intensity -> minutes
    total_training_load: Optional[float] = None

    # Coach Coordination
    weekly_focus: str
    rationale: str
    coach_coordination_notes: str  # how coaches worked together

    # Adaptation
    adaptation_notes: Optional[str] = None
    previous_week_feedback_summary: Optional[str] = None


class CoachMemory(BaseModel):
    """What each coach remembers and learns"""

    coach_id: str
    domain: str

    # Session History
    sessions_created: List[str] = Field(default_factory=list)  # session IDs
    successful_patterns: List[Dict[str, Any]] = Field(default_factory=list)
    unsuccessful_patterns: List[Dict[str, Any]] = Field(default_factory=list)

    # User-Specific Learning
    user_preferences: Dict[str, Any] = Field(default_factory=dict)
    user_adaptations: Dict[str, Any] = Field(default_factory=dict)
    user_limitations: List[str] = Field(default_factory=list)

    # Performance Tracking
    session_completion_rate: Optional[float] = None
    average_user_satisfaction: Optional[float] = None
    adaptation_accuracy: Optional[float] = None  # how well coach predicts user response

    # Coordination Memory
    coordination_history: List[Dict[str, Any]] = Field(default_factory=list)
    other_coach_interactions: Dict[str, List[str]] = Field(default_factory=dict)


class UserControlState(BaseModel):
    """State for user control and interaction"""

    user_id: str

    # Pause/Resume State
    is_paused: bool = False
    paused_at: Optional[datetime] = None
    pause_reason: Optional[str] = None
    planned_resume_date: Optional[datetime] = None

    # Adjustment Preferences
    preferred_intensity_adjustment: Optional[float] = (
        None  # multiplier (0.8 = 80% intensity)
    )
    preferred_duration_adjustment: Optional[float] = None  # multiplier
    blacklisted_exercises: List[str] = Field(default_factory=list)
    preferred_exercises: List[str] = Field(default_factory=list)

    # Session Control
    sessions_to_skip: List[str] = Field(default_factory=list)
    sessions_to_repeat: List[str] = Field(default_factory=list)
    custom_session_requests: List[Dict[str, Any]] = Field(default_factory=list)

    # Feedback Preferences
    feedback_frequency: str = Field(
        default="after_each"
    )  # after_each, weekly, bi_weekly
    reminder_preferences: Dict[str, Any] = Field(default_factory=dict)


class AdaptationData(BaseModel):
    """Data for the adaptation engine"""

    user_id: str

    # Learning Parameters
    learning_rate: float = Field(default=0.1)  # how quickly to adapt
    confidence_threshold: float = Field(default=0.7)  # when to make changes

    # Pattern Recognition
    identified_patterns: List[Dict[str, Any]] = Field(default_factory=list)
    prediction_accuracy: Dict[str, float] = Field(
        default_factory=dict
    )  # metric -> accuracy

    # Adaptation History
    adaptations_made: List[Dict[str, Any]] = Field(default_factory=list)
    adaptation_outcomes: List[Dict[str, Any]] = Field(default_factory=list)

    # Performance Trends
    performance_trends: Dict[str, List[float]] = Field(
        default_factory=dict
    )  # metric -> values over time
    improvement_rate: Optional[float] = None


class EnhancedOnboardingState(OnboardingState):
    """Enhanced onboarding state with session generation capabilities"""

    # Enhanced User Profile
    fitness_profile: Optional[FitnessProfile] = None
    training_history: List[TrainingSession] = Field(default_factory=list)

    # Session Generation State
    current_week: int = 1
    current_day: int = 1
    generated_weekly_plans: Dict[int, WeeklyPlan] = Field(
        default_factory=dict
    )  # week -> plan

    # Coach Intelligence
    coach_memories: Dict[str, CoachMemory] = Field(
        default_factory=dict
    )  # coach_id -> memory
    session_rationale_history: Dict[str, str] = Field(
        default_factory=dict
    )  # session_id -> rationale

    # User Control
    user_control_state: Optional[UserControlState] = None
    feedback_history: List[SessionFeedback] = Field(default_factory=list)

    # Adaptation Engine
    adaptation_data: Optional[AdaptationData] = None
    learning_parameters: Dict[str, float] = Field(default_factory=dict)

    # Integration with existing fields
    # (all existing OnboardingState fields are inherited)


# Helper Functions for State Conversion
def convert_session_to_training_session(
    existing_session: Union[
        StrengthSession, RunningSession, CyclingSession, RecoverySession
    ],
    coach_id: str,
    rationale: str,
    date: datetime,
) -> TrainingSession:
    """Convert existing session schema to enhanced TrainingSession"""

    # Determine domain and extract common fields
    if isinstance(existing_session, StrengthSession):
        domain = "strength"
        duration = existing_session.duration
        intensity = existing_session.intensity_level
        equipment = [
            seg.name for seg in existing_session.segments
        ]  # exercise names as equipment proxy
    elif isinstance(existing_session, RunningSession):
        domain = "running"
        duration = int(existing_session.duration.split(":")[0]) * 60 + int(
            existing_session.duration.split(":")[1]
        )
        intensity = existing_session.intensity_level
        equipment = ["running_shoes"]  # basic running equipment
    elif isinstance(existing_session, CyclingSession):
        domain = "cycling"
        duration = int(existing_session.duration)
        intensity = existing_session.intensity
        equipment = ["bike", "helmet"]
        location = existing_session.location
    elif isinstance(existing_session, RecoverySession):
        domain = "recovery"
        duration = existing_session.duration
        intensity = existing_session.intensity_level
        equipment = [activity.name for activity in existing_session.activities]
    else:
        raise ValueError(f"Unsupported session type: {type(existing_session)}")

    return TrainingSession(
        session_id=existing_session.id,
        date=date,
        domain=domain,
        session_type=getattr(existing_session, "session_type", domain),
        duration_minutes=duration,
        intensity_level=intensity,
        difficulty_level=5,  # default, will be learned
        session_data=existing_session,
        generating_coach=coach_id,
        coach_rationale=rationale,
        equipment_needed=equipment if "equipment" in locals() else [],
        location_type=location if "location" in locals() else None,
    )


def create_fitness_profile_from_onboarding(state: OnboardingState) -> FitnessProfile:
    """Create initial fitness profile from onboarding data"""
    return FitnessProfile(
        user_id=state["user_id"],
        equipment_available=(
            state.get("equipment", "").split(",") if state.get("equipment") else []
        ),
        training_experience_years=None,  # Could be derived from experience_level
        time_constraints={
            "weekly_commitment": state.get("time_commitment"),
            "sessions_per_week": 3,  # default
        },
        current_limitations=[],
        injury_history=[],
        preferred_locations=["gym"],  # default
    )


def update_sidebar_with_session_data(
    sidebar_data: SidebarStateData, weekly_plan: WeeklyPlan
) -> SidebarStateData:
    """Update sidebar data with session generation information"""

    # Convert weekly plan to sidebar format
    updated_sidebar = SidebarStateData(
        current_stage=sidebar_data.current_stage,
        goals=sidebar_data.goals,
        summary_items=sidebar_data.summary_items,
        generated_plan=sidebar_data.generated_plan,
        sport_suggestions=sidebar_data.sport_suggestions,
        selected_sport=sidebar_data.selected_sport,
        selected_sports=sidebar_data.selected_sports,
    )

    # Add weekly plan information
    setattr(
        updated_sidebar,
        "weekly_plan",
        {
            "week_number": weekly_plan.week_number,
            "total_sessions": sum(
                len(sessions) for sessions in weekly_plan.sessions.values()
            ),
            "weekly_focus": weekly_plan.weekly_focus,
            "total_volume_minutes": sum(weekly_plan.weekly_volume.values()),
            "sessions_by_day": {
                day: [
                    {
                        "domain": session.domain,
                        "duration": session.duration_minutes,
                        "type": session.session_type,
                    }
                    for session in sessions
                ]
                for day, sessions in weekly_plan.sessions.items()
            },
        },
    )

    return updated_sidebar
