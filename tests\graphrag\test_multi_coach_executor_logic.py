"""
Test Multi-Coach Executor Logic

Direct test of the multi-coach executor logic to verify that it
preserves Intelligence Hub decisions correctly without going through the graph.
"""

import asyncio
import logging
from typing import Dict, Any
from unittest.mock import AsyncMock, patch

# Configure logging for the test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_multi_coach_logic_directly():
    """Test the multi-coach executor logic directly."""

    logger.info("🧪 Testing Multi-Coach Executor Logic Directly")

    # Create mock coaches - representing what Intelligence Hub wants
    mock_coaches = {}
    expected_coaches = ["cardio_coach", "nutrition_coach", "recovery_coach"]

    for coach_name in expected_coaches:
        mock_coach = AsyncMock()
        mock_coach.execute.return_value = {
            "final_answer": f"Response from {coach_name} for marathon training"
        }
        mock_coaches[coach_name] = mock_coach

    # Add strength_coach to detect fallback issues
    mock_coaches["strength_coach"] = AsyncMock()
    mock_coaches["strength_coach"].execute.return_value = {
        "final_answer": "Response from strength coach (should NOT be called)"
    }

    # Create the smart_coach_executor_node function by defining it locally
    # This replicates the logic from the optimized graph
    async def smart_coach_executor_node(state: Dict[str, Any]) -> Dict[str, Any]:
        """Test version of smart coach executor that uses mock coaches."""
        logger.info("🎯 SMART_EXECUTOR: Starting multi-coach execution")

        # Extract state values
        required_coaches = state.get("required_coaches", [])
        primary_coach = state.get("primary_coach")  # Don't provide default here
        routing_decision = state.get("routing_decision", "direct_coach")
        user_query = state.get("user_query", "")
        execution_steps = state.get("execution_steps", [])
        messages = state.get("messages", [])
        user_profile = state.get("user_profile", {})

        # Apply intelligent defaults only if primary_coach is None
        if primary_coach is None:
            logger.warning(
                "🎯 SMART_EXECUTOR: No primary_coach found in state, using Intelligence Hub default"
            )
            primary_coach = "strength_coach"  # Final fallback

        logger.info(
            f"🎯 SMART_EXECUTOR: Using primary_coach: {primary_coach} from Intelligence Hub"
        )

        coach_responses = {}

        try:
            # Execute multiple coaches for multi-coach scenarios
            if routing_decision == "multi_coach" and len(required_coaches) > 1:
                logger.info(
                    f"🎯 SMART_EXECUTOR: Multi-coach execution for {required_coaches}"
                )

                for coach_name in required_coaches:
                    coach_key = f"{coach_name}_coach"
                    if coach_key in mock_coaches:
                        logger.info(f"🏃‍♂️ SMART_EXECUTOR: Executing {coach_key}")

                        result = await mock_coaches[coach_key].execute(
                            user_message=user_query,
                            conversation_history=messages,
                            user_profile=user_profile,
                        )

                        coach_responses[coach_key] = result.get(
                            "final_answer", f"{coach_name} coach response"
                        )

                logger.info(
                    f"✅ SMART_EXECUTOR: Completed multi-coach execution ({len(coach_responses)} coaches)"
                )

            else:
                # Single coach fallback execution
                logger.info(
                    f"🎯 SMART_EXECUTOR: Single coach execution for {primary_coach}"
                )

                if primary_coach in mock_coaches:
                    result = await mock_coaches[primary_coach].execute(
                        user_message=user_query,
                        conversation_history=messages,
                        user_profile=user_profile,
                    )

                    coach_responses[primary_coach] = result.get(
                        "final_answer", "Coach response"
                    )

            return {
                "current_node": "smart_executor",
                "coach_responses": coach_responses,
                "execution_steps": execution_steps + ["smart_executor"],
                "debug_info": {
                    "node": "smart_executor",
                    "action": "multi_coach_complete",
                    "coaches_count": len(coach_responses),
                },
            }

        except Exception as e:
            logger.error(f"🎯 SMART_EXECUTOR: Error during execution: {e}")
            fallback_response = "I apologize, but I encountered an issue while processing your request. Please try again."

            return {
                "current_node": "smart_executor",
                "final_response": fallback_response,
                "execution_steps": execution_steps + ["smart_executor"],
                "debug_info": {
                    "node": "smart_executor",
                    "action": "error_fallback",
                    "error": str(e),
                },
            }

    # Test Case 1: Multi-coach scenario (Intelligence Hub decides 3 coaches needed)
    logger.info("\n📝 Test 1: Multi-coach marathon training scenario")

    intelligence_hub_output = {
        "routing_decision": "multi_coach",
        "required_coaches": ["cardio", "nutrition", "recovery"],  # Marathon needs all 3
        "primary_coach": "cardio_coach",  # Primary is cardio
        "user_query": "I want to train for a marathon, including nutrition and recovery planning",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    logger.info(
        f"🧠 Intelligence Hub decided: {intelligence_hub_output['required_coaches']}"
    )
    logger.info(f"🧠 Primary coach: {intelligence_hub_output['primary_coach']}")

    result = await smart_coach_executor_node(intelligence_hub_output)
    coach_responses = result.get("coach_responses", {})

    logger.info(f"✅ Executed coaches: {list(coach_responses.keys())}")

    # Verify all required coaches were executed
    for coach in intelligence_hub_output["required_coaches"]:
        coach_key = f"{coach}_coach"
        assert coach_key in coach_responses, (
            f"❌ {coach_key} was not executed! "
            f"Expected: {expected_coaches}, "
            f"Actually executed: {list(coach_responses.keys())}"
        )
        mock_coaches[coach_key].execute.assert_called_once()
        logger.info(f"✅ {coach_key} was properly executed")

    # Verify strength_coach was NOT executed (no fallback to hardcoded default)
    assert "strength_coach" not in coach_responses, (
        f"❌ strength_coach was unexpectedly executed! "
        f"This suggests incorrect fallback logic. "
        f"Executed coaches: {list(coach_responses.keys())}"
    )
    mock_coaches["strength_coach"].execute.assert_not_called()
    logger.info("✅ strength_coach was correctly NOT executed")

    # Test Case 2: Missing primary_coach (edge case)
    logger.info("\n📝 Test 2: Missing primary_coach but has required_coaches")

    # Reset mocks
    for coach in mock_coaches.values():
        coach.execute.reset_mock()

    state_missing_primary = {
        "routing_decision": "multi_coach",
        "required_coaches": ["nutrition", "recovery"],  # Clear required coaches
        # Note: No primary_coach field!
        "user_query": "Help with nutrition and recovery",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    result2 = await smart_coach_executor_node(state_missing_primary)
    coach_responses2 = result2.get("coach_responses", {})

    logger.info(
        f"📋 Executed coaches when primary_coach missing: {list(coach_responses2.keys())}"
    )

    # Should still execute the required coaches based on Intelligence Hub decision
    assert (
        "nutrition_coach" in coach_responses2
    ), "nutrition_coach should be executed based on required_coaches"
    assert (
        "recovery_coach" in coach_responses2
    ), "recovery_coach should be executed based on required_coaches"

    logger.info(
        "✅ Multi-coach executor preserves Intelligence Hub decisions correctly!"
    )

    # Test Case 3: Single coach scenario (should NOT trigger multi-coach logic)
    logger.info("\n📝 Test 3: Single coach scenario")

    # Reset mocks
    for coach in mock_coaches.values():
        coach.execute.reset_mock()

    single_coach_state = {
        "routing_decision": "direct_coach",  # NOT multi_coach
        "required_coaches": ["nutrition"],  # Only one coach
        "primary_coach": "nutrition_coach",
        "user_query": "What should I eat for breakfast?",
        "execution_steps": ["intelligence_hub"],
        "messages": [],
        "user_profile": {},
    }

    result3 = await smart_coach_executor_node(single_coach_state)
    coach_responses3 = result3.get("coach_responses", {})

    logger.info(f"📋 Single coach execution: {list(coach_responses3.keys())}")

    # Should execute only the primary coach, not multi-coach logic
    assert len(coach_responses3) == 1, f"Expected 1 coach, got {len(coach_responses3)}"
    assert "nutrition_coach" in coach_responses3, "Should execute the primary coach"

    logger.info("✅ Single coach scenario handled correctly!")

    return True


async def run_multi_coach_logic_test():
    """Run the multi-coach executor logic test."""

    print("🚀 Starting Multi-Coach Executor Logic Test\n")

    try:
        success = await test_multi_coach_logic_directly()

        if success:
            print("\n🎉 MULTI-COACH EXECUTOR LOGIC TEST PASSED!")
            print("\nThe multi-coach executor logic correctly:")
            print("  ✅ Preserves Intelligence Hub's required_coaches decisions")
            print("  ✅ Executes all required coaches (not just primary_coach)")
            print("  ✅ Handles missing primary_coach edge cases")
            print("  ✅ Distinguishes single vs multi-coach scenarios")
            print("  ✅ Does NOT fallback to strength_coach unnecessarily")
            return True
        else:
            print("\n❌ Multi-coach executor logic test failed")
            return False

    except Exception as e:
        print(f"\n❌ Multi-coach executor logic test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(run_multi_coach_logic_test())
    exit(0 if success else 1)
