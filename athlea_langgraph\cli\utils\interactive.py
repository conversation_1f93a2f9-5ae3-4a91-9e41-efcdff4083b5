"""
Interactive utilities for CLI prompt creation and editing.
"""

from typing import List, Optional

import click

from athlea_langgraph.utils.prompt_models import PromptType


def interactive_prompt_creator() -> dict:
    """Interactive prompt creation wizard."""
    click.echo("🧙 Interactive Prompt Creation Wizard")
    click.echo("=" * 50)

    # Get basic info
    name = click.prompt("Prompt name")

    # Select type
    click.echo("\nSelect prompt type:")
    click.echo("1. Coach (for specialized coaching agents)")
    click.echo("2. Reasoning (for analysis and planning)")
    click.echo("3. System (for system-level operations)")

    type_choice = click.prompt("Enter choice (1-3)", type=int)
    type_map = {1: "coach", 2: "reasoning", 3: "system"}
    prompt_type = type_map.get(type_choice, "coach")

    # Get description
    description = click.prompt("Description (optional)", default="", show_default=False)

    # Get author
    author = click.prompt("Author", default="CLI User")

    # Get tags
    tags_input = click.prompt(
        "Tags (comma-separated, optional)", default="", show_default=False
    )
    tags = [t.strip() for t in tags_input.split(",")] if tags_input else []

    # Content input method
    click.echo("\nChoose content input method:")
    click.echo("1. Type content interactively")
    click.echo("2. Use existing prompt as template")
    click.echo("3. Load from file")

    content_choice = click.prompt("Enter choice (1-3)", type=int)
    content = ""
    template = None

    if content_choice == 1:
        click.echo("\nEnter prompt content (Press Ctrl+D when finished):")
        try:
            content = click.get_text_stream("stdin").read()
        except KeyboardInterrupt:
            click.echo("\nCancelled.")
            return None
    elif content_choice == 2:
        template = click.prompt("Template prompt name")
    elif content_choice == 3:
        file_path = click.prompt("File path")
        try:
            with open(file_path, "r") as f:
                content = f.read()
        except Exception as e:
            click.echo(f"Error reading file: {e}")
            return None

    return {
        "name": name,
        "type": prompt_type,
        "description": description if description else None,
        "author": author,
        "tags": tags,
        "content": content,
        "template": template,
    }


def confirm_action(action: str, target: str, details: Optional[dict] = None) -> bool:
    """Confirm a potentially destructive action."""
    click.echo(f"\n⚠️  Confirm {action}")
    click.echo("-" * 30)
    click.echo(f"Target: {target}")

    if details:
        for key, value in details.items():
            click.echo(f"{key}: {value}")

    return click.confirm(f"\nProceed with {action}?")


def select_from_list(
    items: List[str], prompt_text: str = "Select an item"
) -> Optional[str]:
    """Interactive selection from a list of items."""
    if not items:
        click.echo("No items available for selection.")
        return None

    click.echo(f"\n{prompt_text}:")
    for i, item in enumerate(items, 1):
        click.echo(f"{i}. {item}")

    try:
        choice = click.prompt("Enter number", type=int)
        if 1 <= choice <= len(items):
            return items[choice - 1]
        else:
            click.echo("Invalid choice.")
            return None
    except click.Abort:
        return None


def edit_tags(current_tags: List[str]) -> List[str]:
    """Interactive tag editing."""
    click.echo(f"\nCurrent tags: {', '.join(current_tags) if current_tags else 'None'}")
    click.echo("Tag editing options:")
    click.echo("1. Replace all tags")
    click.echo("2. Add tags")
    click.echo("3. Remove tags")
    click.echo("4. Keep current tags")

    choice = click.prompt("Enter choice (1-4)", type=int)

    if choice == 1:
        new_tags_input = click.prompt("New tags (comma-separated)")
        return [t.strip() for t in new_tags_input.split(",") if t.strip()]
    elif choice == 2:
        add_tags_input = click.prompt("Tags to add (comma-separated)")
        add_tags = [t.strip() for t in add_tags_input.split(",") if t.strip()]
        return list(set(current_tags + add_tags))
    elif choice == 3:
        if not current_tags:
            click.echo("No tags to remove.")
            return current_tags

        remove_tags_input = click.prompt("Tags to remove (comma-separated)")
        remove_tags = [t.strip() for t in remove_tags_input.split(",") if t.strip()]
        return [t for t in current_tags if t not in remove_tags]
    else:
        return current_tags


def format_preview(content: str, max_lines: int = 10) -> str:
    """Format content preview for display."""
    lines = content.split("\n")

    if len(lines) <= max_lines:
        return content

    preview_lines = lines[:max_lines]
    preview = "\n".join(preview_lines)
    preview += f"\n... ({len(lines) - max_lines} more lines)"

    return preview


def prompt_for_changes(current_config: dict) -> dict:
    """Interactive prompt for updating configuration."""
    changes = {}

    click.echo("What would you like to update? (Press Enter to skip)")

    # Description
    current_desc = current_config.get("description", "")
    new_desc = click.prompt(
        f"Description [{current_desc}]", default=current_desc, show_default=False
    )
    if new_desc != current_desc:
        changes["description"] = new_desc

    # Tags
    current_tags = current_config.get("tags", [])
    if click.confirm("Update tags?"):
        changes["tags"] = edit_tags(current_tags)

    # Content
    if click.confirm("Update content?"):
        click.echo("Content update options:")
        click.echo("1. Type new content")
        click.echo("2. Load from file")
        click.echo("3. Keep current content")

        choice = click.prompt("Enter choice (1-3)", type=int)

        if choice == 1:
            click.echo("Enter new content (Press Ctrl+D when finished):")
            try:
                new_content = click.get_text_stream("stdin").read()
                changes["content"] = new_content
            except KeyboardInterrupt:
                click.echo("Content update cancelled.")
        elif choice == 2:
            file_path = click.prompt("File path")
            try:
                with open(file_path, "r") as f:
                    changes["content"] = f.read()
            except Exception as e:
                click.echo(f"Error reading file: {e}")

    return changes
