"""
Test Suite for SpecializedCoachGraph Framework
Tests Issue #26: Phase 3 Individual Specialized Coach ReWOO+Reflection Implementation
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from langchain_core.messages import HumanMessage

from athlea_langgraph.graphs.specialized_coach_graph import (
    ComplexityAssessmentNode,
    DomainSpecificReWOOWorkers,
    SpecializedCoachReWOOExecutor,
    create_specialized_coach_graph,
)
from athlea_langgraph.states.specialized_coach_state import (
    SpecializedCoachState,
    create_specialized_coach_state,
    is_simple_query,
    is_complex_query,
    should_use_rewoo,
    log_execution_step,
    log_error,
    log_warning,
    get_execution_summary,
)


class TestComplexityAssessmentNode:
    """Test the LLM-based complexity assessment node."""

    @pytest.mark.asyncio
    async def test_complexity_assessment_simple_query(self):
        """Test that simple queries are correctly assessed as simple."""
        # Mock the entire Azure OpenAI service instead of the instance
        mock_response = Mock()
        mock_response.content = """
        COMPLEXITY_SCORE: 0.2
        COMPLEXITY_LEVEL: simple
        NEEDS_REWOO: false
        REASONING: This is a basic technique question that requires a straightforward answer about proper squat form.
        """

        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ) as mock_create:
            mock_llm = AsyncMock()
            mock_llm.ainvoke = AsyncMock(return_value=mock_response)
            mock_create.return_value = mock_llm

            node = ComplexityAssessmentNode("strength")
            result = await node.assess_complexity("How do I do a proper squat?")

        assert result["complexity_level"] == "simple"
        assert result["complexity_score"] == 0.2
        assert result["needs_rewoo"] == False
        assert "technique question" in result["reasoning"]
        assert result["assessment_method"] == "llm_based"

    @pytest.mark.asyncio
    async def test_complexity_assessment_complex_query(self):
        """Test that complex queries are correctly assessed as complex."""
        mock_response = Mock()
        mock_response.content = """
        COMPLEXITY_SCORE: 0.9
        COMPLEXITY_LEVEL: complex
        NEEDS_REWOO: true
        REASONING: This requires comprehensive program design with periodization, exercise selection, progression planning, and injury prevention considerations.
        """

        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ) as mock_create:
            mock_llm = AsyncMock()
            mock_llm.ainvoke = AsyncMock(return_value=mock_response)
            mock_create.return_value = mock_llm

            node = ComplexityAssessmentNode("strength")
            result = await node.assess_complexity(
                "Design a 16-week powerlifting competition prep program with periodization"
            )

        assert result["complexity_level"] == "complex"
        assert result["complexity_score"] == 0.9
        assert result["needs_rewoo"] == True
        assert "program design" in result["reasoning"]

    @pytest.mark.asyncio
    async def test_complexity_assessment_error_fallback(self):
        """Test fallback behavior when LLM assessment fails."""
        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ) as mock_create:
            mock_llm = AsyncMock()
            mock_llm.ainvoke = AsyncMock(side_effect=Exception("LLM Error"))
            mock_create.return_value = mock_llm

            node = ComplexityAssessmentNode("strength")
            result = await node.assess_complexity("Test query")

        assert result["complexity_level"] == "moderate"
        assert result["complexity_score"] == 0.6
        assert result["needs_rewoo"] == True
        assert result["assessment_method"] == "fallback"
        assert "Assessment error" in result["reasoning"]


class TestDomainSpecificReWOOWorkers:
    """Test domain-specific ReWOO worker patterns."""

    def test_strength_domain_workers(self):
        """Test strength domain worker configuration."""
        workers = DomainSpecificReWOOWorkers.get_domain_workers("strength")

        assert len(workers) == 4
        worker_ids = [w["worker_id"] for w in workers]

        # Check all expected workers are present
        expected_workers = [
            "exercise_selection",
            "program_structure",
            "progression_planning",
            "injury_prevention",
        ]
        assert all(wid in worker_ids for wid in expected_workers)

        # Check dependencies are correct
        exercise_worker = next(
            w for w in workers if w["worker_id"] == "exercise_selection"
        )
        assert exercise_worker["dependencies"] == []

        structure_worker = next(
            w for w in workers if w["worker_id"] == "program_structure"
        )
        assert "exercise_selection" in structure_worker["dependencies"]

        progression_worker = next(
            w for w in workers if w["worker_id"] == "progression_planning"
        )
        assert "program_structure" in progression_worker["dependencies"]

    def test_nutrition_domain_workers(self):
        """Test nutrition domain worker configuration."""
        workers = DomainSpecificReWOOWorkers.get_domain_workers("nutrition")

        assert len(workers) == 4
        worker_ids = [w["worker_id"] for w in workers]

        expected_workers = [
            "nutritional_assessment",
            "macro_optimization",
            "meal_planning",
            "supplementation_strategy",
        ]
        assert all(wid in worker_ids for wid in expected_workers)

    def test_cardio_domain_workers(self):
        """Test cardio domain worker configuration."""
        workers = DomainSpecificReWOOWorkers.get_domain_workers("cardio")

        assert len(workers) == 4
        worker_ids = [w["worker_id"] for w in workers]

        expected_workers = [
            "fitness_assessment",
            "zone_optimization",
            "program_periodization",
            "performance_monitoring",
        ]
        assert all(wid in worker_ids for wid in expected_workers)

    def test_worker_prompt_templates(self):
        """Test that workers have proper prompt templates."""
        workers = DomainSpecificReWOOWorkers.get_domain_workers("strength")

        for worker in workers:
            assert "prompt_template" in worker
            assert "{user_query}" in worker["prompt_template"]
            assert "{user_profile}" in worker["prompt_template"]
            assert worker["name"] in worker["prompt_template"]


class TestSpecializedCoachReWOOExecutor:
    """Test the ReWOO executor for individual coaches."""

    @pytest.mark.asyncio
    async def test_execution_order_calculation(self):
        """Test that worker execution order respects dependencies."""
        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ):
            executor = SpecializedCoachReWOOExecutor("strength")

        workers = [
            {"worker_id": "a", "dependencies": []},
            {"worker_id": "b", "dependencies": ["a"]},
            {"worker_id": "c", "dependencies": ["b"]},
            {"worker_id": "d", "dependencies": ["a", "b"]},
        ]

        order = executor._calculate_execution_order(workers)

        # Check that dependencies are satisfied
        assert order.index("a") < order.index("b")
        assert order.index("b") < order.index("c")
        assert order.index("a") < order.index("d")
        assert order.index("b") < order.index("d")

    @pytest.mark.asyncio
    async def test_worker_execution_with_mock(self):
        """Test individual worker execution with mocked LLM."""
        mock_response = Mock()
        mock_response.content = "Mock worker response"

        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ) as mock_create:
            mock_llm = AsyncMock()
            mock_llm.ainvoke = AsyncMock(return_value=mock_response)
            mock_create.return_value = mock_llm

            executor = SpecializedCoachReWOOExecutor("strength")

        worker = {
            "worker_id": "test_worker",
            "name": "Test Worker",
            "focus_areas": ["testing"],
            "dependencies": [],
            "prompt_template": "Test prompt: {user_query}",
        }

        result = await executor._execute_single_worker(worker, "test query", None, {})

        assert result["success"] == True
        assert result["result"] == "Mock worker response"
        assert result["worker_name"] == "Test Worker"
        assert result["execution_time"] > 0


class TestSpecializedCoachState:
    """Test the enhanced state management."""

    def test_state_initialization(self):
        """Test that state initializes with correct defaults."""
        state = create_specialized_coach_state()

        assert state["complexity_level"] is None
        assert state["complexity_score"] is None
        assert state["execution_path"] is None
        assert state["worker_results"] == {}
        assert state["reflection_applied"] == False
        assert state["rewoo_enabled"] == True
        assert state["complexity_threshold_simple"] == 0.3
        assert state["complexity_threshold_complex"] == 0.7

    def test_complexity_query_classification(self):
        """Test query complexity classification methods."""
        # Test simple query
        state = create_specialized_coach_state(complexity_score=0.2)
        assert is_simple_query(state) == True
        assert is_complex_query(state) == False
        assert should_use_rewoo(state) == False

        # Test complex query
        state = create_specialized_coach_state(complexity_score=0.8)
        assert is_simple_query(state) == False
        assert is_complex_query(state) == True
        assert should_use_rewoo(state) == True

        # Test moderate query
        state = create_specialized_coach_state(complexity_score=0.5)
        assert is_simple_query(state) == False
        assert is_complex_query(state) == False
        assert should_use_rewoo(state) == True

    def test_execution_logging(self):
        """Test execution logging functionality."""
        state = create_specialized_coach_state()

        log_execution_step(state, "test_step", {"data": "test_data"})
        log_error(state, "test error")
        log_warning(state, "test warning")

        assert len(state["execution_trace"]) == 1
        assert state["execution_trace"][0]["step"] == "test_step"
        assert len(state["error_log"]) == 1
        assert state["error_log"][0] == "test error"
        assert len(state["warning_log"]) == 1
        assert state["warning_log"][0] == "test warning"

    def test_execution_summary(self):
        """Test execution summary generation."""
        state = create_specialized_coach_state(
            coach_domain="strength",
            complexity_level="complex",
            complexity_score=0.8,
            execution_path="complex",
            total_execution_time=25.5,
            workers_executed=4,
            successful_workers=3,
            complex_success=True,
        )

        summary = get_execution_summary(state)

        assert summary["coach_domain"] == "strength"
        assert summary["complexity_level"] == "complex"
        assert summary["execution_path"] == "complex"
        assert summary["total_execution_time"] == 25.5
        assert summary["workers_executed"] == 4
        assert summary["successful_workers"] == 3
        assert summary["success"] == True


class TestSpecializedCoachGraphIntegration:
    """Test the complete graph integration."""

    @pytest.mark.asyncio
    async def test_graph_creation(self):
        """Test that graphs can be created for all domains."""
        domains = ["strength", "nutrition", "cardio"]

        # Mock all Azure OpenAI calls
        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ):
            for domain in domains:
                graph = await create_specialized_coach_graph(
                    domain, {"enable_memory": False}
                )
                assert graph is not None

    @pytest.mark.asyncio
    async def test_simple_query_execution_path(self):
        """Test that simple queries follow the simple execution path."""
        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ):
            graph = await create_specialized_coach_graph(
                "strength", {"enable_memory": False}
            )

            # Test that graph has expected nodes
            assert "complexity_assessment" in graph.nodes
            assert "simple_execution" in graph.nodes
            assert "complex_rewoo" in graph.nodes
            assert "reflection" in graph.nodes

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in graph execution."""
        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ):
            # Test with invalid configuration
            try:
                graph = await create_specialized_coach_graph(
                    "invalid_domain", {"enable_memory": False}
                )
                # Should still create graph but with fallback workers
                assert graph is not None
            except Exception as e:
                # Error handling should be graceful
                assert "invalid_domain" in str(e) or True  # Allow for graceful handling


@pytest.mark.integration
class TestSpecializedCoachGraphEndToEnd:
    """End-to-end integration tests."""

    @pytest.mark.asyncio
    async def test_full_workflow_simple_query(self):
        """Test complete workflow for a simple query."""
        # Mock complexity assessment to route to simple path
        mock_response = Mock()
        mock_response.content = """
        COMPLEXITY_SCORE: 0.2
        COMPLEXITY_LEVEL: simple
        NEEDS_REWOO: false
        REASONING: Simple technique question
        """

        with patch(
            "athlea_langgraph.services.azure_openai_service.create_azure_chat_openai"
        ) as mock_create:
            mock_llm = AsyncMock()
            mock_llm.ainvoke = AsyncMock(return_value=mock_response)
            mock_create.return_value = mock_llm

            graph = await create_specialized_coach_graph(
                "strength", {"enable_memory": False}
            )

            # Test execution would go here
            assert graph is not None

    @pytest.mark.asyncio
    async def test_performance_characteristics(self):
        """Test that performance meets expected characteristics."""
        # Simple path should be faster than complex path
        # This would require controlled timing tests
        pass


if __name__ == "__main__":
    # Run specific tests
    pytest.main([__file__, "-v"])
