"""
Unified GraphRAG Service for Athlea LangGraph Integration

Orchestrates queries to both Azure Cognitive Search and Cosmos DB Gremlin Graph
to provide comprehensive knowledge graph functionality for AI coaches.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
import time
import uuid

from pydantic import BaseModel, Field

from .gremlin_service import get_gremlin_service, GremlinQueryOutput
from ..tools.external.azure_search_retriever import AzureSearchRetrieverTool
from ..config.graphrag_config import get_graphrag_config

logger = logging.getLogger(__name__)


class GraphRAGQueryInput(BaseModel):
    """Input schema for unified GraphRAG queries."""

    query: str = Field(description="Natural language query or research question")
    document_id: Optional[str] = Field(
        default=None, description="Specific document ID to focus on"
    )
    doi: Optional[str] = Field(default=None, description="DOI to search for")
    entities: Optional[List[str]] = Field(
        default=None, description="Specific entities to investigate"
    )
    include_acs: bool = Field(
        default=True, description="Include Azure Cognitive Search results"
    )
    include_graph: bool = Field(
        default=True, description="Include Gremlin graph relationships"
    )
    top_k: int = Field(
        default=10, description="Number of results to return from each source"
    )


class GraphRAGQueryOutput(BaseModel):
    """Output schema for unified GraphRAG query results."""

    success: bool = Field(description="Whether the query was successful")
    query: str = Field(description="Original query")

    # ACS Results
    acs_results: Optional[Dict[str, Any]] = Field(
        default=None, description="Azure Cognitive Search results"
    )
    acs_chunk_count: int = Field(default=0, description="Number of text chunks found")

    # Gremlin Results
    graph_results: Optional[Dict[str, Any]] = Field(
        default=None, description="Gremlin graph query results"
    )
    graph_entity_count: int = Field(
        default=0, description="Number of graph entities found"
    )
    graph_relationship_count: int = Field(
        default=0, description="Number of relationships found"
    )

    # Combined Insights
    synthesis: Optional[str] = Field(
        default=None, description="Synthesized insights from both sources"
    )
    causalities: List[Dict[str, Any]] = Field(
        default=[], description="Extracted causal relationships"
    )
    recommendations: List[str] = Field(
        default=[], description="Actionable recommendations"
    )

    # Metadata
    execution_time_ms: int = Field(description="Total execution time")
    request_id: str = Field(description="Unique request identifier")
    error_type: Optional[str] = Field(default=None, description="Error type if failed")
    message: str = Field(description="Result message or error description")


class GraphRAGService:
    """
    Unified GraphRAG service that orchestrates knowledge retrieval from multiple sources.

    Combines Azure Cognitive Search (for textual evidence) with Cosmos DB Gremlin Graph
    (for structured relationships) to provide comprehensive knowledge graph functionality.
    """

    def __init__(self):
        self.config = get_graphrag_config()
        self.gremlin_service = get_gremlin_service()
        self.acs_tool = AzureSearchRetrieverTool()

    async def execute_graphrag_query(
        self, query_input: Union[Dict[str, Any], GraphRAGQueryInput]
    ) -> GraphRAGQueryOutput:
        """
        Execute a unified GraphRAG query combining ACS and Gremlin results.

        Args:
            query_input: Query parameters

        Returns:
            GraphRAGQueryOutput: Combined results from both sources
        """
        start_time = time.time()
        request_id = str(uuid.uuid4())[:8]

        # Validate input
        if isinstance(query_input, dict):
            try:
                validated_input = GraphRAGQueryInput(**query_input)
            except Exception as e:
                exec_time = int((time.time() - start_time) * 1000)
                return GraphRAGQueryOutput(
                    success=False,
                    query=query_input.get("query", ""),
                    execution_time_ms=exec_time,
                    request_id=request_id,
                    error_type="validation_error",
                    message=f"Input validation failed: {str(e)}",
                )
        else:
            validated_input = query_input

        logger.info(
            f"GraphRAG query request {request_id}: {validated_input.query[:100]}..."
        )

        # Execute parallel queries
        acs_task = None
        gremlin_tasks = []

        try:
            # Prepare ACS query if requested
            if validated_input.include_acs:
                acs_query_params = {
                    "query": validated_input.query,
                    "top_k": validated_input.top_k,
                    "vector_search": True,
                    "hybrid_search": True,
                }

                # Add document filter if specified
                if validated_input.document_id:
                    acs_query_params["filter_expression"] = (
                        f"source_id eq '{validated_input.document_id}'"
                    )

                acs_task = asyncio.create_task(self.acs_tool.invoke(acs_query_params))

            # Prepare Gremlin queries if requested
            if validated_input.include_graph:
                if validated_input.document_id:
                    # Query for specific document relationships
                    gremlin_tasks.append(
                        asyncio.create_task(
                            self.gremlin_service.get_document_relationships(
                                validated_input.document_id
                            )
                        )
                    )
                elif validated_input.doi:
                    # Find document by DOI and get relationships
                    gremlin_tasks.append(
                        asyncio.create_task(
                            self.gremlin_service.find_document_by_doi(
                                validated_input.doi
                            )
                        )
                    )
                elif validated_input.entities:
                    # Query for specific entities
                    for entity in validated_input.entities:
                        gremlin_tasks.append(
                            asyncio.create_task(
                                self.gremlin_service.find_entity_relationships(entity)
                            )
                        )
                else:
                    # General query - search for relevant entities
                    # This would need more sophisticated entity extraction
                    # For now, try some common fitness entities
                    common_entities = [
                        "FIFA 11+",
                        "HIIT",
                        "PILATES",
                        "PROPRIOCEPTIVE TRAINING",
                    ]
                    for entity in common_entities:
                        if entity.lower() in validated_input.query.lower():
                            gremlin_tasks.append(
                                asyncio.create_task(
                                    self.gremlin_service.find_entity_relationships(
                                        entity
                                    )
                                )
                            )

            # Execute all tasks in parallel
            all_tasks = []
            if acs_task:
                all_tasks.append(acs_task)
            all_tasks.extend(gremlin_tasks)

            if not all_tasks:
                exec_time = int((time.time() - start_time) * 1000)
                return GraphRAGQueryOutput(
                    success=False,
                    query=validated_input.query,
                    execution_time_ms=exec_time,
                    request_id=request_id,
                    error_type="no_queries_specified",
                    message="No ACS or Gremlin queries were executed",
                )

            # Wait for all tasks to complete
            results = await asyncio.gather(*all_tasks, return_exceptions=True)

            # Process results
            acs_results = None
            gremlin_results = []

            result_index = 0
            if acs_task:
                acs_result = results[result_index]
                if isinstance(acs_result, Exception):
                    logger.error(f"ACS query failed: {acs_result}")
                else:
                    acs_results = acs_result
                result_index += 1

            for i in range(len(gremlin_tasks)):
                gremlin_result = results[result_index + i]
                if isinstance(gremlin_result, Exception):
                    logger.error(f"Gremlin query {i} failed: {gremlin_result}")
                else:
                    gremlin_results.append(gremlin_result)

            # Synthesize results
            synthesis, causalities, recommendations = await self._synthesize_results(
                validated_input.query, acs_results, gremlin_results
            )

            exec_time = int((time.time() - start_time) * 1000)

            return GraphRAGQueryOutput(
                success=True,
                query=validated_input.query,
                acs_results=acs_results.model_dump() if acs_results else None,
                acs_chunk_count=acs_results.result_count if acs_results else 0,
                graph_results={
                    "queries": len(gremlin_results),
                    "results": [
                        gr.model_dump() for gr in gremlin_results if gr.success
                    ],
                },
                graph_entity_count=sum(
                    gr.result_count for gr in gremlin_results if gr.success
                ),
                graph_relationship_count=self._count_relationships(gremlin_results),
                synthesis=synthesis,
                causalities=causalities,
                recommendations=recommendations,
                execution_time_ms=exec_time,
                request_id=request_id,
                message="GraphRAG query completed successfully",
            )

        except Exception as e:
            exec_time = int((time.time() - start_time) * 1000)
            logger.error(f"GraphRAG query error: {e}")
            return GraphRAGQueryOutput(
                success=False,
                query=validated_input.query,
                execution_time_ms=exec_time,
                request_id=request_id,
                error_type="execution_error",
                message=f"GraphRAG query failed: {str(e)}",
            )

    async def query_futsal_study(self) -> GraphRAGQueryOutput:
        """
        Test query for the futsal study document.
        This is the verification query mentioned in your requirements.
        """
        return await self.execute_graphrag_query(
            GraphRAGQueryInput(
                query="Tell me about the futsal injury prevention study with FIFA 11+ program",
                document_id=self.config.test_document_id,
                doi=self.config.test_document_doi,
                entities=[
                    "FIFA 11+",
                    "FUTSAL",
                    "PILATES",
                    "PROPRIOCEPTIVE TRAINING",
                    "HIIT",
                ],
                include_acs=True,
                include_graph=True,
                top_k=20,
            )
        )

    def _count_relationships(self, gremlin_results: List[GremlinQueryOutput]) -> int:
        """Count total relationships across all Gremlin results."""
        total = 0
        for result in gremlin_results:
            if result.success:
                for item in result.results:
                    if isinstance(item, dict) and "relationships" in item:
                        relationships = item["relationships"]
                        if isinstance(relationships, list):
                            total += len(relationships)
        return total

    async def _synthesize_results(
        self,
        query: str,
        acs_results: Optional[Any],
        gremlin_results: List[GremlinQueryOutput],
    ) -> tuple[Optional[str], List[Dict[str, Any]], List[str]]:
        """
        Synthesize results from ACS and Gremlin into actionable insights.

        This is where the "data fusion" magic happens - combining textual evidence
        with structured relationships to generate coaching recommendations.
        """
        synthesis_parts = []
        causalities = []
        recommendations = []

        # Process ACS results for textual evidence
        if acs_results and acs_results.success and acs_results.results:
            synthesis_parts.append(
                f"Found {len(acs_results.results)} relevant research chunks from {acs_results.result_count} documents."
            )

            # Extract key themes from text chunks
            for chunk in acs_results.results[:5]:  # Top 5 chunks
                if chunk.content:
                    content_snippet = (
                        chunk.content[:200] + "..."
                        if len(chunk.content) > 200
                        else chunk.content
                    )
                    synthesis_parts.append(f"Research evidence: {content_snippet}")

        # Process Gremlin results for structured relationships
        if gremlin_results:
            successful_queries = [gr for gr in gremlin_results if gr.success]
            if successful_queries:
                synthesis_parts.append(
                    f"Found structured relationships from {len(successful_queries)} graph queries."
                )

                # Extract causalities from graph relationships
                for result in successful_queries:
                    for item in result.results:
                        if isinstance(item, dict):
                            causalities.extend(
                                self._extract_causalities_from_graph_item(item)
                            )

        # Generate recommendations based on found causalities
        recommendations = self._generate_recommendations(causalities)

        synthesis = " ".join(synthesis_parts) if synthesis_parts else None
        return synthesis, causalities, recommendations

    def _extract_causalities_from_graph_item(
        self, graph_item: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract causal relationships from a graph query result item."""
        causalities = []

        if "relationships" in graph_item:
            relationships = graph_item["relationships"]
            if isinstance(relationships, list):
                for rel in relationships:
                    if isinstance(rel, dict) and "edge" in rel:
                        edge = rel["edge"]
                        connected_node = rel.get("connected_node", {})

                        # Look for causal edge labels
                        edge_label = edge.get("label", "")
                        if edge_label in [
                            "PREVENTS",
                            "IMPROVES",
                            "ENHANCES",
                            "CAUSES",
                            "REQUIRES",
                        ]:
                            causality = {
                                "relationship": edge_label,
                                "confidence": edge.get("confidence", 0.0),
                                "condition": edge.get("condition"),
                                "source_entity": graph_item.get(
                                    "entity_details", {}
                                ).get("name", "Unknown"),
                                "target_entity": connected_node.get("name", "Unknown"),
                                "dosage": edge.get("dosage_link"),
                                "effect_size": edge.get("effect_size"),
                            }
                            causalities.append(causality)

        return causalities

    def _generate_recommendations(self, causalities: List[Dict[str, Any]]) -> List[str]:
        """Generate actionable recommendations based on extracted causalities."""
        recommendations = []

        for causality in causalities:
            if (
                causality["relationship"] == "PREVENTS"
                and causality.get("confidence", 0) > 0.7
            ):
                rec = f"{causality['source_entity']} can prevent {causality['target_entity']}"
                if causality.get("condition"):
                    rec += f" when {causality['condition']}"
                if causality.get("dosage"):
                    rec += f" (dosage: {causality['dosage']})"
                recommendations.append(rec)

            elif (
                causality["relationship"] == "IMPROVES"
                and causality.get("confidence", 0) > 0.7
            ):
                rec = f"{causality['source_entity']} improves {causality['target_entity']}"
                if causality.get("condition"):
                    rec += f" when {causality['condition']}"
                recommendations.append(rec)

        return recommendations

    async def close(self):
        """Close all underlying connections."""
        await self.gremlin_service.close()
        if hasattr(self.acs_tool, "close"):
            await self.acs_tool.close()


# Global service instance
_graphrag_service: Optional[GraphRAGService] = None


def get_graphrag_service() -> GraphRAGService:
    """Get the global GraphRAG service instance."""
    global _graphrag_service
    if _graphrag_service is None:
        _graphrag_service = GraphRAGService()
    return _graphrag_service
