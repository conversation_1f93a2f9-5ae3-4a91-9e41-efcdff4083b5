"""
MCP Agent Integration

Utilities for integrating MCP (Model Context Protocol) tools with domain agents.
Provides hybrid approach combining direct tools with MCP tools.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from langchain.tools import BaseTool

logger = logging.getLogger(__name__)


class MCPAgentIntegrator:
    """Integrates MCP tools with domain agents for hybrid tool approach."""

    def __init__(self):
        self.mcp_config = {
            "strength": {
                "command": "python",
                "args": ["-m", "mcp_servers.strength_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
            "cardio": {
                "command": "python",
                "args": ["-m", "mcp_servers.cardio_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
            "nutrition": {
                "command": "python",
                "args": ["-m", "mcp_servers.nutrition_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
            "recovery": {
                "command": "python",
                "args": ["-m", "mcp_servers.recovery_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
            "mental": {
                "command": "python",
                "args": ["-m", "mcp_servers.mental_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
            "external": {
                "command": "python",
                "args": ["-m", "mcp_servers.external_mcp.server"],
                "transport": "stdio",
                "env": {},
            },
        }
        self._mcp_clients = {}
        self._connected = False

    async def get_mcp_tools_for_domain(
        self, domain: str, max_retries: int = 3, base_delay: float = 1.0
    ) -> List[BaseTool]:
        """Get MCP tools for a specific domain with retry mechanism."""
        logger.info(f"🔧 Getting MCP tools for domain: {domain}")

        try:
            # Import here to avoid circular imports
            from langchain_mcp_adapters.client import MultiServerMCPClient

            if domain not in self.mcp_config:
                logger.warning(f"⚠️ Domain '{domain}' not found in MCP config")
                return []

            # Create client for specific domain
            domain_config = {domain: self.mcp_config[domain]}

            # Retry mechanism with exponential backoff
            for attempt in range(max_retries + 1):
                try:
                    logger.info(
                        f"🔌 Connecting to {domain} MCP server (attempt {attempt + 1}/{max_retries + 1})..."
                    )

                    # Add delay between attempts (except first)
                    if attempt > 0:
                        delay = base_delay * (2 ** (attempt - 1))
                        logger.info(f"⏱️ Waiting {delay}s before retry...")
                        await asyncio.sleep(delay)

                    client = MultiServerMCPClient(domain_config)

                    # Try to get tools with timeout
                    try:
                        tools = await asyncio.wait_for(client.get_tools(), timeout=10.0)

                        logger.info(f"✅ Successfully connected to {domain} MCP server")
                        logger.info(
                            f"📋 Found {len(tools)} MCP tools for {domain} domain"
                        )
                        for tool in tools:
                            logger.info(f"  - {tool.name}: {tool.description[:60]}...")

                        # Store client for cleanup
                        self._mcp_clients[domain] = client

                        return tools

                    except asyncio.TimeoutError:
                        logger.warning(
                            f"⏱️ Timeout connecting to {domain} MCP server on attempt {attempt + 1}"
                        )
                        try:
                            await client.close()
                        except:
                            pass
                        if attempt == max_retries:
                            raise
                        continue

                except Exception as e:
                    logger.warning(
                        f"⚠️ Connection attempt {attempt + 1} failed for {domain}: {e}"
                    )
                    if attempt == max_retries:
                        logger.error(
                            f"❌ All {max_retries + 1} connection attempts failed for {domain}"
                        )
                        return []
                    continue

            return []

        except ImportError:
            logger.error("❌ LangChain MCP adapters not available")
            return []
        except Exception as e:
            logger.error(f"❌ Failed to get MCP tools for {domain}: {e}")
            logger.info(f"🔄 Continuing with direct tools only for {domain}")
            return []

    async def get_hybrid_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """
        Get both direct and MCP tools for an agent.

        Args:
            agent_type: Type of agent (e.g., 'strength', 'nutrition', 'cardio')

        Returns:
            Combined list of direct tools and MCP tools
        """
        logger.info(f"🔗 Getting hybrid tools for {agent_type} agent...")

        # Get direct tools
        direct_tools = await self._get_direct_tools(agent_type)
        logger.info(f"📋 Found {len(direct_tools)} direct tools")

        # Get MCP tools
        mcp_tools = await self.get_mcp_tools_for_domain(agent_type)
        logger.info(f"🔧 Found {len(mcp_tools)} MCP tools")

        # Combine tools
        all_tools = direct_tools + mcp_tools

        logger.info(f"✅ Total tools for {agent_type} agent: {len(all_tools)}")
        logger.info(f"   - Direct tools: {len(direct_tools)}")
        logger.info(f"   - MCP tools: {len(mcp_tools)}")

        return all_tools

    async def _get_direct_tools(self, agent_type: str) -> List[BaseTool]:
        """Get direct tools using the existing modular tools manager."""
        try:
            from ..tools.modular_agent_tools import get_modular_tools_manager

            tools_manager = await get_modular_tools_manager()

            # Get direct tools based on agent type
            if agent_type == "strength":
                direct_tools = tools_manager.get_strength_agent_tools()
            elif agent_type == "nutrition":
                direct_tools = tools_manager.get_nutrition_agent_tools()
            elif agent_type == "cardio":
                direct_tools = tools_manager.get_cardio_agent_tools()
            elif agent_type == "recovery":
                direct_tools = tools_manager.get_recovery_agent_tools()
            elif agent_type == "mental":
                direct_tools = tools_manager.get_mental_agent_tools()
            else:
                direct_tools = []

        except Exception as e:
            logger.error(f"❌ Failed to get direct tools for {agent_type}: {e}")
            direct_tools = []

        return direct_tools

    async def close_connections(self):
        """Close all MCP client connections."""
        logger.info("🔌 Closing MCP client connections...")

        for domain, client in self._mcp_clients.items():
            try:
                await client.close()
                logger.info(f"✅ Closed {domain} MCP client")
            except Exception as e:
                logger.error(f"❌ Error closing {domain} MCP client: {e}")

        self._mcp_clients.clear()
        self._connected = False
        logger.info("🔌 All MCP connections closed")


# Global instance for easy access
_global_mcp_integrator: Optional[MCPAgentIntegrator] = None


async def get_mcp_integrator() -> MCPAgentIntegrator:
    """Get or create the global MCP integrator instance."""
    global _global_mcp_integrator

    if _global_mcp_integrator is None:
        _global_mcp_integrator = MCPAgentIntegrator()
        logger.info("🔧 Created global MCP integrator")

    return _global_mcp_integrator


async def get_agent_tools(agent_type: str, use_mcp: bool = True) -> List[BaseTool]:
    """
    Convenience function to get tools for an agent.

    Args:
        agent_type: Type of agent ('strength', 'nutrition', etc.)
        use_mcp: Whether to include MCP tools (default: True)

    Returns:
        List of tools for the agent
    """
    integrator = await get_mcp_integrator()

    if use_mcp:
        return await integrator.get_hybrid_tools_for_agent(agent_type)
    else:
        return await integrator._get_direct_tools(agent_type)


# Cleanup function for graceful shutdown
async def cleanup_mcp_connections():
    """Cleanup function to close all MCP connections."""
    global _global_mcp_integrator

    if _global_mcp_integrator:
        await _global_mcp_integrator.close_connections()
        _global_mcp_integrator = None
        logger.info("🧹 MCP integrator cleanup completed")
