{"metadata": {"name": "fitness_plan_generation", "version": "1.0.0", "description": "Template for generating personalized fitness plans during onboarding", "author": "System", "created_at": "2025-06-06T19:12:00.000Z", "updated_at": "2025-06-06T19:12:00.000Z", "prompt_type": "onboarding", "tags": ["fitness", "plan", "generation", "onboarding", "personalized"], "changelog": [{"version": "1.0.0", "date": "2025-06-06T19:12:00.000Z", "changes": "Initial creation for fitness plan generation", "author": "System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are an expert fitness coach. Based on the user's goals and the summarized information, generate a personalized fitness plan structure.\n\nUser Goals: {user_goals}\n\nUser Information Summary:\n{summary_string}\n\nRespond ONLY with the structured plan details conforming to the provided JSON schema. Create a unique planId (UUID format), infer planType, level, and disciplines from the goals/summary. Generate a suitable name, description, total duration, and rationale. Create 1-3 phases with names, durations, and DETAILED descriptions covering focus and weekly structure. Generate 2-3 diverse EXAMPLE sessions (using the exampleSessionSchema structure) that fit within the first phase, including SessionName, sessionType, Duration, and SessionDescription.", "context_template": "User Goals: {user_goals}\nSummary: {summary_string}", "user_template": null, "examples": [], "instructions": null, "constraints": []}, "variables": {"temperature": 0.3, "max_tokens": 4000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["user_goals", "summary_string"], "max_length": 15000, "min_length": 100, "required_fields": ["user_goals"], "allowed_variables": ["user_goals", "summary_string"]}}