"""
Individual Coach Graphs

This module creates individual graphs for each coach that reuse the same
components as the comprehensive hybrid graph but with simplified routing.

Instead of duplicating the entire comprehensive graph, we create focused
graphs that:
1. Start directly with the specified coach
2. Have access to the same tools and capabilities
3. Use the same underlying ReAct executors
4. Can optionally use GraphRAG when needed

This allows each coach to work independently while maintaining the same
setup and capabilities as the comprehensive system.
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

# Import existing components that we'll reuse
from ..agents.aggregation_node import aggregation_node
from ..agents.graphrag_nodes import graphrag_retrieval_node
from ..agents.reasoning_node import reasoning_node
from ..agents.react_coach_executor import ReActCoachExecutor
from ..agents.specialized_coaches import get_tools_manager
from ..states.optimized_state import OptimizedCoachingState
from ..services.azure_openai_service import create_azure_chat_openai

# Import configuration and utilities from optimized comprehensive graph
from .optimized_comprehensive_coaching_graph import (
    ComprehensiveCoachingConfig,
    create_react_coaches,
)

logger = logging.getLogger(__name__)


# --- New Schema for LLM-based Knowledge Assessment ---
class KnowledgeNeedsAnalysis(BaseModel):
    """
    Schema to determine if a user's query requires external research.
    """

    needs_research: bool = Field(
        ...,
        description=(
            "Set to True if the user's query requires looking up recent studies, "
            "scientific evidence, or the latest findings. Set to False if the "
            "question can be answered from general knowledge or existing expertise."
        ),
    )
    reasoning: str = Field(
        ...,
        description="A brief explanation for the decision on whether to conduct research.",
    )


# Available coaches
AVAILABLE_COACHES = [
    "strength_coach",
    "cardio_coach",
    "cycling_coach",
    "nutrition_coach",
    "recovery_coach",
    "mental_coach",
]

# Global caches to prevent repeated loading (same pattern as comprehensive graph)
_tools_manager = None
_react_coaches: Dict[str, ReActCoachExecutor] = {}
_resources_initialized = False
_initialization_lock = asyncio.Lock()


async def initialize_individual_coach_resources(config: ComprehensiveCoachingConfig):
    """
    Initialize resources for individual coach graphs with caching.
    Uses the same pattern as comprehensive graph to prevent repeated loading.
    """
    global _tools_manager, _react_coaches, _resources_initialized

    async with _initialization_lock:
        if _resources_initialized:
            logger.info("✅ INDIVIDUAL_COACH: [GLOBAL] Resources already initialized.")
            return _tools_manager, _react_coaches

        logger.info(
            "🚀 INDIVIDUAL_COACH: [GLOBAL] Initializing resources for the first time..."
        )
        try:
            # Auto-sync prompts to LangSmith for individual coach graphs (can be disabled with DISABLE_PROMPT_SYNC=true)
            if os.getenv("DISABLE_PROMPT_SYNC", "false").lower() != "true":
                try:
                    from ..services.langsmith_prompt_sync import (
                        auto_sync_prompts_on_startup,
                    )

                    await auto_sync_prompts_on_startup()
                except Exception as e:
                    logger.warning(f"  - Prompt sync failed: {e}")
            else:
                logger.info(
                    "  - Prompt sync disabled via DISABLE_PROMPT_SYNC environment variable"
                )

            _tools_manager = await get_tools_manager()
            logger.info("  - Tools manager created.")

            _react_coaches = await create_react_coaches(_tools_manager, config)
            logger.info(f"  - {len(_react_coaches)} ReAct coaches created and cached.")

            _resources_initialized = True
            logger.info(
                "✅ INDIVIDUAL_COACH: [GLOBAL] Resources initialized successfully."
            )

            return _tools_manager, _react_coaches

        except Exception as e:
            logger.error(
                f"❌ INDIVIDUAL_COACH: [GLOBAL] Failed to initialize resources: {e}",
                exc_info=True,
            )
            # Reset flag so next request can retry initialization
            _resources_initialized = False
            raise


async def create_individual_coach_graph(
    coach_name: str,
    config: Optional[Union[Dict[str, Any], ComprehensiveCoachingConfig]] = None,
) -> CompiledStateGraph:
    """
    Create an individual coach graph that reuses comprehensive graph components.

    Args:
        coach_name: The specific coach to create a graph for (e.g., "strength_coach")
        config: Optional configuration parameters

    Returns:
        Compiled graph for the individual coach
    """
    if coach_name not in AVAILABLE_COACHES:
        raise ValueError(
            f"Coach '{coach_name}' not available. Choose from: {AVAILABLE_COACHES}"
        )

    logger.info(f"🏗️ INDIVIDUAL_COACH: Creating graph for {coach_name}")

    if config is None:
        config = {}

    # Parse configuration (reuse existing logic)
    if isinstance(config, ComprehensiveCoachingConfig):
        coaching_config = config
    elif isinstance(config, dict):
        coaching_config = ComprehensiveCoachingConfig(**config)
    else:
        coaching_config = ComprehensiveCoachingConfig()

    # Initialize resources with caching (prevents repeated loading)
    tools_manager, react_coaches = await initialize_individual_coach_resources(
        coaching_config
    )

    # Build the individual coach graph
    uncompiled_graph = await build_individual_coach_graph(
        coach_name, coaching_config, tools_manager, react_coaches
    )

    # Compile with memory if enabled
    if coaching_config.enable_memory:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory enabled for {coach_name}")
        memory = MemorySaver()
        graph = uncompiled_graph.compile(checkpointer=memory)
    else:
        logger.info(f"🧠 INDIVIDUAL_COACH: Memory disabled for {coach_name}")
        graph = uncompiled_graph.compile()

    logger.info(f"✅ INDIVIDUAL_COACH: {coach_name} graph compiled successfully")
    return graph


async def build_individual_coach_graph(
    coach_name: str,
    config: ComprehensiveCoachingConfig,
    tools_manager,
    react_coaches: Dict[str, ReActCoachExecutor],
) -> StateGraph:
    """
    Build the individual coach graph structure.

    Flow: START → reasoning → coach → [optional_graphrag] → END
    """
    logger.info(f"🏗️ INDIVIDUAL_COACH: Building graph structure for {coach_name}")

    if coach_name not in react_coaches:
        raise ValueError(f"Coach executor not found for {coach_name}")

    coach_executor = react_coaches[coach_name]
    logger.info(f"🤖 INDIVIDUAL_COACH: Initialized {coach_name} executor")

    async def enhanced_reasoning_node(state: OptimizedCoachingState) -> Dict[str, Any]:
        """Enhanced reasoning node (reused from comprehensive graph)."""
        result = await reasoning_node(state)
        # Set primary coach to our specific coach
        result["primary_coach"] = coach_name
        result["routing_decision"] = (
            coach_name  # THIS WAS THE BUG! Should be coach_name, not "direct_coach"
        )
        result["required_coaches"] = [coach_name]
        return result

    async def individual_coach_node(state: OptimizedCoachingState) -> Dict[str, Any]:
        """Individual coach node using the same ReAct executor logic."""
        logger.info(f"🏃‍♂️ INDIVIDUAL_COACH: [{coach_name}] Starting execution")

        if isinstance(state, dict):
            user_query = state.get("user_query", "")
            messages = state.get("messages", [])
            user_profile = state.get("user_profile", {})
            execution_steps = state.get("execution_steps", [])
            knowledge_context = state.get("knowledge_context", "")
        else:
            user_query = getattr(state, "user_query", "")
            messages = getattr(state, "messages", [])
            user_profile = getattr(state, "user_profile", {})
            execution_steps = getattr(state, "execution_steps", [])
            knowledge_context = getattr(state, "knowledge_context", "")

        # Get the latest user message if user_query is empty
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        if not user_query:
            greeting_response = f"Hello! I'm your {coach_name.replace('_', ' ').title()}. How can I help you today?"
            return {
                "current_node": coach_name,
                "final_response": greeting_response,
                "messages": messages + [AIMessage(content=greeting_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {"node": coach_name, "action": "greeting"},
            }

        try:
            # If we have knowledge context from GraphRAG, enhance the user query with research context
            enhanced_user_query = user_query
            enhanced_user_profile = user_profile.copy() if user_profile else {}

            if knowledge_context:
                enhanced_user_profile["research_context"] = knowledge_context
                logger.info(
                    f"🔬 INDIVIDUAL_COACH: [{coach_name}] Using GraphRAG context: {len(knowledge_context)} chars"
                )

                # Create an enhanced query that explicitly includes the research context
                enhanced_user_query = f"""User Query: {user_query}

Research Context from Knowledge Base:
{knowledge_context}

Please provide a comprehensive answer using the research context above to inform your response."""

            # Execute the ReAct pattern with enhanced query if we have GraphRAG context
            result = await coach_executor.execute(
                user_message=enhanced_user_query,
                conversation_history=messages,
                user_profile=enhanced_user_profile,
            )

            final_answer = result.get("final_answer", "I'd be happy to help!")

            # Ensure final response is a string
            if not isinstance(final_answer, str):
                import json

                final_answer = json.dumps(final_answer)

            logger.info(f"✅ INDIVIDUAL_COACH: [{coach_name}] Generated response")

            # Ensure execution steps properly reflect GraphRAG usage
            final_execution_steps = execution_steps + [coach_name]
            if knowledge_context and "graphrag_retrieval" not in execution_steps:
                # Insert graphrag_retrieval before the coach step if it's missing
                final_execution_steps = execution_steps + [
                    "graphrag_retrieval",
                    coach_name,
                ]

            return {
                "current_node": coach_name,
                "final_response": final_answer,
                "messages": messages + [AIMessage(content=final_answer)],
                "coach_responses": {coach_name: final_answer},
                "execution_steps": final_execution_steps,
                "debug_info": {
                    "node": coach_name,
                    "success": str(result.get("success", False)),
                    "iterations": str(result.get("iterations", 0)),
                    "action": "individual_coach_complete",
                    "used_graphrag": bool(knowledge_context),
                    "knowledge_context_length": (
                        len(knowledge_context) if knowledge_context else 0
                    ),
                },
            }

        except Exception as e:
            logger.error(f"❌ INDIVIDUAL_COACH: [{coach_name}] Error: {e}")
            error_response = (
                "I apologize, but I encountered an error. Please try again."
            )
            return {
                "current_node": coach_name,
                "final_response": error_response,
                "messages": messages + [AIMessage(content=error_response)],
                "execution_steps": execution_steps + [coach_name],
                "debug_info": {
                    "node": coach_name,
                    "error": str(e),
                    "action": "error_fallback",
                },
            }

    # --- LLM Knowledge Assessment Node ---
    async def llm_knowledge_assessment_node(
        state: OptimizedCoachingState,
    ) -> Dict[str, Any]:
        """
        Uses LLM to determine if GraphRAG is needed for this query.
        """
        if isinstance(state, dict):
            messages = state.get("messages", [])
            execution_steps = state.get("execution_steps", [])
            user_query = state.get("user_query", "")
        else:
            messages = getattr(state, "messages", [])
            execution_steps = getattr(state, "execution_steps", [])
            user_query = getattr(state, "user_query", "")

        if not messages:
            return {
                "knowledge_retrieval_needed": False,
                "current_node": "knowledge_assessment",
                "execution_steps": execution_steps + ["knowledge_assessment"],
                "debug_info": {
                    "node": "knowledge_assessment",
                    "needs_research": False,
                    "reasoning": "No messages found to assess.",
                    "approach": "no_messages",
                },
            }

        # Get the user's original query
        if not user_query and messages:
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        try:
            # Get a specific LLM for this task
            assessment_llm = create_azure_chat_openai(
                node_type="reasoning"
            ).with_structured_output(KnowledgeNeedsAnalysis, include_raw=False)

            # Create a focused assessment prompt
            assessment_prompt = [
                HumanMessage(
                    content=f"""Analyze this user query to determine if it requires external research or scientific evidence:

User Query: "{user_query}"

Consider:
- Does this ask for specific scientific data, studies, or recent findings?
- Would answering require current research beyond general fitness knowledge?
- Does it ask about specific physiological mechanisms, optimal values, or evidence-based recommendations?
- Does the query explicitly mention "research" or ask for evidence-based information?

Be conservative - if in doubt, prefer research to ensure accuracy."""
                )
            ]

            assessment = await assessment_llm.ainvoke(assessment_prompt)

            needs_research = assessment.needs_research
            reasoning = assessment.reasoning
            logger.info(
                f"🔍 INDIVIDUAL_COACH: LLM Assessment - Needs research: {needs_research}. Reason: {reasoning}"
            )

        except Exception as e:
            logger.warning(
                f"⚠️ INDIVIDUAL_COACH: LLM knowledge assessment failed: {e}. Defaulting to no research."
            )
            needs_research = False
            reasoning = f"Error during assessment: {e}"

        return {
            "knowledge_retrieval_needed": needs_research,
            "current_node": "knowledge_assessment",
            "execution_steps": execution_steps + ["knowledge_assessment"],
            "user_query": user_query,  # ← CRITICAL FIX: Ensure user_query is propagated to next nodes
            "debug_info": {
                "node": "knowledge_assessment",
                "needs_research": needs_research,
                "reasoning": reasoning,
                "approach": "llm_based_assessment",
                "user_query": user_query,
            },
        }

    # Build the graph
    builder = StateGraph(OptimizedCoachingState)

    # Add nodes
    builder.add_node("reasoning", enhanced_reasoning_node)
    builder.add_node("knowledge_assessment", llm_knowledge_assessment_node)
    builder.add_node("graphrag_retrieval", graphrag_retrieval_node)
    builder.add_node(coach_name, individual_coach_node)

    # Define flow
    builder.add_edge(START, "reasoning")
    builder.add_edge("reasoning", "knowledge_assessment")

    # Conditional routing based on knowledge needs
    def route_from_knowledge_assessment(state: OptimizedCoachingState) -> str:
        """Route based on whether research is needed."""
        if isinstance(state, dict):
            needs_research = state.get("knowledge_retrieval_needed", False)
        else:
            needs_research = getattr(state, "knowledge_retrieval_needed", False)

        if needs_research:
            logger.info(
                f"🔀 INDIVIDUAL_COACH: Research needed → GraphRAG → {coach_name}"
            )
            return "graphrag_retrieval"
        else:
            logger.info(f"🔀 INDIVIDUAL_COACH: Direct → {coach_name}")
            return coach_name

    builder.add_conditional_edges(
        "knowledge_assessment",
        route_from_knowledge_assessment,
        {
            "graphrag_retrieval": "graphrag_retrieval",
            coach_name: coach_name,
        },
    )

    # GraphRAG flows to coach
    builder.add_edge("graphrag_retrieval", coach_name)

    # Coach flows to END
    builder.add_edge(coach_name, END)

    logger.info(f"✅ INDIVIDUAL_COACH: {coach_name} graph structure built successfully")
    logger.info(
        f"📊 INDIVIDUAL_COACH: Flow: START → reasoning → knowledge_assessment → [{coach_name} | graphrag_retrieval → {coach_name}] → END"
    )

    return builder


# Factory functions for each coach (for langgraph.json)
async def create_strength_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create strength coach graph."""
    return await create_individual_coach_graph("strength_coach", config)


async def create_cardio_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cardio coach graph."""
    return await create_individual_coach_graph("cardio_coach", config)


async def create_cycling_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create cycling coach graph."""
    return await create_individual_coach_graph("cycling_coach", config)


async def create_nutrition_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create nutrition coach graph."""
    return await create_individual_coach_graph("nutrition_coach", config)


async def create_recovery_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create recovery coach graph."""
    return await create_individual_coach_graph("recovery_coach", config)


async def create_mental_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create mental coach graph."""
    return await create_individual_coach_graph("mental_coach", config)
