# LangGraph Studio Guide

This guide will help you set up and use the Athlea Coaching Agent in [LangGraph Studio](https://github.com/langchain-ai/langgraph-studio) for visual debugging and development.

## What is LangGraph Studio?

LangGraph Studio is a specialized IDE for developing LLM applications that provides:
- **Visual Graph Representation**: See your coaching agent's flow visually.
- **Step-by-Step Debugging**: Execute nodes one at a time.
- **State Inspection**: View and edit the agent state at any point.
- **Human-in-the-Loop**: Add interrupts for human input during execution.
- **Thread Management**: Manage conversation threads and sessions.

## Setup Instructions

### Prerequisites

1.  **Docker Desktop** or **Orbstack** (must be running).
2.  **LangGraph Studio**: The desktop app (macOS only) or the web version used with a local LangGraph server.
3.  **Project Files**: Ensure your project has `langgraph.json` (Studio config), `athlea_langgraph/studio_graph.py` (the Studio-compatible graph), and `.env.local` (environment variables).

### Step 1: Configure Environment Variables

Your `.env.local` file must be correctly configured, especially the MongoDB URI for Docker:

```bash
# Required for Azure OpenAI
AZURE_OPENAI_API_KEY=your_key_here
AZURE_OPENAI_ENDPOINT=your_endpoint_here

# Required for LangSmith (auto-configured by Studio)
LANGSMITH_API_KEY=your_langsmith_key
LANGSMITH_TRACING=true

# For local MongoDB, use the internal Docker host
MONGODB_URI=mongodb://host.docker.internal:27017
```

### Step 2: Open Project in LangGraph Studio

1.  Launch LangGraph Studio.
2.  Login with your LangSmith account.
3.  Select your `python-langgraph` project folder.
4.  Wait for Studio to build the Docker environment.

### Step 3: Select and Configure the Graph

1.  In the Studio interface, select the `coaching_agent` graph from the dropdown menu.
2.  Configure the input parameters for the graph run:
    -   `user_id`: A test user ID.
    -   `mongodb_uri`: Should match the value in `.env.local`.
    -   `enable_memory`: Toggle to test with or without memory.
    -   `thread_id`: A unique ID for the conversation.

## Using LangGraph Studio

### Basic Operations

1.  **Start a Conversation**: Set the **Input** field with a user query, like `{"user_query": "I want to start strength training"}`. Click **Submit** to run the graph.
2.  **View Execution**: Watch the nodes in the graph light up as they execute, showing the agent's path (e.g., `reasoning` → `planning` → `strength_coach` → `aggregation`).
3.  **Inspect State**: Click on any completed node to see its state, including inputs, outputs, and any metadata.

### Advanced Features

-   **Add Interrupts**: Pause execution at specific points. You can interrupt on a single node or on all nodes to step through the entire graph.
-   **Edit State**: While paused, click the "pencil" icon on any step to modify the agent's state. You can then **Fork** the execution to create a new run with the modified state, which is excellent for A/B testing different paths.
-   **Human-in-the-Loop**: Use the `human_input` node to create interactive sessions where the agent can explicitly ask for and await human feedback before continuing.
-   **Thread Management**: Use the thread management UI to create, switch between, and compare different conversation histories.

## Testing Scenarios

-   **Specialist Routing**: Use a query like "How do I build muscle mass?" and verify that the graph correctly routes to the `strength_coach`.
-   **Multi-Domain Query**: Use a query like "I need help with strength training and nutrition for my upcoming race" and watch the graph route to multiple specialists.
-   **Clarification**: Use a vague query like "Help me get fit" and verify that the agent enters a clarification loop.

## Troubleshooting

-   **Docker Connection Error**: Ensure Docker Desktop or Orbstack is running before you launch Studio.
-   **MongoDB Connection Issues**: Double-check that you are using `mongodb://host.docker.internal:27017` when running a local MongoDB instance, as the graph runs inside a Docker container.
-   **Module Not Found Error**: Ensure any new dependencies are added to `pyproject.toml` so Studio can install them in the Docker environment.
-   **LangSmith Authentication**: This is typically handled automatically when you log into LangGraph Studio. Ensure you are logged in.

LangGraph Studio is the most effective way to visualize, debug, and understand the complex flows of the Athlea coaching agent. 