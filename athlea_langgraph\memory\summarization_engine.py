"""
Memory Summarization Engine for Automatic Content Compression

Implements LLM-based memory summarization to handle long-term memory growth:
- Conversation batch summarization
- Key insight extraction
- Tiered memory compression
- Quality assessment and validation
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from .mem0_adapter import Mem0Adapter, MemoryType
from .schemas.memory_metadata import ImportanceScore, SummaryLevel, SummaryMetadata

logger = logging.getLogger(__name__)


@dataclass
class SummarizationConfig:
    """Configuration for memory summarization."""

    batch_size: int = 10  # Number of memories to summarize together
    min_content_length: int = (
        100  # Minimum content length to consider for summarization
    )
    max_summary_ratio: float = 0.3  # Maximum summary length as ratio of original
    quality_threshold: float = 0.7  # Minimum quality score to accept summary
    age_threshold_days: int = 30  # Age threshold for automatic summarization
    preserve_keywords: List[str] = None  # Keywords to always preserve

    def __post_init__(self):
        if self.preserve_keywords is None:
            self.preserve_keywords = [
                "goal",
                "injury",
                "preference",
                "limitation",
                "achievement",
            ]


class MemorySummarizationEngine:
    """
    Engine for automatic memory content summarization and compression.

    Features:
    - LLM-based conversation summarization
    - Batch processing for efficiency
    - Key insight extraction and preservation
    - Quality assessment and validation
    - Configurable summarization strategies
    """

    def __init__(self, mem0_adapter: Mem0Adapter, llm_model: str = "gpt-4o-mini"):
        """
        Initialize summarization engine.

        Args:
            mem0_adapter: Configured mem0 adapter instance
            llm_model: LLM model for summarization
        """
        self.mem0_adapter = mem0_adapter
        self.llm = ChatOpenAI(model=llm_model, temperature=0.1)
        self.summarization_stats = {
            "total_summarized": 0,
            "total_compression_ratio": 0.0,
            "quality_scores": [],
        }

    async def summarize_conversation_batch(
        self,
        conversations: List[Dict[str, Any]],
        config: SummarizationConfig = SummarizationConfig(),
    ) -> Dict[str, Any]:
        """
        Summarize a batch of conversation memories.

        Args:
            conversations: List of conversation memory dictionaries
            config: Summarization configuration

        Returns:
            Summary result with content and metadata
        """
        if not conversations:
            return {"summary": "", "key_insights": [], "metadata": {}}

        # Filter conversations that meet criteria
        eligible_conversations = [
            conv
            for conv in conversations
            if len(conv.get("content", "")) >= config.min_content_length
            and conv.get("metadata", {}).get("memory_type") == "conversation"
        ]

        if not eligible_conversations:
            logger.info("No conversations meet summarization criteria")
            return {"summary": "", "key_insights": [], "metadata": {}}

        # Group conversations by time period for better context
        time_groups = self._group_conversations_by_time(eligible_conversations)

        summaries = []
        all_insights = []

        for time_period, group_conversations in time_groups.items():
            if len(group_conversations) < 2:  # Skip single conversations
                continue

            # Create summarization prompt
            conversation_text = self._format_conversations_for_prompt(
                group_conversations
            )

            try:
                # Generate summary
                summary_result = await self._generate_conversation_summary(
                    conversation_text, config
                )

                if summary_result["quality_score"] >= config.quality_threshold:
                    summaries.append(
                        {
                            "time_period": time_period,
                            "summary": summary_result["summary"],
                            "conversation_count": len(group_conversations),
                            "quality_score": summary_result["quality_score"],
                        }
                    )
                    all_insights.extend(summary_result["key_insights"])

            except Exception as e:
                logger.error(
                    f"Failed to summarize conversation group {time_period}: {e}"
                )
                continue

        # Combine summaries if multiple time periods
        if len(summaries) > 1:
            combined_summary = await self._combine_summaries(summaries, config)
        elif len(summaries) == 1:
            combined_summary = summaries[0]["summary"]
        else:
            combined_summary = ""

        # Calculate metadata
        original_length = sum(
            len(conv.get("content", "")) for conv in eligible_conversations
        )
        summary_length = len(combined_summary)

        metadata = {
            "original_conversations": len(eligible_conversations),
            "time_periods": len(time_groups),
            "compression_ratio": (
                summary_length / original_length if original_length > 0 else 0.0
            ),
            "summarization_timestamp": datetime.now().isoformat(),
            "summarization_method": "llm_batch",
        }

        # Update stats
        self.summarization_stats["total_summarized"] += len(eligible_conversations)
        self.summarization_stats["total_compression_ratio"] += metadata[
            "compression_ratio"
        ]

        logger.info(
            f"Summarized {len(eligible_conversations)} conversations into {len(combined_summary)} characters"
        )

        return {
            "summary": combined_summary,
            "key_insights": list(set(all_insights)),  # Remove duplicates
            "metadata": metadata,
        }

    async def extract_key_insights(self, memory_content: str) -> List[str]:
        """
        Extract key insights from memory content.

        Args:
            memory_content: Content to analyze

        Returns:
            List of extracted key insights
        """
        if len(memory_content) < 50:  # Too short for meaningful insights
            return []

        insight_prompt = f"""
        Analyze the following fitness coaching interaction and extract the most important insights about the user.
        Focus on goals, preferences, limitations, achievements, and key learning points.
        
        Return insights as a JSON list of strings, maximum 5 insights.
        
        Content:
        {memory_content}
        
        Format your response as:
        {{"insights": ["insight1", "insight2", ...]}}
        """

        try:
            messages = [
                SystemMessage(
                    content="You are an expert fitness coach analyzing user interactions to extract key insights."
                ),
                HumanMessage(content=insight_prompt),
            ]

            response = await self.llm.ainvoke(messages)
            result = json.loads(response.content)

            insights = result.get("insights", [])
            logger.debug(f"Extracted {len(insights)} insights from content")
            return insights

        except Exception as e:
            logger.error(f"Failed to extract insights: {e}")
            return []

    async def compress_memory_history(
        self,
        user_id: str,
        time_threshold: int = 30,
        config: SummarizationConfig = SummarizationConfig(),
    ) -> Dict[str, Any]:
        """
        Compress old memory history for a user.

        Args:
            user_id: User identifier
            time_threshold: Age threshold in days for compression
            config: Summarization configuration

        Returns:
            Compression results and statistics
        """
        # Get old memories that need compression
        cutoff_date = datetime.now() - timedelta(days=time_threshold)

        # Search for old conversation memories
        old_memories = await self._get_old_memories(user_id, cutoff_date)

        if not old_memories:
            logger.info(f"No old memories found for user {user_id}")
            return {"compressed_count": 0, "space_saved": 0}

        # Group memories by type for different summarization strategies
        conversation_memories = [
            m
            for m in old_memories
            if m.get("metadata", {}).get("memory_type") == "conversation"
        ]
        other_memories = [
            m
            for m in old_memories
            if m.get("metadata", {}).get("memory_type") != "conversation"
        ]

        compression_results = {
            "compressed_count": 0,
            "space_saved": 0,
            "summaries_created": 0,
        }

        # Summarize conversations
        if conversation_memories:
            conversation_summary = await self.summarize_conversation_batch(
                conversation_memories, config
            )

            if conversation_summary["summary"]:
                # Store summary as new memory
                summary_metadata = SummaryMetadata(
                    original_content_length=sum(
                        len(m.get("content", "")) for m in conversation_memories
                    ),
                    summary_content_length=len(conversation_summary["summary"]),
                    summarization_method="llm_batch",
                    summarization_timestamp=datetime.now(),
                    key_points=conversation_summary["key_insights"],
                )

                # Create new summarized memory
                await self.mem0_adapter.add_memory(
                    user_id=user_id,
                    content=conversation_summary["summary"],
                    memory_type=MemoryType.CONVERSATION,
                    metadata={
                        "memory_type": "conversation",
                        "summary_level": SummaryLevel.SUMMARIZED.value,
                        "summary_metadata": summary_metadata.to_dict(),
                        "original_memory_ids": [
                            m.get("id") for m in conversation_memories
                        ],
                        "summarization_timestamp": datetime.now().isoformat(),
                    },
                )

                # Delete original memories
                for memory in conversation_memories:
                    if memory.get("id"):
                        await self.mem0_adapter.delete_memory(memory["id"], user_id)

                compression_results["compressed_count"] += len(conversation_memories)
                compression_results["space_saved"] += (
                    summary_metadata.original_content_length
                    - summary_metadata.summary_content_length
                )
                compression_results["summaries_created"] += 1

        # Handle other memory types with different strategies
        for memory in other_memories:
            memory_type = memory.get("metadata", {}).get("memory_type")

            if memory_type in ["goal", "preference", "injury"]:
                # Keep these important memories but mark as archived
                await self._archive_important_memory(memory, user_id)
            else:
                # Compress or delete less important memories
                await self._compress_secondary_memory(memory, user_id, config)

            compression_results["compressed_count"] += 1

        logger.info(
            f"Compressed {compression_results['compressed_count']} memories for user {user_id}"
        )
        return compression_results

    async def assess_summary_quality(
        self, original_content: str, summary: str
    ) -> float:
        """
        Assess the quality of a generated summary.

        Args:
            original_content: Original content
            summary: Generated summary

        Returns:
            Quality score between 0.0 and 1.0
        """
        if not summary or not original_content:
            return 0.0

        # Length-based assessment
        length_ratio = len(summary) / len(original_content)
        length_score = (
            1.0
            if 0.1 <= length_ratio <= 0.5
            else max(0.0, 1.0 - abs(0.3 - length_ratio) * 2)
        )

        # Content preservation assessment (simplified)
        original_words = set(original_content.lower().split())
        summary_words = set(summary.lower().split())

        # Key term preservation
        key_terms = {
            "goal",
            "workout",
            "exercise",
            "training",
            "nutrition",
            "injury",
            "progress",
            "weight",
        }
        original_key_terms = original_words.intersection(key_terms)
        summary_key_terms = summary_words.intersection(key_terms)

        key_term_score = (
            len(summary_key_terms) / len(original_key_terms)
            if original_key_terms
            else 1.0
        )

        # Combine scores
        quality_score = length_score * 0.4 + key_term_score * 0.6

        return min(1.0, quality_score)

    async def _generate_conversation_summary(
        self,
        conversation_text: str,
        config: SummarizationConfig,
    ) -> Dict[str, Any]:
        """Generate summary for conversation text using LLM."""

        # Calculate target summary length
        target_length = int(len(conversation_text) * config.max_summary_ratio)

        summary_prompt = f"""
        Summarize the following fitness coaching conversations, preserving the most important information about:
        - User's fitness goals and preferences
        - Key achievements and progress
        - Important limitations or injuries
        - Significant coaching insights
        - Action items and plans
        
        Target summary length: approximately {target_length} characters
        Preserve these keywords if they appear: {', '.join(config.preserve_keywords)}
        
        Conversations:
        {conversation_text}
        
        Provide your response in this JSON format:
        {{
            "summary": "comprehensive summary here",
            "key_insights": ["insight1", "insight2", ...],
            "preserved_keywords": ["keyword1", "keyword2", ...]
        }}
        """

        try:
            messages = [
                SystemMessage(
                    content="You are an expert fitness coach creating concise but comprehensive summaries of coaching sessions."
                ),
                HumanMessage(content=summary_prompt),
            ]

            response = await self.llm.ainvoke(messages)
            result = json.loads(response.content)

            summary = result.get("summary", "")
            key_insights = result.get("key_insights", [])

            # Assess quality
            quality_score = await self.assess_summary_quality(
                conversation_text, summary
            )

            return {
                "summary": summary,
                "key_insights": key_insights,
                "quality_score": quality_score,
            }

        except Exception as e:
            logger.error(f"Failed to generate summary: {e}")
            return {
                "summary": "",
                "key_insights": [],
                "quality_score": 0.0,
            }

    async def _combine_summaries(
        self,
        summaries: List[Dict[str, Any]],
        config: SummarizationConfig,
    ) -> str:
        """Combine multiple summaries into a single comprehensive summary."""

        if len(summaries) <= 1:
            return summaries[0]["summary"] if summaries else ""

        summaries_text = "\n\n".join(
            [f"Period {s['time_period']}: {s['summary']}" for s in summaries]
        )

        combine_prompt = f"""
        Combine these chronological summaries of fitness coaching sessions into a single comprehensive summary.
        Focus on progression, consistent themes, and overall user journey.
        
        Summaries to combine:
        {summaries_text}
        
        Create a unified summary that captures the user's fitness journey and key developments.
        """

        try:
            messages = [
                SystemMessage(
                    content="You are an expert fitness coach creating unified summaries of extended coaching relationships."
                ),
                HumanMessage(content=combine_prompt),
            ]

            response = await self.llm.ainvoke(messages)
            return response.content

        except Exception as e:
            logger.error(f"Failed to combine summaries: {e}")
            return summaries_text  # Fallback to concatenated summaries

    def _group_conversations_by_time(
        self, conversations: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Group conversations by time periods for better summarization context."""

        time_groups = {}

        for conv in conversations:
            timestamp_str = conv.get("metadata", {}).get("timestamp")
            if not timestamp_str:
                continue

            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
                # Group by week
                week_start = timestamp - timedelta(days=timestamp.weekday())
                period_key = week_start.strftime("%Y-W%U")

                if period_key not in time_groups:
                    time_groups[period_key] = []
                time_groups[period_key].append(conv)

            except (ValueError, AttributeError):
                continue

        return time_groups

    def _format_conversations_for_prompt(
        self, conversations: List[Dict[str, Any]]
    ) -> str:
        """Format conversations for LLM prompt."""

        formatted_conversations = []

        for i, conv in enumerate(conversations, 1):
            content = conv.get("content", "")
            timestamp = conv.get("metadata", {}).get("timestamp", "Unknown time")

            formatted_conversations.append(
                f"Conversation {i} ({timestamp}):\n{content}\n"
            )

        return "\n".join(formatted_conversations)

    async def _get_old_memories(
        self, user_id: str, cutoff_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get memories older than cutoff date."""

        # Get all user memories (this is a simplified approach)
        all_memories = await self.mem0_adapter.search_memories(
            user_id=user_id,
            query="",  # Empty query to get all
            limit=1000,
        )

        old_memories = []
        for memory in all_memories:
            timestamp_str = memory.get("metadata", {}).get("timestamp")
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(
                        timestamp_str.replace("Z", "+00:00")
                    )
                    if timestamp.replace(tzinfo=None) < cutoff_date:
                        old_memories.append(memory)
                except (ValueError, AttributeError):
                    continue

        return old_memories

    async def _archive_important_memory(
        self, memory: Dict[str, Any], user_id: str
    ) -> None:
        """Archive an important memory instead of compressing it."""

        # Update memory metadata to mark as archived
        memory_id = memory.get("id")
        if memory_id:
            # This would require implementing an update method in mem0_adapter
            logger.info(
                f"Would archive important memory {memory_id} for user {user_id}"
            )

    async def _compress_secondary_memory(
        self,
        memory: Dict[str, Any],
        user_id: str,
        config: SummarizationConfig,
    ) -> None:
        """Compress or delete secondary memories."""

        content = memory.get("content", "")
        if len(content) > config.min_content_length:
            # Create compressed version
            insights = await self.extract_key_insights(content)
            compressed_content = (
                " | ".join(insights) if insights else content[:100] + "..."
            )

            # Update or replace memory
            logger.info(f"Would compress memory {memory.get('id')} for user {user_id}")
        else:
            # Delete very short or redundant memories
            memory_id = memory.get("id")
            if memory_id:
                await self.mem0_adapter.delete_memory(memory_id, user_id)

    def get_summarization_stats(self) -> Dict[str, Any]:
        """Get summarization engine statistics."""

        avg_compression = self.summarization_stats["total_compression_ratio"] / max(
            1, self.summarization_stats["total_summarized"]
        )

        return {
            "total_summarized": self.summarization_stats["total_summarized"],
            "average_compression_ratio": avg_compression,
            "average_quality_score": sum(self.summarization_stats["quality_scores"])
            / max(1, len(self.summarization_stats["quality_scores"])),
        }
