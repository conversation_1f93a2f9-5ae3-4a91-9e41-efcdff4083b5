#!/usr/bin/env python3

import asyncio
import json
import time
import aiohttp


async def test_routing():
    print("🧪 COMPREHENSIVE ROUTING TEST")
    print("=" * 60)
    
    test_cases = [
        ("I need help understanding sleep and recovery relationships", "recovery"),
        ("How do I increase my bench press max?", "strength"), 
        ("I want to train for a marathon", "running"),
        ("How can I improve my cycling training?", "cycling"),
        ("I need a meal plan for muscle building", "nutrition"),
        ("I get nervous before competitions", "mental"),
        ("I want to start exercising but I am a beginner", "general"),
    ]
    
    url = "http://localhost:8000/api/coaching"
    results = []
    
    for i, (question, expected) in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {question}")
        print(f"   Expected: {expected}")
        print("-" * 40)
        
        params = {
            "message": question,
            "threadId": f"test-{i}-{int(time.time())}",
            "userId": "routing_test",
        }
        
        agents_seen = []
        success = False
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        print(f"❌ Error: HTTP {response.status}")
                        continue
                    
                    event_type = None
                    async for line in response.content:
                        line_text = line.decode("utf-8").strip()
                        if not line_text:
                            continue
                        
                        if line_text.startswith("event: "):
                            event_type = line_text.split("event: ")[1].strip()
                            continue
                        
                        if line_text.startswith("data: "):
                            try:
                                data = json.loads(line_text[6:])
                                
                                if event_type == "agent_start":
                                    agent = data.get("agent", "")
                                    if agent:
                                        agents_seen.append(agent)
                                        print(f"  🚀 Agent: {agent}")
                                        
                                        if expected.lower() in agent.lower():
                                            success = True
                                
                                elif event_type == "complete":
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            print(f"❌ Error: {e}")
            continue
        
        if success:
            print(f"  ✅ SUCCESS: {expected} coach activated")
        else:
            print(f"  ❌ FAILED: {expected} coach NOT found")
            print(f"     Agents seen: {agents_seen}")
        
        results.append(success)
        await asyncio.sleep(1)
    
    # Summary
    total = len(results)
    passed = sum(results) if results else 0
    
    if total == 0:
        print("\n" + "="*60)
        print("❌ All tests failed to connect. Is the server running?")
        print("="*60)
        return

    print(f"\n{'='*60}")
    print(f"🏆 RESULTS: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All routing tests PASSED!")
    else:
        print("⚠️  Some routing tests FAILED")
    print("="*60)


async def main():
    """Main function to run tests."""
    print("⏳ Waiting 5 seconds for server to initialize...")
    await asyncio.sleep(5)
    await test_routing()


if __name__ == "__main__":
    asyncio.run(main()) 