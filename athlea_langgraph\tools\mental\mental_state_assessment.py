"""
Mental State Assessment Tool

Comprehensive mental health assessment tool that evaluates current mental state
across multiple domains including mood, stress, anxiety, focus, and energy levels.
Provides personalized insights and recommendations based on evidence-based practices.
"""

import logging
import statistics
from datetime import datetime
from typing import Dict, List

from ..base_tool import BaseDomainTool
from ..schemas.mental_schemas import (
    MentalHealthDomain,
    MentalStateAssessmentInput,
    MentalStateAssessmentOutput,
    StressLevel,
)

logger = logging.getLogger(__name__)


class MentalStateAssessmentTool(BaseDomainTool):
    """
    Comprehensive mental state assessment tool using evidence-based evaluation methods.

    Based on validated mental health assessment frameworks including:
    - Perceived Stress Scale (PSS) principles
    - Depression Anxiety Stress Scales (DASS) methodology
    - WHO-5 Well-Being Index concepts
    - GAD-7 anxiety assessment principles
    """

    domain: str = "mental_training"
    name: str = "mental_state_assessment"
    description: str = (
        "Conduct comprehensive mental state assessment evaluating mood, stress, "
        "anxiety, focus, energy, and overall mental well-being with personalized insights"
    )

    def __init__(self):
        super().__init__()

        # Assessment scoring thresholds (based on research)
        self.score_thresholds = {
            "excellent": (80, 100),
            "good": (60, 79),
            "moderate": (40, 59),
            "concerning": (20, 39),
            "critical": (1, 19),
        }

        # Risk factor indicators
        self.high_risk_indicators = [
            "persistent sadness",
            "thoughts of self-harm",
            "substance abuse",
            "severe sleep disruption",
            "panic attacks",
            "social withdrawal",
            "inability to function",
        ]

        # Evidence-based recommendations database
        self.recommendation_library = {
            "stress_management": [
                "Practice deep breathing exercises (4-7-8 technique)",
                "Implement progressive muscle relaxation",
                "Try mindfulness meditation for 10-15 minutes daily",
                "Establish regular sleep schedule",
                "Engage in regular physical activity",
                "Consider time management strategies",
            ],
            "mood_enhancement": [
                "Engage in pleasant activities daily",
                "Practice gratitude journaling",
                "Maintain social connections",
                "Expose yourself to natural light",
                "Consider behavioral activation techniques",
                "Focus on small, achievable goals",
            ],
            "anxiety_reduction": [
                "Practice grounding techniques (5-4-3-2-1 method)",
                "Use controlled breathing exercises",
                "Challenge negative thought patterns",
                "Gradually face feared situations",
                "Limit caffeine intake",
                "Establish calming routines",
            ],
            "focus_improvement": [
                "Eliminate distractions in work environment",
                "Use the Pomodoro Technique",
                "Practice single-tasking",
                "Take regular breaks",
                "Ensure adequate sleep",
                "Consider mindfulness training",
            ],
            "energy_boost": [
                "Maintain consistent sleep schedule",
                "Eat balanced, regular meals",
                "Stay hydrated throughout the day",
                "Engage in light exercise",
                "Take short breaks during work",
                "Consider vitamin D supplementation",
            ],
        }

    async def assess_mental_state(
        self, assessment_input: MentalStateAssessmentInput
    ) -> MentalStateAssessmentOutput:
        """Conduct comprehensive mental state assessment."""
        try:
            # Calculate overall mental state score
            overall_score = self._calculate_overall_score(assessment_input)

            # Identify primary concerns and strengths
            concerns = self._identify_concerns(assessment_input)
            strengths = self._identify_strengths(assessment_input)

            # Analyze risk and protective factors
            risk_factors = self._analyze_risk_factors(assessment_input)
            protective_factors = self._analyze_protective_factors(assessment_input)

            # Generate personalized recommendations
            recommendations = self._generate_recommendations(assessment_input, concerns)
            immediate_actions = self._generate_immediate_actions(
                assessment_input, concerns
            )

            # Determine follow-up timeline
            follow_up_timeline = self._determine_follow_up_timeline(
                overall_score, concerns
            )

            # Assess need for professional referral
            referral_needed = self._assess_referral_need(
                assessment_input, overall_score, concerns
            )

            return MentalStateAssessmentOutput(
                overall_mental_state_score=overall_score,
                primary_concerns=concerns,
                strengths=strengths,
                risk_factors=risk_factors,
                protective_factors=protective_factors,
                recommendations=recommendations,
                immediate_actions=immediate_actions,
                follow_up_timeline=follow_up_timeline,
                professional_referral_needed=referral_needed,
            )

        except Exception as e:
            logger.error(f"Error in mental state assessment: {e}")
            raise

    def _calculate_overall_score(self, assessment: MentalStateAssessmentInput) -> int:
        """Calculate overall mental state score (1-100)."""
        # Core mental state indicators (weighted)
        core_scores = [
            assessment.mood_rating * 0.2,  # 20% weight
            (11 - assessment.stress_level) * 0.2,  # 20% weight (inverted)
            (11 - assessment.anxiety_level) * 0.15,  # 15% weight (inverted)
            assessment.energy_level * 0.15,  # 15% weight
            assessment.focus_level * 0.15,  # 15% weight
            assessment.motivation_level * 0.1,  # 10% weight
            assessment.confidence_level * 0.05,  # 5% weight
        ]

        # Sleep quality factor (important for mental health)
        sleep_factor = (
            assessment.sleep_quality * 0.7
            + min(assessment.sleep_hours / 8, 1) * 10 * 0.3
        )

        # Calculate base score
        base_score = sum(core_scores) + sleep_factor * 0.1

        # Apply contextual adjustments
        if len(assessment.current_stressors) > 3:
            base_score *= 0.9  # Reduce for high stressor load

        if len(assessment.positive_factors) > 2:
            base_score *= 1.1  # Boost for positive factors

        return max(1, min(100, int(base_score * 10)))

    def _identify_concerns(self, assessment: MentalStateAssessmentInput) -> List[str]:
        """Identify primary mental health concerns."""
        concerns = []

        # Mood concerns
        if assessment.mood_rating <= 4:
            concerns.append("Low mood and potential depressive symptoms")

        # Stress concerns
        if assessment.stress_level >= 7:
            concerns.append("High stress levels requiring intervention")
        elif assessment.stress_level >= 5:
            concerns.append("Moderate stress affecting daily functioning")

        # Anxiety concerns
        if assessment.anxiety_level >= 7:
            concerns.append("Significant anxiety requiring attention")
        elif assessment.anxiety_level >= 5:
            concerns.append("Elevated anxiety levels")

        # Energy concerns
        if assessment.energy_level <= 4:
            concerns.append("Low energy and potential fatigue")

        # Focus concerns
        if assessment.focus_level <= 4:
            concerns.append("Concentration and focus difficulties")

        # Motivation concerns
        if assessment.motivation_level <= 4:
            concerns.append("Low motivation and goal engagement")

        # Confidence concerns
        if assessment.confidence_level <= 4:
            concerns.append("Low self-confidence and self-efficacy")

        # Sleep concerns
        if assessment.sleep_quality <= 4 or assessment.sleep_hours < 6:
            concerns.append("Poor sleep quality affecting mental health")

        # Check for specific high-risk concerns
        if assessment.specific_concerns:
            for concern in assessment.specific_concerns:
                if any(risk in concern.lower() for risk in self.high_risk_indicators):
                    concerns.append(f"High-priority concern: {concern}")

        return concerns[:6]  # Limit to most important concerns

    def _identify_strengths(self, assessment: MentalStateAssessmentInput) -> List[str]:
        """Identify mental health strengths and protective factors."""
        strengths = []

        # High scoring areas
        if assessment.mood_rating >= 7:
            strengths.append("Positive mood and emotional well-being")

        if assessment.stress_level <= 4:
            strengths.append("Good stress management and coping")

        if assessment.anxiety_level <= 4:
            strengths.append("Low anxiety and emotional regulation")

        if assessment.energy_level >= 7:
            strengths.append("High energy levels and vitality")

        if assessment.focus_level >= 7:
            strengths.append("Strong concentration and focus abilities")

        if assessment.motivation_level >= 7:
            strengths.append("High motivation and goal orientation")

        if assessment.confidence_level >= 7:
            strengths.append("Strong self-confidence and self-efficacy")

        if assessment.sleep_quality >= 7 and assessment.sleep_hours >= 7:
            strengths.append("Good sleep quality supporting mental health")

        # Positive contextual factors
        if len(assessment.positive_factors) >= 3:
            strengths.append("Multiple positive life factors present")

        if len(assessment.recent_activities) >= 3:
            strengths.append("Active engagement in various activities")

        return strengths[:5]  # Limit to most important strengths

    def _analyze_risk_factors(
        self, assessment: MentalStateAssessmentInput
    ) -> List[str]:
        """Analyze current risk factors for mental health."""
        risk_factors = []

        # Multiple low scores indicate systemic issues
        low_scores = sum(
            [
                1
                for score in [
                    assessment.mood_rating,
                    assessment.energy_level,
                    assessment.focus_level,
                    assessment.motivation_level,
                    assessment.confidence_level,
                ]
                if score <= 4
            ]
        )

        if low_scores >= 3:
            risk_factors.append(
                "Multiple areas of mental health functioning below optimal"
            )

        # High stress and anxiety combination
        if assessment.stress_level >= 6 and assessment.anxiety_level >= 6:
            risk_factors.append("Combined high stress and anxiety levels")

        # Sleep deprivation
        if assessment.sleep_hours < 6 or assessment.sleep_quality <= 3:
            risk_factors.append("Sleep deprivation affecting mental health")

        # High stressor load
        if len(assessment.current_stressors) >= 4:
            risk_factors.append("High number of current stressors")

        # Recent significant changes
        if assessment.life_changes and len(assessment.life_changes) >= 2:
            risk_factors.append("Recent significant life changes")

        # Medication changes
        if assessment.medication_changes:
            risk_factors.append("Recent medication changes potentially affecting mood")

        return risk_factors

    def _analyze_protective_factors(
        self, assessment: MentalStateAssessmentInput
    ) -> List[str]:
        """Analyze protective factors supporting mental health."""
        protective_factors = []

        # Strong positive factors
        if len(assessment.positive_factors) >= 2:
            protective_factors.append("Multiple positive life influences present")

        # Good sleep
        if assessment.sleep_quality >= 7 and assessment.sleep_hours >= 7:
            protective_factors.append("Adequate restorative sleep")

        # Active lifestyle
        if assessment.recent_activities and len(assessment.recent_activities) >= 3:
            protective_factors.append("Active engagement in various activities")

        # Low stress and anxiety
        if assessment.stress_level <= 4 and assessment.anxiety_level <= 4:
            protective_factors.append("Low stress and anxiety levels")

        # High confidence and motivation
        if assessment.confidence_level >= 7 and assessment.motivation_level >= 7:
            protective_factors.append("Strong self-efficacy and motivation")

        # Good emotional regulation
        if assessment.mood_rating >= 6 and assessment.anxiety_level <= 5:
            protective_factors.append("Good emotional regulation abilities")

        return protective_factors

    def _generate_recommendations(
        self, assessment: MentalStateAssessmentInput, concerns: List[str]
    ) -> List[str]:
        """Generate personalized recommendations based on assessment."""
        recommendations = []

        # Stress management recommendations
        if assessment.stress_level >= 6:
            recommendations.extend(self.recommendation_library["stress_management"][:2])

        # Mood enhancement recommendations
        if assessment.mood_rating <= 5:
            recommendations.extend(self.recommendation_library["mood_enhancement"][:2])

        # Anxiety reduction recommendations
        if assessment.anxiety_level >= 6:
            recommendations.extend(self.recommendation_library["anxiety_reduction"][:2])

        # Focus improvement recommendations
        if assessment.focus_level <= 5:
            recommendations.extend(self.recommendation_library["focus_improvement"][:2])

        # Energy boost recommendations
        if assessment.energy_level <= 5:
            recommendations.extend(self.recommendation_library["energy_boost"][:2])

        # Sleep hygiene recommendations
        if assessment.sleep_quality <= 5 or assessment.sleep_hours < 7:
            recommendations.append(
                "Establish consistent sleep schedule and bedtime routine"
            )
            recommendations.append(
                "Create sleep-conducive environment (dark, cool, quiet)"
            )

        return recommendations[:8]  # Limit to manageable number

    def _generate_immediate_actions(
        self, assessment: MentalStateAssessmentInput, concerns: List[str]
    ) -> List[str]:
        """Generate immediate actions to take today."""
        immediate_actions = []

        # High priority immediate actions
        if assessment.stress_level >= 8:
            immediate_actions.append("Practice 5-minute deep breathing exercise now")

        if assessment.anxiety_level >= 8:
            immediate_actions.append(
                "Use grounding technique: name 5 things you can see, 4 you can touch, 3 you can hear"
            )

        if assessment.mood_rating <= 3:
            immediate_actions.append("Reach out to a trusted friend or family member")

        if assessment.energy_level <= 3:
            immediate_actions.append(
                "Take a 10-minute walk outside or do light stretching"
            )

        if assessment.sleep_hours < 5:
            immediate_actions.append(
                "Plan for early bedtime tonight (at least 8 hours before wake time)"
            )

        # General immediate actions
        immediate_actions.append("Drink a glass of water and have a healthy snack")
        immediate_actions.append("Write down one thing you're grateful for today")

        return immediate_actions[:5]  # Limit to achievable number

    def _determine_follow_up_timeline(
        self, overall_score: int, concerns: List[str]
    ) -> str:
        """Determine appropriate follow-up assessment timeline."""
        # Check for high-risk indicators in concerns
        high_risk_present = any(
            "high-priority" in concern.lower() or "critical" in concern.lower()
            for concern in concerns
        )

        if high_risk_present or overall_score <= 30:
            return "Within 24-48 hours - urgent follow-up needed"
        elif overall_score <= 50 or len(concerns) >= 4:
            return "Within 3-5 days"
        elif overall_score <= 70 or len(concerns) >= 2:
            return "Within 1 week"
        else:
            return "Within 2 weeks"

    def _assess_referral_need(
        self,
        assessment: MentalStateAssessmentInput,
        overall_score: int,
        concerns: List[str],
    ) -> bool:
        """Assess whether professional mental health referral is needed."""
        # Critical score threshold
        if overall_score <= 30:
            return True

        # Multiple severe symptoms
        severe_symptoms = sum(
            [
                1
                for score in [assessment.mood_rating, assessment.energy_level]
                if score <= 3
            ]
        ) + sum(
            [
                1
                for score in [assessment.stress_level, assessment.anxiety_level]
                if score >= 8
            ]
        )

        if severe_symptoms >= 2:
            return True

        # High-priority concerns present
        if any("high-priority" in concern.lower() for concern in concerns):
            return True

        # Persistent multiple concerns
        if len(concerns) >= 5:
            return True

        return False

    def invoke(self, input_data: str) -> str:
        """
        LangChain tool interface method for mental state assessment.

        Args:
            input_data: JSON string containing assessment parameters

        Returns:
            JSON string with assessment results
        """
        try:
            import json
            import asyncio

            # Parse input data
            if isinstance(input_data, str):
                data = json.loads(input_data)
            else:
                data = input_data

            # Create assessment input
            assessment_input = MentalStateAssessmentInput(**data)

            # Run assessment
            async def run_assessment():
                return await self.assess_mental_state(assessment_input)

            # Get event loop or create new one
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Create a new task if loop is already running
                    import concurrent.futures

                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, run_assessment())
                        result = future.result()
                else:
                    result = loop.run_until_complete(run_assessment())
            except RuntimeError:
                # No event loop, create one
                result = asyncio.run(run_assessment())

            # Return JSON string
            return json.dumps(result.dict(), default=str)

        except Exception as e:
            logger.error(f"Mental state assessment tool error: {e}")
            return json.dumps(
                {
                    "error": f"Assessment failed: {str(e)}",
                    "overall_mental_state_score": 50,
                    "primary_concerns": ["Unable to complete assessment"],
                    "recommendations": ["Please try again later"],
                    "professional_referral_needed": False,
                }
            )
