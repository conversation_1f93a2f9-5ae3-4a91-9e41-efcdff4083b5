#!/usr/bin/env python3
"""
Main CLI entry point for Athlea LangGraph prompt management.

Usage:
    python -m athlea_langgraph.cli [COMMAND] [OPTIONS]
"""

import sys
from pathlib import Path
from typing import Optional

import click

# Add the project root to the path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from athlea_langgraph.utils import PromptLoader

from .commands import ab_testing, development, prompt_management, version_management
from .commands.langsmith_integration import langsmith
from .utils.formatters import format_prompt_details, format_prompt_list


# Global CLI context
class CLIContext:
    """Global context for CLI commands."""

    def __init__(self):
        self.loader: Optional[PromptLoader] = None
        self.prompts_dir: str = "athlea_langgraph/prompts"
        self.verbose: bool = False

    def get_loader(self) -> PromptLoader:
        """Get or create PromptLoader instance."""
        if self.loader is None:
            self.loader = PromptLoader(self.prompts_dir)
            self.loader.initialize()
        return self.loader


pass_context = click.make_pass_decorator(CLIContext, ensure=True)


@click.group()
@click.option(
    "--prompts-dir",
    default="athlea_langgraph/prompts",
    help="Directory containing prompt files",
    type=click.Path(exists=True, file_okay=False, dir_okay=True),
)
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@pass_context
def cli(ctx: CLIContext, prompts_dir: str, verbose: bool):
    """Athlea LangGraph Prompt Management CLI

    Manage externalized prompts, versions, and A/B testing configurations.
    """
    ctx.prompts_dir = prompts_dir
    ctx.verbose = verbose

    if verbose:
        click.echo(f"Using prompts directory: {prompts_dir}")


# Add command groups
cli.add_command(prompt_management.prompt)
cli.add_command(version_management.version)
cli.add_command(development.dev)
cli.add_command(ab_testing.ab)
cli.add_command(langsmith)


# Quick access commands (shortcuts)
@cli.command()
@click.argument("name", required=False)
@pass_context
def list(ctx: CLIContext, name: Optional[str]):
    """List all available prompts or filter by name pattern."""
    loader = ctx.get_loader()
    prompts = loader.list_prompts()

    if name:
        prompts = [p for p in prompts if name.lower() in p.lower()]

    if not prompts:
        click.echo("No prompts found.")
        return

    click.echo(format_prompt_list(prompts, ctx.verbose))


@cli.command()
@click.argument("name")
@pass_context
def show(ctx: CLIContext, name: str):
    """Show details for a specific prompt."""
    try:
        loader = ctx.get_loader()
        config = loader.load_prompt(name)
        click.echo(format_prompt_details(config, ctx.verbose))
    except Exception as e:
        click.echo(f"Error loading prompt '{name}': {e}", err=True)
        sys.exit(1)


@cli.command()
@pass_context
def status(ctx: CLIContext):
    """Show prompt system status and statistics."""
    loader = ctx.get_loader()
    cache_stats = loader.get_cache_stats()
    all_prompts = loader.list_prompts()

    # Group prompts by type
    coaches = [p for p in all_prompts if "coach" in p]
    reasoning = [
        p
        for p in all_prompts
        if any(word in p for word in ["template", "extraction", "completion", "system"])
    ]
    system = [p for p in all_prompts if p.startswith(("goal", "sample"))]

    click.echo("🤖 Athlea LangGraph Prompt System Status")
    click.echo("=" * 50)
    click.echo(f"📁 Prompts Directory: {ctx.prompts_dir}")
    click.echo(f"📊 Total Prompts: {len(all_prompts)}")
    click.echo(f"🏋️  Coach Prompts: {len(coaches)}")
    click.echo(f"🧠 Reasoning Prompts: {len(reasoning)}")
    click.echo(f"⚙️  System Prompts: {len(system)}")
    click.echo(f"💾 Cached Prompts: {cache_stats['cached_prompts']}")
    click.echo(f"📝 Tracked Files: {cache_stats['tracked_files']}")

    if ctx.verbose:
        click.echo(f"\nCoach Prompts: {', '.join(coaches)}")
        click.echo(f"Reasoning Prompts: {', '.join(reasoning)}")
        click.echo(f"System Prompts: {', '.join(system)}")


if __name__ == "__main__":
    cli()
