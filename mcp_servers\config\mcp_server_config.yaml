servers:
  strength:
    name: "strength-mcp-server"
    description: "Strength training and resistance exercise tools"
    port: 8001
    path: "python-langgraph/mcp_servers/strength_mcp/server.py"
    domains: ["strength", "resistance", "muscle_building", "powerlifting", "bodybuilding"]
    tools:
      - search_exercises
      - generate_strength_program
      - calculate_1rm
      - assess_strength_imbalances
      - plan_periodization
      - track_strength_progress
    health_check: "/health"
    environment:
      - OPENAI_API_KEY
      - ATHLEA_DB_URL
    dependencies:
      - athlea_langgraph.tools.strength
      - openai
      - pydantic

  nutrition:
    name: "nutrition-mcp-server"
    description: "Nutrition planning and dietary analysis tools"
    port: 8002
    path: "python-langgraph/mcp_servers/nutrition_mcp/server.py"
    domains: ["nutrition", "diet", "meal_planning", "supplements", "hydration"]
    tools:
      - calculate_daily_calories
      - generate_meal_plan
      - analyze_nutrition
      - recommend_supplements
      - track_hydration
    health_check: "/health"
    environment:
      - NUTRITION_API_KEY
      - USDA_API_KEY
    dependencies:
      - athlea_langgraph.tools.nutrition
      - requests
      - pydantic

  cardio:
    name: "cardio-mcp-server"
    description: "Cardiovascular training and endurance tools"
    port: 8003
    path: "python-langgraph/mcp_servers/cardio_mcp/server.py"
    domains: ["cardio", "endurance", "running", "cycling", "heart_rate", "vo2max"]
    tools:
      - generate_running_route
      - calculate_pace_zones
      - analyze_heart_rate_data
      - generate_endurance_training_plan
      - calculate_vo2max_estimate
      - plan_interval_workout
    health_check: "/health"
    environment:
      - AZURE_MAPS_KEY
      - STRAVA_API_KEY
    dependencies:
      - athlea_langgraph.tools.cardio
      - azure-maps
      - geopy

  recovery:
    name: "recovery-mcp-server"
    description: "Recovery, sleep, and stress management tools"
    port: 8004
    path: "python-langgraph/mcp_servers/recovery_mcp/server.py"
    domains: ["recovery", "sleep", "stress", "mobility", "meditation"]
    tools:
      - analyze_sleep_data
      - recommend_recovery_protocols
      - assess_stress_levels
      - plan_mobility_session
      - generate_meditation_guide
      - track_hrv_recovery
    health_check: "/health"
    environment:
      - SLEEP_API_KEY
      - HRV_API_KEY
    dependencies:
      - athlea_langgraph.tools.recovery
      - scipy
      - numpy

  mental:
    name: "mental-mcp-server"
    description: "Mental health and performance psychology tools"
    port: 8005
    path: "python-langgraph/mcp_servers/mental_mcp/server.py"
    domains: ["mental_health", "psychology", "motivation", "mindfulness", "cognitive"]
    tools:
      - assess_mental_state
      - generate_motivation_plan
      - recommend_mindfulness_exercises
      - track_mood_patterns
      - design_visualization_session
    health_check: "/health"
    environment:
      - MENTAL_HEALTH_API_KEY
    dependencies:
      - athlea_langgraph.tools.mental
      - textstat
      - nltk

  external:
    name: "external-mcp-server"
    description: "External API integrations and data sources"
    port: 8006
    path: "python-langgraph/mcp_servers/external_mcp/server.py"
    domains: ["external", "apis", "locations", "weather", "search", "data"]
    tools:
      - search_locations
      - get_route_directions
      - search_web
      - query_airtable
      - get_weather_data
      - search_wikipedia
      - find_nearby_facilities
      - calculate_travel_carbon
    health_check: "/health"
    environment:
      - AZURE_MAPS_KEY
      - GOOGLE_SEARCH_API_KEY
      - AIRTABLE_API_KEY
      - WEATHER_API_KEY
      - WIKIPEDIA_API_KEY
    dependencies:
      - athlea_langgraph.tools.external
      - azure-maps
      - google-api-python-client
      - pyairtable
      - requests

# Global configuration
global:
  protocol_version: "2024-11-05"
  timeout_seconds: 30
  max_retries: 3
  log_level: "INFO"
  
  # Load balancing configuration
  load_balancer:
    enabled: true
    algorithm: "round_robin"  # round_robin, least_connections, ip_hash
    health_check_interval: 30
    unhealthy_threshold: 3
    healthy_threshold: 2
  
  # Security configuration
  security:
    require_auth: true
    auth_type: "bearer_token"  # bearer_token, api_key, mutual_tls
    token_validation_url: "${ATHLEA_AUTH_URL}/validate"
    rate_limiting:
      enabled: true
      requests_per_minute: 1000
      burst_size: 100
  
  # Monitoring and observability
  monitoring:
    metrics_enabled: true
    metrics_port: 9090
    tracing_enabled: true
    tracing_endpoint: "${JAEGER_ENDPOINT}"
    logging:
      format: "json"
      level: "INFO"
      output: ["stdout", "file"]
      file_path: "/var/log/mcp-servers.log"
  
  # Development configuration
  development:
    hot_reload: true
    debug_mode: true
    cors_enabled: true
    cors_origins: ["http://localhost:3000", "https://athlea.app"]

# Deployment configuration
deployment:
  docker:
    base_image: "python:3.11-slim"
    registry: "ghcr.io/athlea"
    tag_prefix: "mcp-server"
    
  kubernetes:
    namespace: "athlea-mcp"
    replicas: 2
    resources:
      requests:
        cpu: "100m"
        memory: "256Mi"
      limits:
        cpu: "500m"
        memory: "512Mi"
    
    service:
      type: "ClusterIP"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
    
    ingress:
      enabled: true
      class: "nginx"
      annotations:
        cert-manager.io/cluster-issuer: "letsencrypt-prod"
        nginx.ingress.kubernetes.io/rate-limit: "1000"
      hosts:
        - host: "strength-mcp.athlea.app"
          paths: ["/"]
        - host: "nutrition-mcp.athlea.app"
          paths: ["/"]
        - host: "cardio-mcp.athlea.app"
          paths: ["/"]
        - host: "recovery-mcp.athlea.app"
          paths: ["/"]
        - host: "mental-mcp.athlea.app"
          paths: ["/"]
        - host: "external-mcp.athlea.app"
          paths: ["/"]

# Client configuration for athlea-web integration
client_config:
  typescript_generation:
    enabled: true
    output_path: "athlea-web/lib/mcp-clients"
    generate_types: true
    generate_hooks: true
    
  connection_pools:
    max_connections_per_server: 10
    connection_timeout: 5
    read_timeout: 30
    retry_attempts: 3
    
  caching:
    enabled: true
    cache_type: "redis"  # redis, memory, none
    ttl_seconds: 300
    cache_key_prefix: "mcp_cache:" 