#!/usr/bin/env python3
"""
Comprehensive Test Suite for Phase 2 Advanced Memory System

Tests all Phase 2 components:
- DomainMemoryManager: Multi-domain memory separation
- AdvancedRetrievalPipeline: Hybrid search and ranking
- MemorySummarizationEngine: LLM-based summarization
- MemoryDecayManager: Intelligent lifecycle management
- MemoryAnalyticsEngine: Real-time monitoring
- AdvancedMemoryManager: Unified interface

Requirements:
- mem0 environment configured
- LLM model available for summarization
- Test database access
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List

import pytest

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Phase 2 components
from athlea_langgraph.memory import (  # Core adapters; Phase 2 managers; Schemas and configs
    AdvancedMemoryManager,
    AdvancedRetrievalPipeline,
    AnalyticsConfig,
    CoachingDomain,
    DateRange,
    DecayConfig,
    DomainClassification,
    DomainMemoryManager,
    Mem0<PERSON>dapter,
    MemoryAnalyticsEngine,
    MemoryDecayManager,
    MemoryOperationType,
    MemorySummarizationEngine,
    MemoryType,
    RetrievalContext,
    SearchConfig,
    SummarizationConfig,
)


class TestAdvancedMemoryPhase2:
    """Comprehensive test suite for Phase 2 advanced memory features."""

    @pytest.fixture
    async def mem0_adapter(self):
        """Create a test mem0 adapter."""
        return Mem0Adapter(environment="test")

    @pytest.fixture
    async def domain_manager(self, mem0_adapter):
        """Create a domain memory manager for testing."""
        return DomainMemoryManager(mem0_adapter)

    @pytest.fixture
    async def retrieval_pipeline(self, mem0_adapter):
        """Create an advanced retrieval pipeline for testing."""
        return AdvancedRetrievalPipeline(mem0_adapter)

    @pytest.fixture
    async def summarization_engine(self, mem0_adapter):
        """Create a summarization engine for testing."""
        return MemorySummarizationEngine(mem0_adapter)

    @pytest.fixture
    async def decay_manager(self, mem0_adapter):
        """Create a decay manager for testing."""
        return MemoryDecayManager(mem0_adapter)

    @pytest.fixture
    async def analytics_engine(self, mem0_adapter):
        """Create an analytics engine for testing."""
        return MemoryAnalyticsEngine(mem0_adapter)

    @pytest.fixture
    async def advanced_manager(self):
        """Create an advanced memory manager for testing."""
        return AdvancedMemoryManager(environment="test", enable_analytics=True)

    @pytest.fixture
    def test_user_id(self):
        """Generate a test user ID."""
        return f"test_user_{uuid.uuid4().hex[:8]}"

    async def test_domain_classification(self, domain_manager):
        """Test automatic domain classification."""
        print("\n🧪 Testing Domain Classification")

        test_cases = [
            {
                "content": "I want to start strength training with deadlifts and squats",
                "expected_domain": CoachingDomain.STRENGTH,
            },
            {
                "content": "Need help with my running pace for marathon training",
                "expected_domain": CoachingDomain.RUNNING,
            },
            {
                "content": "What should I eat after workouts for muscle recovery?",
                "expected_domain": CoachingDomain.NUTRITION,
            },
            {
                "content": "I'm feeling stressed and need mental motivation tips",
                "expected_domain": CoachingDomain.MENTAL,
            },
        ]

        for case in test_cases:
            classification = await domain_manager.classify_memory_domain(
                case["content"]
            )

            assert (
                classification.primary_domain == case["expected_domain"]
            ), f"Expected {case['expected_domain'].value}, got {classification.primary_domain.value}"

            assert (
                classification.confidence_scores[case["expected_domain"]] > 0.1
            ), "Confidence score should be significant"

            print(
                f"✅ Classified '{case['content'][:50]}...' as {classification.primary_domain.value}"
            )

    async def test_domain_memory_storage(self, domain_manager, test_user_id):
        """Test domain-aware memory storage."""
        print("\n💾 Testing Domain Memory Storage")

        memories = [
            {
                "content": "User completed 3x8 deadlifts at 225lbs today",
                "domain": CoachingDomain.STRENGTH,
                "memory_type": MemoryType.WORKOUT_LOG,
            },
            {
                "content": "User prefers morning cardio sessions",
                "domain": CoachingDomain.CARDIO,
                "memory_type": MemoryType.PREFERENCE,
            },
        ]

        stored_ids = []
        for memory in memories:
            memory_id = await domain_manager.store_domain_memory(
                user_id=test_user_id,
                content=memory["content"],
                domain=memory["domain"],
                memory_type=memory["memory_type"],
            )

            assert memory_id is not None, "Memory ID should be returned"
            stored_ids.append(memory_id)
            print(f"✅ Stored memory {memory_id} in {memory['domain'].value} domain")

        return stored_ids

    async def test_domain_search(self, domain_manager, test_user_id):
        """Test domain-specific memory search."""
        print("\n🔍 Testing Domain-Specific Search")

        # First store some domain-specific memories
        await self.test_domain_memory_storage(domain_manager, test_user_id)

        # Search within specific domains
        strength_memories = await domain_manager.search_domain_memories(
            user_id=test_user_id,
            query="deadlift workout",
            domains=[CoachingDomain.STRENGTH],
            limit=5,
        )

        cardio_memories = await domain_manager.search_domain_memories(
            user_id=test_user_id,
            query="morning cardio",
            domains=[CoachingDomain.CARDIO],
            limit=5,
        )

        assert len(strength_memories) > 0, "Should find strength memories"
        assert len(cardio_memories) > 0, "Should find cardio memories"

        # Verify domain relevance
        for memory in strength_memories:
            domains = memory.get("matching_domains", [])
            assert (
                CoachingDomain.STRENGTH.value in domains
            ), "Should match strength domain"

        print(
            f"✅ Found {len(strength_memories)} strength and {len(cardio_memories)} cardio memories"
        )

    async def test_advanced_retrieval_ranking(self, retrieval_pipeline, test_user_id):
        """Test advanced retrieval with hybrid ranking."""
        print("\n🎯 Testing Advanced Retrieval Ranking")

        # Create search configuration
        config = SearchConfig(
            semantic_weight=0.6,
            keyword_weight=0.3,
            metadata_weight=0.1,
            recency_boost=0.2,
            importance_boost=0.3,
        )

        # Perform hybrid search
        ranked_memories = await retrieval_pipeline.hybrid_search(
            user_id=test_user_id,
            query="strength training workout",
            search_config=config,
            limit=10,
        )

        print(f"✅ Hybrid search returned {len(ranked_memories)} ranked memories")

        # Verify ranking scores are calculated
        for memory in ranked_memories[:3]:  # Check top 3
            assert hasattr(memory, "final_score"), "Should have final score"
            assert hasattr(memory, "ranking_factors"), "Should have ranking factors"
            assert memory.final_score > 0, "Final score should be positive"

            print(f"  Memory {memory.memory_id}: score={memory.final_score:.3f}")

    async def test_contextual_retrieval(self, retrieval_pipeline, test_user_id):
        """Test context-aware retrieval strategies."""
        print("\n🎪 Testing Contextual Retrieval")

        session_types = ["coaching", "check_in", "planning"]

        for session_type in session_types:
            memories = await retrieval_pipeline.get_contextual_memories(
                user_id=test_user_id,
                session_type=session_type,
                query="fitness goals",
                limit=5,
            )

            print(
                f"✅ {session_type} session: found {len(memories)} contextual memories"
            )

            # Verify memories have contextual ranking
            for memory in memories:
                assert "ranking_factors" in memory, "Should have ranking factors"

    async def test_memory_summarization(self, summarization_engine, test_user_id):
        """Test memory summarization engine."""
        print("\n📝 Testing Memory Summarization")

        # Create mock conversation memories
        conversations = []
        for i in range(5):
            conversations.append(
                {
                    "id": f"conv_{i}",
                    "content": f"This is a detailed conversation {i} about strength training goals, "
                    f"workout preferences, and nutrition planning. The user discussed their "
                    f"progress and asked for advice on improving their deadlift form.",
                    "metadata": {
                        "memory_type": "conversation",
                        "timestamp": (datetime.now() - timedelta(days=i)).isoformat(),
                    },
                }
            )

        # Test conversation summarization
        config = SummarizationConfig(
            batch_size=5,
            max_summary_ratio=0.3,
            quality_threshold=0.7,
        )

        summary_result = await summarization_engine.summarize_conversation_batch(
            conversations, config
        )

        assert summary_result["summary"], "Should generate summary"
        assert len(summary_result["key_insights"]) > 0, "Should extract insights"

        metadata = summary_result["metadata"]
        assert metadata["original_conversations"] == 5, "Should track original count"
        assert metadata["compression_ratio"] < 1.0, "Should achieve compression"

        print(f"✅ Summarized {metadata['original_conversations']} conversations")
        print(f"   Compression ratio: {metadata['compression_ratio']:.2f}")
        print(f"   Key insights: {len(summary_result['key_insights'])}")

    async def test_key_insight_extraction(self, summarization_engine):
        """Test key insight extraction."""
        print("\n💡 Testing Key Insight Extraction")

        content = """
        User has been consistently doing strength training for 3 months.
        Primary goal is to build muscle mass while maintaining current weight.
        Prefers morning workouts between 6-8 AM due to work schedule.
        Has a previous knee injury that limits certain squat movements.
        Recently achieved a new personal record in deadlifting 275 lbs.
        Struggles with protein intake and needs meal planning assistance.
        """

        insights = await summarization_engine.extract_key_insights(content)

        assert len(insights) > 0, "Should extract insights"
        assert any(
            "muscle" in insight.lower() for insight in insights
        ), "Should capture goals"
        assert any(
            "injury" in insight.lower() for insight in insights
        ), "Should capture limitations"

        print(f"✅ Extracted {len(insights)} insights")
        for insight in insights:
            print(f"   - {insight}")

    async def test_importance_scoring(self, decay_manager):
        """Test importance scoring and decay calculation."""
        print("\n⭐ Testing Importance Scoring")

        # Mock memory with different characteristics
        memories = [
            {
                "id": "goal_memory",
                "content": "User's primary goal is to lose 20 pounds",
                "metadata": {
                    "memory_type": "goal",
                    "timestamp": (datetime.now() - timedelta(days=10)).isoformat(),
                },
            },
            {
                "id": "conversation_memory",
                "content": "Casual conversation about weekend plans",
                "metadata": {
                    "memory_type": "conversation",
                    "timestamp": (datetime.now() - timedelta(days=30)).isoformat(),
                },
            },
        ]

        config = DecayConfig(
            default_decay_rate=0.02,
            min_age_for_decay=7,
        )

        for memory in memories:
            importance = await decay_manager.calculate_importance_score(
                memory, datetime.now(), config
            )

            print(f"✅ {memory['id']}: importance = {importance:.3f}")

            # Goals should have higher importance than conversations
            if memory["metadata"]["memory_type"] == "goal":
                assert importance > 0.8, "Goals should have high importance"
            else:
                assert importance < 0.8, "Conversations should have lower importance"

    async def test_memory_archival(self, decay_manager, test_user_id):
        """Test memory archival based on decay."""
        print("\n📦 Testing Memory Archival")

        # Mock old memories that should be archived
        config = DecayConfig(
            archive_threshold=0.3,
            min_age_for_decay=1,  # Short age for testing
        )

        archived_ids = await decay_manager.archive_old_memories(
            user_id=test_user_id,
            age_threshold=30,  # 30 days
            config=config,
        )

        print(f"✅ Archived {len(archived_ids)} old memories")

    async def test_redundancy_cleanup(self, decay_manager, test_user_id):
        """Test redundant memory cleanup."""
        print("\n🧹 Testing Redundancy Cleanup")

        config = DecayConfig(
            deletion_threshold=0.05,
            max_memories_per_user=1000,
        )

        deleted_count = await decay_manager.cleanup_redundant_memories(
            user_id=test_user_id,
            config=config,
        )

        print(f"✅ Cleaned up {deleted_count} redundant memories")

    async def test_analytics_event_tracking(self, analytics_engine, test_user_id):
        """Test analytics event tracking."""
        print("\n📊 Testing Analytics Event Tracking")

        # Track various events
        events = [
            (MemoryOperationType.ADD, 150.0, True, {"memory_type": "goal"}),
            (MemoryOperationType.SEARCH, 250.0, True, {"query": "workout"}),
            (MemoryOperationType.SEARCH, 500.0, False, None, "Search timeout"),
            (MemoryOperationType.SUMMARIZE, 1500.0, True, {"conversations": 5}),
        ]

        tracked_ids = []
        for op_type, duration, success, metadata, *error in events:
            error_msg = error[0] if error else None

            event_id = await analytics_engine.track_event(
                user_id=test_user_id,
                operation_type=op_type,
                duration_ms=duration,
                success=success,
                metadata=metadata,
                error_message=error_msg,
            )

            tracked_ids.append(event_id)
            print(f"✅ Tracked {op_type.value} event: {event_id}")

        assert len(tracked_ids) == 4, "Should track all events"

    async def test_analytics_reporting(self, analytics_engine, test_user_id):
        """Test analytics report generation."""
        print("\n📈 Testing Analytics Reporting")

        # First track some events
        await self.test_analytics_event_tracking(analytics_engine, test_user_id)

        # Generate report
        date_range = DateRange(
            start_date=datetime.now() - timedelta(days=1),
            end_date=datetime.now(),
        )

        report = await analytics_engine.generate_analytics_report(
            date_range=date_range,
            user_id=test_user_id,
        )

        assert (
            report.retrieval_metrics.total_queries >= 0
        ), "Should have retrieval metrics"
        assert (
            report.performance_metrics.error_rate >= 0
        ), "Should have performance metrics"
        assert len(report.recommendations) >= 0, "Should have recommendations"

        print(f"✅ Generated analytics report")
        print(f"   Total queries: {report.retrieval_metrics.total_queries}")
        print(f"   Error rate: {report.performance_metrics.error_rate:.1%}")
        print(f"   Recommendations: {len(report.recommendations)}")

    async def test_real_time_metrics(self, analytics_engine):
        """Test real-time metrics calculation."""
        print("\n⚡ Testing Real-Time Metrics")

        metrics = await analytics_engine.get_real_time_metrics()

        assert "timestamp" in metrics, "Should have timestamp"
        assert "success_rate" in metrics, "Should have success rate"
        assert "average_latency_ms" in metrics, "Should have latency"
        assert "health_status" in metrics, "Should have health status"

        print(f"✅ Real-time metrics calculated")
        print(f"   Success rate: {metrics['success_rate']:.1%}")
        print(f"   Avg latency: {metrics['average_latency_ms']:.1f}ms")

    async def test_user_pattern_analysis(self, analytics_engine, test_user_id):
        """Test user usage pattern analysis."""
        print("\n👤 Testing User Pattern Analysis")

        patterns = await analytics_engine.analyze_user_patterns(
            user_id=test_user_id,
            days_back=7,
        )

        if "error" not in patterns:
            assert "operation_patterns" in patterns, "Should have operation patterns"
            assert "temporal_patterns" in patterns, "Should have temporal patterns"

            print(f"✅ Analyzed user patterns")
            print(f"   Total events: {patterns['analysis_period']['total_events']}")
        else:
            print(f"⚠️  No events found for user (expected for new test user)")

    async def test_system_health_monitoring(self, analytics_engine):
        """Test system health monitoring."""
        print("\n🏥 Testing System Health Monitoring")

        health_status = await analytics_engine.get_system_health()

        assert health_status.status_level is not None, "Should have status level"
        assert health_status.timestamp is not None, "Should have timestamp"

        print(f"✅ System health: {health_status.status_level.value}")
        print(f"   Active alerts: {len(health_status.active_alerts)}")

    async def test_advanced_memory_manager_integration(self, test_user_id):
        """Test the unified advanced memory manager."""
        print("\n🚀 Testing Advanced Memory Manager Integration")

        # Create manager with all features enabled
        manager = AdvancedMemoryManager(
            environment="test",
            enable_analytics=True,
            enable_auto_maintenance=False,  # Disable for testing
        )

        try:
            # Test memory storage with domain classification
            memory_id = await manager.store_memory(
                user_id=test_user_id,
                content="I want to improve my deadlift form and add more weight",
                memory_type=MemoryType.GOAL,
                # Let it auto-classify domain
            )

            assert memory_id is not None, "Should store memory successfully"
            print(f"✅ Stored memory with auto-classification: {memory_id}")

            # Test advanced search
            memories = await manager.search_memories(
                user_id=test_user_id,
                query="deadlift strength training",
                domains=[CoachingDomain.STRENGTH],
                session_type="coaching",
                use_advanced_ranking=True,
            )

            print(f"✅ Advanced search found {len(memories)} memories")

            # Test analytics report
            report = await manager.get_analytics_report(
                days_back=1, user_id=test_user_id
            )

            if "error" not in report:
                print(f"✅ Generated analytics report")
            else:
                print(f"⚠️  Analytics not fully enabled: {report['error']}")

            # Test system health
            health = await manager.get_system_health()

            if "error" not in health:
                print(f"✅ System health: {health['status_level']}")
            else:
                print(f"⚠️  Health monitoring not available: {health['error']}")

            # Test user stats
            stats = await manager.get_user_memory_stats(test_user_id)

            if "error" not in stats:
                total_memories = stats.get("basic_stats", {}).get("total_memories", 0)
                print(f"✅ User stats: {total_memories} total memories")
            else:
                print(f"⚠️  Could not get user stats: {stats['error']}")

        finally:
            # Clean up
            await manager.shutdown()
            print("✅ Manager shutdown completed")

    async def test_query_expansion(self, retrieval_pipeline, test_user_id):
        """Test query expansion functionality."""
        print("\n🔄 Testing Query Expansion")

        original_query = "workout"
        expanded_query = await retrieval_pipeline.expand_query(
            original_query, test_user_id
        )

        assert len(expanded_query) >= len(
            original_query
        ), "Expanded query should be longer or equal"
        assert "workout" in expanded_query.lower(), "Should contain original term"

        print(f"✅ Expanded '{original_query}' to '{expanded_query[:100]}...'")

    async def test_decay_recommendations(self, decay_manager, test_user_id):
        """Test decay recommendation system."""
        print("\n💡 Testing Decay Recommendations")

        recommendations = await decay_manager.get_decay_recommendations(test_user_id)

        assert "total_memories" in recommendations, "Should have total count"
        assert (
            "archival_candidates" in recommendations
        ), "Should have archival candidates"
        assert (
            "deletion_candidates" in recommendations
        ), "Should have deletion candidates"
        assert "storage_analysis" in recommendations, "Should have storage analysis"

        print(f"✅ Generated decay recommendations")
        print(f"   Total memories: {recommendations['total_memories']}")
        print(f"   Archival candidates: {len(recommendations['archival_candidates'])}")
        print(f"   Deletion candidates: {len(recommendations['deletion_candidates'])}")

    # Main test runner
    async def run_comprehensive_tests(self):
        """Run all Phase 2 advanced memory tests."""
        print("🚀 Starting Comprehensive Phase 2 Advanced Memory Tests")
        print("=" * 60)

        # Generate test user ID
        test_user_id = f"test_user_{uuid.uuid4().hex[:8]}"
        print(f"Test User ID: {test_user_id}")

        # Initialize components
        try:
            mem0_adapter = Mem0Adapter(environment="test")
            domain_manager = DomainMemoryManager(mem0_adapter)
            retrieval_pipeline = AdvancedRetrievalPipeline(mem0_adapter)
            summarization_engine = MemorySummarizationEngine(mem0_adapter)
            decay_manager = MemoryDecayManager(mem0_adapter)
            analytics_engine = MemoryAnalyticsEngine(mem0_adapter)

            print("✅ Initialized all Phase 2 components")

        except Exception as e:
            print(f"❌ Failed to initialize components: {e}")
            return False

        # Define all tests
        tests = [
            (
                "Domain Classification",
                lambda: self.test_domain_classification(domain_manager),
            ),
            (
                "Domain Memory Storage",
                lambda: self.test_domain_memory_storage(domain_manager, test_user_id),
            ),
            (
                "Domain-Specific Search",
                lambda: self.test_domain_search(domain_manager, test_user_id),
            ),
            (
                "Advanced Retrieval Ranking",
                lambda: self.test_advanced_retrieval_ranking(
                    retrieval_pipeline, test_user_id
                ),
            ),
            (
                "Contextual Retrieval",
                lambda: self.test_contextual_retrieval(
                    retrieval_pipeline, test_user_id
                ),
            ),
            (
                "Query Expansion",
                lambda: self.test_query_expansion(retrieval_pipeline, test_user_id),
            ),
            (
                "Memory Summarization",
                lambda: self.test_memory_summarization(
                    summarization_engine, test_user_id
                ),
            ),
            (
                "Key Insight Extraction",
                lambda: self.test_key_insight_extraction(summarization_engine),
            ),
            ("Importance Scoring", lambda: self.test_importance_scoring(decay_manager)),
            (
                "Memory Archival",
                lambda: self.test_memory_archival(decay_manager, test_user_id),
            ),
            (
                "Redundancy Cleanup",
                lambda: self.test_redundancy_cleanup(decay_manager, test_user_id),
            ),
            (
                "Decay Recommendations",
                lambda: self.test_decay_recommendations(decay_manager, test_user_id),
            ),
            (
                "Analytics Event Tracking",
                lambda: self.test_analytics_event_tracking(
                    analytics_engine, test_user_id
                ),
            ),
            (
                "Analytics Reporting",
                lambda: self.test_analytics_reporting(analytics_engine, test_user_id),
            ),
            (
                "Real-Time Metrics",
                lambda: self.test_real_time_metrics(analytics_engine),
            ),
            (
                "User Pattern Analysis",
                lambda: self.test_user_pattern_analysis(analytics_engine, test_user_id),
            ),
            (
                "System Health Monitoring",
                lambda: self.test_system_health_monitoring(analytics_engine),
            ),
            (
                "Advanced Manager Integration",
                lambda: self.test_advanced_memory_manager_integration(test_user_id),
            ),
        ]

        # Run tests
        results = []
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                await test_func()
                results.append((test_name, True, None))
                print(f"✅ {test_name} PASSED")

            except Exception as e:
                results.append((test_name, False, str(e)))
                print(f"❌ {test_name} FAILED: {e}")
                logger.error(f"Test {test_name} failed: {e}", exc_info=True)

        # Summary
        print(f"\n{'='*60}")
        print("📊 PHASE 2 ADVANCED MEMORY SYSTEM TEST RESULTS")
        print(f"{'='*60}")

        passed = sum(1 for _, success, _ in results if success)
        total = len(results)

        for test_name, success, error in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            if error:
                print(f"    Error: {error}")

        print(f"\nSummary: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

        if passed == total:
            print("🎉 All Phase 2 advanced memory tests passed!")
            print("\n🚀 Phase 2 Features Successfully Validated:")
            print("  ✅ Multi-domain memory separation")
            print("  ✅ Advanced hybrid search and ranking")
            print("  ✅ LLM-based memory summarization")
            print("  ✅ Intelligent memory decay and cleanup")
            print("  ✅ Real-time analytics and monitoring")
            print("  ✅ Unified advanced memory management")
        else:
            print(
                "⚠️  Some tests failed. The Phase 2 system may need additional configuration."
            )
            print("   Common issues:")
            print("   - mem0 environment not configured")
            print("   - LLM model not available")
            print("   - Database connection issues")

        return passed == total


# Standalone test function for pytest integration
async def test_phase2_advanced_memory():
    """Pytest-compatible test function for Phase 2 advanced memory system."""
    tester = TestAdvancedMemoryPhase2()
    success = await tester.run_comprehensive_tests()
    assert success, "Phase 2 advanced memory system tests failed"


# Main execution for standalone testing
if __name__ == "__main__":

    async def main():
        """Run the comprehensive test suite."""
        print("🎯 Phase 2 Advanced Memory System - Comprehensive Test Suite")
        print(
            "Testing all components: Domain, Retrieval, Summarization, Decay, Analytics"
        )
        print()

        # Check environment
        print("🔧 Environment Check:")
        required_vars = ["OPENAI_API_KEY"]  # Add other required vars

        for var in required_vars:
            if os.getenv(var):
                print(f"  ✅ {var} is set")
            else:
                print(f"  ⚠️  {var} not set (may affect some tests)")

        print()

        # Run tests
        tester = TestAdvancedMemoryPhase2()
        await tester.run_comprehensive_tests()

    asyncio.run(main())
