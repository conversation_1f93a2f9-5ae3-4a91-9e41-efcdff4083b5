"""
Mental Domain MCP Server

Exposes mental health and performance psychology tools via Model Context Protocol (MCP).
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>ult,
    GetPromptResult,
    InitializeR<PERSON>ult,
    Prompt,
    PromptArgument,
)
from mcp.types import TextContent
from mcp.types import TextContent as TextContentType
from mcp.types import Tool

# Import our mental health tools
from athlea_langgraph.tools.mental import (
    GoalTracker,
    MentalStateAssessmentTool,
    MoodPatternAnalyzer,
    StressLevelTracker,
)
from athlea_langgraph.tools.schemas.mental_schemas import (
    GoalCategory,
    GoalProgressInput,
    MentalHealthGoalInput,
    MentalStateAssessmentInput,
    MoodEntryInput,
    MoodPatternAnalysisInput,
    StressTrackerInput,
)

logger = logging.getLogger(__name__)

# Initialize our mental health tools
mental_state_assessor = MentalStateAssessmentTool()
stress_tracker = StressLevelTracker()
mood_analyzer = MoodPatternAnalyzer()
goal_tracker = GoalTracker()

app = Server("mental-mcp-server")

MENTAL_TOOLS = [
    Tool(
        name="assess_mental_state",
        description="Comprehensive mental health assessment evaluating mood, stress, anxiety, energy, focus, motivation, and confidence levels.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "mood_rating": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current mood rating (1-10, 1=very negative, 10=very positive)",
                },
                "stress_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current stress level (1-10)",
                },
                "anxiety_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current anxiety level (1-10)",
                },
                "energy_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current energy level (1-10)",
                },
                "focus_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current focus level (1-10)",
                },
                "motivation_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current motivation level (1-10)",
                },
                "confidence_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current confidence level (1-10)",
                },
                "sleep_hours": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 24,
                    "description": "Hours of sleep last night",
                },
                "sleep_quality": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Sleep quality rating (1-10)",
                },
                "current_stressors": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Current sources of stress",
                },
                "positive_factors": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Current positive factors in life",
                },
                "recent_activities": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Recent activities within last 24 hours",
                },
                "specific_concerns": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific mental health concerns",
                },
            },
            "required": [
                "user_id",
                "mood_rating",
                "stress_level",
                "anxiety_level",
                "energy_level",
                "focus_level",
                "motivation_level",
                "confidence_level",
                "sleep_hours",
                "sleep_quality",
            ],
        },
    ),
    Tool(
        name="track_stress_level",
        description="Track and analyze stress levels with personalized coping strategies and management recommendations.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "stress_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current stress level (1-10)",
                },
                "stress_triggers": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Identified stress triggers",
                },
                "stress_symptoms": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Physical or emotional symptoms of stress",
                },
                "coping_strategies_used": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Coping strategies currently being used",
                },
                "context": {
                    "type": "string",
                    "description": "Context or situation causing stress",
                },
                "duration_hours": {
                    "type": "number",
                    "minimum": 0,
                    "description": "How long stress has been present (hours)",
                },
                "intensity_change": {
                    "type": "string",
                    "enum": ["increasing", "decreasing", "stable", "fluctuating"],
                    "description": "How stress intensity is changing",
                },
            },
            "required": ["user_id", "stress_level", "stress_triggers"],
        },
    ),
    Tool(
        name="log_mood_entry",
        description="Log a mood entry with immediate feedback and insights for mood tracking.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "mood_rating": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current mood rating (1-10)",
                },
                "mood_descriptors": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Words describing current mood",
                },
                "activities_before": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Activities done before mood entry",
                },
                "social_context": {
                    "type": "string",
                    "description": "Social context when mood was experienced",
                },
                "physical_state": {
                    "type": "string",
                    "description": "Physical state description",
                },
                "time_of_day": {
                    "type": "string",
                    "description": "Time when mood was experienced (HH:MM format)",
                },
                "notes": {
                    "type": "string",
                    "description": "Additional notes about mood",
                },
            },
            "required": ["user_id", "mood_rating", "mood_descriptors", "time_of_day"],
        },
    ),
    Tool(
        name="analyze_mood_patterns",
        description="Analyze mood patterns over time to identify trends, triggers, and correlations.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "analysis_period_days": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 365,
                    "description": "Number of days to analyze",
                },
                "include_correlations": {
                    "type": "boolean",
                    "description": "Whether to include activity correlations",
                },
                "focus_areas": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific areas to focus analysis on",
                },
            },
            "required": ["user_id", "analysis_period_days"],
        },
    ),
    Tool(
        name="create_mental_health_goal",
        description="Create a SMART mental health goal with validation and optimization suggestions.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "goal_category": {
                    "type": "string",
                    "enum": [
                        "mental_health",
                        "stress_management",
                        "habit_formation",
                        "performance",
                        "wellbeing",
                        "mindfulness",
                    ],
                    "description": "Category of the mental health goal",
                },
                "specific_outcome": {
                    "type": "string",
                    "description": "Specific, detailed outcome description",
                },
                "measurement_method": {
                    "type": "string",
                    "description": "How progress will be measured",
                },
                "difficulty_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Goal difficulty level (1-10)",
                },
                "target_completion_date": {
                    "type": "string",
                    "format": "date",
                    "description": "Target completion date (YYYY-MM-DD)",
                },
                "motivation_reasons": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Reasons and motivations for the goal",
                },
                "daily_actions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Daily actions to achieve the goal",
                },
                "weekly_milestones": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Weekly milestones to track progress",
                },
                "potential_obstacles": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Potential obstacles and challenges",
                },
                "support_strategies": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Strategies for overcoming obstacles",
                },
                "accountability_methods": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Methods for staying accountable",
                },
            },
            "required": [
                "user_id",
                "goal_category",
                "specific_outcome",
                "difficulty_level",
                "target_completion_date",
                "motivation_reasons",
            ],
        },
    ),
    Tool(
        name="track_goal_progress",
        description="Track progress on a mental health goal with insights and recommendations.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"},
                "goal_id": {"type": "string", "description": "Goal identifier"},
                "progress_percentage": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Current progress percentage",
                },
                "motivation_level": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Current motivation level for the goal",
                },
                "actions_completed": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Actions completed toward the goal",
                },
                "challenges_encountered": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Challenges encountered while pursuing goal",
                },
                "strategies_used": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Strategies used to make progress",
                },
                "mood_during_progress": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10,
                    "description": "Mood while working on goal",
                },
                "insights_learned": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Insights learned during goal pursuit",
                },
                "goal_adjustment_needed": {
                    "type": "boolean",
                    "description": "Whether goal adjustment is needed",
                },
            },
            "required": [
                "user_id",
                "goal_id",
                "progress_percentage",
                "motivation_level",
            ],
        },
    ),
    Tool(
        name="generate_goal_summary",
        description="Generate a comprehensive summary of all user goals and progress.",
        inputSchema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string", "description": "User identifier"}
            },
            "required": ["user_id"],
        },
    ),
]


@app.list_tools()
async def list_tools() -> List[Tool]:
    return MENTAL_TOOLS


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    try:
        logger.info(f"Mental MCP: Calling tool {name} with arguments: {arguments}")

        if name == "assess_mental_state":
            # Create assessment input
            assessment_input = MentalStateAssessmentInput(**arguments)
            result = await mental_state_assessor.assess_mental_state(assessment_input)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text", text=json.dumps(result.model_dump(), indent=2)
                    )
                ]
            )

        elif name == "track_stress_level":
            # Create stress tracker input
            stress_input = StressTrackerInput(**arguments)
            result = await stress_tracker.track_stress_level(stress_input)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text", text=json.dumps(result.model_dump(), indent=2)
                    )
                ]
            )

        elif name == "log_mood_entry":
            # Create mood entry input
            mood_input = MoodEntryInput(**arguments)
            result = await mood_analyzer.log_mood_entry(mood_input)
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "analyze_mood_patterns":
            # Create mood pattern analysis input
            analysis_input = MoodPatternAnalysisInput(**arguments)
            result = await mood_analyzer.analyze_mood_patterns(analysis_input)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text", text=json.dumps(result.model_dump(), indent=2)
                    )
                ]
            )

        elif name == "create_mental_health_goal":
            # Convert string date to datetime if needed
            if "target_completion_date" in arguments:
                if isinstance(arguments["target_completion_date"], str):
                    arguments["target_completion_date"] = datetime.fromisoformat(
                        arguments["target_completion_date"]
                    )

            # Convert goal_category string to enum
            if "goal_category" in arguments:
                category_mapping = {
                    "mental_health": GoalCategory.MENTAL_HEALTH,
                    "stress_management": GoalCategory.STRESS_MANAGEMENT,
                    "habit_formation": GoalCategory.HABIT_FORMATION,
                    "performance": GoalCategory.PERFORMANCE,
                    "wellbeing": GoalCategory.WELLBEING,
                    "mindfulness": GoalCategory.MINDFULNESS,
                }
                arguments["goal_category"] = category_mapping[
                    arguments["goal_category"]
                ]

            goal_input = MentalHealthGoalInput(**arguments)
            result = await goal_tracker.create_goal(goal_input)
            return CallToolResult(
                content=[TextContent(type="text", text=json.dumps(result, indent=2))]
            )

        elif name == "track_goal_progress":
            # Create goal progress input
            progress_input = GoalProgressInput(**arguments)
            result = await goal_tracker.track_goal_progress(progress_input)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text", text=json.dumps(result.model_dump(), indent=2)
                    )
                ]
            )

        elif name == "generate_goal_summary":
            # Generate goal summary
            user_id = arguments["user_id"]
            result = await goal_tracker.generate_goal_summary(user_id)
            return CallToolResult(
                content=[
                    TextContent(
                        type="text", text=json.dumps(result.model_dump(), indent=2)
                    )
                ]
            )

        else:
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return CallToolResult(
            content=[
                TextContent(type="text", text=json.dumps({"error": str(e)}, indent=2))
            ]
        )


@app.list_prompts()
async def list_prompts() -> List[Prompt]:
    return [
        Prompt(
            name="pre_competition_mental_prep",
            description="Guide user through a pre-competition mental preparation routine.",
            arguments=[
                PromptArgument(
                    name="sport_type",
                    description="Type of sport/competition",
                    required=True,
                ),
                PromptArgument(
                    name="time_until_event_hours",
                    description="Hours remaining before the event",
                    required=True,
                ),
            ],
        ),
        Prompt(
            name="daily_mindfulness_reminder",
            description="Provide a short mindfulness exercise prompt for daily practice.",
            arguments=[],  # No arguments needed for a generic daily reminder
        ),
        Prompt(
            name="stress_management_guidance",
            description="Provide personalized stress management guidance based on stress level.",
            arguments=[
                PromptArgument(
                    name="stress_level",
                    description="Current stress level (1-10)",
                    required=True,
                ),
                PromptArgument(
                    name="stress_triggers",
                    description="Current stress triggers",
                    required=False,
                ),
            ],
        ),
        Prompt(
            name="goal_setting_guide",
            description="Guide user through SMART goal setting process.",
            arguments=[
                PromptArgument(
                    name="goal_area",
                    description="Area of focus for the goal",
                    required=True,
                ),
            ],
        ),
    ]


@app.get_prompt()
async def get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
    prompt_text = ""
    description = ""

    if name == "pre_competition_mental_prep":
        sport_type = arguments.get("sport_type", "your sport")
        time_until_event = arguments.get("time_until_event_hours", "upcoming")
        description = "Pre-competition mental preparation prompt"
        prompt_text = f"""
        For your {sport_type} competition in {time_until_event} hours, let's focus your mind.
        1. Visualize your ideal performance. What does it look, feel, and sound like?
        2. What are 1-2 key process goals you will focus on during the competition?
        3. Repeat a positive affirmation or mantra (e.g., 'I am prepared and confident').
        4. Take 5 deep, calming breaths.
        You've got this!
        """

    elif name == "daily_mindfulness_reminder":
        description = "Daily mindfulness exercise prompt"
        prompt_text = """
        Time for a quick moment of mindfulness. 
        Take 1 minute to focus on your breath. Notice the sensation of air entering and leaving your body. 
        If your mind wanders, gently bring your attention back to your breath. 
        This small practice can help center you for the day.
        """

    elif name == "stress_management_guidance":
        stress_level = arguments.get("stress_level", "5")
        stress_triggers = arguments.get("stress_triggers", "general stressors")
        description = "Personalized stress management guidance"

        if int(stress_level) >= 8:
            prompt_text = f"""
            Your stress level is quite high ({stress_level}/10). Let's use immediate relief techniques:
            
            1. STOP technique: Stop what you're doing, Take a breath, Observe your thoughts/feelings, Proceed mindfully
            2. Box breathing: Breathe in for 4 counts, hold for 4, out for 4, hold for 4
            3. Ground yourself: Name 5 things you can see, 4 you can touch, 3 you can hear, 2 you can smell, 1 you can taste
            
            For triggers like "{stress_triggers}", consider:
            - Breaking tasks into smaller, manageable pieces
            - Asking for support from others
            - Reminding yourself that this feeling will pass
            
            If stress remains very high, consider reaching out to a professional for support.
            """
        elif int(stress_level) >= 5:
            prompt_text = f"""
            Your stress level is moderate ({stress_level}/10). Here are some effective strategies:
            
            1. Progressive muscle relaxation: Tense and release each muscle group
            2. Mindful breathing: Focus on slow, deep breaths for 2-3 minutes
            3. Quick walk or light movement to release tension
            
            For managing "{stress_triggers}":
            - Identify what aspects you can and cannot control
            - Focus your energy on what you can influence
            - Use positive self-talk and affirmations
            """
        else:
            prompt_text = f"""
            Your stress level is manageable ({stress_level}/10). Great job! Here are maintenance strategies:
            
            1. Continue regular stress-prevention practices
            2. Stay aware of early warning signs
            3. Maintain healthy boundaries and self-care routines
            
            Keep monitoring for triggers like "{stress_triggers}" and use proactive coping strategies.
            """

    elif name == "goal_setting_guide":
        goal_area = arguments.get("goal_area", "personal development")
        description = "SMART goal setting guidance"
        prompt_text = f"""
        Let's create a SMART goal for {goal_area}:
        
        S - Specific: What exactly do you want to achieve? Be detailed and clear.
        M - Measurable: How will you track progress? What metrics will you use?
        A - Achievable: Is this goal realistic given your current situation and resources?
        R - Relevant: Why is this goal important to you? How does it align with your values?
        T - Time-bound: When do you want to achieve this goal?
        
        Example for {goal_area}:
        Instead of "I want to be less stressed," try:
        "I will practice 10 minutes of meditation daily for the next 30 days to reduce my stress levels, tracking my practice in a journal and rating my stress levels weekly."
        
        Questions to help you:
        1. What specific outcome do you want in {goal_area}?
        2. How will you measure your progress?
        3. What daily actions will move you toward this goal?
        4. What obstacles might you face, and how will you handle them?
        5. Who or what can support you in achieving this goal?
        """

    else:
        raise ValueError(f"Unknown prompt: {name}")

    return GetPromptResult(
        description=description,
        messages=[TextContentType(type="text", text=prompt_text)],
    )


async def main():
    """Run the mental health MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            protocolVersion="2024-11-05",
            serverInfo={"name": "mental-mcp-server", "version": "0.1.0"},
        )


if __name__ == "__main__":
    asyncio.run(main())
