"""
Default values and configurations for the Athlea LangGraph system.
"""

# Timeout defaults
DEFAULT_TIMEOUT_SECONDS: int = 30
DEFAULT_API_TIMEOUT_SECONDS: int = 10
DEFAULT_SYNTHESIS_TIMEOUT_SECONDS: int = 30
DEFAULT_TASK_TIMEOUT_SECONDS: int = 30

# Duration defaults
DEFAULT_SESSION_DURATION_MINUTES: int = 60
DEFAULT_RECOVERY_DURATION_MINUTES: int = 30
DEFAULT_MOBILITY_DURATION_SECONDS: int = 30
DEFAULT_REST_PERIOD_SECONDS: int = 90

# API operation defaults
DEFAULT_MAX_RETRIES: int = 3
DEFAULT_BATCH_SIZE: int = 10
DEFAULT_PAGE_SIZE: int = 50
DEFAULT_MAX_RESULTS: int = 10

# Training defaults
DEFAULT_SETS: int = 3
DEFAULT_REPS: str = "8-10"
DEFAULT_INTENSITY: str = "moderate"
DEFAULT_SKILL_CATEGORY: str = "intermediate"

# Nutrition defaults
DEFAULT_MEALS_PER_DAY: int = 3
DEFAULT_CALORIE_RANGE_MIN: int = 800
DEFAULT_CALORIE_RANGE_MAX: int = 5000

# Cache and memory defaults
DEFAULT_CACHE_TTL_SECONDS: int = 3600
DEFAULT_MEMORY_RETENTION_DAYS: int = 30
DEFAULT_BATCH_PROCESSING_SIZE: int = 100

# Streaming defaults
DEFAULT_CHUNK_DELAY_MS: int = 0
DEFAULT_NODE_TRANSITION_DELAY_MS: int = 500
DEFAULT_BUFFER_SIZE: int = 10
DEFAULT_FLUSH_INTERVAL_MS: int = 100

# Health check defaults
DEFAULT_HEALTH_CHECK_INTERVAL_MINUTES: int = 15
DEFAULT_ERROR_RATE_THRESHOLD: float = 0.05
DEFAULT_LATENCY_THRESHOLD_MS: int = 2000

# Format defaults
DEFAULT_ENCODING: str = "utf-8"
DEFAULT_DATE_FORMAT: str = "%Y-%m-%d"
DEFAULT_DATETIME_FORMAT: str = "%Y-%m-%d %H:%M:%S"

# Domain-specific duration strings
DEFAULT_CURRENT_5K_TIME: str = "30 minutes"
DEFAULT_SLEEP_HOURS: str = "7-9 hours"
DEFAULT_MICRO_HABIT_DURATION: str = "5-10 minutes"
DEFAULT_HOLD_DURATION: str = "30 seconds"
DEFAULT_HOLD_DURATION_RANGE: str = "30-60 seconds"
DEFAULT_REST_PERIOD_RANGE: str = "60-90 seconds"
DEFAULT_EXTENDED_REST_PERIOD: str = "90-120 seconds"
DEFAULT_LONG_REST_PERIOD: str = "120-180 seconds"

# System limits
DEFAULT_EXERCISE_LIMIT: int = 10
DEFAULT_MAX_PARALLEL_WORKERS: int = 4
DEFAULT_MAX_DISTANCE_KM: float = 10.0
DEFAULT_SEARCH_RADIUS_METERS: int = 50000
