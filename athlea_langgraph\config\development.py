"""
Development environment configuration.
"""

from pydantic import ConfigDict
from .base import BaseConfig, Environment


class DevelopmentConfig(BaseConfig):
    """Development-specific configuration."""

    model_config = ConfigDict(
        env_file=".env", case_sensitive=False, extra="ignore", populate_by_name=True
    )

    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True


config = DevelopmentConfig()
