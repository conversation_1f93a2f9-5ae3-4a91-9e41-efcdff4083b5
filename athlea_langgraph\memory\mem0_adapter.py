"""
mem0 Adapter for LangGraph Integration

This module provides a bridge between LangGraph and mem0 memory system,
offering enhanced memory capabilities including:
- User profile management
- Semantic memory search
- Conversation memory storage
- Context retrieval for personalization
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Union

try:
    from mem0 import Memory

    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False
    Memory = None
    print("Warning: mem0 not available. Memory features will be disabled.")

from athlea_langgraph.config.mem0_config import create_mem0_config_dict

logger = logging.getLogger(__name__)


class MemoryType(Enum):
    """Types of memories that can be stored."""

    CONVERSATION = "conversation"
    USER_PROFILE = "user_profile"
    WORKOUT_LOG = "workout_log"
    GOAL = "goal"
    PREFERENCE = "preference"
    INJURY = "injury"
    PROGRESS = "progress"
    INSIGHT = "insight"


@dataclass
class MemoryEntry:
    """Structure for memory entries."""

    content: str
    memory_type: MemoryType
    metadata: Dict[str, Any]
    timestamp: datetime
    user_id: str
    memory_id: Optional[str] = None


class Mem0Adapter:
    """
    Adapter class for mem0 integration with LangGraph.

    Provides high-level interface for memory operations including:
    - User profile management
    - Semantic memory search
    - Conversation logging
    - Context retrieval for AI agents
    """

    def __init__(self, environment: str = "development"):
        """
        Initialize mem0 adapter.

        Args:
            environment: Environment name (development, staging, production)
        """
        self.environment = environment

        if not MEM0_AVAILABLE:
            logger.warning("mem0 not available. Memory operations will be disabled.")
            self.memory = None
            return

        self.config = create_mem0_config_dict(environment)

        try:
            self.memory = Memory.from_config(self.config)
            logger.info(f"Mem0 adapter initialized for {environment} environment")
        except Exception as e:
            logger.error(f"Failed to initialize mem0: {e}")
            raise

    async def add_memory(
        self,
        user_id: str,
        content: str,
        memory_type: MemoryType = MemoryType.CONVERSATION,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Add a memory for a specific user.

        Args:
            user_id: Unique user identifier
            content: Memory content/text
            memory_type: Type of memory being stored
            metadata: Additional metadata for the memory

        Returns:
            Memory ID assigned by mem0
        """
        if not MEM0_AVAILABLE or self.memory is None:
            logger.warning("mem0 not available. Cannot add memory.")
            return "mem0_unavailable"

        if metadata is None:
            metadata = {}

        # Enhance metadata
        memory_metadata = {
            **metadata,
            "memory_type": memory_type.value,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": user_id,
        }

        try:
            # Add memory to mem0
            result = self.memory.add(
                messages=[{"role": "user", "content": content}],
                user_id=user_id,
                metadata=memory_metadata,
            )

            memory_id = (
                result.get("results", [{}])[0].get("id")
                if result.get("results")
                else None
            )

            logger.info(
                f"Added {memory_type.value} memory for user {user_id}: {memory_id}"
            )
            return memory_id

        except Exception as e:
            logger.error(f"Failed to add memory for user {user_id}: {e}")
            raise

    async def search_memories(
        self,
        user_id: str,
        query: str,
        memory_types: Optional[List[MemoryType]] = None,
        limit: int = 10,
    ) -> List[Dict[str, Any]]:
        """
        Search user's memories semantically.

        Args:
            user_id: Unique user identifier
            query: Search query
            memory_types: Specific memory types to search (optional)
            limit: Maximum number of results

        Returns:
            List of relevant memories with scores
        """
        if not MEM0_AVAILABLE or self.memory is None:
            logger.warning("mem0 not available. Cannot search memories.")
            return []

        try:
            # Search in mem0
            results = self.memory.search(query=query, user_id=user_id, limit=limit)

            memories = []
            for result in results.get("results", []):
                memory = {
                    "id": result.get("id"),
                    "content": result.get("memory"),
                    "score": result.get("score", 0.0),
                    "metadata": result.get("metadata", {}),
                    "created_at": result.get("created_at"),
                    "updated_at": result.get("updated_at"),
                }

                # Filter by memory types if specified
                if memory_types:
                    memory_type = memory["metadata"].get("memory_type")
                    if memory_type and memory_type in [mt.value for mt in memory_types]:
                        memories.append(memory)
                else:
                    memories.append(memory)

            logger.info(
                f"Found {len(memories)} memories for user {user_id} query: '{query}'"
            )
            return memories

        except Exception as e:
            logger.error(f"Failed to search memories for user {user_id}: {e}")
            return []

    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """
        Get structured user profile by searching for profile-related memories.

        Args:
            user_id: Unique user identifier

        Returns:
            Structured user profile dictionary
        """
        try:
            # Search for profile-related memories
            profile_memories = await self.search_memories(
                user_id=user_id,
                query="profile preferences goals fitness level",
                memory_types=[
                    MemoryType.USER_PROFILE,
                    MemoryType.GOAL,
                    MemoryType.PREFERENCE,
                ],
                limit=20,
            )

            # Structure the profile
            profile = {
                "user_id": user_id,
                "goals": [],
                "preferences": [],
                "fitness_level": None,
                "injuries": [],
                "last_updated": None,
                "raw_memories": profile_memories,
            }

            # Extract structured data from memories
            for memory in profile_memories:
                content = memory["content"].lower()
                memory_type = memory["metadata"].get("memory_type")

                if "goal" in content or memory_type == MemoryType.GOAL.value:
                    profile["goals"].append(memory["content"])
                elif "prefer" in content or memory_type == MemoryType.PREFERENCE.value:
                    profile["preferences"].append(memory["content"])
                elif (
                    "fitness level" in content
                    or "beginner" in content
                    or "advanced" in content
                ):
                    profile["fitness_level"] = memory["content"]
                elif "injury" in content or memory_type == MemoryType.INJURY.value:
                    profile["injuries"].append(memory["content"])

                # Update last_updated timestamp
                created_at = memory.get("created_at")
                if created_at and (
                    not profile["last_updated"] or created_at > profile["last_updated"]
                ):
                    profile["last_updated"] = created_at

            logger.info(f"Retrieved profile for user {user_id}")
            return profile

        except Exception as e:
            logger.error(f"Failed to get user profile for user {user_id}: {e}")
            return {"user_id": user_id, "error": str(e)}

    async def update_user_profile(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update user profile by adding new profile memories.

        Args:
            user_id: Unique user identifier
            updates: Dictionary of profile updates

        Returns:
            Success status
        """
        try:
            success_count = 0

            for key, value in updates.items():
                if value:  # Only add non-empty values
                    content = f"User {key}: {value}"

                    # Determine memory type based on key
                    if key in ["goal", "goals"]:
                        memory_type = MemoryType.GOAL
                    elif key in ["preference", "preferences"]:
                        memory_type = MemoryType.PREFERENCE
                    elif key in ["injury", "injuries"]:
                        memory_type = MemoryType.INJURY
                    else:
                        memory_type = MemoryType.USER_PROFILE

                    await self.add_memory(
                        user_id=user_id,
                        content=content,
                        memory_type=memory_type,
                        metadata={"profile_field": key},
                    )
                    success_count += 1

            logger.info(f"Updated {success_count} profile fields for user {user_id}")
            return success_count > 0

        except Exception as e:
            logger.error(f"Failed to update user profile for user {user_id}: {e}")
            return False

    async def log_conversation(
        self,
        user_id: str,
        user_message: str,
        assistant_message: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log a conversation exchange.

        Args:
            user_id: Unique user identifier
            user_message: User's message
            assistant_message: Assistant's response
            metadata: Additional conversation metadata

        Returns:
            Memory ID for the conversation
        """
        conversation_content = f"User: {user_message}\nAssistant: {assistant_message}"

        conversation_metadata = {
            **(metadata or {}),
            "user_message": user_message,
            "assistant_message": assistant_message,
            "conversation_id": metadata.get("conversation_id") if metadata else None,
        }

        return await self.add_memory(
            user_id=user_id,
            content=conversation_content,
            memory_type=MemoryType.CONVERSATION,
            metadata=conversation_metadata,
        )

    async def log_workout(self, user_id: str, workout_data: Dict[str, Any]) -> str:
        """
        Log workout session data.

        Args:
            user_id: Unique user identifier
            workout_data: Workout information dictionary

        Returns:
            Memory ID for the workout log
        """
        workout_content = f"Workout completed: {workout_data.get('type', 'Unknown')} - {workout_data.get('description', '')}"

        return await self.add_memory(
            user_id=user_id,
            content=workout_content,
            memory_type=MemoryType.WORKOUT_LOG,
            metadata=workout_data,
        )

    async def track_progress(self, user_id: str, progress_data: Dict[str, Any]) -> str:
        """
        Track user progress/achievements.

        Args:
            user_id: Unique user identifier
            progress_data: Progress information dictionary

        Returns:
            Memory ID for the progress entry
        """
        progress_content = f"Progress update: {progress_data.get('metric', '')} - {progress_data.get('value', '')}"

        return await self.add_memory(
            user_id=user_id,
            content=progress_content,
            memory_type=MemoryType.PROGRESS,
            metadata=progress_data,
        )

    async def get_relevant_context(
        self,
        user_id: str,
        query: str,
        context_types: Optional[List[MemoryType]] = None,
        max_tokens: int = 2000,
    ) -> Dict[str, Any]:
        """
        Get relevant context for a user query to inject into LLM prompts.

        Args:
            user_id: Unique user identifier
            query: Current user query/context
            context_types: Types of memories to include
            max_tokens: Maximum token count for context

        Returns:
            Dictionary with relevant context and user profile
        """
        try:
            # Get user profile
            profile = await self.get_user_profile(user_id)

            # Search for relevant memories
            relevant_memories = await self.search_memories(
                user_id=user_id, query=query, memory_types=context_types, limit=10
            )

            # Format context for injection
            context = {
                "user_profile": {
                    "goals": profile.get("goals", [])[:3],  # Top 3 goals
                    "preferences": profile.get("preferences", [])[
                        :3
                    ],  # Top 3 preferences
                    "fitness_level": profile.get("fitness_level"),
                    "injuries": profile.get("injuries", []),
                },
                "relevant_memories": [],
                "context_summary": "",
            }

            # Add relevant memories (truncate to stay within token limit)
            current_tokens = 0
            for memory in relevant_memories[:5]:  # Top 5 most relevant
                memory_text = memory["content"]
                estimated_tokens = (
                    len(memory_text.split()) * 1.3
                )  # Rough token estimation

                if current_tokens + estimated_tokens < max_tokens:
                    context["relevant_memories"].append(
                        {
                            "content": memory_text,
                            "score": memory["score"],
                            "type": memory["metadata"].get("memory_type"),
                        }
                    )
                    current_tokens += estimated_tokens

            # Create context summary
            if context["relevant_memories"]:
                context["context_summary"] = (
                    f"Retrieved {len(context['relevant_memories'])} relevant memories for context."
                )

            logger.info(
                f"Retrieved context for user {user_id}: {len(context['relevant_memories'])} memories"
            )
            return context

        except Exception as e:
            logger.error(f"Failed to get relevant context for user {user_id}: {e}")
            return {
                "user_profile": {},
                "relevant_memories": [],
                "context_summary": "Error retrieving context",
            }

    async def get_recent_activities(
        self, user_id: str, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get recent user activities (workouts, conversations, etc.).

        Args:
            user_id: Unique user identifier
            limit: Number of recent activities to retrieve

        Returns:
            List of recent activities
        """
        try:
            activities = await self.search_memories(
                user_id=user_id,
                query="recent activity workout conversation",
                memory_types=[
                    MemoryType.WORKOUT_LOG,
                    MemoryType.CONVERSATION,
                    MemoryType.PROGRESS,
                ],
                limit=limit,
            )

            # Sort by timestamp (most recent first)
            activities.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            logger.info(
                f"Retrieved {len(activities)} recent activities for user {user_id}"
            )
            return activities

        except Exception as e:
            logger.error(f"Failed to get recent activities for user {user_id}: {e}")
            return []

    async def delete_memory(self, memory_id: str, user_id: str) -> bool:
        """
        Delete a specific memory.

        Args:
            memory_id: Memory ID to delete
            user_id: User ID for verification

        Returns:
            Success status
        """
        try:
            self.memory.delete(memory_id=memory_id)
            logger.info(f"Deleted memory {memory_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete memory {memory_id} for user {user_id}: {e}")
            return False

    async def get_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a user.

        Args:
            user_id: Unique user identifier

        Returns:
            Dictionary with memory statistics
        """
        try:
            # Get all memories for user
            all_memories = await self.search_memories(
                user_id=user_id, query="", limit=1000  # Empty query to get all
            )

            # Count by type
            type_counts = {}
            for memory in all_memories:
                memory_type = memory["metadata"].get("memory_type", "unknown")
                type_counts[memory_type] = type_counts.get(memory_type, 0) + 1

            stats = {
                "total_memories": len(all_memories),
                "by_type": type_counts,
                "oldest_memory": (
                    min([m.get("created_at") for m in all_memories])
                    if all_memories
                    else None
                ),
                "newest_memory": (
                    max([m.get("created_at") for m in all_memories])
                    if all_memories
                    else None
                ),
            }

            logger.info(
                f"Retrieved memory stats for user {user_id}: {stats['total_memories']} total memories"
            )
            return stats

        except Exception as e:
            logger.error(f"Failed to get memory stats for user {user_id}: {e}")
            return {"error": str(e)}


# Convenience functions
async def create_mem0_adapter(environment: str = "development") -> Mem0Adapter:
    """Create and return a mem0 adapter instance."""
    return Mem0Adapter(environment=environment)


__all__ = ["Mem0Adapter", "MemoryType", "MemoryEntry", "create_mem0_adapter"]
