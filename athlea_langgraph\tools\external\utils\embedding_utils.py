"""
Embedding Utilities

Functions for generating embeddings, primarily using Azure OpenAI.
Matches the JavaScript implementation pattern for consistency.
"""

import logging
import os
import re
from typing import List, Optional

from openai import AsyncAzureOpenAI

logger = logging.getLogger(__name__)


def extract_deployment_id_from_url(url: str) -> Optional[str]:
    """
    Extract deployment ID from Azure OpenAI embedding endpoint URL.

    Matches the JavaScript implementation pattern:
    /deployments/DEPLOYMENT_ID/embeddings

    Args:
        url: The Azure OpenAI embedding endpoint URL

    Returns:
        The extracted deployment ID or None if not found
    """
    if not url:
        return None

    # Match pattern: deployments/DEPLOYMENT_ID/embeddings
    match = re.search(r"/deployments/([^/]+)/embeddings", url)

    if match and match.group(1):
        return match.group(1)

    return None


async def get_azure_openai_embedding(
    text: str,
    azure_openai_api_key: Optional[str] = None,
    azure_openai_endpoint: Optional[str] = None,
    azure_openai_embedding_deployment_id: Optional[str] = None,
    api_version: str = "2023-05-15",
) -> List[float]:
    """
    Generates an embedding for the given text using Azure OpenAI.

    Updated to match JavaScript implementation pattern:
    - Supports EMBEDDING_API_KEY and EMBEDDING_ENDPOINT environment variables
    - Extracts deployment ID from URL if not explicitly provided
    - Falls back to legacy environment variable names for backward compatibility
    - Now async to work properly with asyncio.wait_for

    Args:
        text: The input text to embed.
        azure_openai_api_key: Azure OpenAI API key. Uses fallback order:
            1. Provided parameter
            2. EMBEDDING_API_KEY env var (JS pattern)
            3. AZURE_OPENAI_API_KEY env var (legacy)
        azure_openai_endpoint: Azure OpenAI endpoint. Uses fallback order:
            1. Provided parameter
            2. EMBEDDING_ENDPOINT env var (JS pattern)
            3. AZURE_OPENAI_ENDPOINT env var (legacy)
        azure_openai_embedding_deployment_id: Deployment ID for the embedding model.
            Uses fallback order:
            1. Provided parameter
            2. Extracted from endpoint URL
            3. AZURE_OPENAI_EMBEDDING_DEPLOYMENT_ID env var
            4. Default: "text-embedding-3-large"
        api_version: Azure OpenAI API version.

    Returns:
        A list of floats representing the embedding.

    Raises:
        ValueError: If any required environment variables or parameters are missing.
        Exception: If the OpenAI API call fails.
    """
    # Use JavaScript pattern first, then fall back to legacy names
    key = (
        azure_openai_api_key
        or os.getenv("EMBEDDING_API_KEY")  # JS pattern
        or os.getenv("AZURE_OPENAI_API_KEY")  # Legacy
    )

    endpoint = (
        azure_openai_endpoint
        or os.getenv("EMBEDDING_ENDPOINT")  # JS pattern
        or os.getenv("AZURE_OPENAI_ENDPOINT")  # Legacy
    )

    # Try to extract deployment ID from URL first (JS pattern)
    url_deployment_id = extract_deployment_id_from_url(endpoint) if endpoint else None

    # Priority: parameter > URL extraction > env var > default
    deployment_id = (
        azure_openai_embedding_deployment_id
        or url_deployment_id
        or os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_ID")
        or "text-embedding-3-large"
    )

    logger.info(f"Using embedding endpoint: {endpoint}")
    logger.info(f"Using deployment ID: {deployment_id}")
    if url_deployment_id:
        logger.info(f"Deployment ID extracted from URL: {url_deployment_id}")

    if not all([key, endpoint, deployment_id]):
        missing_vars = []
        if not key:
            missing_vars.append("EMBEDDING_API_KEY or AZURE_OPENAI_API_KEY")
        if not endpoint:
            missing_vars.append("EMBEDDING_ENDPOINT or AZURE_OPENAI_ENDPOINT")
        if not deployment_id:
            missing_vars.append("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_ID")
        error_msg = f"Missing Azure OpenAI configuration: {', '.join(missing_vars)}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    try:
        client = AsyncAzureOpenAI(
            api_key=key,
            azure_endpoint=endpoint,
            api_version=api_version,
        )

        text_to_embed = text.replace("\n", " ")  # OpenAI recommendation
        response = await client.embeddings.create(
            input=[text_to_embed], model=deployment_id
        )
        embedding = response.data[0].embedding
        logger.info(
            f"Successfully generated embedding of dimension {len(embedding)} for text snippet: '{text[:50]}...'"
        )
        return embedding
    except Exception as e:
        logger.error(f"Azure OpenAI embedding generation failed: {e}")
        raise
