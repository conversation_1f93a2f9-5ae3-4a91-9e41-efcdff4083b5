{"metadata": {"name": "completion_check", "version": "1.0.0", "description": "Migrated COMPLETION_CHECK_PROMPT from agents/onboarding/check_completion_node.py", "author": "<PERSON><PERSON>", "created_at": "2025-05-30T13:36:12.552153", "updated_at": "2025-05-30T13:36:12.552153", "prompt_type": "planning", "tags": ["planning"], "changelog": [{"version": "1.0.0", "date": "2025-05-30T13:36:12.552153", "changes": "Initial migration from hardcoded prompt", "author": "<PERSON><PERSON>", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "Analyze the provided conversation history, focusing ONLY on information explicitly stated by the USER.\n\n**Your Task:** Verify if the USER has provided information covering ALL of the following categories:\n1.  **Specific Fitness Goal(s):** At least one clear goal mentioned for their primary sport(s).\n2.  **Experience Level:** Some statement about their experience (overall or per sport).\n3.  **Time Commitment:** Details on days/week, duration, or time of day.\n4.  **Equipment Access:** Mention of available equipment or workout location relevant to their goals.\n5.  **Priorities/Connections/Seasonality:** Statement on how goals relate, primary focus, or relevant time constraints.\n\n**Additional Check:** Examine the VERY LAST user message. Does it clearly indicate readiness to proceed or explicitly ask for the plan (e.g., \"Okay\", \"Yes\", \"Let's do it\", \"Generate the plan\", \"Sounds good\", confirming the last piece of info)?\n\n**Response Rules:**\n- Respond ONLY with the word \"true\" IF AND ONLY IF:\n- There is clear evidence from the USER for **ALL 5 required categories** listed above.\n- AND the **last user message** indicates readiness.\n- Respond ONLY with the word \"false\" otherwise (if any category is missing OR the user isn't ready).\n\nDo not provide any explanation or other text.", "context_template": null, "user_template": null, "examples": [], "instructions": "**\n- Respond ONLY with the word \"true\" IF AND ONLY IF:\n    - There is clear evidence from the USER for **ALL 5 required categories** listed above.\n    - AND the **last user message** indicates readiness.\n- Respond ONLY with the word \"false\" otherwise (if any category is missing OR the user isn't ready).\n\nDo not provide any explanation or other text.", "constraints": []}, "variables": {"temperature": 0.4, "max_tokens": 2000, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": [], "max_length": 10000, "min_length": 50, "required_fields": [], "allowed_variables": []}}