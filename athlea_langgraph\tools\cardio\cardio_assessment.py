"""
Comprehensive Cardio Assessment Tool

This tool provides a complete cardiovascular fitness assessment system including:
- VO2 max estimation and cardiovascular fitness evaluation
- Heart rate analysis (resting, max, recovery)
- Performance testing and benchmarking
- Training history and experience assessment
- Goal-specific recommendations for endurance training
- Equipment and environmental considerations
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator

from ..base_tool import BaseDomainTool

logger = logging.getLogger(__name__)


class CardiovascularMetricsInput(BaseModel):
    """Input schema for cardiovascular metrics assessment."""

    resting_heart_rate: Optional[int] = Field(
        None, ge=30, le=120, description="Resting heart rate in BPM"
    )
    max_heart_rate: Optional[int] = Field(
        None, ge=120, le=220, description="Maximum heart rate in BPM (if known)"
    )
    recovery_heart_rate: Optional[int] = Field(
        None, ge=0, le=100, description="Heart rate drop in first minute after exercise"
    )
    blood_pressure_systolic: Optional[int] = Field(
        None, ge=80, le=200, description="Systolic blood pressure"
    )
    blood_pressure_diastolic: Optional[int] = Field(
        None, ge=40, le=120, description="Diastolic blood pressure"
    )
    vo2_max_known: Optional[float] = Field(
        None, ge=10, le=90, description="Known VO2 max if previously tested"
    )


class PerformanceTestingInput(BaseModel):
    """Input schema for performance testing data."""

    # Running performance
    mile_time_seconds: Optional[int] = Field(
        None, ge=240, le=1800, description="1-mile run time in seconds"
    )
    five_k_time_seconds: Optional[int] = Field(
        None, ge=600, le=3600, description="5K run time in seconds"
    )
    ten_k_time_seconds: Optional[int] = Field(
        None, ge=1200, le=7200, description="10K run time in seconds"
    )
    half_marathon_time_seconds: Optional[int] = Field(
        None, ge=3600, le=14400, description="Half marathon time in seconds"
    )
    marathon_time_seconds: Optional[int] = Field(
        None, ge=7200, le=28800, description="Marathon time in seconds"
    )

    # Cycling performance
    cycling_ftp_watts: Optional[int] = Field(
        None,
        ge=50,
        le=600,
        description="Cycling FTP (Functional Threshold Power) in watts",
    )
    cycling_20k_time_seconds: Optional[int] = Field(
        None, ge=1200, le=7200, description="20km cycling time in seconds"
    )

    # General endurance
    step_test_recovery_hr: Optional[int] = Field(
        None, ge=60, le=180, description="Heart rate 1 min after 3-min step test"
    )
    cooper_test_distance: Optional[int] = Field(
        None, ge=1000, le=4000, description="12-minute Cooper test distance in meters"
    )

    # Submaximal testing
    walk_test_hr: Optional[int] = Field(
        None, ge=80, le=160, description="HR during 1-mile walk test"
    )
    walk_test_time_seconds: Optional[int] = Field(
        None, ge=600, le=1800, description="1-mile walk test time in seconds"
    )


class CardioExperienceInput(BaseModel):
    """Input schema for cardio training experience assessment."""

    years_training: float = Field(
        ..., ge=0, description="Years of regular cardio training"
    )
    weekly_frequency: int = Field(
        ..., ge=0, le=14, description="Cardio sessions per week"
    )
    primary_activities: List[str] = Field(
        ...,
        min_length=1,
        description="Primary cardio activities (running, cycling, swimming, etc.)",
    )
    longest_continuous_activity: int = Field(
        ..., ge=0, description="Longest continuous activity duration in minutes"
    )
    competition_experience: bool = Field(
        default=False, description="Has participated in cardio competitions/events"
    )
    previous_injuries: List[str] = Field(
        default=[], description="Previous cardio-related injuries"
    )
    current_pain_areas: List[str] = Field(
        default=[], description="Current pain or discomfort areas"
    )
    training_consistency: int = Field(
        ...,
        ge=1,
        le=5,
        description="Self-rated training consistency (1=poor, 5=excellent)",
    )


class CardioGoalsInput(BaseModel):
    """Input schema for cardio training goals assessment."""

    primary_goals: List[str] = Field(
        ..., min_length=1, description="Primary cardio goals"
    )
    target_events: List[str] = Field(default=[], description="Target races or events")
    goal_timeline: str = Field(..., description="Timeline for achieving goals")
    weekly_time_availability: int = Field(
        ..., ge=60, description="Available training time per week in minutes"
    )
    intensity_preference: str = Field(
        ..., description="Preferred training intensity (low/moderate/high/mixed)"
    )
    motivation_level: int = Field(
        ..., ge=1, le=5, description="Self-rated motivation level (1=low, 5=high)"
    )


class EquipmentEnvironmentInput(BaseModel):
    """Input schema for equipment and environment assessment."""

    gym_access: bool = Field(..., description="Has access to a gym")
    home_equipment: List[str] = Field(
        default=[], description="Available home cardio equipment"
    )
    outdoor_access: bool = Field(
        ..., description="Has access to safe outdoor training areas"
    )
    preferred_environment: str = Field(
        ..., description="Preferred training environment (indoor/outdoor/mixed)"
    )
    climate_considerations: List[str] = Field(
        default=[], description="Climate factors affecting training"
    )
    elevation_training: Optional[int] = Field(
        None, ge=0, le=5000, description="Training elevation in meters"
    )
    pool_access: bool = Field(default=False, description="Has access to swimming pool")


class CardioAssessmentInput(BaseModel):
    """Complete cardio assessment input schema."""

    user_id: str = Field(..., description="Unique user identifier")
    age: int = Field(..., ge=13, le=100, description="User age")
    gender: str = Field(..., description="User gender")
    weight_kg: float = Field(..., gt=0, description="User weight in kg")
    height_cm: float = Field(..., gt=0, description="User height in cm")
    cardiovascular_metrics: CardiovascularMetricsInput
    performance_testing: PerformanceTestingInput
    experience: CardioExperienceInput
    goals: CardioGoalsInput
    equipment_environment: EquipmentEnvironmentInput
    assessment_date: datetime = Field(default_factory=datetime.now)


class CardiovascularFitnessResults(BaseModel):
    """Cardiovascular fitness assessment results."""

    estimated_vo2_max: float = Field(..., description="Estimated VO2 max (ml/kg/min)")
    vo2_max_category: str = Field(..., description="VO2 max fitness category")
    estimated_max_hr: int = Field(..., description="Estimated maximum heart rate")
    resting_hr_assessment: str = Field(..., description="Resting heart rate assessment")
    cardiovascular_efficiency: float = Field(
        ..., description="CV efficiency score (1-10)"
    )
    heart_rate_reserve: int = Field(
        ..., description="Heart rate reserve (max - resting)"
    )
    recovery_assessment: str = Field(..., description="Recovery capacity assessment")


class PerformanceAnalysisResults(BaseModel):
    """Performance analysis results."""

    current_fitness_level: str = Field(
        ..., description="Overall fitness level classification"
    )
    running_performance_level: str = Field(
        ..., description="Running-specific performance level"
    )
    endurance_capacity: str = Field(..., description="Endurance capacity assessment")
    speed_vs_endurance_profile: str = Field(
        ..., description="Speed vs endurance strength profile"
    )
    performance_benchmarks: Dict[str, Any] = Field(
        ..., description="Performance benchmarks and comparisons"
    )
    improvement_potential: Dict[str, str] = Field(
        ..., description="Areas with highest improvement potential"
    )


class TrainingReadinessResults(BaseModel):
    """Training readiness and experience results."""

    experience_level: str = Field(..., description="Training experience classification")
    training_readiness_score: float = Field(
        ..., description="Overall training readiness (1-10)"
    )
    injury_risk_factors: List[str] = Field(
        ..., description="Identified injury risk factors"
    )
    training_load_capacity: str = Field(
        ..., description="Recommended training load capacity"
    )
    progression_rate: str = Field(..., description="Recommended progression rate")


class CardioRecommendationsOutput(BaseModel):
    """Comprehensive cardio training recommendations."""

    immediate_priorities: List[str] = Field(
        ..., description="Immediate training focus areas"
    )
    training_type_recommendation: str = Field(
        ..., description="Recommended training approach"
    )
    weekly_structure: Dict[str, Any] = Field(
        ..., description="Recommended weekly training structure"
    )
    target_heart_rate_zones: Dict[str, Dict[str, int]] = Field(
        ..., description="Personalized HR zones"
    )
    progression_timeline: Dict[str, str] = Field(
        ..., description="Suggested progression timeline"
    )
    equipment_recommendations: List[str] = Field(
        ..., description="Equipment purchase priorities"
    )
    professional_referrals: List[str] = Field(
        ..., description="Recommended professional consultations"
    )
    safety_considerations: List[str] = Field(
        ..., description="Important safety considerations"
    )


class CardioAssessmentOutput(BaseModel):
    """Complete cardio assessment output schema."""

    user_id: str
    assessment_id: str
    assessment_date: datetime
    cardiovascular_fitness: CardiovascularFitnessResults
    performance_analysis: PerformanceAnalysisResults
    training_readiness: TrainingReadinessResults
    recommendations: CardioRecommendationsOutput
    overall_fitness_score: float = Field(
        ..., description="Overall fitness score (1-100)"
    )
    next_reassessment_date: datetime = Field(
        ..., description="Recommended next assessment date"
    )


class CardioAssessmentTool(BaseDomainTool):
    """
    Comprehensive cardio assessment tool providing detailed evaluation
    of cardiovascular fitness, performance capabilities, training readiness,
    and personalized endurance training recommendations.
    """

    domain: str = "cardiovascular_training"
    name: str = "cardio_assessment"
    description: str = (
        "Comprehensive cardiovascular fitness and endurance training assessment"
    )

    def __init__(self):
        super().__init__()
        self.vo2_max_norms = self._load_vo2_max_norms()
        self.performance_standards = self._load_performance_standards()

    def _load_vo2_max_norms(self) -> Dict[str, Dict[str, Dict[str, float]]]:
        """Load VO2 max norms by age and gender."""
        return {
            "male": {
                "20-29": {
                    "excellent": 51.0,
                    "good": 45.0,
                    "average": 39.0,
                    "fair": 33.0,
                    "poor": 25.0,
                },
                "30-39": {
                    "excellent": 49.0,
                    "good": 43.0,
                    "average": 37.0,
                    "fair": 31.0,
                    "poor": 23.0,
                },
                "40-49": {
                    "excellent": 47.0,
                    "good": 41.0,
                    "average": 35.0,
                    "fair": 29.0,
                    "poor": 21.0,
                },
                "50-59": {
                    "excellent": 45.0,
                    "good": 39.0,
                    "average": 33.0,
                    "fair": 27.0,
                    "poor": 19.0,
                },
                "60+": {
                    "excellent": 43.0,
                    "good": 37.0,
                    "average": 31.0,
                    "fair": 25.0,
                    "poor": 17.0,
                },
            },
            "female": {
                "20-29": {
                    "excellent": 44.0,
                    "good": 38.0,
                    "average": 32.0,
                    "fair": 26.0,
                    "poor": 20.0,
                },
                "30-39": {
                    "excellent": 42.0,
                    "good": 36.0,
                    "average": 30.0,
                    "fair": 24.0,
                    "poor": 18.0,
                },
                "40-49": {
                    "excellent": 40.0,
                    "good": 34.0,
                    "average": 28.0,
                    "fair": 22.0,
                    "poor": 16.0,
                },
                "50-59": {
                    "excellent": 38.0,
                    "good": 32.0,
                    "average": 26.0,
                    "fair": 20.0,
                    "poor": 14.0,
                },
                "60+": {
                    "excellent": 36.0,
                    "good": 30.0,
                    "average": 24.0,
                    "fair": 18.0,
                    "poor": 12.0,
                },
            },
        }

    def _load_performance_standards(self) -> Dict[str, Dict[str, Dict[str, int]]]:
        """Load performance standards for common distances."""
        return {
            "5k": {
                "male": {
                    "elite": 900,
                    "competitive": 1080,
                    "recreational": 1500,
                    "beginner": 1800,
                },
                "female": {
                    "elite": 1020,
                    "competitive": 1200,
                    "recreational": 1680,
                    "beginner": 2100,
                },
            },
            "10k": {
                "male": {
                    "elite": 1860,
                    "competitive": 2220,
                    "recreational": 3000,
                    "beginner": 3600,
                },
                "female": {
                    "elite": 2100,
                    "competitive": 2460,
                    "recreational": 3360,
                    "beginner": 4200,
                },
            },
            "half_marathon": {
                "male": {
                    "elite": 3960,
                    "competitive": 4680,
                    "recreational": 6600,
                    "beginner": 7800,
                },
                "female": {
                    "elite": 4500,
                    "competitive": 5220,
                    "recreational": 7380,
                    "beginner": 9000,
                },
            },
            "marathon": {
                "male": {
                    "elite": 8400,
                    "competitive": 10800,
                    "recreational": 14400,
                    "beginner": 18000,
                },
                "female": {
                    "elite": 9600,
                    "competitive": 12000,
                    "recreational": 16200,
                    "beginner": 21600,
                },
            },
        }

    def _get_age_group(self, age: int) -> str:
        """Get age group for VO2 max norms."""
        if age < 30:
            return "20-29"
        elif age < 40:
            return "30-39"
        elif age < 50:
            return "40-49"
        elif age < 60:
            return "50-59"
        else:
            return "60+"

    def estimate_vo2_max(self, assessment_input: CardioAssessmentInput) -> float:
        """Estimate VO2 max using available data."""
        cv_metrics = assessment_input.cardiovascular_metrics
        performance = assessment_input.performance_testing

        # If VO2 max is known, use it
        if cv_metrics.vo2_max_known:
            return cv_metrics.vo2_max_known

        # Estimate from Cooper test (most accurate)
        if performance.cooper_test_distance:
            vo2_max = (performance.cooper_test_distance - 504.9) / 44.73
            return max(vo2_max, 15.0)  # Minimum reasonable VO2 max

        # Estimate from 1.5 mile run time (if we can calculate from mile time)
        if performance.mile_time_seconds:
            # Extrapolate 1.5 mile time from mile time (rough estimate)
            mile_time_min = performance.mile_time_seconds / 60
            estimated_1_5_mile_time = mile_time_min * 1.6  # Assumes slight slowdown
            vo2_max = 483 / estimated_1_5_mile_time
            return max(vo2_max, 15.0)

        # Estimate from step test
        if performance.step_test_recovery_hr:
            vo2_max = 111.33 - (0.42 * performance.step_test_recovery_hr)
            return max(vo2_max, 15.0)

        # Estimate from resting HR (least accurate but better than nothing)
        if cv_metrics.resting_heart_rate:
            # Simple formula based on resting HR
            rhr = cv_metrics.resting_heart_rate
            age = assessment_input.age
            if assessment_input.gender.lower() == "male":
                vo2_max = 67 - (0.5 * age) - (0.05 * rhr)
            else:
                vo2_max = 65 - (0.5 * age) - (0.05 * rhr)
            return max(vo2_max, 15.0)

        # Fallback: age-based estimate
        age = assessment_input.age
        if assessment_input.gender.lower() == "male":
            return max(55 - (0.5 * age), 20.0)
        else:
            return max(50 - (0.5 * age), 18.0)

    def assess_cardiovascular_fitness(
        self, assessment_input: CardioAssessmentInput
    ) -> CardiovascularFitnessResults:
        """Assess cardiovascular fitness metrics."""
        cv_metrics = assessment_input.cardiovascular_metrics
        age = assessment_input.age
        gender = assessment_input.gender.lower()

        # Estimate VO2 max
        vo2_max = self.estimate_vo2_max(assessment_input)

        # Categorize VO2 max
        age_group = self._get_age_group(age)
        norms = self.vo2_max_norms[gender][age_group]

        if vo2_max >= norms["excellent"]:
            vo2_category = "excellent"
        elif vo2_max >= norms["good"]:
            vo2_category = "good"
        elif vo2_max >= norms["average"]:
            vo2_category = "average"
        elif vo2_max >= norms["fair"]:
            vo2_category = "fair"
        else:
            vo2_category = "poor"

        # Estimate max HR
        if cv_metrics.max_heart_rate:
            max_hr = cv_metrics.max_heart_rate
        else:
            max_hr = int(220 - age)  # Simple age-based formula

        # Assess resting HR
        rhr_assessment = "unknown"
        if cv_metrics.resting_heart_rate:
            rhr = cv_metrics.resting_heart_rate
            if gender == "male":
                if rhr < 60:
                    rhr_assessment = "excellent"
                elif rhr < 70:
                    rhr_assessment = "good"
                elif rhr < 80:
                    rhr_assessment = "average"
                else:
                    rhr_assessment = "needs_improvement"
            else:  # female
                if rhr < 65:
                    rhr_assessment = "excellent"
                elif rhr < 75:
                    rhr_assessment = "good"
                elif rhr < 85:
                    rhr_assessment = "average"
                else:
                    rhr_assessment = "needs_improvement"

        # Calculate cardiovascular efficiency
        efficiency_score = min(vo2_max / norms["average"] * 5, 10)

        # Heart rate reserve
        hr_reserve = max_hr - (cv_metrics.resting_heart_rate or 70)

        # Recovery assessment
        recovery_assessment = "unknown"
        if cv_metrics.recovery_heart_rate:
            recovery_hr = cv_metrics.recovery_heart_rate
            if recovery_hr >= 25:
                recovery_assessment = "excellent"
            elif recovery_hr >= 15:
                recovery_assessment = "good"
            elif recovery_hr >= 10:
                recovery_assessment = "average"
            else:
                recovery_assessment = "needs_improvement"

        return CardiovascularFitnessResults(
            estimated_vo2_max=vo2_max,
            vo2_max_category=vo2_category,
            estimated_max_hr=max_hr,
            resting_hr_assessment=rhr_assessment,
            cardiovascular_efficiency=efficiency_score,
            heart_rate_reserve=hr_reserve,
            recovery_assessment=recovery_assessment,
        )

    def analyze_performance(
        self,
        assessment_input: CardioAssessmentInput,
        cv_fitness: CardiovascularFitnessResults,
    ) -> PerformanceAnalysisResults:
        """Analyze performance capabilities and benchmarks."""
        performance = assessment_input.performance_testing
        gender = assessment_input.gender.lower()

        # Determine overall fitness level based on VO2 max
        fitness_level = cv_fitness.vo2_max_category

        # Analyze running performance
        running_level = "untested"
        performance_benchmarks = {}

        if performance.five_k_time_seconds:
            time_5k = performance.five_k_time_seconds
            standards = self.performance_standards["5k"][gender]

            if time_5k <= standards["elite"]:
                running_level = "elite"
            elif time_5k <= standards["competitive"]:
                running_level = "competitive"
            elif time_5k <= standards["recreational"]:
                running_level = "recreational"
            else:
                running_level = "beginner"

            performance_benchmarks["5k_time"] = {
                "current_seconds": time_5k,
                "performance_level": running_level,
                "percentile_estimate": self._calculate_running_percentile(
                    time_5k, "5k", gender
                ),
            }

        # Analyze other distances if available
        for distance in ["10k", "half_marathon", "marathon"]:
            time_attr = f"{distance.replace('_', '_')}_time_seconds"
            if hasattr(performance, time_attr):
                time_value = getattr(performance, time_attr)
                if time_value:
                    standards = self.performance_standards[distance][gender]
                    if time_value <= standards["elite"]:
                        level = "elite"
                    elif time_value <= standards["competitive"]:
                        level = "competitive"
                    elif time_value <= standards["recreational"]:
                        level = "recreational"
                    else:
                        level = "beginner"

                    performance_benchmarks[f"{distance}_time"] = {
                        "current_seconds": time_value,
                        "performance_level": level,
                        "percentile_estimate": self._calculate_running_percentile(
                            time_value, distance, gender
                        ),
                    }

        # Determine endurance capacity
        if cv_fitness.vo2_max_category in ["excellent", "good"]:
            endurance_capacity = "high"
        elif cv_fitness.vo2_max_category == "average":
            endurance_capacity = "moderate"
        else:
            endurance_capacity = "developing"

        # Speed vs endurance profile
        speed_vs_endurance = self._analyze_speed_endurance_profile(
            performance_benchmarks
        )

        # Improvement potential
        improvement_potential = self._identify_improvement_areas(
            cv_fitness, performance_benchmarks, assessment_input.experience
        )

        return PerformanceAnalysisResults(
            current_fitness_level=fitness_level,
            running_performance_level=running_level,
            endurance_capacity=endurance_capacity,
            speed_vs_endurance_profile=speed_vs_endurance,
            performance_benchmarks=performance_benchmarks,
            improvement_potential=improvement_potential,
        )

    def _calculate_running_percentile(
        self, time_seconds: int, distance: str, gender: str
    ) -> int:
        """Calculate approximate percentile for running performance."""
        standards = self.performance_standards[distance][gender]

        if time_seconds <= standards["elite"]:
            return 95
        elif time_seconds <= standards["competitive"]:
            return 80
        elif time_seconds <= standards["recreational"]:
            return 50
        else:
            return 25

    def _analyze_speed_endurance_profile(self, benchmarks: Dict[str, Any]) -> str:
        """Analyze speed vs endurance strengths."""
        if not benchmarks:
            return "insufficient_data"

        # Compare performance across distances
        if "5k_time" in benchmarks and "half_marathon_time" in benchmarks:
            five_k_perf = benchmarks["5k_time"]["percentile_estimate"]
            half_perf = benchmarks["half_marathon_time"]["percentile_estimate"]

            if five_k_perf > half_perf + 15:
                return "speed_oriented"
            elif half_perf > five_k_perf + 15:
                return "endurance_oriented"
            else:
                return "balanced"

        return "needs_more_data"

    def _identify_improvement_areas(
        self,
        cv_fitness: CardiovascularFitnessResults,
        benchmarks: Dict[str, Any],
        experience: CardioExperienceInput,
    ) -> Dict[str, str]:
        """Identify areas with highest improvement potential."""
        improvements = {}

        if cv_fitness.vo2_max_category in ["poor", "fair"]:
            improvements["aerobic_base"] = "high_potential"

        if cv_fitness.recovery_assessment in ["needs_improvement", "unknown"]:
            improvements["recovery_capacity"] = "moderate_potential"

        if experience.years_training < 2:
            improvements["training_experience"] = "high_potential"

        if not benchmarks:
            improvements["performance_testing"] = "establish_baselines"

        return improvements

    def assess_training_readiness(
        self, assessment_input: CardioAssessmentInput
    ) -> TrainingReadinessResults:
        """Assess training readiness and experience factors."""
        experience = assessment_input.experience

        # Determine experience level
        years = experience.years_training
        frequency = experience.weekly_frequency
        consistency = experience.training_consistency

        if years >= 3 and frequency >= 5 and consistency >= 4:
            exp_level = "advanced"
        elif years >= 1 and frequency >= 3 and consistency >= 3:
            exp_level = "intermediate"
        elif years >= 0.5 or frequency >= 2:
            exp_level = "novice"
        else:
            exp_level = "beginner"

        # Calculate readiness score
        readiness_factors = [
            min(years * 2, 10),  # Experience
            min(frequency / 5 * 10, 10),  # Frequency
            consistency * 2,  # Consistency
        ]

        if experience.competition_experience:
            readiness_factors.append(2)

        readiness_score = min(sum(readiness_factors) / len(readiness_factors), 10)

        # Identify injury risk factors
        risk_factors = []
        if experience.previous_injuries:
            risk_factors.append("previous_injury_history")
        if experience.current_pain_areas:
            risk_factors.append("current_pain_reported")
        if experience.longest_continuous_activity > 180 and years < 2:
            risk_factors.append("excessive_duration_for_experience")
        if frequency > 10:
            risk_factors.append("high_training_frequency")

        # Training load capacity
        if exp_level == "advanced":
            load_capacity = "high"
        elif exp_level == "intermediate":
            load_capacity = "moderate"
        else:
            load_capacity = "conservative"

        # Progression rate
        if risk_factors:
            progression_rate = "conservative"
        elif exp_level in ["beginner", "novice"]:
            progression_rate = "gradual"
        else:
            progression_rate = "moderate"

        return TrainingReadinessResults(
            experience_level=exp_level,
            training_readiness_score=readiness_score,
            injury_risk_factors=risk_factors,
            training_load_capacity=load_capacity,
            progression_rate=progression_rate,
        )

    def generate_recommendations(
        self,
        assessment_input: CardioAssessmentInput,
        cv_fitness: CardiovascularFitnessResults,
        performance: PerformanceAnalysisResults,
        readiness: TrainingReadinessResults,
    ) -> CardioRecommendationsOutput:
        """Generate comprehensive training recommendations."""
        goals = assessment_input.goals
        equipment = assessment_input.equipment_environment

        # Immediate priorities
        immediate_priorities = []

        if cv_fitness.vo2_max_category in ["poor", "fair"]:
            immediate_priorities.append("build_aerobic_base")
        if readiness.injury_risk_factors:
            immediate_priorities.append("address_injury_risk_factors")
        if cv_fitness.recovery_assessment == "needs_improvement":
            immediate_priorities.append("improve_recovery_capacity")
        if not performance.performance_benchmarks:
            immediate_priorities.append("establish_fitness_baselines")

        # Training type recommendation
        if "weight_loss" in goals.primary_goals:
            training_type = "aerobic_base_with_intervals"
        elif "endurance_events" in goals.primary_goals:
            training_type = "periodized_endurance"
        elif "general_fitness" in goals.primary_goals:
            training_type = "balanced_cardio_fitness"
        else:
            training_type = "general_aerobic_development"

        # Weekly structure
        weekly_structure = self._create_weekly_structure(
            goals, readiness, assessment_input.goals.weekly_time_availability
        )

        # Heart rate zones
        hr_zones = self._calculate_heart_rate_zones(cv_fitness.estimated_max_hr)

        # Progression timeline
        progression_timeline = {
            "weeks_1_4": "base_building_adaptation",
            "weeks_5_8": "progressive_volume_increase",
            "weeks_9_12": "intensity_introduction",
            "weeks_13_plus": "event_specific_preparation",
        }

        if readiness.experience_level == "beginner":
            progression_timeline = {
                "weeks_1_6": "movement_adaptation",
                "weeks_7_12": "basic_aerobic_development",
                "weeks_13_20": "progressive_duration_increase",
                "weeks_21_plus": "intensity_introduction",
            }

        # Equipment recommendations
        equipment_recs = []
        if not equipment.gym_access and not equipment.home_equipment:
            equipment_recs.extend(
                ["heart_rate_monitor", "running_shoes", "basic_fitness_tracker"]
            )
        if "cycling" in goals.primary_goals and not any(
            "bike" in eq for eq in equipment.home_equipment
        ):
            equipment_recs.append("quality_bicycle_or_trainer")

        # Professional referrals
        referrals = []
        if readiness.injury_risk_factors:
            referrals.append("sports_medicine_physician_or_physical_therapist")
        if cv_fitness.vo2_max_category == "poor":
            referrals.append("exercise_physiologist_for_testing")
        if "competition" in str(goals.target_events).lower():
            referrals.append("endurance_sports_coach")

        # Safety considerations
        safety_considerations = []
        if assessment_input.age > 45 and cv_fitness.vo2_max_category in [
            "poor",
            "fair",
        ]:
            safety_considerations.append("medical_clearance_recommended")
        if readiness.injury_risk_factors:
            safety_considerations.append("gradual_progression_essential")
        safety_considerations.append("monitor_heart_rate_during_exercise")
        safety_considerations.append("proper_hydration_and_nutrition")

        return CardioRecommendationsOutput(
            immediate_priorities=immediate_priorities,
            training_type_recommendation=training_type,
            weekly_structure=weekly_structure,
            target_heart_rate_zones=hr_zones,
            progression_timeline=progression_timeline,
            equipment_recommendations=equipment_recs,
            professional_referrals=referrals,
            safety_considerations=safety_considerations,
        )

    def _create_weekly_structure(
        self,
        goals: CardioGoalsInput,
        readiness: TrainingReadinessResults,
        weekly_minutes: int,
    ) -> Dict[str, Any]:
        """Create weekly training structure recommendation."""
        # Calculate sessions per week based on experience and time
        if readiness.experience_level == "beginner":
            sessions_per_week = min(3, weekly_minutes // 30)
        elif readiness.experience_level == "novice":
            sessions_per_week = min(4, weekly_minutes // 40)
        else:
            sessions_per_week = min(6, weekly_minutes // 45)

        sessions_per_week = max(2, sessions_per_week)  # Minimum 2 sessions

        # Session duration
        avg_session_duration = weekly_minutes // sessions_per_week

        return {
            "sessions_per_week": sessions_per_week,
            "average_session_duration": avg_session_duration,
            "intensity_distribution": "80_percent_easy_20_percent_hard",
            "weekly_progression": "10_percent_volume_increase_max",
            "recovery_days": 7 - sessions_per_week,
        }

    def _calculate_heart_rate_zones(self, max_hr: int) -> Dict[str, Dict[str, int]]:
        """Calculate personalized heart rate training zones."""
        return {
            "zone_1_recovery": {
                "min_hr": int(max_hr * 0.5),
                "max_hr": int(max_hr * 0.6),
            },
            "zone_2_aerobic_base": {
                "min_hr": int(max_hr * 0.6),
                "max_hr": int(max_hr * 0.7),
            },
            "zone_3_aerobic_threshold": {
                "min_hr": int(max_hr * 0.7),
                "max_hr": int(max_hr * 0.8),
            },
            "zone_4_lactate_threshold": {
                "min_hr": int(max_hr * 0.8),
                "max_hr": int(max_hr * 0.9),
            },
            "zone_5_neuromuscular": {"min_hr": int(max_hr * 0.9), "max_hr": max_hr},
        }

    async def assess_cardio(
        self, assessment_input: CardioAssessmentInput
    ) -> CardioAssessmentOutput:
        """
        Perform comprehensive cardiovascular fitness assessment.

        Args:
            assessment_input: Complete assessment input data

        Returns:
            CardioAssessmentOutput: Comprehensive assessment results and recommendations
        """
        try:
            logger.info(
                f"Starting cardio assessment for user {assessment_input.user_id}"
            )

            # Perform individual assessments
            cv_fitness = self.assess_cardiovascular_fitness(assessment_input)
            performance_analysis = self.analyze_performance(
                assessment_input, cv_fitness
            )
            training_readiness = self.assess_training_readiness(assessment_input)

            # Generate recommendations
            recommendations = self.generate_recommendations(
                assessment_input, cv_fitness, performance_analysis, training_readiness
            )

            # Calculate overall fitness score
            vo2_score = min(cv_fitness.estimated_vo2_max * 2, 100)  # VO2 max component
            efficiency_score = (
                cv_fitness.cardiovascular_efficiency * 10
            )  # Efficiency component
            readiness_score = (
                training_readiness.training_readiness_score * 10
            )  # Readiness component

            overall_score = (vo2_score + efficiency_score + readiness_score) / 3

            # Calculate next assessment date
            if training_readiness.experience_level == "beginner":
                next_assessment = assessment_input.assessment_date + timedelta(
                    weeks=8
                )  # 2 months
            else:
                next_assessment = assessment_input.assessment_date + timedelta(
                    weeks=16
                )  # 4 months

            # Generate unique assessment ID
            import uuid

            assessment_id = str(uuid.uuid4())

            logger.info(
                f"Completed cardio assessment for user {assessment_input.user_id}"
            )

            return CardioAssessmentOutput(
                user_id=assessment_input.user_id,
                assessment_id=assessment_id,
                assessment_date=assessment_input.assessment_date,
                cardiovascular_fitness=cv_fitness,
                performance_analysis=performance_analysis,
                training_readiness=training_readiness,
                recommendations=recommendations,
                overall_fitness_score=overall_score,
                next_reassessment_date=next_assessment,
            )

        except Exception as e:
            logger.error(f"Error in cardio assessment: {str(e)}")
            raise
