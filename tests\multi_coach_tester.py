import asyncio
import json
import logging
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_test_graph,
)
from athlea_langgraph.states.optimized_state import OptimizedCoachingState
from langchain_core.messages import HumanMessage, ToolMessage, AIMessage

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# --- Multi-Coach Test Scenarios ---
MULTI_COACH_TEST_CASES = [
    {
        "test_name": "Comprehensive Training Plan: Strength + Nutrition + Recovery",
        "user_query": "I want to build muscle mass. I need a complete plan that includes strength training exercises, proper nutrition for muscle gain, and recovery protocols. I'm 25, male, 175cm, 70kg, intermediate level.",
        "expected_coaches": ["strength", "nutrition", "recovery"],
        "expected_routing": "multi_coach",
        "expected_tools": [
            "search_strength_exercises",
            "calculate_daily_calories",
            "generate_mobility_protocol",
        ],
        "description": "Complex request requiring multiple specialized domains",
    },
    {
        "test_name": "Weight Loss Strategy: Nutrition + Cardio + Mental",
        "user_query": "I want to lose 10kg safely. I need a nutrition plan for weight loss, cardio training recommendations, and mental strategies to stay motivated during my weight loss journey.",
        "expected_coaches": ["nutrition", "cardio", "mental"],
        "expected_routing": "multi_coach",
        "expected_tools": [
            "calculate_daily_calories",
            "comprehensive_cardio_assessment",
            "web_search",
        ],
        "description": "Multi-domain weight loss approach",
    },
    {
        "test_name": "Athletic Performance: Strength + Cardio + Recovery",
        "user_query": "I'm training for a marathon but also want to maintain strength. I need strength training that complements endurance, cardio periodization, and recovery strategies for high-volume training.",
        "expected_coaches": ["strength", "cardio", "recovery"],
        "expected_routing": "multi_coach",
        "expected_tools": [
            "search_strength_exercises",
            "calculate_training_zones",
            "optimize_sleep",
        ],
        "description": "Complex athletic performance optimization",
    },
    {
        "test_name": "Beginner Wellness: All Domains",
        "user_query": "I'm completely new to fitness and wellness. I need guidance on strength training basics, proper nutrition, cardiovascular health, recovery practices, and mental preparation for starting a fitness journey.",
        "expected_coaches": ["strength", "nutrition", "cardio", "recovery", "mental"],
        "expected_routing": "multi_coach",
        "expected_tools": [
            "search_strength_exercises",
            "calculate_daily_calories",
            "calculate_heart_rate_zones",
            "assess_wellness",
            "mental_state_assessment",
        ],
        "description": "Comprehensive beginner guidance across all domains",
    },
]


async def run_multi_coach_test(graph, test_case):
    """Runs a single multi-coach test case and analyzes the output."""
    test_name = test_case["test_name"]
    user_query = test_case["user_query"]

    logger.info(f"--- Running Multi-Coach Test: {test_name} ---")
    logger.info(f"Query: '{user_query}'")
    logger.info(f"Expected coaches: {test_case['expected_coaches']}")

    thread_id = f"multi_test_{test_name.replace(' ', '_').lower()}"
    config = {"configurable": {"thread_id": thread_id}}

    # Enable debug logging for multi-coach flows
    multi_coach_logger = logging.getLogger(
        "athlea_langgraph.graphs.optimized_comprehensive_coaching_graph"
    )
    multi_coach_logger.setLevel(logging.INFO)

    final_state = await graph.ainvoke(
        {
            "user_query": user_query,
            "messages": [],
            "execution_steps": [],
            "config": config,
        },
        config=config,
    )

    # Analyze the results
    final_response = final_state.get("final_response", "N/A")
    coach_responses = final_state.get("coach_responses", {})

    state_snapshot = graph.get_state(config)
    final_values = state_snapshot.values if state_snapshot else {}

    routing_decision = final_values.get("routing_decision")
    primary_coach = final_values.get("primary_coach")
    required_coaches = final_values.get("required_coaches", [])
    execution_steps = final_values.get("execution_steps", [])
    messages = final_values.get("messages", [])

    # Check for tool calls across all messages
    tool_calls = []
    for msg in messages:
        if isinstance(msg, AIMessage) and msg.tool_calls:
            for tc in msg.tool_calls:
                tool_calls.append({"name": tc["name"], "args": tc["args"]})

    # --- Analysis ---
    print(f"\n=== MULTI-COACH ANALYSIS: {test_name} ===")
    print(f"DESCRIPTION: {test_case['description']}")
    print(f"EXPECTED ROUTING: {test_case['expected_routing']}")
    print(f"ACTUAL ROUTING: {routing_decision}")
    print(f"EXPECTED COACHES: {test_case['expected_coaches']}")
    print(f"REQUIRED COACHES: {required_coaches}")
    print(f"PRIMARY COACH: {primary_coach}")

    print(f"\n--- EXECUTION FLOW ---")
    print(f"EXECUTION STEPS: {execution_steps}")

    print(f"\n--- COACH RESPONSES ACCUMULATION ---")
    print(
        f"COACH RESPONSES COLLECTED: {list(coach_responses.keys()) if coach_responses else 'None'}"
    )
    if coach_responses:
        for coach_name, response in coach_responses.items():
            response_preview = (
                response[:100] + "..." if len(response) > 100 else response
            )
            print(f"  - {coach_name}: {response_preview}")
    else:
        print("  ❌ NO COACH RESPONSES ACCUMULATED!")

    print(f"\n--- TOOL USAGE ANALYSIS ---")
    if tool_calls:
        print(f"TOOLS CALLED: {[tc['name'] for tc in tool_calls]}")
        for tc in tool_calls:
            print(
                f"  - {tc['name']}: {list(tc['args'].keys()) if tc['args'] else 'no args'}"
            )
    else:
        print("TOOLS CALLED: None")

    print(f"\n--- AGGREGATION CHECK ---")
    aggregation_used = "aggregation" in execution_steps
    print(f"AGGREGATION NODE USED: {aggregation_used}")

    if aggregation_used:
        print("✅ Multi-coach workflow correctly used aggregation")
    else:
        print("❌ Multi-coach workflow should have used aggregation but didn't!")

    print(f"\n--- FINAL RESPONSE ---")
    print(f"RESPONSE LENGTH: {len(final_response)} characters")
    print("-" * 40)
    print(final_response[:500] + "..." if len(final_response) > 500 else final_response)
    print("-" * 40)

    # Success criteria for multi-coach tests
    success = analyze_multi_coach_result(
        test_case["expected_routing"],
        test_case["expected_coaches"],
        routing_decision,
        required_coaches,
        coach_responses,
        aggregation_used,
        execution_steps,
    )

    print(
        f"\n{'✅ MULTI-COACH TEST PASSED' if success else '❌ MULTI-COACH TEST FAILED'}"
    )
    print("=" * 60 + "\n")

    return success


def analyze_multi_coach_result(
    expected_routing,
    expected_coaches,
    actual_routing,
    required_coaches,
    coach_responses,
    aggregation_used,
    execution_steps,
):
    """Analyze multi-coach test results and determine success."""

    success_criteria = []

    # 1. Check if routing decision is correct
    routing_correct = actual_routing == expected_routing
    success_criteria.append(
        (
            "Routing Decision",
            routing_correct,
            f"Expected: {expected_routing}, Got: {actual_routing}",
        )
    )

    # 2. Check if required coaches include expected coaches
    coaches_correct = all(coach in required_coaches for coach in expected_coaches)
    success_criteria.append(
        (
            "Required Coaches",
            coaches_correct,
            f"Expected coaches {expected_coaches} in {required_coaches}",
        )
    )

    # 3. Check if aggregation was used for multi-coach workflows
    aggregation_correct = (
        aggregation_used if expected_routing == "multi_coach" else True
    )
    success_criteria.append(
        (
            "Aggregation Usage",
            aggregation_correct,
            f"Aggregation should be used for multi-coach: {aggregation_used}",
        )
    )

    # 4. Check if coach responses were accumulated
    responses_accumulated = (
        len(coach_responses) > 0 if expected_routing == "multi_coach" else True
    )
    success_criteria.append(
        (
            "Response Accumulation",
            responses_accumulated,
            f"Coach responses accumulated: {len(coach_responses)} coaches",
        )
    )

    # 5. Check if multiple coaches actually executed (for multi-coach scenarios)
    if expected_routing == "multi_coach":
        coach_execution_correct = (
            len(coach_responses) >= 2
        )  # At least 2 coaches should have executed
        success_criteria.append(
            (
                "Multiple Coach Execution",
                coach_execution_correct,
                f"Expected multiple coaches, got {len(coach_responses)}",
            )
        )
    else:
        coach_execution_correct = True
        success_criteria.append(
            (
                "Multiple Coach Execution",
                coach_execution_correct,
                "N/A for non-multi-coach",
            )
        )

    # Print detailed analysis
    print(f"\n--- SUCCESS CRITERIA ANALYSIS ---")
    all_passed = True
    for criterion, passed, details in success_criteria:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{criterion}: {status} - {details}")
        if not passed:
            all_passed = False

    return all_passed


async def main():
    """Main function to run all multi-coach tests."""
    logger.info("🚀 Starting Multi-Coach Test Suite for Optimized Coaching Graph 🚀")
    graph = await create_optimized_test_graph(config={"enable_memory": True})

    passed_tests = 0
    total_tests = len(MULTI_COACH_TEST_CASES)

    for test_case in MULTI_COACH_TEST_CASES:
        success = await run_multi_coach_test(graph, test_case)
        if success:
            passed_tests += 1

    print(f"\n🎯 MULTI-COACH TEST SUMMARY:")
    print(f"   Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")

    if passed_tests == total_tests:
        print("   ✅ ALL MULTI-COACH TESTS PASSED!")
    else:
        print(
            f"   ❌ {total_tests - passed_tests} TESTS FAILED - Multi-coach aggregation needs attention"
        )

    logger.info("✅ Multi-Coach Test Suite Completed.")


if __name__ == "__main__":
    asyncio.run(main())
