from unittest.mock import AsyncMock, MagicMock
from unittest.mock import patch

import pytest
from fastapi import HTTPEx<PERSON>

# Attempt to import actual MCP types, fall back to mocks if not available
try:
    from mcp import ToolCall
except ImportError:
    print(
        "Warning: mcp.ToolCall not found, using a mock. Tests might not be fully representative."
    )

    class ToolCall:
        def __init__(self, name: str, input: dict):
            self.name = name
            self.input = input


try:
    from mcp.server import Request
except ImportError:
    print(
        "Warning: mcp.server.Request not found, using a mock. Tests might not be fully representative."
    )

    class Request:
        pass


from athlea_langgraph.tools.schemas import (
    AirtableQuerySchema,
)

# Imports from the external MCP server and related schemas
from mcp_servers.external_mcp.server import call_tool as external_call_tool_handler
from mcp_servers.external_mcp.server import list_tools as external_list_tools_handler
from mcp_servers.external_mcp.server import (
    EXTERNAL_TOOLS as external_module_tools_definition,
)

# Import tool instances that should be available
from mcp_servers.external_mcp.server import (
    azure_maps,
    google_search,
    airtable,
    weather_api,
    wikipedia,
)


@pytest.fixture
def mock_request():
    return Request()


@pytest.mark.asyncio
async def test_list_tools_external_mcp(mock_request):
    listed_tools = await external_list_tools_handler(mock_request)
    assert len(listed_tools) == len(external_module_tools_definition)
    assert listed_tools == external_module_tools_definition
    tool_names = [t.name for t in listed_tools]
    assert "search_locations" in tool_names
    assert "search_web" in tool_names
    assert "query_airtable" in tool_names
    assert "get_weather_data" in tool_names
    assert "search_wikipedia" in tool_names


# --- Test cases for each tool ---


@pytest.mark.asyncio
async def test_call_tool_search_locations_valid(mock_request):
    # Mock the actual method that gets called in the server
    with patch.object(
        azure_maps, "search_address", new_callable=AsyncMock
    ) as mock_search:
        mock_search.return_value = {"results": [{"address": "123 Main St"}]}

    tool_call_input = {
        "query": "coffee shop",
    }
    tool_call = ToolCall(name="search_locations", input=tool_call_input)
    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains the expected structure
    assert len(result.content) > 0
    assert "results" in result.content[0].text or "address" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_search_web_valid(mock_request):
    # Mock the actual method that gets called in the server
    with patch.object(
        google_search, "search_web", new_callable=AsyncMock
    ) as mock_search:
        mock_search.return_value = {"results": [{"title": "Search Result"}]}

    tool_call_input = {"query": "python programming"}
    tool_call = ToolCall(name="search_web", input=tool_call_input)
    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains the expected structure
    assert len(result.content) > 0
    assert "results" in result.content[0].text or "title" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_query_airtable_valid(mock_request):
    # Mock the actual method that gets called in the server
    with patch.object(airtable, "query_table", new_callable=AsyncMock) as mock_query:
        mock_query.return_value = {"records": [{"id": "rec1"}]}

    tool_call_input = {
        "base_id": "appTestBase",
        "table_name": "tblTestTable",
    }
    tool_call = ToolCall(name="query_airtable", input=tool_call_input)
    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains the expected structure
    assert len(result.content) > 0
    assert "records" in result.content[0].text or "id" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_get_weather_data_valid(mock_request):
    # Mock the actual method that gets called in the server
    with patch.object(
        weather_api, "get_weather", new_callable=AsyncMock
    ) as mock_weather:
        mock_weather.return_value = {"temp_c": 20}

    tool_call_input = {"location": "London"}
    tool_call = ToolCall(name="get_weather_data", input=tool_call_input)
    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains the expected structure
    assert len(result.content) > 0
    assert "temp" in result.content[0].text or "20" in result.content[0].text


@pytest.mark.asyncio
async def test_call_tool_search_wikipedia_valid(mock_request):
    # Mock the actual method that gets called in the server
    with patch.object(
        wikipedia, "search_articles", new_callable=AsyncMock
    ) as mock_search:
        mock_search.return_value = {"summary": "Article summary"}

    tool_call_input = {"query": "Albert Einstein"}
    tool_call = ToolCall(name="search_wikipedia", input=tool_call_input)
    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains the expected structure
    assert len(result.content) > 0
    assert "summary" in result.content[0].text or "Article" in result.content[0].text


# --- Test cases for invalid inputs and unknown tool ---


@pytest.mark.asyncio
async def test_call_tool_invalid_tool_name_external_mcp(mock_request):
    tool_call = ToolCall(name="non_existent_external_tool", input={})

    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains error message
    assert "content" in result
    assert len(result["content"]) > 0
    assert "Error" in result["content"][0]["text"]


@pytest.mark.asyncio
async def test_call_tool_search_locations_invalid_input(mock_request):
    tool_call_input = {}  # Missing required query field
    tool_call = ToolCall(name="search_locations", input=tool_call_input)

    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains error message
    assert "content" in result
    assert len(result["content"]) > 0
    assert "Error" in result["content"][0]["text"]


@pytest.mark.asyncio
async def test_call_tool_query_airtable_missing_input(mock_request):
    tool_call_input = {"base_id": "appTestBase"}  # Missing table_name
    tool_call = ToolCall(name="query_airtable", input=tool_call_input)

    result = await external_call_tool_handler(mock_request, tool_call)

    # Check that result contains error message
    assert "content" in result
    assert len(result["content"]) > 0
    assert "Error" in result["content"][0]["text"]
