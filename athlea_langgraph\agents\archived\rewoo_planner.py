"""
ReWOO Planner Agent for Athlea Coaching System.

Implements the "Plan" phase of the ReWOO pattern, analyzing user queries
and creating optimal execution plans for parallel coaching agent coordination.
"""

import asyncio
import logging
import time
import uuid
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.rewoo_state import (
    ExecutionPlan,
    ReWOOConfig,
    ReWOOState,
    Task,
)

logger = logging.getLogger(__name__)


class ReWOOPlanner:
    """
    ReWOO Planner Agent for intelligent task decomposition and coordination.

    Analyzes user coaching queries and creates optimized execution plans
    for parallel worker agent coordination without observation loops.
    """

    def __init__(self, config: Optional[ReWOOConfig] = None):
        """
        Initialize the ReWOO Planner.

        Args:
            config: ReWOO configuration settings
        """
        self.config = config or {}
        self.llm = create_azure_chat_openai()

        # Domain expertise mapping
        self.domain_capabilities = {
            "strength": {
                "keywords": [
                    "strength",
                    "weight",
                    "lifting",
                    "powerlifting",
                    "bodybuilding",
                    "muscle",
                    "exercise",
                    "form",
                    "technique",
                    "deadlift",
                    "squat",
                    "bench",
                ],
                "tools": [
                    "exercise_library",
                    "form_analysis",
                    "progression_calculator",
                ],
                "typical_duration": 20,
            },
            "nutrition": {
                "keywords": [
                    "nutrition",
                    "diet",
                    "meal",
                    "food",
                    "protein",
                    "carbs",
                    "calories",
                    "macros",
                    "supplements",
                    "weight loss",
                    "weight gain",
                ],
                "tools": ["nutrition_calculator", "meal_planner", "supplement_advisor"],
                "typical_duration": 15,
            },
            "cardio": {
                "keywords": [
                    "cardio",
                    "running",
                    "cycling",
                    "endurance",
                    "heart rate",
                    "pace",
                    "distance",
                    "aerobic",
                    "marathon",
                    "training",
                ],
                "tools": [
                    "training_plan_generator",
                    "pace_calculator",
                    "heart_rate_zones",
                ],
                "typical_duration": 18,
            },
            "recovery": {
                "keywords": [
                    "recovery",
                    "sleep",
                    "rest",
                    "stretching",
                    "mobility",
                    "injury",
                    "pain",
                    "rehabilitation",
                    "massage",
                ],
                "tools": ["recovery_protocols", "sleep_optimizer", "injury_assessment"],
                "typical_duration": 12,
            },
            "mental": {
                "keywords": [
                    "motivation",
                    "goals",
                    "mindset",
                    "psychology",
                    "confidence",
                    "stress",
                    "anxiety",
                    "performance",
                ],
                "tools": ["goal_setter", "motivation_tracker", "mindset_coach"],
                "typical_duration": 10,
            },
            "cycling": {
                "keywords": [
                    "cycling",
                    "bike",
                    "cadence",
                    "power",
                    "watts",
                    "cycling technique",
                    "bike fit",
                    "indoor cycling",
                ],
                "tools": [
                    "bike_fit_analyzer",
                    "power_zone_calculator",
                    "route_planner",
                ],
                "typical_duration": 16,
            },
        }

    async def plan(self, state: ReWOOState) -> ReWOOState:
        """
        Create execution plan for parallel coaching agent coordination.

        Args:
            state: Current ReWOO state

        Returns:
            Updated state with execution plan
        """

        start_time = time.time()
        logger.info("🧠 ReWOO Planner: Starting query analysis and task decomposition")

        try:
            # Analyze the user query
            query_analysis = await self._analyze_query(
                state["user_query"], state.get("user_profile")
            )

            # Determine required domains
            required_domains = await self._identify_required_domains(query_analysis)

            # Create tasks for each domain
            tasks = await self._create_domain_tasks(
                query_analysis, required_domains, state.get("user_profile")
            )

            # Determine execution strategy
            execution_strategy = self._determine_execution_strategy(tasks)

            # Create execution plan
            execution_plan = ExecutionPlan(
                {
                    "plan_id": str(uuid.uuid4()),
                    "query_analysis": query_analysis,
                    "required_domains": required_domains,
                    "tasks": tasks,
                    "execution_strategy": execution_strategy,
                    "estimated_total_time": self._estimate_total_time(
                        tasks, execution_strategy
                    ),
                    "confidence": self._calculate_plan_confidence(
                        query_analysis, tasks
                    ),
                }
            )

            # Update state
            planning_time = time.time() - start_time
            logger.info(
                f"✅ ReWOO Planner: Plan created in {planning_time:.2f}s - {len(tasks)} tasks across {len(required_domains)} domains"
            )

            return {
                **state,
                "execution_plan": execution_plan,
                "required_domains": required_domains,
                "active_tasks": tasks,
                "planning_start_time": start_time,
                "parallel_execution_status": {
                    task["task_id"]: "pending" for task in tasks
                },
                "messages": state["messages"]
                + [
                    AIMessage(
                        content=f"📋 Analysis complete. Created execution plan with {len(tasks)} tasks across {len(required_domains)} coaching domains: {', '.join(required_domains)}"
                    )
                ],
            }

        except Exception as e:
            logger.error(f"❌ ReWOO Planner error: {str(e)}")
            # Fallback to single-agent approach
            return await self._create_fallback_plan(state, str(e))

    async def _analyze_query(
        self, user_query: str, user_profile: Optional[Dict[str, Any]]
    ) -> str:
        """
        Analyze user query to understand coaching requirements.

        Args:
            user_query: User's coaching request
            user_profile: User profile and preferences

        Returns:
            Detailed query analysis
        """

        analysis_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content="""You are an expert fitness coaching analyst. Your job is to analyze user queries and understand their coaching needs across multiple fitness domains.

Analyze the query for:
1. Primary coaching domain(s) needed (strength, nutrition, cardio, recovery, mental, cycling)
2. Specific requirements and goals
3. Complexity level (simple, moderate, complex)
4. User experience level (beginner, intermediate, advanced)
5. Safety considerations and contraindications
6. Interdependencies between different coaching areas

Provide a comprehensive analysis that will help coordinate multiple coaching specialists."""
                ),
                HumanMessage(
                    content=f"User Query: {user_query}\n\nUser Profile: {user_profile or 'No profile provided'}\n\nProvide detailed analysis:"
                ),
            ]
        )

        response = await self.llm.ainvoke(analysis_prompt.format_messages())
        return response.content

    async def _identify_required_domains(self, query_analysis: str) -> List[str]:
        """
        Identify which coaching domains are required based on analysis.

        Args:
            query_analysis: Detailed query analysis

        Returns:
            List of required coaching domains
        """

        domain_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content="""Based on the query analysis, identify which coaching domains are needed.

Available domains:
- strength: Weight training, powerlifting, bodybuilding, exercise form
- nutrition: Diet planning, macros, supplements, weight management  
- cardio: Running, endurance training, heart rate, aerobic fitness
- recovery: Sleep, rest, injury prevention, mobility, rehabilitation
- mental: Motivation, goal setting, sports psychology, mindset
- cycling: Bike training, power zones, cycling technique, bike fit

Return ONLY a comma-separated list of required domains. Be selective - only include domains that are directly relevant."""
                ),
                HumanMessage(
                    content=f"Query Analysis:\n{query_analysis}\n\nRequired domains:"
                ),
            ]
        )

        response = await self.llm.ainvoke(domain_prompt.format_messages())
        domains = [domain.strip() for domain in response.content.split(",")]

        # Validate domains
        valid_domains = [d for d in domains if d in self.domain_capabilities]

        logger.info(f"🎯 Identified required domains: {valid_domains}")
        return valid_domains

    async def _create_domain_tasks(
        self,
        query_analysis: str,
        required_domains: List[str],
        user_profile: Optional[Dict[str, Any]],
    ) -> List[Task]:
        """
        Create specific tasks for each required domain.

        Args:
            query_analysis: Query analysis
            required_domains: Required coaching domains
            user_profile: User profile and preferences

        Returns:
            List of tasks for worker agents
        """

        tasks = []

        for i, domain in enumerate(required_domains):
            domain_info = self.domain_capabilities[domain]

            task = Task(
                {
                    "task_id": f"task_{domain}_{uuid.uuid4().hex[:8]}",
                    "task_type": domain,
                    "description": await self._create_task_description(
                        domain, query_analysis, user_profile
                    ),
                    "dependencies": self._determine_task_dependencies(
                        domain, required_domains
                    ),
                    "worker_agent": f"{domain}_coach",
                    "priority": self._calculate_task_priority(domain, query_analysis),
                    "estimated_duration": domain_info["typical_duration"],
                }
            )

            tasks.append(task)

        logger.info(f"📋 Created {len(tasks)} domain-specific tasks")
        return tasks

    async def _create_task_description(
        self, domain: str, query_analysis: str, user_profile: Optional[Dict[str, Any]]
    ) -> str:
        """
        Create specific task description for a domain worker.

        Args:
            domain: Coaching domain
            query_analysis: Query analysis
            user_profile: User profile

        Returns:
            Task description for the worker agent
        """

        task_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content=f"""You are creating a specific task for the {domain} coaching specialist.

Based on the query analysis, create a focused task description that:
1. Clearly defines what the {domain} coach should address
2. Includes specific requirements from the user query
3. Considers the user's profile and limitations
4. Focuses only on {domain}-related aspects
5. Is actionable and specific

Keep it concise but comprehensive."""
                ),
                HumanMessage(
                    content=f"Domain: {domain}\nQuery Analysis: {query_analysis}\nUser Profile: {user_profile}\n\nTask description:"
                ),
            ]
        )

        response = await self.llm.ainvoke(task_prompt.format_messages())
        return response.content.strip()

    def _determine_task_dependencies(
        self, domain: str, all_domains: List[str]
    ) -> List[str]:
        """
        Determine task dependencies based on domain relationships.

        Args:
            domain: Current domain
            all_domains: All required domains

        Returns:
            List of task IDs this task depends on
        """

        # Define domain dependencies
        dependencies_map = {
            "strength": [],  # Can run independently
            "nutrition": [],  # Can run independently
            "cardio": [],  # Can run independently
            "recovery": ["strength", "cardio"],  # Often depends on training plans
            "mental": [],  # Can run independently
            "cycling": [],  # Can run independently
        }

        # For now, return empty dependencies to enable maximum parallelization
        # In production, you might want more sophisticated dependency logic
        return []

    def _calculate_task_priority(self, domain: str, query_analysis: str) -> int:
        """
        Calculate task priority based on domain and query analysis.

        Args:
            domain: Coaching domain
            query_analysis: Query analysis

        Returns:
            Priority score (1-10, higher is more important)
        """

        # Default priorities
        priority_map = {
            "strength": 8,  # Often central to fitness goals
            "nutrition": 9,  # Fundamental for most goals
            "cardio": 7,  # Important for general fitness
            "recovery": 6,  # Supporting but important
            "mental": 5,  # Supporting psychological aspects
            "cycling": 7,  # Sport-specific when needed
        }

        base_priority = priority_map.get(domain, 5)

        # Adjust based on query analysis keywords
        query_lower = query_analysis.lower()
        domain_keywords = self.domain_capabilities[domain]["keywords"]

        keyword_matches = sum(
            1 for keyword in domain_keywords if keyword in query_lower
        )
        priority_adjustment = min(keyword_matches, 3)  # Cap at +3

        return min(base_priority + priority_adjustment, 10)

    def _determine_execution_strategy(self, tasks: List[Task]) -> str:
        """
        Determine the best execution strategy for the tasks.

        Args:
            tasks: List of tasks to execute

        Returns:
            Execution strategy
        """

        if len(tasks) <= 1:
            return "sequential"
        elif len(tasks) <= self.config.get("max_parallel_workers", 4):
            return "parallel"
        else:
            return "hybrid"

    def _estimate_total_time(self, tasks: List[Task], execution_strategy: str) -> int:
        """
        Estimate total execution time for the plan.

        Args:
            tasks: List of tasks
            execution_strategy: Execution strategy

        Returns:
            Estimated time in seconds
        """

        if execution_strategy == "parallel":
            # Parallel execution time is the maximum task duration
            return max(task["estimated_duration"] for task in tasks) if tasks else 0
        elif execution_strategy == "sequential":
            # Sequential execution time is the sum of all tasks
            return sum(task["estimated_duration"] for task in tasks)
        else:  # hybrid
            # Estimate hybrid execution (simplified)
            max_parallel = self.config.get("max_parallel_workers", 4)
            if len(tasks) <= max_parallel:
                return max(task["estimated_duration"] for task in tasks)
            else:
                # Rough estimate for hybrid execution
                return sum(task["estimated_duration"] for task in tasks) // 2

    def _calculate_plan_confidence(
        self, query_analysis: str, tasks: List[Task]
    ) -> float:
        """
        Calculate confidence in the execution plan.

        Args:
            query_analysis: Query analysis
            tasks: List of tasks

        Returns:
            Confidence score (0.0-1.0)
        """

        base_confidence = 0.8

        # Adjust based on number of domains (more domains = slightly lower confidence)
        domain_penalty = (len(tasks) - 1) * 0.05

        # Adjust based on task complexity (simplified heuristic)
        complexity_penalty = 0.0
        if "complex" in query_analysis.lower():
            complexity_penalty = 0.1
        elif "simple" in query_analysis.lower():
            complexity_penalty = -0.1

        confidence = base_confidence - domain_penalty - complexity_penalty
        return max(0.5, min(1.0, confidence))  # Clamp between 0.5 and 1.0

    async def _create_fallback_plan(
        self, state: ReWOOState, error_message: str
    ) -> ReWOOState:
        """
        Create a fallback plan when planning fails.

        Args:
            state: Current state
            error_message: Error that occurred

        Returns:
            State with fallback plan
        """

        logger.warning(f"⚠️ Creating fallback plan due to error: {error_message}")

        # Simple fallback: single strength coach task
        fallback_task = Task(
            {
                "task_id": f"fallback_{uuid.uuid4().hex[:8]}",
                "task_type": "strength",
                "description": f"Handle user query: {state['user_query']}",
                "dependencies": [],
                "worker_agent": "strength_coach",
                "priority": 5,
                "estimated_duration": 20,
            }
        )

        fallback_plan = ExecutionPlan(
            {
                "plan_id": f"fallback_{uuid.uuid4().hex[:8]}",
                "query_analysis": f"Fallback analysis for: {state['user_query']}",
                "required_domains": ["strength"],
                "tasks": [fallback_task],
                "execution_strategy": "sequential",
                "estimated_total_time": 20,
                "confidence": 0.6,
            }
        )

        return {
            **state,
            "execution_plan": fallback_plan,
            "required_domains": ["strength"],
            "active_tasks": [fallback_task],
            "parallel_execution_status": {fallback_task["task_id"]: "pending"},
            "messages": state["messages"]
            + [
                AIMessage(
                    content=f"⚠️ Planning encountered an issue. Using fallback approach with strength coaching focus."
                )
            ],
        }


async def rewoo_planner_node(state: ReWOOState) -> ReWOOState:
    """
    ReWOO Planner node for LangGraph integration.

    Args:
        state: Current ReWOO state

    Returns:
        Updated state with execution plan
    """

    config = state.get("coordination_metadata", {}).get("config")
    planner = ReWOOPlanner(config)

    return await planner.plan(state)
