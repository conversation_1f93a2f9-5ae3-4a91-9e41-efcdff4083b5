#!/usr/bin/env python3
"""
LangSmith Integration Commands

CLI commands for exporting prompts to LangSmith format and importing optimized versions back.
This enhances the existing unified prompt system with LangSmith workflow support.
"""

import os
import sys
from pathlib import Path

import click

from athlea_langgraph.utils.prompt_loader import get_prompt_loader_sync
from athlea_langgraph.utils.prompt_models import PromptConfig


@click.group()
def langsmith():
    """LangSmith integration commands for prompt testing workflows."""
    pass


@langsmith.command()
@click.argument("prompt_name")
@click.option("--output", "-o", help="Output file path (default: stdout)")
def export(prompt_name: str, output: str):
    """Export a prompt to LangSmith format for testing in the playground."""
    try:
        loader = get_prompt_loader_sync()
        config = loader.load_prompt_sync(prompt_name)

        # Convert to LangChain format like our sync service does
        from athlea_langgraph.services.langsmith_prompt_sync import get_prompt_syncer

        syncer = get_prompt_syncer()
        langchain_prompt = syncer._convert_json_to_langchain_prompt(config)

        if not langchain_prompt:
            click.echo(f"❌ Failed to convert {prompt_name} to LangChain format")
            sys.exit(1)

        # Format for output
        output_content = f"""# LangSmith Prompt: {prompt_name}

**Template:**
```
{langchain_prompt.template}
```

**Input Variables:**
{', '.join(langchain_prompt.input_variables) if langchain_prompt.input_variables else 'None'}

**Metadata:**
- Version: {config.metadata.version}
- Description: {config.metadata.description}
- Updated: {config.metadata.updated_at}

---
*Generated by athlea-langgraph CLI*
*Ready to copy-paste into LangSmith Playground*
"""

        if output:
            with open(output, "w") as f:
                f.write(output_content)
            click.echo(f"✅ Exported {prompt_name} to {output}")
        else:
            click.echo(output_content)

    except Exception as e:
        click.echo(f"❌ Export failed: {e}")
        sys.exit(1)


@langsmith.command()
@click.argument("prompt_name")
@click.argument("optimized_file")
def import_optimized(prompt_name: str, optimized_file: str):
    """Import an optimized prompt from LangSmith back to the system."""
    try:
        # Read optimized prompt
        with open(optimized_file, "r") as f:
            optimized_content = f.read().strip()

        # Load current config
        loader = get_prompt_loader_sync()
        config = loader.load_prompt_sync(prompt_name)

        # Convert LangSmith variables {{var}} back to LangGraph format {var}
        import re

        langgraph_content = re.sub(r"\{\{([^{}]+)\}\}", r"{\1}", optimized_content)

        # Update the prompt content
        config.prompt.system = langgraph_content

        # Bump version
        version_parts = config.metadata.version.split(".")
        patch_version = int(version_parts[2]) + 1
        config.metadata.version = (
            f"{version_parts[0]}.{version_parts[1]}.{patch_version}"
        )

        # Add changelog entry
        from datetime import datetime
        from athlea_langgraph.utils.prompt_models import ChangelogEntry

        config.metadata.changelog.append(
            ChangelogEntry(
                version=config.metadata.version,
                date=datetime.now().isoformat(),
                changes="Imported optimized version from LangSmith",
                author="LangSmith Import",
            )
        )

        # Save updated config
        saved_path = loader.save_prompt(config, prompt_name)
        click.echo(f"✅ Imported optimized {prompt_name} v{config.metadata.version}")
        click.echo(f"📁 Saved to: {saved_path}")

    except Exception as e:
        click.echo(f"❌ Error importing optimized prompt: {e}", err=True)
        sys.exit(1)


@langsmith.command()
@click.argument("prompt_name")
def validate_conversion(prompt_name: str):
    """Test variable conversion between LangGraph and LangSmith formats."""
    try:
        loader = get_prompt_loader_sync()
        config = loader.load_prompt_sync(prompt_name)

        original = config.prompt.system

        # Convert to LangSmith
        import re

        langsmith_format = re.sub(r"\{([^{}]+)\}", r"{{\1}}", original)

        # Convert back to LangGraph
        converted_back = re.sub(r"\{\{([^{}]+)\}\}", r"{\1}", langsmith_format)

        click.echo("=== Original LangGraph Format ===")
        click.echo(original[:200] + "..." if len(original) > 200 else original)
        click.echo("\n=== LangSmith Format ===")
        click.echo(
            langsmith_format[:200] + "..."
            if len(langsmith_format) > 200
            else langsmith_format
        )
        click.echo("\n=== Converted Back ===")
        click.echo(
            converted_back[:200] + "..."
            if len(converted_back) > 200
            else converted_back
        )

        if original == converted_back:
            click.echo("\n✅ Conversion is reversible - no data loss")
        else:
            click.echo("\n⚠️ Conversion may have issues - check manually")

    except Exception as e:
        click.echo(f"❌ Error validating conversion: {e}", err=True)
        sys.exit(1)


@langsmith.command()
@click.option(
    "--force", is_flag=True, help="Force upload even if prompts already exist"
)
def sync(force: bool):
    """Manually sync all local prompts to LangSmith."""
    try:
        from athlea_langgraph.services.langsmith_prompt_sync import get_prompt_syncer

        click.echo("🔄 Syncing Local Prompts to LangSmith")
        click.echo("=" * 40)

        syncer = get_prompt_syncer()

        # Check if LangSmith is configured
        if not syncer._get_client():
            click.echo("❌ LangSmith not configured (LANGSMITH_API_KEY missing)")
            sys.exit(1)

        click.echo(f"📤 Uploading prompts (force={'Yes' if force else 'No'})...")

        results = syncer.sync_all_prompts_sync(force=force)

        click.echo(f"\n📊 Sync Results:")
        click.echo(f"   ✅ Uploaded: {results['uploaded']}")
        click.echo(f"   ⏭️ Skipped: {results['skipped']}")
        click.echo(f"   ❌ Failed: {results['failed']}")

        if results["uploaded"] > 0:
            click.echo(f"\n🎉 Successfully uploaded {results['uploaded']} prompts!")
            click.echo("🌐 View them at: https://smith.langchain.com/prompts")

    except Exception as e:
        click.echo(f"❌ Sync failed: {e}")
        sys.exit(1)


@langsmith.command()
def status():
    """Check LangSmith sync status and configuration."""
    try:
        import asyncio
        from athlea_langgraph.services.langsmith_prompt_sync import get_prompt_syncer
        from athlea_langgraph.utils.prompt_loader import get_prompt_loader_sync

        syncer = get_prompt_syncer()

        click.echo("📝 LangSmith Prompt Sync Status")
        click.echo("=" * 40)

        # Check configuration
        api_key = os.getenv("LANGSMITH_API_KEY")
        project = os.getenv("LANGCHAIN_PROJECT", "athlea-coaching")

        click.echo(f"API Key: {'✅ Configured' if api_key else '❌ Missing'}")
        click.echo(f"Project: {project}")
        # Check if sync is enabled based on client availability
        has_client = syncer._get_client() is not None
        click.echo(f"Sync Enabled: {'✅ Yes' if has_client else '❌ No'}")

        if has_client:
            click.echo(
                f"LangSmith URL: https://smith.langchain.com/projects/p/{project}/prompts"
            )

        # Count local prompts
        try:
            import asyncio
            from athlea_langgraph.utils.prompt_loader import get_prompt_loader

            # Run async code properly
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def get_prompt_info():
                loader = await get_prompt_loader("athlea_langgraph/prompts")
                prompts = await loader.list_prompts()
                return prompts

            prompts = loop.run_until_complete(get_prompt_info())
            loop.close()

            click.echo(f"Local Prompts: {len(prompts)} found")

            # Group by type
            coach_prompts = [p for p in prompts if "coach" in p.lower()]
            system_prompts = [
                p for p in prompts if "system" in p.lower() or "reasoning" in p.lower()
            ]
            other_prompts = [
                p for p in prompts if p not in coach_prompts + system_prompts
            ]

            click.echo(f"  - Coach prompts: {len(coach_prompts)}")
            click.echo(f"  - System prompts: {len(system_prompts)}")
            click.echo(f"  - Other prompts: {len(other_prompts)}")

        except Exception as e:
            click.echo(f"❌ Error loading local prompts: {e}")

    except Exception as e:
        click.echo(f"❌ Status check failed: {e}", err=True)
        sys.exit(1)


@langsmith.command()
@click.option(
    "--private-only",
    is_flag=True,
    help="Delete only private prompts (keep public ones)",
)
@click.option("--force", is_flag=True, help="Skip confirmation prompts")
def clear_online(private_only: bool, force: bool):
    """Clear prompts from LangSmith's online interface (NOT local files)."""
    try:
        from langsmith import Client

        click.echo("🗑️  LangSmith Online Prompt Cleanup")
        click.echo("=" * 40)
        click.echo("⚠️  This deletes prompts from LangSmith online")
        click.echo("   (Your local files will NOT be touched)")
        click.echo()

        # Check configuration
        api_key = os.getenv("LANGSMITH_API_KEY")
        if not api_key:
            click.echo("❌ LANGSMITH_API_KEY not set")
            click.echo("Get your key from: https://smith.langchain.com/settings")
            sys.exit(1)

        try:
            client = Client()
        except Exception as e:
            click.echo(f"❌ Error connecting to LangSmith: {e}")
            sys.exit(1)

        # List prompts
        click.echo("🔍 Fetching prompts from LangSmith...")
        # LangSmith list_prompts() returns an iterator with two items:
        # 1. ('repos', [list of prompts])
        # 2. ('total', total_count)
        all_prompts = []

        for result in client.list_prompts():
            if isinstance(result, tuple) and len(result) == 2:
                key, value = result
                if key == "repos" and isinstance(value, list):
                    # This is the list of prompts
                    all_prompts.extend(value)
                elif key == "total":
                    # This is just the total count
                    click.echo(f"📊 Total prompts available: {value}")

        prompts = all_prompts

        if not prompts:
            click.echo("ℹ️  No prompts found in LangSmith workspace.")
            return

        # Filter prompts
        if private_only:
            prompts_to_delete = [p for p in prompts if not p.is_public]
            operation_text = f"private prompts"
        else:
            prompts_to_delete = prompts
            operation_text = f"ALL prompts"

        if not prompts_to_delete:
            click.echo(f"ℹ️  No {operation_text} found to delete.")
            return

        click.echo(f"📊 Found {len(prompts_to_delete)} {operation_text} to delete:")

        # Show prompts
        for i, prompt in enumerate(prompts_to_delete[:5], 1):
            prompt_name = (
                prompt.repo_handle if hasattr(prompt, "repo_handle") else "Unknown"
            )
            visibility = (
                "🌍 Public"
                if (hasattr(prompt, "is_public") and prompt.is_public)
                else "🔒 Private"
            )
            click.echo(f"   {i}. {prompt_name} ({visibility})")

        if len(prompts_to_delete) > 5:
            click.echo(f"   ... and {len(prompts_to_delete) - 5} more")

        # Confirmation
        if not force:
            click.echo(
                f"\n🚨 WARNING: This will permanently delete {len(prompts_to_delete)} prompts from LangSmith!"
            )
            if not click.confirm("Continue?", abort=True):
                return

        # Delete prompts
        click.echo(f"\n🚀 Deleting {len(prompts_to_delete)} prompts...")

        deleted_count = 0
        failed_count = 0

        for prompt in prompts_to_delete:
            prompt_name = (
                prompt.repo_handle if hasattr(prompt, "repo_handle") else "Unknown"
            )

            try:
                client.delete_prompt(prompt_name)
                deleted_count += 1
                click.echo(f"   ✅ {prompt_name}")
            except Exception as e:
                failed_count += 1
                click.echo(f"   ❌ {prompt_name}: {e}")

        # Summary
        click.echo(f"\n✅ Completed!")
        click.echo(f"   🗑️  Deleted: {deleted_count}")
        if failed_count > 0:
            click.echo(f"   ❌ Failed: {failed_count}")

        click.echo("\n🌐 Check: https://smith.langchain.com/prompts")

    except ImportError:
        click.echo(
            "❌ langsmith package not found. Install with: pip install langsmith"
        )
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Error: {e}")
        sys.exit(1)


@langsmith.command()
def list_online():
    """List all prompts currently in LangSmith's online interface."""
    try:
        from langsmith import Client

        # Check configuration
        api_key = os.getenv("LANGSMITH_API_KEY")
        if not api_key:
            click.echo("❌ LANGSMITH_API_KEY not set")
            sys.exit(1)

        client = Client()

        # List prompts
        click.echo("🔍 Fetching prompts from LangSmith...")
        # LangSmith list_prompts() returns an iterator with two items:
        # 1. ('repos', [list of prompts])
        # 2. ('total', total_count)
        all_prompts = []

        for result in client.list_prompts():
            if isinstance(result, tuple) and len(result) == 2:
                key, value = result
                if key == "repos" and isinstance(value, list):
                    # This is the list of prompts
                    all_prompts.extend(value)
                elif key == "total":
                    # This is just the total count
                    click.echo(f"📊 Total prompts available: {value}")

        prompts = all_prompts

        if not prompts:
            click.echo("ℹ️  No prompts found in LangSmith workspace.")
            return

        click.echo(f"\n📊 Found {len(prompts)} prompts in LangSmith:")
        click.echo("=" * 50)

        public_count = 0
        private_count = 0

        for i, prompt in enumerate(prompts, 1):
            prompt_name = (
                prompt.repo_handle if hasattr(prompt, "repo_handle") else "Unknown"
            )
            is_public = prompt.is_public if hasattr(prompt, "is_public") else False
            created_at = (
                prompt.created_at.strftime("%Y-%m-%d")
                if hasattr(prompt, "created_at") and prompt.created_at
                else "Unknown"
            )

            if is_public:
                public_count += 1
                visibility = "🌍 Public"
            else:
                private_count += 1
                visibility = "🔒 Private"

            click.echo(f"{i:3d}. {prompt_name}")
            click.echo(f"     {visibility} | Created: {created_at}")

            if hasattr(prompt, "description") and prompt.description:
                description = (
                    prompt.description[:60] + "..."
                    if len(prompt.description) > 60
                    else prompt.description
                )
                click.echo(f"     Description: {description}")
            click.echo()

        # Summary
        click.echo("=" * 50)
        click.echo(f"📈 Summary:")
        click.echo(f"   Total: {len(prompts)} prompts")
        click.echo(f"   🌍 Public: {public_count}")
        click.echo(f"   🔒 Private: {private_count}")
        click.echo(f"\n🌐 View online: https://smith.langchain.com/prompts")

    except ImportError:
        click.echo(
            "❌ langsmith package not found. Install with: pip install langsmith"
        )
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    langsmith()
