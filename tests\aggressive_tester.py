import asyncio
import json
import logging
import re
import platform
import sys
from athlea_langgraph.graphs.optimized_comprehensive_coaching_graph import (
    create_optimized_test_graph,
)
from athlea_langgraph.states.optimized_state import OptimizedCoachingState
from langchain_core.messages import HumanMessage, ToolMessage, AIMessage

# Windows-specific asyncio policy fix
if platform.system() == "Windows":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# --- Test Scenarios ---
TEST_CASES = [
    {
        "test_name": "Nutrition: Full-Context Tool Call",
        "user_query": "I am a 30 year old male, 180cm tall, weigh 80kg, and I'm moderately active. I want to maintain my weight. Please calculate my daily calories.",
        "expected_routing": "nutrition_coach",
        "expected_tool": "calculate_daily_calories",
    },
    {
        "test_name": "Strength: Simple Tool Call",
        "user_query": "I need some beginner exercises for my chest.",
        "expected_routing": "strength_coach",
        "expected_tool": "search_strength_exercises",
    },
    {
        "test_name": "Cardio: Research with Knowledge Base",
        "user_query": "What does the latest science say about the benefits of high-intensity interval training (HIIT) for cardiovascular health?",
        "expected_routing": "cardio_coach",
        "expected_tool": "graphrag_search",
    },
    {
        "test_name": "Recovery: Clarification Question (No Tool)",
        "user_query": "I feel sore.",
        "expected_routing": "recovery_coach",
        "expected_tool": None,
        "expected_behavior": "Ask for more details about the soreness.",
    },
    {
        "test_name": "Mental: Research Request",
        "user_query": "Are there any good mindfulness apps recommended by major tech review sites this year?",
        "expected_routing": "mental_coach",
        "expected_tool": "graphrag_search",
    },
    {
        "test_name": "Multi-Coach: Strength and Nutrition Aggregation",
        "user_query": "I am a tennis player and I want to improve my strength and also need nutrition advice.",
        "expected_routing": "multi_coach",
        "expected_coaches": ["strength", "nutrition"],
        "expected_tool": None,
        "expected_behavior": "A single, synthesized response combining advice from both strength and nutrition coaches.",
    },
]


async def run_single_test(graph, test_case):
    """Runs a single test case and analyzes the output."""
    test_name = test_case["test_name"]
    user_query = test_case["user_query"]

    logger.info(f"--- Running Test: {test_name} ---")
    logger.info(f"Query: '{user_query}'")

    thread_id = f"test_{test_name.replace(' ', '_').lower()}"
    config = {"configurable": {"thread_id": thread_id}}

    try:
        final_state = await graph.ainvoke(
            {
                "user_query": user_query,
                "messages": [],
                "execution_steps": [],
                "config": config,
            },
            config=config,
        )

        # Extract data from final state with comprehensive error handling
        final_response = final_state.get("final_response", "")
        aggregated_response = final_state.get("aggregated_response", "")
        routing_decision = final_state.get("routing_decision", "")
        primary_coach = final_state.get("primary_coach", "unknown")
        required_coaches = final_state.get("required_coaches", [])
        coach_responses = final_state.get("coach_responses", {})
        messages = final_state.get("messages", [])
        execution_steps = final_state.get("execution_steps", [])
        
        # FIX: Extract clarification response directly from messages if the route was clarification
        clarification_response = ""
        if routing_decision == "clarification":
            # The last message is often the clarification question
            if messages and hasattr(messages[-1], 'content'):
                clarification_response = messages[-1].content

        # DEBUG: Print comprehensive state info for debugging
        if test_case["expected_routing"] == "multi_coach" or test_case.get("expected_behavior"):
            print(f"\n🐛 DEBUG - State inspection for {test_name}:")
            print(f"  - final_state keys: {list(final_state.keys())}")
            print(f"  - routing_decision: {routing_decision}")
            print(f"  - primary_coach: {primary_coach}")
            print(f"  - required_coaches: {required_coaches}")
            print(f"  - coach_responses keys: {list(coach_responses.keys())}")
            print(f"  - final_response length: {len(final_response) if final_response else 0}")
            print(f"  - aggregated_response length: {len(aggregated_response) if aggregated_response else 0}")
            print(f"  - clarification_response length: {len(clarification_response) if clarification_response else 0}")
            print(f"  - execution_steps: {execution_steps}")

        # Extract tool calls from messages AND response content
        tool_calls = []
        tools_from_messages = []
        
        # Check for tool calls in message objects
        for msg in messages:
            if hasattr(msg, "tool_calls") and msg.tool_calls:
                for tc in msg.tool_calls:
                    tool_calls.append({"name": tc["name"], "args": tc["args"]})
                    tools_from_messages.append(tc["name"])

        # Extract tools mentioned in response content
        response_content = aggregated_response or final_response or clarification_response or ""
        tools_from_content = extract_tools_from_response(response_content)
        
        # Combine all detected tools
        all_tools = list(set(tools_from_messages + tools_from_content))

        print(f"\n--- ANALYSIS: {test_name} ---")
        print(f"EXPECTED ROUTE: {test_case['expected_routing']}")
        print(f"ACTUAL ROUTE: {primary_coach}")

        if test_case["expected_routing"] == "multi_coach":
            print(f"EXPECTED COACHES: {test_case['expected_coaches']}")
            print(f"ACTUAL REQUIRED COACHES: {required_coaches}")
            print(f"COACH RESPONSES CAPTURED: {list(coach_responses.keys())}")

        if test_case.get("expected_tool"):
            print(f"EXPECTED TOOL: {test_case['expected_tool']}")
        else:
            print(f"EXPECTED BEHAVIOR: {test_case.get('expected_behavior', 'N/A')}")

        if all_tools:
            print(f"ACTUAL TOOLS USED: {all_tools}")
            if tool_calls:
                print(f"  - Tool call details: {tool_calls}")
        else:
            print("ACTUAL TOOLS USED: None")

        # Display response content with priority: aggregated > clarification > final
        displayed_response = ""
        if aggregated_response:
            displayed_response = aggregated_response
            print(f"\nAGGREGATED RESPONSE:\n{'-'*20}\n{aggregated_response}\n{'-'*20}\n")
        elif clarification_response:
            displayed_response = clarification_response
            print(f"\nCLARIFICATION RESPONSE:\n{'-'*20}\n{clarification_response}\n{'-'*20}\n")
        elif final_response:
            displayed_response = final_response
            print(f"\nFINAL RESPONSE:\n{'-'*20}\n{final_response}\n{'-'*20}\n")
        else:
            print("\nNO RESPONSE RETURNED\n")

        # Analyze the test result with corrected logic
        success = analyze_test_result(
            test_case,
            final_state,
            all_tools,
            displayed_response
        )
        
        print("-" * 40 + "\n")
        return success
        
    except Exception as e:
        logger.error(f"Exception in test '{test_name}': {e}")
        print(f"❌ EXCEPTION in {test_name}: {e}")
        return False


def analyze_test_result(test_case, final_state, detected_tools, response_content):
    """Analyze the test result and determine if it passed."""
    try:
        test_name = test_case["test_name"]
        expected_route = test_case["expected_routing"]
        expected_tool = test_case.get("expected_tool")
        expected_behavior = test_case.get("expected_behavior")
        
        # Extract actual routing information from state
        primary_coach = final_state.get("primary_coach", "unknown")
        required_coaches = final_state.get("required_coaches", [])
        coach_responses = final_state.get("coach_responses", {})
        routing_decision = final_state.get("routing_decision", "")
        
        # Improved route determination logic
        if expected_route == "multi_coach":
            # For multi-coach, check if multiple coaches were used or routing decision indicates multi-coach
            actual_route = "multi_coach" if (len(required_coaches) > 1 or routing_decision == "multi_coach") else primary_coach
        else:
            # For single coach, use primary_coach but also check routing_decision
            actual_route = routing_decision if routing_decision and routing_decision != "unknown" else primary_coach

        # Route matching logic with improved flexibility
        route_match = False
        if expected_route == "multi_coach":
            route_match = (len(required_coaches) > 1 or routing_decision == "multi_coach")
        else:
            # Check both primary_coach and routing_decision for single coach routes
            route_match = (expected_route in str(actual_route) or 
                          expected_route in str(primary_coach) or
                          expected_route in str(routing_decision))

        # Tool matching logic
        tool_match = True  # Default to true if no tool expected
        if expected_tool:
            tool_match = expected_tool in detected_tools

        # Special handling for clarification tests with improved detection
        if expected_behavior and ("clarification" in expected_behavior.lower() or "Ask for more details" in expected_behavior):
            # Check if response contains questions or requests for more info
            question_indicators = ["?", "what", "where", "when", "how", "which", "tell me more", "please provide", "could you", "can you tell", "would you", "more information", "details"]
            is_clarification = any(indicator in response_content.lower() for indicator in question_indicators)
            
            # Also check if we have a response at all (clarification should always provide a response)
            has_response = bool(response_content and response_content.strip())
            
            success = has_response and is_clarification and not detected_tools
            verdict = "✅ PASS" if success else "❌ FAIL"
            
            print(f"EXPECTED ROUTE: {expected_route}")
            print(f"ACTUAL ROUTE: {actual_route}")
            print(f"EXPECTED BEHAVIOR: {expected_behavior}")
            print(f"ACTUAL TOOLS USED: {detected_tools if detected_tools else 'None'}")
            print(f"HAS RESPONSE: {has_response}")
            print(f"CLARIFICATION DETECTED: {is_clarification}")
            print(f"{verdict} - {'Clarification behavior matches expectations' if success else 'Expected clarification behavior not found'}")
            
            if not success:
                if not has_response:
                    print(f"  - Missing response: Expected clarification response but got none")
                if not is_clarification:
                    print(f"  - Not clarification: Response doesn't contain clarifying questions")
                if detected_tools:
                    print(f"  - Unexpected tools: Clarification shouldn't use tools but got {detected_tools}")
            
            return success

        # Multi-coach specific validation with enhanced logic
        if expected_route == "multi_coach":
            expected_coaches = test_case.get("expected_coaches", [])
            coaches_match = all(coach in [c.replace("_coach", "") for c in required_coaches] for coach in expected_coaches)
            
            # Check if responses were properly collected - be more lenient
            aggregated_response = final_state.get("aggregated_response", "")
            final_response = final_state.get("final_response", "")
            
            # Enhanced response checking
            has_aggregated_response = bool(aggregated_response and aggregated_response.strip())
            has_final_response = bool(final_response and final_response.strip())
            has_coach_responses = len(coach_responses) > 0
            
            # Check if aggregation was attempted (look for aggregation in execution steps)
            execution_steps = final_state.get("execution_steps", [])
            aggregation_attempted = any("aggregation" in str(step).lower() for step in execution_steps)
            
            # Consider it successful if we have evidence of multi-coach execution and some form of response
            coaches_executed = len(required_coaches) >= len(expected_coaches)
            has_response = has_aggregated_response or has_final_response or has_coach_responses
            
            success = route_match and coaches_match and coaches_executed and has_response
            verdict = "✅ PASS" if success else "❌ FAIL"
            
            print(f"EXPECTED ROUTE: {expected_route}")
            print(f"ACTUAL ROUTE: {actual_route}")
            print(f"EXPECTED COACHES: {expected_coaches}")
            print(f"ACTUAL COACHES: {[c.replace('_coach', '') for c in required_coaches]}")
            print(f"COACHES MATCH: {coaches_match}")
            print(f"COACHES EXECUTED: {coaches_executed}")
            print(f"HAS AGGREGATED RESPONSE: {has_aggregated_response}")
            print(f"HAS FINAL RESPONSE: {has_final_response}")
            print(f"AGGREGATION ATTEMPTED: {aggregation_attempted}")
            print(f"{verdict} - {'Multi-coach workflow successful' if success else 'Multi-coach workflow failed'}")
            
            if not success:
                if not route_match:
                    print(f"  - Route issue: Expected multi-coach but got {actual_route}")
                if not coaches_match:
                    print(f"  - Coach mismatch: Expected {expected_coaches}, got {[c.replace('_coach', '') for c in required_coaches]}")
                if not coaches_executed:
                    print(f"  - Execution issue: Not enough coaches executed")
                if not has_response:
                    print(f"  - Response issue: No response found in any expected location")
            
            return success

        # Regular single-coach tests
        if expected_tool:
            success = route_match and tool_match
            verdict = "✅ PASS" if success else "❌ FAIL"
            
            print(f"EXPECTED ROUTE: {expected_route}")
            print(f"ACTUAL ROUTE: {actual_route}")
            print(f"EXPECTED TOOL: {expected_tool}")
            print(f"ACTUAL TOOLS USED: {detected_tools if detected_tools else 'None'}")
            print(f"ROUTE MATCH: {route_match}")
            print(f"TOOL MATCH: {tool_match}")
            print(f"{verdict} - {'All expectations met' if success else 'Expectations not met'}")
            
            if not success:
                if not route_match:
                    print(f"  - Route Mismatch: Expected {expected_route}, got {actual_route}")
                if not tool_match:
                    print(f"  - Tool Mismatch: Expected {expected_tool}, but got {detected_tools}")
            
            return success
        else:
            # No specific tool expected, just check routing
            success = route_match
            verdict = "✅ PASS" if success else "❌ FAIL"
            print(f"EXPECTED ROUTE: {expected_route}")
            print(f"ACTUAL ROUTE: {actual_route}")
            print(f"ROUTE MATCH: {route_match}")
            print(f"{verdict} - {'Route matches expectation' if success else 'Route mismatch'}")
            return success

    except Exception as e:
        print(f"❌ ANALYSIS ERROR: {e}")
        logger.exception("Error in analyze_test_result")
        return False


def extract_tools_from_response(response_text):
    """Extract tools that were actually called from the response text."""
    if not response_text:
        return []
        
    tools_used = []

    # More precise tool usage patterns - avoiding false positives
    tool_patterns = [
        r"Used ([\w_]+):",  # "Used graphrag_search:" pattern
        r"Tool ([\w_]+)\s",  # "Tool graphrag_search " pattern (with space)
        r"called ([\w_]+_[\w_]+)",  # "called graphrag_search" pattern - requires underscore
        r"calling ([\w_]+_[\w_]+)",  # "calling graphrag_search" pattern - requires underscore
        r"([\w_]+_[\w_]+) tool",  # "graphrag_search tool" pattern - requires underscore
        r"tool ([\w_]+_[\w_]+)\b",  # "tool graphrag_search" pattern - requires underscore and word boundary
        r"executed ([\w_]+_[\w_]+)",  # "executed graphrag_search" pattern
        r"using ([\w_]+_[\w_]+)",  # "using graphrag_search" pattern
    ]

    for pattern in tool_patterns:
        matches = re.findall(pattern, response_text, re.IGNORECASE)
        tools_used.extend(matches)

    # Remove duplicates and filter out common false positives
    unique_tools = []
    # Common words that shouldn't be considered tools
    excluded_words = {'in', 'on', 'at', 'to', 'for', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'research', 'findings', 'data', 'information', 'results'}
    
    for tool in tools_used:
        if tool not in unique_tools and tool.lower() not in excluded_words and len(tool) > 2:
            unique_tools.append(tool)

    return unique_tools


async def main():
    """Main function to run all aggressive tests."""
    logger.info("🚀 Starting Aggressive Test Suite for Optimized Coaching Graph 🚀")
    
    try:
        graph = await create_optimized_test_graph(config={"enable_memory": True})
        
        passed_tests = 0
        total_tests = len(TEST_CASES)
        
        for test_case in TEST_CASES:
            try:
                success = await run_single_test(graph, test_case)
                if success:
                    passed_tests += 1
            except Exception as e:
                logger.error(f"Test '{test_case['test_name']}' failed with exception: {e}")
                print(f"❌ EXCEPTION in {test_case['test_name']}: {e}")
        
        print(f"\n🏁 TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
        logger.info("✅ Aggressive Test Suite Completed.")
        
    except Exception as e:
        logger.error(f"Failed to create test graph: {e}")
        print(f"❌ CRITICAL ERROR: Could not initialize test graph: {e}")


if __name__ == "__main__":
    # Ensure graceful shutdown of the event loop
    try:
        asyncio.run(main())
    except (KeyboardInterrupt, SystemExit):
        logger.info("Test suite interrupted by user.")
    finally:
        logger.info("Test suite finished.")
