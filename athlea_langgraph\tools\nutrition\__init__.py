"""
Nutrition Domain Tools

This module contains all tools specific to nutrition planning, macro calculation,
and dietary coaching. Tools are organized to support nutrition-focused coaching agents.

Available Tools:
- meal_plan_tool: Comprehensive meal plan generation with detailed nutritional information
- recipe_recommendation_tool: Recipe search and recommendations using Edamam API
- calorie_calculator: Caloric needs and macro calculation
- nutrition_assessment: Dietary analysis and recommendations

LangChain Tool Wrappers:
- calculate_daily_calories: Calculate daily caloric needs
- calculate_macro_targets: Calculate macro targets
- generate_meal_plan: Generate comprehensive meal plans
- search_recipes: Search for recipes with filters
- get_recipe_recommendations: Get personalized recipe recommendations
"""

from .calorie_calculator import (
    NutritionCalorieCalculator,
    calculate_daily_calories,
    calculate_macro_targets,
)
from .meal_plan_tool import NutritionMealPlanTool
from .nutrition_assessment import NutritionAssessmentTool
from .recipe_recommendation_tool import NutritionRecipeRecommendationTool

# Import LangChain wrappers from other tools
try:
    from .meal_plan_tool import generate_meal_plan
except ImportError:
    generate_meal_plan = None

try:
    from .recipe_recommendation_tool import get_recipe_recommendations, search_recipes
except ImportError:
    search_recipes = None
    get_recipe_recommendations = None

__all__ = [
    "NutritionMealPlanTool",
    "NutritionRecipeRecommendationTool",
    "NutritionCalorieCalculator",
    "NutritionAssessmentTool",
    "calculate_daily_calories",
    "calculate_macro_targets",
]
# Add optional LangChain tools if available
if generate_meal_plan:
    __all__.append("generate_meal_plan")
if search_recipes:
    __all__.append("search_recipes")
if get_recipe_recommendations:
    __all__.append("get_recipe_recommendations")
