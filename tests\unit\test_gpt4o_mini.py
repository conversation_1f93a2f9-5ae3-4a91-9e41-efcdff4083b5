#!/usr/bin/env python3
"""
Test GPT-4o-mini Configuration

This script tests the updated Azure OpenAI configuration with GPT-4o-mini
to verify tool calling and streaming work correctly.
"""

import asyncio
import json

from langchain_core.messages import HumanMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.tools.external.airtable_mcp import get_airtable_langchain_tool


async def test_gpt4o_mini():
    """Test GPT-4o-mini with tool calling."""
    print("🧪 Testing GPT-4o-mini Configuration")
    print("=" * 60)

    try:
        # Create LLM with new configuration (streaming should be True by default)
        llm = create_azure_chat_openai(temperature=0.7, max_tokens=1000)

        # Test basic functionality first
        print("🚀 Testing basic LLM functionality...")
        basic_response = await llm.ainvoke(
            [HumanMessage(content="Hello! Can you tell me about strength training?")]
        )

        print(f"✅ Basic response received: {type(basic_response)}")
        print(f"📝 Content preview: {basic_response.content[:200]}...")

        # Test streaming functionality
        print("\n🌊 Testing streaming functionality...")
        streaming_llm = create_azure_chat_openai(
            temperature=0.7, max_tokens=500, streaming=True
        )

        print("🚀 Testing streaming response...")
        chunks = []
        async for chunk in streaming_llm.astream(
            [HumanMessage(content="Explain the benefits of deadlifts in 2 sentences.")]
        ):
            if chunk.content:
                chunks.append(chunk.content)
                print(f"📦 Chunk: {chunk.content}", end="", flush=True)

        print(f"\n✅ Streaming test completed. Received {len(chunks)} chunks.")

        # Now test with tools
        print("\n🔧 Testing tool binding...")
        airtable_tool = get_airtable_langchain_tool()
        llm_with_tools = llm.bind_tools([airtable_tool])

        # Test message that should trigger a tool call
        messages = [
            SystemMessage(
                content="You are a helpful fitness assistant with access to tools."
            ),
            HumanMessage(content="Search for deadlift exercises in the database"),
        ]

        print("🚀 Calling GPT-4o-mini with tool binding...")
        response = await llm_with_tools.ainvoke(messages)

        print(f"✅ Tool response received: {type(response)}")
        print(f"📝 Response content: {response.content}")

        # Examine the response structure
        print("\n🔍 Response Structure Analysis:")
        print(f"  - Type: {type(response)}")
        print(f"  - Has content: {hasattr(response, 'content')}")
        print(f"  - Has tool_calls: {hasattr(response, 'tool_calls')}")
        print(f"  - Has additional_kwargs: {hasattr(response, 'additional_kwargs')}")

        if hasattr(response, "tool_calls") and response.tool_calls:
            print(f"  - tool_calls count: {len(response.tool_calls)}")
            for i, tc in enumerate(response.tool_calls):
                print(f"    Tool {i+1}: {tc['name']} (ID: {tc['id']})")
                print(f"    Args: {tc['args']}")

        if hasattr(response, "additional_kwargs"):
            print(
                f"  - additional_kwargs keys: {list(response.additional_kwargs.keys())}"
            )

        # Test streaming with tools
        print("\n🌊🔧 Testing streaming with tool binding...")
        streaming_tool_llm = llm_with_tools

        tool_messages = [
            SystemMessage(
                content="You are a helpful fitness assistant. Use tools when needed."
            ),
            HumanMessage(
                content="Tell me about proper squat form and search for squat exercises."
            ),
        ]

        print("🚀 Testing streaming with tools...")
        tool_chunks = []
        async for chunk in streaming_tool_llm.astream(tool_messages):
            if chunk.content:
                tool_chunks.append(chunk.content)
                print(f"📦 Tool chunk: {chunk.content}", end="", flush=True)
            if hasattr(chunk, "tool_calls") and chunk.tool_calls:
                print(f"\n🔧 Tool call detected: {chunk.tool_calls[0]['name']}")

        print(
            f"\n✅ Streaming with tools completed. Received {len(tool_chunks)} content chunks."
        )

        print("\n✅ GPT-4o-mini configuration test completed successfully!")
        print("\n📊 Summary:")
        print("  ✅ Basic LLM functionality")
        print("  ✅ Streaming responses")
        print("  ✅ Tool binding and calling")
        print("  ✅ Streaming with tools")
        print("  ✅ Using GPT-4o-mini deployment")
        print("  ✅ Using new Azure endpoint")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_gpt4o_mini())
