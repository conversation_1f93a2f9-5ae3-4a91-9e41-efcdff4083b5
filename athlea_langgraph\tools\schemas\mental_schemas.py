"""
Mental Training Domain Schemas

Pydantic schemas for strict input/output validation of mental training tools.
Evidence-based schemas for mental health assessment and intervention tools.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


# Enums for mental training
class MoodState(str, Enum):
    """Mood state categories."""

    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


class StressLevel(str, Enum):
    """Stress level categories."""

    MINIMAL = "minimal"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    SEVERE = "severe"


class MentalHealthDomain(str, Enum):
    """Mental health assessment domains."""

    MOOD = "mood"
    ANXIETY = "anxiety"
    STRESS = "stress"
    FOCUS = "focus"
    MOTIVATION = "motivation"
    CONFIDENCE = "confidence"
    SLEEP = "sleep"
    ENERGY = "energy"


class GoalCategory(str, Enum):
    """Goal categories for tracking."""

    MENTAL_HEALTH = "mental_health"
    STRESS_MANAGEMENT = "stress_management"
    HABIT_FORMATION = "habit_formation"
    PERFORMANCE = "performance"
    WELLBEING = "wellbeing"
    MINDFULNESS = "mindfulness"


class GoalStatus(str, Enum):
    """Goal completion status."""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    PAUSED = "paused"
    ABANDONED = "abandoned"


# Mental State Assessment Schemas
class MentalStateAssessmentInput(BaseModel):
    """Input for comprehensive mental state assessment."""

    user_id: str = Field(..., description="Unique user identifier")

    # Core mental state indicators (1-10 scales)
    mood_rating: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current mood rating (1=very negative, 10=very positive)",
    )
    stress_level: int = Field(
        ..., ge=1, le=10, description="Current stress level (1=minimal, 10=severe)"
    )
    anxiety_level: int = Field(
        ..., ge=1, le=10, description="Current anxiety level (1=calm, 10=very anxious)"
    )
    energy_level: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current energy level (1=exhausted, 10=very energetic)",
    )
    focus_level: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current focus/concentration (1=scattered, 10=laser focused)",
    )
    motivation_level: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current motivation level (1=none, 10=extremely motivated)",
    )
    confidence_level: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current confidence level (1=very low, 10=very high)",
    )

    # Sleep and physical factors
    sleep_hours: float = Field(
        ..., ge=0, le=24, description="Hours of sleep last night"
    )
    sleep_quality: int = Field(
        ..., ge=1, le=10, description="Sleep quality rating (1=very poor, 10=excellent)"
    )

    # Contextual information
    current_stressors: List[str] = Field(
        default=[], description="Current sources of stress"
    )
    positive_factors: List[str] = Field(
        default=[], description="Current positive influences"
    )
    recent_activities: List[str] = Field(
        default=[], description="Activities in past 24 hours"
    )

    # Optional detailed assessment
    specific_concerns: Optional[List[str]] = Field(
        None, description="Specific mental health concerns"
    )
    medication_changes: Optional[bool] = Field(
        None, description="Recent medication changes"
    )
    life_changes: Optional[List[str]] = Field(
        None, description="Recent significant life changes"
    )


class MentalStateAssessmentOutput(BaseModel):
    """Output for mental state assessment analysis."""

    overall_mental_state_score: int = Field(
        ..., ge=1, le=100, description="Overall mental state score (1-100)"
    )
    primary_concerns: List[str] = Field(
        ..., description="Identified primary mental health concerns"
    )
    strengths: List[str] = Field(..., description="Identified mental health strengths")
    risk_factors: List[str] = Field(
        ..., description="Current risk factors for mental health"
    )
    protective_factors: List[str] = Field(..., description="Current protective factors")
    recommendations: List[str] = Field(
        ..., description="Personalized mental health recommendations"
    )
    immediate_actions: List[str] = Field(..., description="Immediate actions to take")
    follow_up_timeline: str = Field(
        ..., description="Recommended follow-up assessment timeline"
    )
    professional_referral_needed: bool = Field(
        ..., description="Whether professional mental health referral is recommended"
    )


# Stress Level Tracker Schemas
class StressTrackerInput(BaseModel):
    """Input for stress level tracking."""

    user_id: str = Field(..., description="Unique user identifier")
    stress_level: int = Field(
        ..., ge=1, le=10, description="Current stress level (1-10)"
    )
    stress_triggers: List[str] = Field(..., description="Identified stress triggers")
    stress_symptoms: List[str] = Field(
        default=[], description="Physical/mental stress symptoms experienced"
    )
    coping_strategies_used: List[str] = Field(
        default=[], description="Coping strategies attempted"
    )
    context: Optional[str] = Field(
        None, description="Context or situation causing stress"
    )
    duration_hours: Optional[float] = Field(
        None, ge=0, description="How long stress has been present (hours)"
    )
    intensity_change: Optional[str] = Field(
        None, description="How stress level has changed (increasing/decreasing/stable)"
    )


class StressTrackerOutput(BaseModel):
    """Output for stress tracking analysis."""

    stress_category: StressLevel = Field(..., description="Categorized stress level")
    stress_trend: str = Field(..., description="Trend analysis of stress levels")
    trigger_patterns: List[str] = Field(
        ..., description="Identified stress trigger patterns"
    )
    effective_coping_strategies: List[str] = Field(
        ..., description="Most effective coping strategies identified"
    )
    stress_management_recommendations: List[str] = Field(
        ..., description="Personalized stress management recommendations"
    )
    warning_signs: List[str] = Field(..., description="Warning signs to monitor")
    immediate_relief_techniques: List[str] = Field(
        ..., description="Immediate stress relief techniques"
    )
    long_term_strategies: List[str] = Field(
        ..., description="Long-term stress management strategies"
    )


# Mood Pattern Analyzer Schemas
class MoodEntryInput(BaseModel):
    """Input for single mood tracking entry."""

    user_id: str = Field(..., description="Unique user identifier")
    mood_rating: int = Field(
        ..., ge=1, le=10, description="Mood rating (1=very negative, 10=very positive)"
    )
    mood_descriptors: List[str] = Field(
        ..., description="Words describing current mood"
    )
    contributing_factors: List[str] = Field(
        default=[], description="Factors contributing to current mood"
    )
    activities_before: List[str] = Field(
        default=[], description="Activities in past 2-3 hours"
    )
    social_context: Optional[str] = Field(
        None, description="Social situation (alone, with others, etc.)"
    )
    location: Optional[str] = Field(None, description="Current location/environment")
    time_of_day: Optional[str] = Field(None, description="Time of day for mood entry")


class MoodPatternAnalysisInput(BaseModel):
    """Input for mood pattern analysis over time."""

    user_id: str = Field(..., description="Unique user identifier")
    analysis_period_days: int = Field(
        default=30, ge=7, le=365, description="Period for analysis in days"
    )
    include_correlations: bool = Field(
        default=True, description="Include correlation analysis with other factors"
    )


class MoodPatternAnalysisOutput(BaseModel):
    """Output for mood pattern analysis."""

    average_mood: float = Field(..., description="Average mood rating over period")
    mood_trend: str = Field(
        ..., description="Overall mood trend (improving/declining/stable)"
    )
    mood_volatility: str = Field(
        ..., description="Mood stability (stable/moderate/volatile)"
    )
    best_mood_days: List[str] = Field(..., description="Days with highest mood ratings")
    challenging_mood_days: List[str] = Field(
        ..., description="Days with lowest mood ratings"
    )
    mood_triggers_positive: List[str] = Field(
        ..., description="Factors associated with positive moods"
    )
    mood_triggers_negative: List[str] = Field(
        ..., description="Factors associated with negative moods"
    )
    time_patterns: Dict[str, Any] = Field(
        ..., description="Time-based mood patterns (time of day, day of week)"
    )
    activity_correlations: Dict[str, float] = Field(
        ..., description="Activities correlated with mood changes"
    )
    recommendations: List[str] = Field(
        ..., description="Personalized recommendations based on patterns"
    )
    insights: List[str] = Field(..., description="Key insights about mood patterns")


# Goal Setting & Tracking Schemas
class MentalHealthGoalInput(BaseModel):
    """Input for creating mental health goals."""

    user_id: str = Field(..., description="Unique user identifier")
    goal_title: str = Field(..., description="Clear, concise goal title")
    goal_description: str = Field(..., description="Detailed goal description")
    goal_category: GoalCategory = Field(..., description="Goal category")
    target_behavior: str = Field(..., description="Specific behavior to change/adopt")

    # SMART goal components
    specific_outcome: str = Field(..., description="Specific, measurable outcome")
    measurement_method: str = Field(..., description="How progress will be measured")
    target_completion_date: datetime = Field(..., description="Target completion date")
    difficulty_level: int = Field(
        ..., ge=1, le=10, description="Self-assessed difficulty level (1-10)"
    )

    # Implementation details
    daily_actions: List[str] = Field(..., description="Daily actions to achieve goal")
    weekly_milestones: List[str] = Field(
        default=[], description="Weekly milestone markers"
    )
    potential_obstacles: List[str] = Field(
        default=[], description="Anticipated obstacles"
    )
    support_strategies: List[str] = Field(
        default=[], description="Strategies for overcoming obstacles"
    )

    # Motivation and accountability
    motivation_reasons: List[str] = Field(
        ..., description="Reasons for pursuing this goal"
    )
    accountability_methods: List[str] = Field(
        default=[], description="Methods for staying accountable"
    )


class GoalProgressInput(BaseModel):
    """Input for tracking goal progress."""

    goal_id: str = Field(..., description="Unique goal identifier")
    user_id: str = Field(..., description="Unique user identifier")
    progress_percentage: int = Field(
        ..., ge=0, le=100, description="Current progress percentage (0-100)"
    )
    actions_completed: List[str] = Field(
        ..., description="Actions completed since last update"
    )
    challenges_encountered: List[str] = Field(
        default=[], description="Challenges or obstacles encountered"
    )
    strategies_used: List[str] = Field(
        default=[], description="Strategies used to overcome challenges"
    )
    mood_during_progress: int = Field(
        ..., ge=1, le=10, description="Mood while working on goal (1-10)"
    )
    motivation_level: int = Field(
        ..., ge=1, le=10, description="Current motivation level for this goal (1-10)"
    )
    next_steps: List[str] = Field(default=[], description="Planned next steps")

    # Optional reflection
    insights_learned: Optional[List[str]] = Field(
        None, description="Insights or lessons learned"
    )
    goal_adjustment_needed: Optional[bool] = Field(
        None, description="Whether goal needs adjustment"
    )


class GoalTrackingOutput(BaseModel):
    """Output for goal tracking analysis."""

    goal_status: GoalStatus = Field(..., description="Current goal status")
    progress_trend: str = Field(
        ..., description="Progress trend (accelerating/steady/slowing)"
    )
    completion_likelihood: str = Field(
        ..., description="Likelihood of completion (high/medium/low)"
    )
    time_to_completion_estimate: Optional[str] = Field(
        None, description="Estimated time to completion"
    )

    # Performance analysis
    consistency_score: int = Field(
        ..., ge=1, le=10, description="Consistency of effort score (1-10)"
    )
    most_effective_strategies: List[str] = Field(
        ..., description="Most effective strategies identified"
    )
    biggest_challenges: List[str] = Field(
        ..., description="Biggest challenges identified"
    )

    # Recommendations
    adjustment_recommendations: List[str] = Field(
        ..., description="Recommended goal or strategy adjustments"
    )
    motivation_boosters: List[str] = Field(
        ..., description="Strategies to boost motivation"
    )
    support_recommendations: List[str] = Field(
        ..., description="Additional support recommendations"
    )
    celebration_milestones: List[str] = Field(
        ..., description="Milestones worth celebrating"
    )


class GoalSummaryOutput(BaseModel):
    """Output for overall goal tracking summary."""

    total_goals: int = Field(..., description="Total number of goals")
    active_goals: int = Field(..., description="Number of active goals")
    completed_goals: int = Field(..., description="Number of completed goals")
    average_progress: float = Field(
        ..., description="Average progress across all goals"
    )
    goal_completion_rate: float = Field(
        ..., description="Historical goal completion rate"
    )

    most_successful_categories: List[str] = Field(
        ..., description="Goal categories with highest success rates"
    )
    improvement_areas: List[str] = Field(..., description="Areas needing improvement")
    overall_insights: List[str] = Field(
        ..., description="Overall insights about goal achievement patterns"
    )
    recommendations: List[str] = Field(
        ..., description="Overall recommendations for goal setting and achievement"
    )
