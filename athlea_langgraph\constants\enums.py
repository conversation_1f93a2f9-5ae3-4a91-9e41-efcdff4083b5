"""
General-purpose enums for the Athlea LangGraph system.
Domain-specific enums remain in their respective schema files.
"""

from enum import Enum


class IntensityLevel(str, Enum):
    """General intensity levels used across domains."""

    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


class TimeUnit(str, Enum):
    """Time units for durations and timeouts."""

    SECONDS = "seconds"
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"


class HTTPContentType(str, Enum):
    """HTTP content types."""

    APPLICATION_JSON = "application/json"
    TEXT_PLAIN = "text/plain"
    TEXT_HTML = "text/html"


class LogLevel(str, Enum):
    """Logging levels."""

    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class OperationStatus(str, Enum):
    """General operation status values."""

    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class QueryType(str, Enum):
    """Query types for search operations."""

    SIMPLE = "simple"
    FULL = "full"


class SortDirection(str, Enum):
    """Sort direction options."""

    ASC = "asc"
    DESC = "desc"


class ValidationMode(str, Enum):
    """Validation modes for Pydantic models."""

    BEFORE = "before"
    AFTER = "after"
