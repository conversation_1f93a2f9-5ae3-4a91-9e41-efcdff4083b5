"""
Memory Analytics Engine for System Monitoring and Performance Insights

Provides comprehensive analytics and monitoring for the memory system including:
- Real-time performance tracking
- Usage pattern analysis
- Health monitoring and alerting
- Optimization recommendations
"""

import asyncio
import json
import logging
import uuid
from collections import Counter, defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from .mem0_adapter import Mem0Adapter
from .schemas.analytics_schemas import (
    AnalyticsReport,
    DateRange,
    HealthStatus,
    HealthStatusLevel,
    MemoryAnalyticsEvent,
    MemoryOperationType,
    PerformanceMetrics,
    RetrievalMetrics,
    StorageMetrics,
)

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsConfig:
    """Configuration for memory analytics."""

    event_retention_days: int = 30  # How long to keep analytics events
    health_check_interval_minutes: int = 15  # Health check frequency
    alert_thresholds: Dict[str, float] = field(
        default_factory=lambda: {
            "error_rate": 0.05,  # 5% error rate threshold
            "avg_latency_ms": 2000,  # 2 second latency threshold
            "storage_growth_rate": 0.2,  # 20% daily growth threshold
        }
    )
    batch_size: int = 1000  # Events to process in one batch


class MemoryAnalyticsEngine:
    """
    Analytics engine for comprehensive memory system monitoring.

    Features:
    - Real-time event tracking
    - Performance metrics calculation
    - Health status monitoring
    - Usage pattern analysis
    - Automated alerting and recommendations
    """

    def __init__(self, mem0_adapter: Mem0Adapter):
        """
        Initialize analytics engine.

        Args:
            mem0_adapter: Configured mem0 adapter instance
        """
        self.mem0_adapter = mem0_adapter
        self.events_buffer = []  # In-memory buffer for recent events
        self.metrics_cache = {}  # Cache for computed metrics
        self.health_status = HealthStatus(
            status_level=HealthStatusLevel.HEALTHY,
            timestamp=datetime.now(),
        )
        self.config = AnalyticsConfig()

    async def track_event(
        self,
        user_id: str,
        operation_type: MemoryOperationType,
        duration_ms: float,
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
    ) -> str:
        """
        Track a memory operation event.

        Args:
            user_id: User identifier
            operation_type: Type of memory operation
            duration_ms: Operation duration in milliseconds
            success: Whether operation succeeded
            metadata: Additional event metadata
            error_message: Error message if operation failed

        Returns:
            Event ID
        """
        event_id = str(uuid.uuid4())
        event = MemoryAnalyticsEvent(
            event_id=event_id,
            user_id=user_id,
            operation_type=operation_type,
            timestamp=datetime.now(),
            duration_ms=duration_ms,
            success=success,
            metadata=metadata or {},
            error_message=error_message,
        )

        # Add to buffer
        self.events_buffer.append(event)

        # Process buffer if it's getting large
        if len(self.events_buffer) >= self.config.batch_size:
            await self._process_events_buffer()

        # Update real-time health status
        await self._update_health_status(event)

        logger.debug(
            f"Tracked {operation_type.value} event for user {user_id} (duration: {duration_ms:.2f}ms)"
        )
        return event_id

    async def generate_analytics_report(
        self,
        date_range: DateRange,
        user_id: Optional[str] = None,
    ) -> AnalyticsReport:
        """
        Generate comprehensive analytics report for a time period.

        Args:
            date_range: Date range for the report
            user_id: Optional user ID to filter by

        Returns:
            Comprehensive analytics report
        """
        logger.info(
            f"Generating analytics report for {date_range.start_date} to {date_range.end_date}"
        )

        # Get events for the date range
        events = await self._get_events_for_period(date_range, user_id)

        # Calculate metrics
        retrieval_metrics = await self._calculate_retrieval_metrics(events, user_id)
        storage_metrics = await self._calculate_storage_metrics(date_range, user_id)
        performance_metrics = await self._calculate_performance_metrics(events)

        # Analyze user engagement
        user_engagement = await self._analyze_user_engagement(events, date_range)

        # Generate recommendations
        recommendations = await self._generate_recommendations(
            retrieval_metrics, storage_metrics, performance_metrics
        )

        report = AnalyticsReport(
            start_time=date_range.start_date,
            end_time=date_range.end_date,
            retrieval_metrics=retrieval_metrics,
            storage_metrics=storage_metrics,
            performance_metrics=performance_metrics,
            user_engagement=user_engagement,
            recommendations=recommendations,
        )

        logger.info(f"Generated analytics report with {len(events)} events")
        return report

    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """
        Get real-time system metrics.

        Returns:
            Dictionary of current system metrics
        """
        current_time = datetime.now()

        # Get recent events (last hour)
        recent_events = [
            event
            for event in self.events_buffer
            if (current_time - event.timestamp).total_seconds() <= 3600
        ]

        # Calculate real-time metrics
        total_operations = len(recent_events)
        successful_operations = sum(1 for event in recent_events if event.success)

        if total_operations > 0:
            success_rate = successful_operations / total_operations
            error_rate = 1.0 - success_rate
            avg_latency = (
                sum(event.duration_ms for event in recent_events) / total_operations
            )
        else:
            success_rate = 1.0
            error_rate = 0.0
            avg_latency = 0.0

        # Operations per minute
        ops_per_minute = len(
            [
                event
                for event in recent_events
                if (current_time - event.timestamp).total_seconds() <= 60
            ]
        )

        return {
            "timestamp": current_time.isoformat(),
            "total_operations_last_hour": total_operations,
            "success_rate": success_rate,
            "error_rate": error_rate,
            "average_latency_ms": avg_latency,
            "operations_per_minute": ops_per_minute,
            "health_status": self.health_status.to_dict(),
        }

    async def analyze_user_patterns(
        self,
        user_id: str,
        days_back: int = 30,
    ) -> Dict[str, Any]:
        """
        Analyze usage patterns for a specific user.

        Args:
            user_id: User identifier
            days_back: Number of days to analyze

        Returns:
            User pattern analysis
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        date_range = DateRange(start_date, end_date)

        # Get user events
        user_events = await self._get_events_for_period(date_range, user_id)

        if not user_events:
            return {"error": "No events found for user"}

        # Analyze patterns
        operation_counts = Counter(event.operation_type.value for event in user_events)

        # Temporal patterns
        hourly_distribution = defaultdict(int)
        daily_distribution = defaultdict(int)

        for event in user_events:
            hour = event.timestamp.hour
            day = event.timestamp.strftime("%A")
            hourly_distribution[hour] += 1
            daily_distribution[day] += 1

        # Performance patterns
        avg_latencies = defaultdict(list)
        for event in user_events:
            avg_latencies[event.operation_type.value].append(event.duration_ms)

        avg_latency_by_operation = {
            op: sum(latencies) / len(latencies)
            for op, latencies in avg_latencies.items()
        }

        # Memory usage patterns
        memory_stats = await self._get_user_memory_stats(user_id)

        return {
            "user_id": user_id,
            "analysis_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_events": len(user_events),
            },
            "operation_patterns": dict(operation_counts),
            "temporal_patterns": {
                "hourly_distribution": dict(hourly_distribution),
                "daily_distribution": dict(daily_distribution),
                "most_active_hour": (
                    max(hourly_distribution.items(), key=lambda x: x[1])[0]
                    if hourly_distribution
                    else None
                ),
                "most_active_day": (
                    max(daily_distribution.items(), key=lambda x: x[1])[0]
                    if daily_distribution
                    else None
                ),
            },
            "performance_patterns": avg_latency_by_operation,
            "memory_usage": memory_stats,
        }

    async def get_system_health(self) -> HealthStatus:
        """
        Get current system health status.

        Returns:
            Current health status with alerts
        """
        current_time = datetime.now()

        # Update health status based on recent metrics
        await self._perform_health_check()

        # Add timestamps and component status
        self.health_status.timestamp = current_time

        return self.health_status

    async def get_optimization_recommendations(
        self,
        user_id: Optional[str] = None,
    ) -> List[str]:
        """
        Get system optimization recommendations.

        Args:
            user_id: Optional user ID for user-specific recommendations

        Returns:
            List of optimization recommendations
        """
        recommendations = []

        # Analyze recent performance
        recent_metrics = await self.get_real_time_metrics()

        # Performance recommendations
        if recent_metrics["error_rate"] > self.config.alert_thresholds["error_rate"]:
            recommendations.append(
                f"High error rate detected ({recent_metrics['error_rate']:.1%}). "
                "Consider investigating memory storage issues or network connectivity."
            )

        if (
            recent_metrics["average_latency_ms"]
            > self.config.alert_thresholds["avg_latency_ms"]
        ):
            recommendations.append(
                f"High average latency ({recent_metrics['average_latency_ms']:.0f}ms). "
                "Consider optimizing query complexity or adding caching."
            )

        # Storage recommendations
        if user_id:
            user_stats = await self._get_user_memory_stats(user_id)
            if user_stats.get("total_memories", 0) > 1000:
                recommendations.append(
                    f"User {user_id} has {user_stats['total_memories']} memories. "
                    "Consider running memory summarization or archival."
                )

        # System-wide recommendations
        total_events = len(self.events_buffer)
        if total_events > 5000:
            recommendations.append(
                "Large events buffer detected. Consider increasing batch processing frequency."
            )

        return recommendations

    async def export_analytics_data(
        self,
        date_range: DateRange,
        format: str = "json",
    ) -> str:
        """
        Export analytics data for external analysis.

        Args:
            date_range: Date range for export
            format: Export format ("json", "csv")

        Returns:
            Exported data as string
        """
        events = await self._get_events_for_period(date_range)

        if format == "json":
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "date_range": date_range.to_dict(),
                "total_events": len(events),
                "events": [event.to_dict() for event in events],
            }
            return json.dumps(export_data, indent=2)

        elif format == "csv":
            # Simple CSV export
            csv_lines = [
                "event_id,user_id,operation_type,timestamp,duration_ms,success,error_message"
            ]

            for event in events:
                csv_lines.append(
                    f"{event.event_id},{event.user_id},{event.operation_type.value},"
                    f"{event.timestamp.isoformat()},{event.duration_ms},{event.success},"
                    f'"{event.error_message or ""}"'
                )

            return "\n".join(csv_lines)

        else:
            raise ValueError(f"Unsupported export format: {format}")

    async def _process_events_buffer(self) -> None:
        """Process the events buffer and store to persistent storage."""

        if not self.events_buffer:
            return

        # In a real implementation, this would persist events to a database
        # For now, we'll just log the processing
        logger.info(f"Processing {len(self.events_buffer)} analytics events")

        # Clear buffer after processing
        self.events_buffer.clear()

    async def _update_health_status(self, event: MemoryAnalyticsEvent) -> None:
        """Update health status based on new event."""

        # Simple health check based on recent events
        if not event.success:
            if event.error_message and "critical" in event.error_message.lower():
                self.health_status.status_level = HealthStatusLevel.CRITICAL
                self.health_status.add_alert(
                    f"Critical error in {event.operation_type.value}: {event.error_message}"
                )
            else:
                self.health_status.status_level = max(
                    self.health_status.status_level, HealthStatusLevel.WARNING
                )
                self.health_status.add_alert(
                    f"Operation failure: {event.operation_type.value}"
                )

        # Check for high latency
        if event.duration_ms > self.config.alert_thresholds["avg_latency_ms"]:
            self.health_status.status_level = max(
                self.health_status.status_level, HealthStatusLevel.WARNING
            )
            self.health_status.add_alert(
                f"High latency detected: {event.duration_ms:.0f}ms for {event.operation_type.value}"
            )

    async def _perform_health_check(self) -> None:
        """Perform comprehensive health check."""

        current_time = datetime.now()

        # Reset health status
        self.health_status = HealthStatus(
            status_level=HealthStatusLevel.HEALTHY,
            timestamp=current_time,
        )

        # Check recent error rates
        recent_events = [
            event
            for event in self.events_buffer
            if (current_time - event.timestamp).total_seconds()
            <= 900  # Last 15 minutes
        ]

        if recent_events:
            error_rate = sum(1 for event in recent_events if not event.success) / len(
                recent_events
            )
            avg_latency = sum(event.duration_ms for event in recent_events) / len(
                recent_events
            )

            if error_rate > self.config.alert_thresholds["error_rate"]:
                self.health_status.add_alert(f"High error rate: {error_rate:.1%}")

            if avg_latency > self.config.alert_thresholds["avg_latency_ms"]:
                self.health_status.add_alert(
                    f"High average latency: {avg_latency:.0f}ms"
                )

        # Add component status
        self.health_status.component_status = {
            "memory_adapter": HealthStatusLevel.HEALTHY,  # Would check actual adapter health
            "analytics_engine": HealthStatusLevel.HEALTHY,
            "event_processing": HealthStatusLevel.HEALTHY,
        }

    async def _get_events_for_period(
        self,
        date_range: DateRange,
        user_id: Optional[str] = None,
    ) -> List[MemoryAnalyticsEvent]:
        """Get events for a specific time period."""

        # Filter events from buffer (in real implementation, would query database)
        filtered_events = []

        for event in self.events_buffer:
            if date_range.start_date <= event.timestamp <= date_range.end_date:
                if user_id is None or event.user_id == user_id:
                    filtered_events.append(event)

        return filtered_events

    async def _calculate_retrieval_metrics(
        self,
        events: List[MemoryAnalyticsEvent],
        user_id: Optional[str] = None,
    ) -> RetrievalMetrics:
        """Calculate retrieval performance metrics."""

        search_events = [
            e for e in events if e.operation_type == MemoryOperationType.SEARCH
        ]

        if not search_events:
            return RetrievalMetrics(
                total_queries=0,
                average_latency_ms=0.0,
                hit_rate=0.0,
                average_relevance_score=0.0,
                unique_users=0,
            )

        total_queries = len(search_events)
        avg_latency = sum(e.duration_ms for e in search_events) / total_queries
        successful_searches = sum(1 for e in search_events if e.success)
        hit_rate = successful_searches / total_queries

        # Extract relevance scores from metadata
        relevance_scores = []
        for event in search_events:
            score = event.metadata.get("relevance_score")
            if score is not None:
                relevance_scores.append(score)

        avg_relevance = (
            sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0
        )
        unique_users = len(set(e.user_id for e in search_events))

        # Domain distribution
        domain_counts = Counter()
        for event in search_events:
            domain = event.metadata.get("primary_domain", "unknown")
            domain_counts[domain] += 1

        return RetrievalMetrics(
            total_queries=total_queries,
            average_latency_ms=avg_latency,
            hit_rate=hit_rate,
            average_relevance_score=avg_relevance,
            unique_users=unique_users,
            domain_distribution=dict(domain_counts),
        )

    async def _calculate_storage_metrics(
        self,
        date_range: DateRange,
        user_id: Optional[str] = None,
    ) -> StorageMetrics:
        """Calculate storage usage metrics."""

        # This would normally query the actual storage system
        # For now, we'll return placeholder metrics
        return StorageMetrics(
            total_memories=1000,  # Placeholder
            memories_by_type={"conversation": 500, "goal": 100, "preference": 50},
            memories_by_domain={"strength": 200, "cardio": 150, "nutrition": 100},
            storage_size_mb=50.0,
            growth_rate_daily=0.05,
            summarized_memories=50,
            archived_memories=25,
        )

    async def _calculate_performance_metrics(
        self,
        events: List[MemoryAnalyticsEvent],
    ) -> PerformanceMetrics:
        """Calculate system performance metrics."""

        if not events:
            return PerformanceMetrics()

        # Calculate average latency by operation type
        latencies_by_type = defaultdict(list)
        for event in events:
            latencies_by_type[event.operation_type].append(event.duration_ms)

        avg_latencies = {
            op_type: sum(latencies) / len(latencies)
            for op_type, latencies in latencies_by_type.items()
        }

        # Calculate operations per second
        if events:
            time_span = (events[-1].timestamp - events[0].timestamp).total_seconds()
            ops_per_second = len(events) / max(1, time_span)
        else:
            ops_per_second = 0.0

        # Calculate error rate
        failed_events = sum(1 for event in events if not event.success)
        error_rate = failed_events / len(events)

        return PerformanceMetrics(
            average_operation_latency=avg_latencies,
            operations_per_second=ops_per_second,
            error_rate=error_rate,
            cache_hit_rate=0.8,  # Placeholder
        )

    async def _analyze_user_engagement(
        self,
        events: List[MemoryAnalyticsEvent],
        date_range: DateRange,
    ) -> Dict[str, Any]:
        """Analyze user engagement patterns."""

        unique_users = set(event.user_id for event in events)

        # Daily active users
        daily_users = defaultdict(set)
        for event in events:
            day = event.timestamp.date()
            daily_users[day].add(event.user_id)

        avg_daily_users = sum(len(users) for users in daily_users.values()) / max(
            1, len(daily_users)
        )

        # User activity distribution
        user_activity = Counter(event.user_id for event in events)

        return {
            "total_unique_users": len(unique_users),
            "average_daily_active_users": avg_daily_users,
            "total_user_sessions": len(events),
            "average_actions_per_user": (
                sum(user_activity.values()) / len(unique_users) if unique_users else 0
            ),
            "most_active_users": user_activity.most_common(5),
        }

    async def _generate_recommendations(
        self,
        retrieval_metrics: RetrievalMetrics,
        storage_metrics: StorageMetrics,
        performance_metrics: PerformanceMetrics,
    ) -> List[str]:
        """Generate optimization recommendations based on metrics."""

        recommendations = []

        # Performance recommendations
        if performance_metrics.error_rate > 0.05:
            recommendations.append(
                f"High error rate ({performance_metrics.error_rate:.1%}). "
                "Investigate system stability and error handling."
            )

        if retrieval_metrics.average_latency_ms > 1000:
            recommendations.append(
                f"High search latency ({retrieval_metrics.average_latency_ms:.0f}ms). "
                "Consider query optimization or caching improvements."
            )

        # Storage recommendations
        if storage_metrics.growth_rate_daily > 0.1:
            recommendations.append(
                f"High storage growth rate ({storage_metrics.growth_rate_daily:.1%} daily). "
                "Consider implementing memory summarization or archival."
            )

        # Usage recommendations
        if retrieval_metrics.hit_rate < 0.7:
            recommendations.append(
                f"Low search hit rate ({retrieval_metrics.hit_rate:.1%}). "
                "Review query strategies and memory organization."
            )

        return recommendations

    async def _get_user_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """Get memory statistics for a specific user."""

        # This would normally call the mem0_adapter
        # For now, return placeholder data
        return {
            "total_memories": 150,
            "memories_by_type": {"conversation": 80, "goal": 10, "preference": 5},
            "storage_size_kb": 500,
            "last_activity": datetime.now().isoformat(),
        }
