"""
Nutrition Meal Plan Tool

Python implementation of meal plan generation with detailed nutritional information.
Equivalent to the TypeScript nutritionSessionGenerationTool.
"""

import math
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from ..base_tool import BaseDomainTool


class MealIngredient(BaseModel):
    """Individual ingredient in a meal item."""

    name: str
    quantity: str
    unit: Optional[str] = None
    calories: int


class MealItem(BaseModel):
    """Individual meal item with ingredients and preparation."""

    description: str
    ingredients: List[MealIngredient]
    preparation: Optional[str] = None
    total_calories: int


class Meal(BaseModel):
    """Complete meal with all items."""

    items: List[MealItem]
    meal_calories: int


class DailyPlan(BaseModel):
    """Complete daily meal plan."""

    breakfast: Meal
    morning_snack: Meal
    lunch: Meal
    afternoon_snack: Meal
    dinner: Meal
    daily_total_calories: int


class Macronutrients(BaseModel):
    """Macronutrient breakdown."""

    protein: str
    carbohydrates: str
    fat: str


class NutritionPlan(BaseModel):
    """Complete nutrition plan for a day."""

    day: str
    session_type: str = "nutrition"
    caloric_intake: str
    macronutrients: Macronutrients
    meals: DailyPlan
    rationale: str


class MealPlanInput(BaseModel):
    """Input parameters for meal plan generation."""

    date: str = Field(description="Date for the meal plan (YYYY-MM-DD format)")
    calorie_target: Optional[int] = Field(
        default=2000, description="Target total calories for the day"
    )
    diet_type: Optional[str] = Field(
        default="balanced",
        description="Type of diet (balanced, low-carb, high-protein, etc.)",
    )


class NutritionMealPlanTool(BaseDomainTool):
    """
    Meal plan generation tool for nutrition domain.

    Generates comprehensive daily meal plans with:
    - Detailed meals (breakfast, lunch, dinner, snacks)
    - Complete ingredient lists with quantities and calories
    - Macronutrient breakdowns
    - Diet type customization
    - Calorie targeting
    """

    domain: str = "nutrition"
    name: str = "nutrition_meal_plan"
    description: str = (
        "Generate comprehensive daily meal plans with detailed nutritional information"
    )

    def __init__(self):
        super().__init__()

    async def generate_meal_plan(self, meal_input: MealPlanInput) -> NutritionPlan:
        """Generate a complete daily meal plan."""
        try:
            # Fixed calorie breakdown per meal
            breakfast_calories = 395
            morning_snack_calories = 165
            lunch_calories = 380
            afternoon_snack_calories = 185
            dinner_calories = 585
            calculated_daily_total_calories = (
                breakfast_calories
                + morning_snack_calories
                + lunch_calories
                + afternoon_snack_calories
                + dinner_calories
            )

            current_calorie_target = (
                meal_input.calorie_target or calculated_daily_total_calories
            )

            # Determine macro percentages based on diet type
            macro_percentages = self._get_macro_percentages(meal_input.diet_type)

            # Calculate macronutrient grams
            protein_grams = round(
                (current_calorie_target * macro_percentages["protein"]) / 4
            )
            carbs_grams = round(
                (current_calorie_target * macro_percentages["carbs"]) / 4
            )
            fat_grams = round((current_calorie_target * macro_percentages["fat"]) / 9)

            # Generate meals
            meals = self._generate_meals(
                breakfast_calories,
                morning_snack_calories,
                lunch_calories,
                afternoon_snack_calories,
                dinner_calories,
                calculated_daily_total_calories,
            )

            # Create nutrition plan
            nutrition_plan = NutritionPlan(
                day=meal_input.date,
                session_type="nutrition",
                caloric_intake=str(current_calorie_target),
                macronutrients=Macronutrients(
                    protein=f"{protein_grams}g",
                    carbohydrates=f"{carbs_grams}g",
                    fat=f"{fat_grams}g",
                ),
                meals=meals,
                rationale=self._generate_rationale(
                    meal_input.diet_type, current_calorie_target, macro_percentages
                ),
            )

            return nutrition_plan

        except Exception as e:
            raise Exception(f"Error generating meal plan: {str(e)}")

    def _get_macro_percentages(self, diet_type: str) -> Dict[str, float]:
        """Get macronutrient percentages based on diet type."""
        diet_type_lower = diet_type.lower()

        if "high-protein" in diet_type_lower:
            return {"carbs": 0.3, "protein": 0.4, "fat": 0.3}
        elif "low-carb" in diet_type_lower:
            return {"carbs": 0.2, "protein": 0.4, "fat": 0.4}
        else:  # balanced diet default
            return {"carbs": 0.45, "protein": 0.25, "fat": 0.3}

    def _generate_meals(
        self,
        breakfast_calories: int,
        morning_snack_calories: int,
        lunch_calories: int,
        afternoon_snack_calories: int,
        dinner_calories: int,
        daily_total_calories: int,
    ) -> DailyPlan:
        """Generate all meals for the day."""

        breakfast = Meal(
            items=[
                MealItem(
                    description="Oatmeal with fruits and nuts",
                    ingredients=[
                        MealIngredient(
                            name="Rolled oats", quantity="50", unit="g", calories=180
                        ),
                        MealIngredient(
                            name="Banana", quantity="1", unit="medium", calories=105
                        ),
                        MealIngredient(
                            name="Almonds", quantity="15", unit="g", calories=90
                        ),
                        MealIngredient(
                            name="Honey", quantity="1", unit="tsp", calories=20
                        ),
                    ],
                    preparation="Cook oats with water, top with sliced banana, chopped almonds, and a drizzle of honey",
                    total_calories=breakfast_calories,
                )
            ],
            meal_calories=breakfast_calories,
        )

        morning_snack = Meal(
            items=[
                MealItem(
                    description="Greek yogurt with berries",
                    ingredients=[
                        MealIngredient(
                            name="Greek yogurt", quantity="150", unit="g", calories=130
                        ),
                        MealIngredient(
                            name="Mixed berries", quantity="50", unit="g", calories=35
                        ),
                    ],
                    preparation="Mix berries into yogurt",
                    total_calories=morning_snack_calories,
                )
            ],
            meal_calories=morning_snack_calories,
        )

        lunch = Meal(
            items=[
                MealItem(
                    description="Chicken and vegetable wrap",
                    ingredients=[
                        MealIngredient(
                            name="Whole wheat tortilla",
                            quantity="1",
                            unit="medium",
                            calories=120,
                        ),
                        MealIngredient(
                            name="Grilled chicken breast",
                            quantity="100",
                            unit="g",
                            calories=165,
                        ),
                        MealIngredient(
                            name="Mixed vegetables",
                            quantity="50",
                            unit="g",
                            calories=25,
                        ),
                        MealIngredient(
                            name="Hummus", quantity="30", unit="g", calories=70
                        ),
                    ],
                    preparation="Spread hummus on tortilla, add sliced chicken and vegetables, roll into a wrap",
                    total_calories=lunch_calories,
                )
            ],
            meal_calories=lunch_calories,
        )

        afternoon_snack = Meal(
            items=[
                MealItem(
                    description="Apple with peanut butter",
                    ingredients=[
                        MealIngredient(
                            name="Apple", quantity="1", unit="medium", calories=95
                        ),
                        MealIngredient(
                            name="Natural peanut butter",
                            quantity="15",
                            unit="g",
                            calories=90,
                        ),
                    ],
                    preparation="Slice apple and serve with peanut butter for dipping",
                    total_calories=afternoon_snack_calories,
                )
            ],
            meal_calories=afternoon_snack_calories,
        )

        dinner = Meal(
            items=[
                MealItem(
                    description="Salmon with quinoa and roasted vegetables",
                    ingredients=[
                        MealIngredient(
                            name="Salmon fillet", quantity="150", unit="g", calories=280
                        ),
                        MealIngredient(
                            name="Quinoa", quantity="50", unit="g", calories=170
                        ),
                        MealIngredient(
                            name="Broccoli", quantity="80", unit="g", calories=27
                        ),
                        MealIngredient(
                            name="Bell peppers", quantity="80", unit="g", calories=25
                        ),
                        MealIngredient(
                            name="Olive oil", quantity="10", unit="ml", calories=80
                        ),
                        MealIngredient(
                            name="Lemon", quantity="1/4", unit="", calories=3
                        ),
                    ],
                    preparation="Bake salmon with lemon, cook quinoa, roast vegetables with olive oil",
                    total_calories=dinner_calories,
                )
            ],
            meal_calories=dinner_calories,
        )

        return DailyPlan(
            breakfast=breakfast,
            morning_snack=morning_snack,
            lunch=lunch,
            afternoon_snack=afternoon_snack,
            dinner=dinner,
            daily_total_calories=daily_total_calories,
        )

    def _generate_rationale(
        self, diet_type: str, calorie_target: int, macro_percentages: Dict[str, float]
    ) -> str:
        """Generate rationale for the meal plan."""
        return (
            f"This {diet_type} nutrition plan aims for approximately {calorie_target} calories "
            f"with a target macronutrient split of roughly "
            f"{macro_percentages['carbs'] * 100:.0f}% carbohydrates, "
            f"{macro_percentages['protein'] * 100:.0f}% protein, and "
            f"{macro_percentages['fat'] * 100:.0f}% fat. "
            f"This supports energy levels and recovery, tailored for a {diet_type} approach."
        )

    # Legacy method for backward compatibility with meal_planner.py
    async def plan_meals(self, meal_input):
        """Generate meal plans (legacy compatibility method)."""
        if isinstance(meal_input, dict):
            # Convert dict input to MealPlanInput
            input_data = MealPlanInput(
                date=meal_input.get("date", datetime.now().strftime("%Y-%m-%d")),
                calorie_target=meal_input.get("calorie_target", 2000),
                diet_type=meal_input.get("diet_type", "balanced"),
            )
        else:
            input_data = meal_input

        plan = await self.generate_meal_plan(input_data)

        # Return simplified format for backward compatibility
        return {
            "meal_plan": f"{plan.session_type} plan for {plan.day}",
            "recipes": [
                plan.meals.breakfast.items[0].description,
                plan.meals.lunch.items[0].description,
                plan.meals.dinner.items[0].description,
            ],
            "calorie_target": plan.caloric_intake,
            "macronutrients": plan.macronutrients.model_dump(),
            "full_plan": plan.model_dump(),
        }


import json

# LangChain Tool Wrappers
from langchain_core.tools import tool


@tool
def generate_meal_plan(meal_plan_data: str) -> str:
    """
    Generate a comprehensive daily meal plan with detailed nutritional information.

    Use this tool for:
    - Creating complete daily meal plans
    - Planning meals for specific calorie targets
    - Generating diet-specific meal plans
    - Getting detailed ingredient lists and preparation instructions

    Input should be JSON string:
    {
        "date": "2024-01-15",
        "calorie_target": 2000,
        "diet_type": "balanced"
    }
    """
    try:
        data = json.loads(meal_plan_data)

        meal_input = MealPlanInput(**data)
        meal_tool = NutritionMealPlanTool()

        # For demo purposes, return synchronous result with basic meal plan
        result = {
            "day": meal_input.date,
            "session_type": "nutrition",
            "caloric_intake": str(meal_input.calorie_target or 2000),
            "macronutrients": {
                "protein": f"{int((meal_input.calorie_target or 2000) * 0.25 / 4)}g",
                "carbohydrates": f"{int((meal_input.calorie_target or 2000) * 0.45 / 4)}g",
                "fat": f"{int((meal_input.calorie_target or 2000) * 0.30 / 9)}g",
            },
            "meals": {
                "breakfast": {
                    "description": "Oatmeal with fruits and nuts",
                    "calories": 395,
                    "ingredients": [
                        "Rolled oats (50g)",
                        "Banana (1 medium)",
                        "Almonds (15g)",
                        "Honey (1 tsp)",
                    ],
                },
                "lunch": {
                    "description": "Chicken and vegetable wrap",
                    "calories": 380,
                    "ingredients": [
                        "Whole wheat tortilla",
                        "Grilled chicken breast (100g)",
                        "Mixed vegetables",
                        "Hummus (30g)",
                    ],
                },
                "dinner": {
                    "description": "Salmon with quinoa and roasted vegetables",
                    "calories": 585,
                    "ingredients": [
                        "Salmon fillet (150g)",
                        "Quinoa (50g)",
                        "Broccoli (80g)",
                        "Bell peppers (80g)",
                        "Olive oil (10ml)",
                    ],
                },
                "snacks": {
                    "description": "Greek yogurt with berries + Apple with peanut butter",
                    "calories": 350,
                    "ingredients": [
                        "Greek yogurt (150g)",
                        "Mixed berries",
                        "Apple",
                        "Peanut butter (15g)",
                    ],
                },
            },
            "rationale": f"This {meal_input.diet_type} nutrition plan aims for approximately {meal_input.calorie_target or 2000} calories with balanced macronutrients to support energy levels and recovery.",
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        return f"Meal plan generation failed: {str(e)}"
