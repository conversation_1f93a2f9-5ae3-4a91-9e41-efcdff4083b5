"""
Airtable MCP Tool Schemas

Pydantic schemas for the Airtable MCP integration tool.
These schemas match the Zod schemas from the Next.js implementation.
"""

from typing import List, Literal, Optional

from pydantic import BaseModel, Field, model_validator


class SortObject(BaseModel):
    """Sort object for Airtable queries."""

    field: str = Field(description="The name of the field to sort by.")
    direction: Optional[Literal["asc", "desc"]] = Field(
        default=None, description="The sort direction. Default is asc (ascending)."
    )


class ListBasesInput(BaseModel):
    """Input schema for listing Airtable bases."""

    pass  # No parameters needed


class ListBasesOutput(BaseModel):
    """Output schema for listing Airtable bases."""

    success: bool
    bases: Optional[List[dict]] = None
    error_type: Optional[str] = None
    message: str
    execution_time_ms: int


class ListTablesInput(BaseModel):
    """Input schema for listing tables in an Airtable base."""

    baseId: str = Field(description="The ID of the Airtable base.")


class ListTablesOutput(BaseModel):
    """Output schema for listing tables in an Airtable base."""

    success: bool
    tables: Optional[List[dict]] = None
    base_id: str
    error_type: Optional[str] = None
    message: str
    execution_time_ms: int


class ListRecordsInput(BaseModel):
    """Input schema for listing records from an Airtable table."""

    baseId: str = Field(description="The ID of the Airtable base.")
    tableId: str = Field(description="The ID or name of the table within the base.")
    maxRecords: Optional[int] = Field(
        default=100,
        gt=0,
        description="Maximum number of records to return. Default is 100.",
    )


class ListRecordsOutput(BaseModel):
    """Output schema for listing records from an Airtable table."""

    success: bool
    records: Optional[List[dict]] = None
    base_id: str
    table_id: str
    record_count: int = 0
    error_type: Optional[str] = None
    message: str
    execution_time_ms: int


class SearchRecordsInput(BaseModel):
    """Input schema for searching records in an Airtable table with formula filter."""

    baseId: str = Field(description="The ID of the Airtable base.")
    tableId: str = Field(description="The ID or name of the table within the base.")
    filterByFormula: str = Field(
        description="Airtable formula to filter records. Example: {IntensityLevel}='4' for high intensity."
    )
    maxRecords: Optional[int] = Field(
        default=100,
        gt=0,
        description="Maximum number of records to return. Default is 100.",
    )
    view: Optional[str] = Field(
        default=None,
        description="The name or ID of a view in the specified table. Only records in this view will be returned.",
    )
    sort: Optional[List[SortObject]] = Field(
        default=None,
        description="List of sort objects that define the sort order for the records.",
    )


class SearchRecordsOutput(BaseModel):
    """Output schema for searching records in an Airtable table."""

    success: bool
    records: Optional[List[dict]] = None
    base_id: str
    table_id: str
    filter_formula: str
    record_count: int = 0
    error_type: Optional[str] = None
    message: str
    execution_time_ms: int


class AirtableMCPInput(BaseModel):
    """Unified input schema for Airtable MCP operations."""

    operation: Literal["list_bases", "list_tables", "list_records", "search_records"]
    baseId: Optional[str] = None
    tableId: Optional[str] = None
    filterByFormula: Optional[str] = None
    maxRecords: Optional[int] = Field(default=100, gt=0)
    view: Optional[str] = None
    sort: Optional[List[SortObject]] = None

    @model_validator(mode="after")
    def validate_operation_fields(self):
        """Validate that required fields are provided for each operation."""
        if self.operation == "list_tables":
            if not self.baseId:
                raise ValueError("baseId is required for list_tables operation")

        elif self.operation == "list_records":
            if not self.baseId or not self.tableId:
                raise ValueError(
                    "baseId and tableId are required for list_records operation"
                )

        elif self.operation == "search_records":
            if not self.baseId or not self.tableId or not self.filterByFormula:
                raise ValueError(
                    "baseId, tableId, and filterByFormula are required for search_records operation"
                )

        return self


class AirtableMCPOutput(BaseModel):
    """Unified output schema for Airtable MCP operations."""

    success: bool
    operation: str
    data: Optional[List[dict]] = None
    base_id: Optional[str] = None
    table_id: Optional[str] = None
    filter_formula: Optional[str] = None
    data_count: int = 0
    error_type: Optional[str] = None
    message: str
    execution_time_ms: int
    mcp_server_status: Optional[str] = None
