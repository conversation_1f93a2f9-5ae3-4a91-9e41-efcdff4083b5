"""
Simple Individual Agent Tests

Quick tests for each modular agent to verify they work independently.
This is useful for development and debugging without running the full integration suite.

Usage:
    cd python-langgraph
    python -m tests.integration.test_individual_agents_simple
"""

import asyncio
import logging
from typing import Any, Dict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_strength_agent():
    """Test the strength agent specifically."""
    print("💪 Testing Strength Agent")
    print("-" * 30)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.strength_agent import (
            StrengthAgent,
            strength_agent_node,
        )

        # Test basic agent creation
        print("\n1. Testing StrengthAgent class instantiation...")
        agent = StrengthAgent()
        print(f"✅ StrengthAgent created: {agent.name}")
        print(f"Domain: {agent.domain}")
        print(f"Tools: {len(agent.tools)}")

        # Test state creation and node execution
        print("\n2. Testing strength_agent_node execution...")
        test_state = {
            "messages": [{"role": "user", "content": "I want to build muscle"}],
            "user_query": "I want to build muscle",
            "user_profile": {"fitness_level": "beginner"},
        }

        result = await strength_agent_node(test_state)

        if result and result.get("response"):
            print(f"✅ Response generated: {result['response'][:100]}...")
            print(f"   Agent: {result.get('agent_name', 'unknown')}")
            print(f"   Domain: {result.get('agent_domain', 'unknown')}")
            print(f"   Completed: {result.get('specialist_completed', False)}")
        else:
            print("⚠️ No response generated")

    except Exception as e:
        print(f"❌ Error testing strength agent: {e}")


async def test_nutrition_agent():
    """Test the nutrition agent specifically."""
    print("\n🥗 Testing Nutrition Agent")
    print("-" * 30)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.nutrition_agent import (
            NutritionAgent,
            nutrition_agent_node,
        )

        # Test basic agent creation
        print("\n1. Testing NutritionAgent class instantiation...")
        agent = NutritionAgent()
        print(f"✅ NutritionAgent created: {agent.name}")
        print(f"Domain: {agent.domain}")
        print(f"Tools: {len(agent.tools)}")

        # Test state creation and node execution
        print("\n2. Testing nutrition_agent_node execution...")
        test_state = {
            "messages": [
                {"role": "user", "content": "What should I eat to build muscle?"}
            ],
            "user_query": "What should I eat to build muscle?",
            "user_profile": {"dietary_restrictions": ["vegetarian"]},
        }

        result = await nutrition_agent_node(test_state)

        if result and result.get("response"):
            print(f"✅ Response generated: {result['response'][:100]}...")
            print(f"   Agent: {result.get('agent_name', 'unknown')}")
            print(f"   Domain: {result.get('agent_domain', 'unknown')}")
            print(f"   Completed: {result.get('specialist_completed', False)}")
        else:
            print("⚠️ No response generated")

    except Exception as e:
        print(f"❌ Error testing nutrition agent: {e}")


async def test_cardio_agent():
    """Test the cardio agent specifically."""
    print("\n🏃 Testing Cardio Agent")
    print("-" * 30)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.cardio_agent import CardioAgent, cardio_agent_node

        # Test basic agent creation
        print("\n1. Testing CardioAgent class instantiation...")
        agent = CardioAgent()
        print(f"✅ CardioAgent created: {agent.name}")
        print(f"Domain: {agent.domain}")
        print(f"Tools: {len(agent.tools)}")

        # Test state creation and node execution
        print("\n2. Testing cardio_agent_node execution...")
        test_state = {
            "messages": [{"role": "user", "content": "I want to improve my running"}],
            "user_query": "I want to improve my running",
            "user_profile": {"current_activity": "walking"},
        }

        result = await cardio_agent_node(test_state)

        if result and result.get("response"):
            print(f"✅ Response generated: {result['response'][:100]}...")
            print(f"   Agent: {result.get('agent_name', 'unknown')}")
            print(f"   Domain: {result.get('agent_domain', 'unknown')}")
            print(f"   Completed: {result.get('specialist_completed', False)}")
        else:
            print("⚠️ No response generated")

    except Exception as e:
        print(f"❌ Error testing cardio agent: {e}")


async def test_planning_node_simple():
    """Test the planning node with simple queries."""
    print("\n🎯 Testing Planning Node (Simple)")
    print("-" * 30)

    try:
        from langchain_core.messages import HumanMessage

        from athlea_langgraph.agents.planning_node import planning_node

        test_queries = [
            "I want to start strength training",
            "What should I eat for better nutrition?",
            "I need help with cardio and endurance",
        ]

        for query in test_queries:
            print(f"\nQuery: {query}")

            test_state = {
                "messages": [HumanMessage(content=query)],
                "user_query": query,
                "user_profile": {"fitness_level": "beginner"},
            }

            result = await planning_node(test_state)

            if result:
                routing = result.get("routing_decision")
                domains = result.get("required_domains", [])
                print(f"  Routing: {routing}")
                print(f"  Domains: {domains}")
            else:
                print("  ⚠️ No routing decision")

    except Exception as e:
        print(f"❌ Error testing planning node: {e}")


async def main():
    """Run simple individual agent tests."""
    print("🧪 Simple Individual Agent Tests")
    print("=" * 50)
    print("Testing modular agents individually for development/debugging")
    print()

    await test_strength_agent()
    await test_nutrition_agent()
    await test_cardio_agent()
    await test_planning_node_simple()

    print("\n✅ Simple tests completed!")
    print("\n📝 Notes:")
    print("- These are basic functionality tests")
    print("- Some failures are expected if environment isn't fully set up")
    print("- The goal is to verify the modular architecture works")
    print("- For full testing, run the comprehensive test suite")


if __name__ == "__main__":
    asyncio.run(main())
