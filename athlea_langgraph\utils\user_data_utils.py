"""
User data utilities for fetching training profiles and current plans from MongoDB.

This module provides functionality to retrieve user-specific training data
that is used to personalize coaching responses and recommendations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from pymongo import MongoClient
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class UserDataError(Exception):
    """Custom exception for user data operations."""

    pass


class TrainingProfile:
    """Training profile data structure."""

    def __init__(self, data: Dict[str, Any]):
        self.data = data

    def get_domain_data(self, domain: str) -> Dict[str, Any]:
        """Get training data for a specific domain."""
        return self.data.get(domain, {})

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.data


class TrainingPlan:
    """Training plan data structure."""

    def __init__(self, data: Dict[str, Any]):
        self.data = data
        self.plan_id = data.get("plan_id", "")
        self.plan_name = data.get("plan_name", "")
        self.plan_type = data.get("plan_type", "")
        self.plan_description = data.get("plan_description", "")
        self.current_phase = data.get("current_phase", 0)
        self.phases = data.get("phases", [])

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.data


class UserDataManager:
    """Manager for fetching and processing user data from MongoDB."""

    def __init__(self, mongodb_uri: str):
        self.mongodb_uri = mongodb_uri
        self.client: Optional[MongoClient] = None
        self.db = None
        self.collection = None
        self.executor = ThreadPoolExecutor(max_workers=5)

    def _get_connection(self):
        """Get MongoDB connection."""
        if not self.client:
            try:
                self.client = MongoClient(self.mongodb_uri)
                self.db = self.client["AthleaUserData"]
                self.collection = self.db["users"]
                logger.info("Successfully connected to MongoDB for user data")
            except Exception as e:
                logger.error(f"Failed to connect to MongoDB: {e}")
                raise UserDataError(f"MongoDB connection failed: {e}")
        return self.collection

    async def fetch_training_profile(self, user_id: str) -> Optional[TrainingProfile]:
        """
        Fetch user's training profile from MongoDB.

        Args:
            user_id: The user's unique identifier

        Returns:
            TrainingProfile object or None if not found
        """
        logger.info(f"Fetching training profile for userId: {user_id}")

        try:
            collection = self._get_connection()

            # Run MongoDB query in thread pool to avoid blocking
            def _fetch_profile():
                return collection.find_one(
                    {"user_id": user_id}, {"training_profile": 1, "_id": 0}
                )

            loop = asyncio.get_event_loop()
            user_info = await loop.run_in_executor(self.executor, _fetch_profile)

            if not user_info or not user_info.get("training_profile"):
                logger.warning(f"No training profile found for userId: {user_id}")
                return None

            profile_data = user_info["training_profile"]

            # Clean up training profile data similar to TypeScript version
            if "elevate_profile" in profile_data:
                del profile_data["elevate_profile"]

            # Fix nutrition field if it's corrupted (indexed array-like object)
            if "nutrition" in profile_data and isinstance(
                profile_data["nutrition"], dict
            ):
                if any(key.isdigit() for key in profile_data["nutrition"].keys()):
                    try:
                        nutrition_text = "".join(
                            str(v) for v in profile_data["nutrition"].values()
                        )
                        profile_data["nutrition"] = nutrition_text
                        logger.info(
                            f"Converted indexed nutrition object to string for userId: {user_id}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error converting nutrition data for userId {user_id}: {e}"
                        )
                        del profile_data["nutrition"]

            logger.info(f"Successfully fetched training profile for userId: {user_id}")
            return TrainingProfile(profile_data)

        except Exception as e:
            logger.error(f"Error fetching training profile for userId {user_id}: {e}")
            return None

    async def fetch_current_plan(self, user_id: str) -> Optional[TrainingPlan]:
        """
        Fetch user's current training plan from MongoDB.

        Args:
            user_id: The user's unique identifier

        Returns:
            TrainingPlan object or None if not found
        """
        logger.info(f"Fetching current plan for userId: {user_id}")

        try:
            collection = self._get_connection()

            # Run MongoDB query in thread pool to avoid blocking
            def _fetch_plan():
                return collection.find_one(
                    {"user_id": user_id}, {"currentPlan": 1, "_id": 0}
                )

            loop = asyncio.get_event_loop()
            user_info = await loop.run_in_executor(self.executor, _fetch_plan)

            if not user_info or not user_info.get("currentPlan"):
                logger.warning(f"No current plan found for userId: {user_id}")
                return None

            plan_data = user_info["currentPlan"]
            logger.info(f"Successfully fetched current plan for userId: {user_id}")
            return TrainingPlan(plan_data)

        except Exception as e:
            logger.error(f"Error fetching current plan for userId {user_id}: {e}")
            return None

    async def fetch_user_data(
        self, user_id: str
    ) -> Tuple[Optional[TrainingProfile], Optional[TrainingPlan]]:
        """
        Fetch both training profile and current plan for a user.

        Args:
            user_id: The user's unique identifier

        Returns:
            Tuple of (TrainingProfile, TrainingPlan) - either can be None
        """
        logger.info(f"Fetching complete user data for userId: {user_id}")

        try:
            # Fetch both concurrently
            profile_task = self.fetch_training_profile(user_id)
            plan_task = self.fetch_current_plan(user_id)

            training_profile, current_plan = await asyncio.gather(
                profile_task, plan_task, return_exceptions=True
            )

            # Handle exceptions
            if isinstance(training_profile, Exception):
                logger.error(f"Exception fetching training profile: {training_profile}")
                training_profile = None

            if isinstance(current_plan, Exception):
                logger.error(f"Exception fetching current plan: {current_plan}")
                current_plan = None

            return training_profile, current_plan

        except Exception as e:
            logger.error(f"Error fetching user data for userId {user_id}: {e}")
            return None, None

    def close(self):
        """Close database connection and thread pool."""
        if self.client:
            self.client.close()
        self.executor.shutdown(wait=True)


def format_training_profile_for_prompt(profile: Optional[TrainingProfile]) -> str:
    """
    Format training profile data for LLM prompt consumption.

    Args:
        profile: TrainingProfile object or None

    Returns:
        Formatted string for prompt inclusion
    """
    if not profile:
        return "No training profile data available."

    try:
        profile_data = profile.to_dict()
        formatted_sections = []

        # Define domains to process
        domains = [
            "general",
            "strength",
            "running",
            "cycling",
            "nutrition",
            "recovery",
            "mental",
        ]

        for domain in domains:
            domain_data = profile_data.get(domain, {})
            if domain_data:
                formatted_sections.append(f"\n{domain.upper()} PROFILE:")
                for key, value in domain_data.items():
                    if value is not None and str(value).strip():
                        # Clean up the key name
                        clean_key = key.replace("_", " ").title()
                        formatted_sections.append(f"- {clean_key}: {value}")

        if formatted_sections:
            return "\n".join(formatted_sections)
        else:
            return "Training profile exists but contains no detailed information."

    except Exception as e:
        logger.error(f"Error formatting training profile: {e}")
        return "Error processing training profile data."


def format_current_plan_for_prompt(
    plan: Optional[TrainingPlan], current_domain: Optional[str] = None
) -> str:
    """
    Format current training plan for LLM prompt consumption.

    Args:
        plan: TrainingPlan object or None
        current_domain: Optional domain to focus on

    Returns:
        Formatted string for prompt inclusion
    """
    if not plan:
        return "No current training plan available."

    try:
        plan_data = plan.to_dict()
        formatted_sections = []

        # Basic plan information
        formatted_sections.append("CURRENT TRAINING PLAN:")
        formatted_sections.append(f"- Plan Name: {plan.plan_name}")
        formatted_sections.append(f"- Plan Type: {plan.plan_type}")

        if plan.plan_description:
            formatted_sections.append(f"- Description: {plan.plan_description}")

        # Current phase information
        if plan.phases and len(plan.phases) > plan.current_phase:
            current_phase = plan.phases[plan.current_phase]
            formatted_sections.append(f"\nCURRENT PHASE:")
            formatted_sections.append(
                f"- Phase Name: {current_phase.get('phaseName', 'Unknown')}"
            )
            formatted_sections.append(
                f"- Duration: {current_phase.get('duration', 'Unknown')}"
            )
            formatted_sections.append(
                f"- Focus: {current_phase.get('focus', 'Unknown')}"
            )

            if current_domain and current_domain in current_phase:
                domain_specifics = current_phase[current_domain]
                formatted_sections.append(
                    f"\n{current_domain.upper()} SPECIFIC DETAILS:"
                )
                for key, value in domain_specifics.items():
                    if value is not None:
                        clean_key = key.replace("_", " ").title()
                        formatted_sections.append(f"- {clean_key}: {value}")

        return "\n".join(formatted_sections)

    except Exception as e:
        logger.error(f"Error formatting current plan: {e}")
        return "Error processing current plan data."


def extract_training_insights(
    profile: Optional[TrainingProfile],
    plan: Optional[TrainingPlan],
    domain: Optional[str] = None,
) -> str:
    """
    Extract key training insights for coaching context.

    Args:
        profile: TrainingProfile object or None
        plan: TrainingPlan object or None
        domain: Optional specific domain to focus on

    Returns:
        Formatted insights string
    """
    insights = []

    try:
        # Profile insights
        if profile:
            profile_data = profile.to_dict()

            if domain and domain in profile_data:
                domain_data = profile_data[domain]
                insights.append(f"{domain.upper()} BACKGROUND:")

                # Extract key metrics based on domain
                if domain == "strength":
                    if "experience_level" in domain_data:
                        insights.append(
                            f"- Experience: {domain_data['experience_level']}"
                        )
                    if "training_frequency" in domain_data:
                        insights.append(
                            f"- Training Frequency: {domain_data['training_frequency']}"
                        )

                elif domain == "running":
                    if "weekly_mileage" in domain_data:
                        insights.append(
                            f"- Weekly Mileage: {domain_data['weekly_mileage']}"
                        )
                    if "pace_zones" in domain_data:
                        insights.append(f"- Pace Zones: {domain_data['pace_zones']}")

                elif domain == "nutrition":
                    if "dietary_preferences" in domain_data:
                        insights.append(
                            f"- Dietary Preferences: {domain_data['dietary_preferences']}"
                        )
                    if "daily_calorie_target" in domain_data:
                        insights.append(
                            f"- Calorie Target: {domain_data['daily_calorie_target']}"
                        )

        # Plan insights
        if plan:
            plan_data = plan.to_dict()
            insights.append(f"\nCURRENT FOCUS:")
            insights.append(f"- Plan Type: {plan.plan_type}")

            if plan.phases and len(plan.phases) > plan.current_phase:
                current_phase = plan.phases[plan.current_phase]
                if "focus" in current_phase:
                    insights.append(f"- Phase Focus: {current_phase['focus']}")

        return (
            "\n".join(insights)
            if insights
            else "No specific training insights available."
        )

    except Exception as e:
        logger.error(f"Error extracting training insights: {e}")
        return "Error processing training insights."


# Global user data manager instance
_user_data_manager: Optional[UserDataManager] = None


def get_user_data_manager(mongodb_uri: str) -> UserDataManager:
    """Get or create global user data manager instance."""
    global _user_data_manager
    if _user_data_manager is None:
        _user_data_manager = UserDataManager(mongodb_uri)
    return _user_data_manager


async def fetch_user_data_for_coaching(
    user_id: str, mongodb_uri: str
) -> Dict[str, Any]:
    """
    Convenience function to fetch user data for coaching sessions.

    Args:
        user_id: The user's unique identifier
        mongodb_uri: MongoDB connection string

    Returns:
        Dictionary with training_profile and current_plan data
    """
    manager = get_user_data_manager(mongodb_uri)

    try:
        training_profile, current_plan = await manager.fetch_user_data(user_id)

        return {
            "training_profile": (
                training_profile.to_dict() if training_profile else None
            ),
            "current_plan": current_plan.to_dict() if current_plan else None,
            "training_profile_formatted": format_training_profile_for_prompt(
                training_profile
            ),
            "current_plan_formatted": format_current_plan_for_prompt(current_plan),
        }

    except Exception as e:
        logger.error(f"Error in fetch_user_data_for_coaching for userId {user_id}: {e}")
        return {
            "training_profile": None,
            "current_plan": None,
            "training_profile_formatted": "Error fetching training profile.",
            "current_plan_formatted": "Error fetching current plan.",
        }
