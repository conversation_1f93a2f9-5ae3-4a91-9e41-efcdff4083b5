# Environment variables
.env
.env.*
!.env.example

# Virtual environments
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Test and coverage artifacts
.pytest_cache/
.coverage
.coverage.*
htmlcov/
nosetests.xml
coverage.xml

# Build artifacts
build/
dist/
*.egg-info/
*.egg

# Editor and IDE directories
.idea/
.vscode/

# Other
.DS_Store
*.log
*.swp
*.swo

# LangSmith API files
.langgraph_api/
langgraph.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# Logs
logs/
*.log

# Database
*.db
*.sqlite

# API keys and secrets
secrets.json
config.json

# LangGraph checkpoints
checkpoints/
.langgraph/

# Azure
.azure/ 