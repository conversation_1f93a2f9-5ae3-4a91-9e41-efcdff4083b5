#!/usr/bin/env python3
"""
n8n LangGraph Integration API

FastAPI wrapper that exposes LangGraph coaching workflows as HTTP endpoints
that can be called from n8n workflows. This service uses a dynamic graph
factory to automatically discover and serve available LangGraph graphs.

Now includes automatic ngrok tunnel creation for cloud n8n access.
"""

import asyncio
import logging
import subprocess
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
import requests
import json

from fastapi import FastAPI, HTTPException, BackgroundTasks, Path, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import uvicorn

# Import your existing LangGraph components
from athlea_langgraph.graph_factory import get_graph_factory
from athlea_langgraph.services.n8n_client import create_n8n_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for ngrok
ngrok_process = None
public_url = None


def start_ngrok():
    """Start ngrok tunnel and return the public URL"""
    global ngrok_process, public_url

    try:
        # Start ngrok in the background
        ngrok_process = subprocess.Popen(
            ["ngrok", "http", "8001", "--log=stdout"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # Wait a moment for ngrok to start
        time.sleep(3)

        # Get the public URL from ngrok API
        try:
            response = requests.get("http://localhost:4040/api/tunnels")
            tunnels = response.json()

            if tunnels.get("tunnels"):
                public_url = tunnels["tunnels"][0]["public_url"]
                logger.info(f"🌐 ngrok tunnel created: {public_url}")
                logger.info(
                    f"🔗 n8n can now access your service at: {public_url}/graphs"
                )
                return public_url
            else:
                logger.warning("⚠️ No ngrok tunnels found")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to get ngrok URL: {e}")
            return None

    except FileNotFoundError:
        logger.warning("⚠️ ngrok not found. Install with: brew install ngrok")
        logger.info("💡 Service will run on localhost:8001 only")
        return None
    except Exception as e:
        logger.error(f"❌ Failed to start ngrok: {e}")
        return None


def stop_ngrok():
    """Stop the ngrok process"""
    global ngrok_process
    if ngrok_process:
        ngrok_process.terminate()
        ngrok_process.wait()
        logger.info("🛑 ngrok tunnel stopped")


app = FastAPI(
    title="Athlea LangGraph n8n Integration API",
    description="Dynamic LangGraph integration for n8n workflows with automatic ngrok tunneling",
    version="2.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the dynamic graph factory
graph_factory = get_graph_factory()

# --- Request/Response Models ---


class GraphExecutionRequest(BaseModel):
    """Request model for executing coaching workflows."""

    message: str = Field(..., description="User's input message")
    user_id: str = Field(..., description="User identifier")
    thread_id: Optional[str] = Field(None, description="Conversation thread ID")
    graph_config: Optional[Dict[str, Any]] = Field(
        None, description="Graph configuration"
    )


class GraphExecutionResponse(BaseModel):
    """Response model for graph execution."""

    response: str = Field(..., description="Graph's response")
    tools_used: List[str] = Field(default_factory=list, description="Tools used")
    reasoning_steps: List[str] = Field(
        default_factory=list, description="Reasoning steps"
    )
    execution_time: float = Field(..., description="Execution time in seconds")
    thread_id: str = Field(..., description="Thread ID for session continuity")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Execution metadata"
    )


class N8nWorkflowRequest(BaseModel):
    """Request model for n8n workflow triggers."""

    workflow_id: str = Field(..., description="n8n workflow ID to trigger")
    data: Dict[str, Any] = Field(..., description="Data to send to the workflow")
    wait_for_completion: bool = Field(
        False, description="Whether to wait for completion"
    )


class N8nWorkflowResponse(BaseModel):
    """Response model for n8n workflow execution."""

    workflow_id: str = Field(..., description="n8n workflow ID that was triggered")
    execution_id: Optional[str] = Field(None, description="n8n execution ID")
    status: str = Field(..., description="Execution status")
    result: Optional[Dict[str, Any]] = Field(None, description="Workflow result data")


class GraphRequest(BaseModel):
    """Request model for executing a specific graph."""

    message: str = Field(..., description="User's input message")
    user_id: str = Field(..., description="User identifier")
    thread_id: Optional[str] = Field(None, description="Conversation thread ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class GraphResponse(BaseModel):
    """Response model for executing a specific graph."""

    response: str = Field(..., description="Graph's final response")
    tools_used: List[str] = Field(
        default_factory=list, description="Tools invoked during execution"
    )
    reasoning_steps: List[str] = Field(
        default_factory=list, description="Reasoning process"
    )
    execution_time: float = Field(..., description="Execution time in seconds")
    thread_id: str = Field(..., description="Thread ID for session continuity")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional execution metadata"
    )


# --- API Endpoints ---


@app.get("/graphs")
async def list_available_graphs():
    """List all available graphs discovered by the factory."""
    return {"available_graphs": graph_factory.list_available_graphs()}


@app.post("/graphs/{graph_name}/execute", response_model=GraphResponse)
async def execute_graph_endpoint(
    graph_name: str = Path(..., description="Name of the graph to execute"),
    request: GraphRequest = Body(..., description="Graph execution request"),
):
    """
    Execute a specific graph dynamically.

    This endpoint can execute any available graph by name.
    """
    try:
        logger.info(f"Executing graph '{graph_name}' for user {request.user_id}")

        # Get the dynamic graph factory
        factory = get_graph_factory()

        # Check if graph exists
        available_graphs = factory.list_available_graphs()
        if graph_name not in available_graphs:
            raise HTTPException(
                status_code=404,
                detail=f"Graph '{graph_name}' not found. Available graphs: {available_graphs}",
            )

        # Create the graph instance
        graph = factory.create_graph(graph_name)

        # Prepare the execution config
        thread_id = request.thread_id or f"n8n_{request.user_id}_{graph_name}"
        config = {"configurable": {"thread_id": thread_id}}

        # Prepare the input state based on graph type
        if graph_name == "onboarding":
            # Special handling for onboarding graph
            input_state = {
                "user_id": request.user_id,
                "user_input": request.message,
                "messages": [],
                "thread_id": thread_id,
            }
        else:
            # Generic input for other graphs
            input_state = {
                "user_id": request.user_id,
                "message": request.message,
                "thread_id": thread_id,
                "metadata": request.metadata or {},
            }

        # Execute the graph
        start_time = datetime.now()

        try:
            # Try to invoke the graph
            result = await graph.ainvoke(input_state, config)

        except Exception as graph_error:
            # If graph execution fails, try to get the current state
            logger.warning(f"Graph execution interrupted/failed: {graph_error}")
            try:
                # Get the current state to see what happened
                snapshot = await graph.aget_state(config)
                result = snapshot.values if snapshot else {}
                logger.info("Retrieved current state after interruption")
            except Exception as state_error:
                logger.error(f"Could not retrieve state: {state_error}")
                result = {}

        execution_time = (datetime.now() - start_time).total_seconds()

        # Extract the AI response - look for the last AI message
        response_text = "Sorry, I could not generate a response."
        if "messages" in result and result["messages"]:
            # Find the last AI message
            for message in reversed(result["messages"]):
                if hasattr(message, "content") and hasattr(message, "type"):
                    if message.type == "ai" or (
                        hasattr(message, "name") and "ai" in message.name.lower()
                    ):
                        response_text = message.content
                        break
                elif isinstance(message, dict) and message.get("type") == "ai":
                    response_text = message.get("content", response_text)
                    break
                elif isinstance(message, dict) and "content" in message:
                    # Fallback - use any message with content
                    response_text = message["content"]

        # Extract additional information
        tools_used = []
        reasoning_steps = []

        # Look for tool usage and reasoning in the result
        if "tool_calls" in result:
            tools_used = result["tool_calls"]
        if "reasoning" in result:
            reasoning_steps = result["reasoning"]
        if "sidebar_data" in result:
            # Add sidebar data as reasoning steps
            reasoning_steps.append(f"Generated sidebar data: {result['sidebar_data']}")

        # Check if the graph needs more input
        needs_input = result.get("needs_input", False) or result.get(
            "requires_input", False
        )
        if needs_input:
            reasoning_steps.append("Graph is requesting additional input from user")

        # Check current stage for onboarding
        current_stage = result.get("onboarding_stage", "unknown")
        if current_stage != "unknown":
            reasoning_steps.append(f"Current onboarding stage: {current_stage}")

        logger.info(
            f"Graph execution completed. Response length: {len(response_text)} chars"
        )

        return GraphResponse(
            response=response_text,
            tools_used=tools_used,
            reasoning_steps=reasoning_steps,
            execution_time=execution_time,
            thread_id=thread_id,
            metadata={
                "graph_name": graph_name,
                "user_id": request.user_id,
                "timestamp": start_time.isoformat(),
                "needs_input": needs_input,
                "current_stage": current_stage,
                **request.metadata,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing graph {graph_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Graph execution failed: {str(e)}")


@app.post("/coaching/stream")
async def stream_coaching_workflow(request: GraphExecutionRequest):
    """
    Stream coaching workflow execution for real-time responses.

    This endpoint provides streaming responses compatible with n8n's
    Server-Sent Events or webhook processing.
    """

    # TODO: Implement streaming with graph.astream() using the dynamic factory
    async def generate_coaching_stream():
        """Generate streaming coaching responses."""
        try:
            # Start workflow
            yield f"data: {{'type': 'start', 'message': 'Initializing coaching session...'}}\n\n"

            # TODO: Replace with actual streaming LangGraph execution
            # For now, simulate streaming steps
            steps = [
                "Analyzing your request...",
                "Searching knowledge base...",
                "Generating personalized recommendations...",
                "Finalizing coaching plan...",
            ]

            for i, step in enumerate(steps):
                await asyncio.sleep(1)  # Simulate processing time
                yield f"data: {{'type': 'progress', 'step': {i+1}, 'message': '{step}'}}\n\n"

            # Final response
            final_response = f"Based on your request about {request.message}, here's my coaching recommendation..."
            yield f"data: {{'type': 'complete', 'response': '{final_response}'}}\n\n"

        except Exception as e:
            yield f"data: {{'type': 'error', 'message': 'Error: {str(e)}'}}\n\n"

    return StreamingResponse(
        generate_coaching_stream(),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"},
    )


@app.post("/n8n/trigger", response_model=N8nWorkflowResponse)
async def trigger_n8n_workflow(request: N8nWorkflowRequest):
    """
    Trigger an n8n workflow from within LangGraph execution.

    This allows your LangGraph agents to trigger n8n workflows
    for external integrations, notifications, etc.
    """
    try:
        logger.info(
            f"Triggering n8n workflow {request.workflow_id} for user {request.user_id}"
        )

        async with create_n8n_client() as n8n:
            execution_id = await n8n.execute_workflow(request.workflow_id, request.data)
            # We can optionally wait for the result if needed
            result = await n8n.wait_for_execution(execution_id, timeout_seconds=300)

        return N8nWorkflowResponse(
            workflow_id=request.workflow_id,
            execution_id=execution_id,
            status=result.get("status", "completed"),
            result={
                "workflow_id": request.workflow_id,
                "user_id": request.user_id,
                "timestamp": datetime.now().isoformat(),
                "processed_data": result.get("data", {}).get("json", {}),
            },
        )

    except Exception as e:
        logger.exception(f"Error triggering n8n workflow: {e}")
        raise HTTPException(
            status_code=500, detail=f"Workflow trigger failed: {str(e)}"
        )


@app.get("/health")
async def health_check():
    """Health check endpoint for n8n monitoring."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "athlea-langgraph-n8n-integration",
    }


@app.get("/coaching/coaches")
async def list_available_coaches():
    """List available coach types for n8n workflow configuration."""
    return {
        "coaches": [
            {
                "id": "strength",
                "name": "Strength Training Coach",
                "description": "Specializes in weightlifting and strength building",
            },
            {
                "id": "cycling",
                "name": "Cycling Coach",
                "description": "Expert in cycling training and performance",
            },
            {
                "id": "running",
                "name": "Running Coach",
                "description": "Focused on running technique and endurance",
            },
            {
                "id": "nutrition",
                "name": "Nutrition Coach",
                "description": "Provides dietary guidance and meal planning",
            },
            {
                "id": "general",
                "name": "General Fitness Coach",
                "description": "Comprehensive fitness guidance",
            },
        ]
    }


@app.get("/n8n/webhooks/coaching/{coach_type}")
async def coaching_webhook_handler(coach_type: str, user_id: str, message: str):
    """
    Webhook endpoint that n8n can call to trigger coaching workflows.

    DEPRECATED: Use the /graphs/{graph_name}/execute endpoint instead.
    This is kept for backward compatibility.
    """
    request = GraphExecutionRequest(message=message, user_id=user_id)

    # Map coach_type to a graph_name (e.g., 'comprehensive_coaching')
    graph_name = "comprehensive_coaching"  # Or map based on coach_type

    result = await execute_graph_endpoint(graph_name, request)
    return result


@app.post("/n8n/webhooks/coaching")
async def coaching_webhook_handler_post(request: GraphExecutionRequest):
    """
    POST webhook endpoint for n8n Cloud integration.

    DEPRECATED: Use the /graphs/{graph_name}/execute endpoint instead.
    """
    try:
        logger.info(f"Received coaching request from n8n: user_id={request.user_id}")

        # Assuming a default graph for this webhook
        graph_name = "comprehensive_coaching"
        result = await execute_graph_endpoint(graph_name, request)

        # Add n8n-specific response format
        return {
            "status": "success",
            "coaching_response": result.response,
            "thread_id": result.thread_id,
            "execution_time": result.execution_time,
            "tools_used": result.tools_used,
            "reasoning_steps": result.reasoning_steps,
            "metadata": result.metadata,
            "n8n_data": {
                "user_id": request.user_id,
                "timestamp": datetime.now().isoformat(),
                "needs_follow_up": len(result.tools_used) > 0,
            },
        }

    except Exception as e:
        logger.error(f"Error in n8n coaching webhook: {e}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat(),
        }


@app.post("/coaching/execute", response_model=GraphExecutionResponse)
async def coaching_endpoint(request: GraphExecutionRequest):
    """
    Execute coaching workflow with intelligent graph selection.

    This endpoint automatically selects the appropriate coaching graph
    based on the request context.
    """
    # For now, use comprehensive coaching graph
    # TODO: Add intelligent graph selection logic
    graph_name = "comprehensive_coaching"  # Or map based on coach_type

    # Convert to the new request format
    graph_request = GraphRequest(
        message=request.message,
        user_id=request.user_id,
        thread_id=request.thread_id,
        metadata=request.graph_config or {},
    )

    result = await execute_graph_endpoint(graph_name, graph_request)

    # Convert back to the expected response format
    return GraphExecutionResponse(
        response=result.response,
        tools_used=result.tools_used,
        reasoning_steps=result.reasoning_steps,
        execution_time=result.execution_time,
        thread_id=result.thread_id,
        metadata=result.metadata,
    )


@app.post("/webhook", response_model=GraphExecutionResponse)
async def n8n_webhook_endpoint(request: dict):
    """
    Generic webhook endpoint for n8n integrations.

    This endpoint accepts various request formats from n8n workflows.
    """
    try:
        # Convert n8n webhook to our internal format
        graph_request = GraphRequest(
            message=request.get("message", ""),
            user_id=request.get("user_id", "webhook_user"),
            thread_id=request.get("thread_id"),
            metadata=request.get("metadata", {}),
        )

        # Assuming a default graph for this webhook
        graph_name = "comprehensive_coaching"
        result = await execute_graph_endpoint(graph_name, graph_request)

        # Convert to the expected response format
        return GraphExecutionResponse(
            response=result.response,
            tools_used=result.tools_used,
            reasoning_steps=result.reasoning_steps,
            execution_time=result.execution_time,
            thread_id=result.thread_id,
            metadata=result.metadata,
        )

    except Exception as e:
        logger.error(f"Webhook processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Webhook failed: {str(e)}")


async def async_workflow_executor(request: GraphExecutionRequest, graph_name: str):
    """Background task for async workflow execution."""
    try:
        logger.info(f"Background execution started for graph {graph_name}")

        # Convert to the new request format
        graph_request = GraphRequest(
            message=request.message,
            user_id=request.user_id,
            thread_id=request.thread_id,
            metadata=request.graph_config or {},
        )

        # Assuming a default graph for async execution
        result = await execute_graph_endpoint(graph_name, graph_request)

        # TODO: Trigger follow-up n8n workflows based on result
        logger.info(f"Background execution completed for user {request.user_id}")

    except Exception as e:
        logger.error(f"Background execution failed: {e}")


@app.on_event("startup")
async def startup_event():
    """Start ngrok tunnel on service startup"""
    logger.info("🚀 n8n LangGraph Integration Service starting...")

    # Start ngrok tunnel
    tunnel_url = start_ngrok()
    if tunnel_url:
        logger.info("✅ Service ready with ngrok tunnel!")
        logger.info(f"📝 Use this URL in your n8n HTTP Request: {tunnel_url}/graphs")
    else:
        logger.info("✅ Service ready on localhost only!")
        logger.info("📝 For local n8n: http://localhost:8001/graphs")


@app.on_event("shutdown")
async def shutdown_event():
    """Stop ngrok tunnel on service shutdown"""
    logger.info("🛑 Shutting down n8n LangGraph Integration Service...")
    stop_ngrok()
    logger.info("👋 Service stopped")


if __name__ == "__main__":
    logger.info("🚀 Starting Athlea LangGraph n8n Integration Service...")
    logger.info("🔧 Initializing dynamic graph factory...")

    # Initialize the graph factory to verify it works
    try:
        factory = get_graph_factory()
        graphs = factory.list_available_graphs()
        logger.info(f"✅ Found {len(graphs)} available graphs: {', '.join(graphs)}")
    except Exception as e:
        logger.error(f"❌ Failed to initialize graph factory: {e}")
        exit(1)

    logger.info("📡 Starting FastAPI server on port 8001...")
    logger.info("🌐 ngrok tunnel will be created automatically...")
    logger.info("💡 Check the startup logs for your ngrok URL!")

    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
