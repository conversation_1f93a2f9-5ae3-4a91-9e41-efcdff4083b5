#!/usr/bin/env python3
"""
Direct Web Search Tool Test

Tests the web search tool directly without package imports.
"""

import asyncio
import sys
from pathlib import Path

# Add the specific module path
sys.path.insert(0, str(Path(__file__).parent / "athlea_langgraph" / "tools"))

async def test_web_search():
    print("🌐 Testing Web Search Tool Directly")
    print("-" * 50)
    
    try:
        # Import directly from the module file
        from web_search_tool import WebSearchTool, search_web, web_search_tool
        
        print("✅ Web search tool imported successfully")
        
        # Create tool instance
        tool = WebSearchTool()
        print(f"✅ Tool created: {tool.name}")
        print(f"   Description: {tool.description[:100]}...")
        
        # Test async search
        print("\n🔍 Testing async web search...")
        result = await search_web("strength training techniques")
        
        print("✅ Async search completed")
        print(f"   Result length: {len(result)} characters")
        print(f"   Result preview: {result[:300]}...")
        
        # Test tool's _arun method
        print("\n🔍 Testing tool's _arun method...")
        result2 = await tool._arun("cardio training benefits")
        
        print("✅ Tool _arun completed")
        print(f"   Result length: {len(result2)} characters")
        print(f"   Result preview: {result2[:300]}...")
        
        # Test with JSON input
        print("\n🔍 Testing JSON input...")
        json_input = '{"query": "nutrition for athletes", "max_results": 3}'
        result3 = await tool._arun(json_input)
        
        print("✅ JSON input test completed")
        print(f"   Result length: {len(result3)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Web search tool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 Direct Web Search Tool Test")
    print("=" * 60)
    
    success = await test_web_search()
    
    print("\n📋 TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("✅ Web search tool is working correctly!")
        print("\n🎯 Key Findings:")
        print("- Tool can be imported and instantiated")
        print("- Async search functionality works")
        print("- Both string and JSON inputs are supported")
        print("- Fallback functionality is operational")
        print("\n🔧 Next Steps:")
        print("1. The web search tool is ready for agent integration")
        print("2. MCP integration can be added when available")
        print("3. Tool calls should work in the optimized graph")
    else:
        print("❌ Web search tool has issues that need to be resolved")

if __name__ == "__main__":
    asyncio.run(main()) 