"""
Individual Agent Graphs

This module provides individual graphs for testing and debugging
each specialized agent in isolation. Useful for development and testing.
"""

import logging
from typing import Any, Dict, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from ...agents.head_coach import clarification_node, head_coach_node
from ...agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    strength_coach_node,
)

# Import state and agents
from ...states.state import AgentState

logger = logging.getLogger(__name__)


def safe_get_content(message: Union[Dict[str, Any], BaseMessage]) -> str:
    """
    Safely extract content from either a dict or message object.

    Args:
        message: Either a dict with 'content' key or a BaseMessage object

    Returns:
        The content string, or empty string if not found
    """
    if isinstance(message, dict):
        return message.get("content", "")
    elif hasattr(message, "content"):
        return message.content
    else:
        return str(message) if message else ""


class IndividualAgentState(AgentState):
    """Simplified state for individual agent testing."""

    current_agent: Optional[str] = None
    agent_response: Optional[str] = None
    debug_info: Dict[str, Any] = {}


async def _archived_create_head_coach_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the head coach routing and clarification logic."""

    if config is None:
        config = {}

    logger.info("Creating head coach test graph")

    async def head_coach_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node that demonstrates head coach routing logic."""

        # For testing, we'll simulate the head coach decision making
        user_query = state.get("user_query", "")
        if not user_query and state.get("messages"):
            for msg in reversed(state["messages"]):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

        # Use the head coach node for routing logic
        result = await head_coach_node(state, config)

        # Add clarification if needed
        if not result or not result.get("current_step"):
            clarification_result = await clarification_node(state, config)
            return {
                "messages": state.get("messages", [])
                + clarification_result.get("messages", []),
                "current_agent": "head_coach",
                "agent_response": clarification_result.get("clarification_output", ""),
                "debug_info": {
                    "agent": "head_coach",
                    "action": "clarification",
                    "query": user_query,
                },
            }

        return {
            "messages": state.get("messages", [])
            + [
                AIMessage(
                    content=f"Head coach processed query: {user_query}. Routing decision made.",
                    name="head_coach",
                )
            ],
            "current_agent": "head_coach",
            "agent_response": f"Query analyzed and routing decision made for: {user_query}",
            "debug_info": {
                "agent": "head_coach",
                "action": "routing",
                "query": user_query,
                "result": result,
            },
        }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("head_coach", head_coach_test_node)
    builder.add_edge(START, "head_coach")
    builder.add_edge("head_coach", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Head coach test graph compiled successfully")
    return graph


async def _archived_create_strength_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the strength agent."""

    if config is None:
        config = {}

    logger.info("Creating strength agent test graph")

    async def strength_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for strength agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the strength agent node
            result = await strength_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "strength_agent",
                "agent_response": agent_response,
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "strength_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in strength agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Strength agent test error: {str(e)}",
                        name="strength_agent",
                    )
                ],
                "current_agent": "strength_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "strength_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("strength_agent", strength_test_node)
    builder.add_edge(START, "strength_agent")
    builder.add_edge("strength_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Strength agent test graph compiled successfully")
    return graph


async def _archived_create_nutrition_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the nutrition agent."""

    if config is None:
        config = {}

    logger.info("Creating nutrition agent test graph")

    async def nutrition_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for nutrition agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the nutrition agent node
            result = await nutrition_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "nutrition_agent",
                "agent_response": agent_response,
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "nutrition_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in nutrition agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Nutrition agent test error: {str(e)}",
                        name="nutrition_agent",
                    )
                ],
                "current_agent": "nutrition_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "nutrition_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("nutrition_agent", nutrition_test_node)
    builder.add_edge(START, "nutrition_agent")
    builder.add_edge("nutrition_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Nutrition agent test graph compiled successfully")
    return graph


async def _archived_create_cardio_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the cardio agent."""

    if config is None:
        config = {}

    logger.info("Creating cardio agent test graph")

    async def cardio_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for cardio agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the cardio agent node
            result = await cardio_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "cardio_agent",
                "agent_response": agent_response,
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "cardio_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in cardio agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Cardio agent test error: {str(e)}",
                        name="cardio_agent",
                    )
                ],
                "current_agent": "cardio_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "cardio_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("cardio_agent", cardio_test_node)
    builder.add_edge(START, "cardio_agent")
    builder.add_edge("cardio_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Cardio agent test graph compiled successfully")
    return graph


async def _archived_create_recovery_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the recovery agent."""

    if config is None:
        config = {}

    logger.info("Creating recovery agent test graph")

    async def recovery_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for recovery agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the recovery agent node
            result = await recovery_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "recovery_agent",
                "agent_response": agent_response,
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "recovery_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in recovery agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Recovery agent test error: {str(e)}",
                        name="recovery_agent",
                    )
                ],
                "current_agent": "recovery_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "recovery_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("recovery_agent", recovery_test_node)
    builder.add_edge(START, "recovery_agent")
    builder.add_edge("recovery_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Recovery agent test graph compiled successfully")
    return graph


async def _archived_create_mental_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the mental agent."""

    if config is None:
        config = {}

    logger.info("Creating mental agent test graph")

    async def mental_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for mental agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the mental agent node
            result = await mental_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "mental_agent",
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "mental_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in mental agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Mental agent test error: {str(e)}",
                        name="mental_agent",
                    )
                ],
                "current_agent": "mental_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "mental_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("mental_agent", mental_test_node)
    builder.add_edge(START, "mental_agent")
    builder.add_edge("mental_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Mental agent test graph compiled successfully")
    return graph


async def _archived_create_cycling_agent_graph(
    config: Optional[Dict[str, Any]] = None,
) -> CompiledStateGraph:
    """Create a simple graph for testing the cycling agent."""

    if config is None:
        config = {}

    logger.info("Creating cycling agent test graph")

    async def cycling_test_node(state: IndividualAgentState) -> Dict[str, Any]:
        """Test node for cycling agent."""

        try:
            # Ensure we have proper message format for the agent
            messages = list(state.get("messages", []))

            # Always check for user input in various possible dictionary keys
            user_input = (
                state.get("input")
                or state.get("user_query")
                or state.get("query")
                or state.get("text")
            )

            # If we found user input, add it as a new message
            if user_input:
                from langchain_core.messages import HumanMessage

                new_message = HumanMessage(content=str(user_input))
                messages.append(new_message)

            # Update state with proper messages
            updated_state = {**state, "messages": messages}

            # Call the cycling agent node
            result = await cycling_coach_node(updated_state, config)

            # Safely get content from the last message
            response_messages = result.get("messages", [])
            agent_response = ""
            if response_messages:
                last_message = response_messages[-1]
                agent_response = safe_get_content(last_message)

            return {
                "messages": messages
                + response_messages,  # Combine user input + AI response
                "current_agent": "cycling_agent",
                "agent_response": agent_response,
                "debug_info": {
                    "agent": "cycling_agent",
                    "specialist_completed": result.get("specialist_completed", False),
                    "metadata": result.get("agent_metadata", {}),
                    "input_messages_count": len(messages),
                    "response_messages_count": len(response_messages),
                    "user_input_found": user_input is not None,
                    "user_input_value": str(user_input) if user_input else None,
                },
            }

        except Exception as e:
            logger.error(f"Error in cycling agent test: {e}")
            return {
                "messages": state.get("messages", [])
                + [
                    AIMessage(
                        content=f"Cycling agent test error: {str(e)}",
                        name="cycling_agent",
                    )
                ],
                "current_agent": "cycling_agent",
                "agent_response": f"Error: {str(e)}",
                "debug_info": {"agent": "cycling_agent", "error": str(e)},
            }

    # Build graph
    builder = StateGraph(IndividualAgentState)
    builder.add_node("cycling_agent", cycling_test_node)
    builder.add_edge(START, "cycling_agent")
    builder.add_edge("cycling_agent", END)

    # Compile with memory if enabled
    if config.get("enable_memory", False):
        checkpointer = MemorySaver()
        graph = builder.compile(checkpointer=checkpointer)
    else:
        graph = builder.compile()

    logger.info("Cycling agent test graph compiled successfully")
    return graph


# Export all graph creation functions
# __all__ = [
#     "_archived_create_head_coach_graph",
#     "_archived_create_strength_agent_graph",
#     "_archived_create_nutrition_agent_graph",
#     "_archived_create_cardio_agent_graph",
#     "_archived_create_recovery_agent_graph",
#     "_archived_create_mental_agent_graph",
#     "_archived_create_cycling_agent_graph",
#     "safe_get_content",
#     "IndividualAgentState",
# ]
