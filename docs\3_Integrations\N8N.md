# n8n Integration Guide

This document provides a complete guide to integrating the Python-based LangGraph coaching system with the n8n workflow automation platform.

## 1. Integration Architecture

Direct integration between Python LangGraph and the JavaScript-based n8n is not supported. Therefore, we use a **hybrid architecture** where a FastAPI server acts as a bridge between the two systems.

```mermaid
graph LR
    A[n8n Trigger] --> B[Webhook/API Call];
    B --> C[FastAPI Wrapper];
    C --> D[Athlea LangGraph Workflow];
    D --> E[Response Processing];
    E --> F[n8n Follow-up Actions];
    F --> G[Email/Slack/Calendar];
```

**Benefits of this Approach:**
-   **Decoupled Systems**: We can maintain our Python LangGraph code without needing to translate it to JavaScript.
-   **Leverage n8n's Strengths**: We use n8n for what it excels at: workflow automation, scheduling, and connecting to hundreds of third-party services.
-   **Scalable and Maintainable**: The API-based integration is robust and easy to manage.

## 2. Setup and Configuration

The integration is powered by a complete n8n client service and a type-safe configuration system.

### Step 1: Set Environment Variables

Add your n8n API key and other optional settings to your `.env` file.

```bash
# Required - Your n8n API key from your n8n instance
N8N_API_KEY=your_n8n_api_key_here

# Optional - Your n8n instance URL (defaults to localhost:5678)
N8N_API_URL=https://your-n8n-instance.com/api/v1

# Optional - Webhook base URL for callbacks from n8n
N8N_WEBHOOK_BASE_URL=https://your-app.com/api/webhooks/n8n
```

The `n8n_client` is enabled automatically when the `N8N_API_KEY` is present.

### Step 2: Test the Integration

You can verify the connection to your n8n instance using the provided test scripts (`tests/test_n8n_client.py` and `tests/test_n8n_config.py`) or by running a simple script:

```python
import asyncio
from athlea_langgraph.services.n8n_client import create_n8n_client

async def test_n8n_connection():
    async with create_n8n_client() as client:
        if not client.config.is_enabled:
            print("n8n client is disabled. Set N8N_API_KEY to enable it.")
            return
        try:
            workflows = await client.list_workflows()
            print(f"✅ Successfully connected to n8n. Found {len(workflows)} workflows.")
        except Exception as e:
            print(f"❌ Error connecting to n8n: {e}")

asyncio.run(test_n8n_connection())
```

## 3. Usage and Examples

The `n8n_client` provides a suite of methods for interacting with n8n workflows.

### Triggering an n8n Workflow from LangGraph

You can trigger an n8n workflow from any part of the LangGraph system (e.g., an agent node or a dedicated tool).

```python
from athlea_langgraph.services.n8n_client import create_n8n_client

async def coaching_node(state):
    # ... coaching logic ...
    
    # After generating a response, trigger a follow-up n8n workflow
    async with create_n8n_client() as n8n:
        await n8n.execute_workflow(
            "follow-up-notifications-workflow-id",
            {
                "user_id": state["user_id"],
                "coaching_result": state["final_response"],
                "next_session_date": "2024-08-15"
            }
        )
    
    return state
```

### Triggering a LangGraph Workflow from n8n

The reverse is also possible. An n8n workflow can initiate a coaching session by calling the FastAPI wrapper.

1.  **Set up the FastAPI Wrapper**: Run the integration server which exposes your LangGraph workflows as HTTP endpoints (e.g., `POST /coaching/execute`).
2.  **Create an n8n Workflow**:
    -   Start with a **Webhook** trigger node. This provides a URL that can receive requests.
    -   Add an **HTTP Request** node to call the FastAPI endpoint (e.g., `http://host.docker.internal:8001/coaching/execute`).
    -   Pass the required data in the request body (e.g., `{ "message": "I need a new workout", "user_id": "user123" }`).
    -   Add subsequent nodes to handle the response from the LangGraph agent (e.g., send an email, update a database).

## 4. Production Deployment

Your production setup involves your deployed LangGraph API and your n8n Cloud instance.

### Step 1: Import Production Workflow to n8n

-   In your n8n Cloud instance (`https://athlea.app.n8n.cloud`), import the production workflow JSON file (`n8n_production_workflow.json`). This template contains the necessary nodes and logic.

### Step 2: Configure the LangGraph API Endpoint in n8n

-   In the n8n workflow, locate the "Call LangGraph API" node (an HTTP Request node).
-   Update the URL to point to your **publicly accessible LangGraph API endpoint**.
    -   For local testing with n8n Cloud, you can use a tool like **ngrok** to expose your local server. `ngrok http 8001` will give you a public URL.
    -   For a full production deployment, this would be your server's public IP or domain name (e.g., `https://api.athlea.com/n8n/webhooks/coaching`).

### Step 3: Activate the n8n Workflow

-   Save and **activate** the workflow in n8n Cloud. This makes the webhook "live" and ready to receive requests. The production webhook URL will look something like this: `https://athlea.app.n8n.cloud/webhook/0bd1c98d-0d78-49bf-ac94-d98d9a96b3ef`.

### Step 4: Secure Your Endpoint

For production, it is critical to secure your API.
-   **Add API Key Authentication**: Modify your FastAPI endpoint to require a secret API key in the header. Configure the HTTP Request node in n8n to send this header with every request.
-   **Use HTTPS**: Ensure your deployed API uses HTTPS.

### Example Production Workflow

```mermaid
graph LR
    A[External App/User] --> B[n8n Cloud Webhook URL];
    B --> C{Validate & Process Input};
    C --> D[Call Deployed LangGraph API];
    D --> E{Process AI Response};
    E --> F[Send Formatted Email];
    F --> G[Return Confirmation];
```

This setup creates a powerful, automated, and scalable system that combines the strengths of both LangGraph's AI agents and n8n's robust workflow automation. 