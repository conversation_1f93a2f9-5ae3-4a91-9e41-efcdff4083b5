#!/usr/bin/env python3
"""
Phase 2 Memory Schemas Test Suite

Tests the schema components of the Phase 2 advanced memory system without
external dependencies. This validates the data structures and logic of:
- Domain schemas and classification
- Analytics schemas and metrics
- Memory metadata and importance scoring
- Configuration classes
"""

import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict

import pytest

# Add the project root to the path for direct imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", ".."))

# Import Phase 2 schema components directly from files
try:
    from athlea_langgraph.memory.schemas.analytics_schemas import (
        AnalyticsReport,
        DateRange,
        HealthStatus,
        HealthStatusLevel,
        MemoryAnalyticsEvent,
        MemoryOperationType,
        PerformanceMetrics,
        RetrievalMetrics,
        StorageMetrics,
    )
    from athlea_langgraph.memory.schemas.domain_schemas import (
        DOMAIN_KEYWORDS,
        CoachingDomain,
        DomainClassification,
        MemoryDomainMetadata,
    )
    from athlea_langgraph.memory.schemas.memory_metadata import (
        AdvancedMemoryMetadata,
        ImportanceDecayMode,
        ImportanceScore,
        MemoryReference,
        SummaryLevel,
        SummaryMetadata,
    )

    IMPORTS_SUCCESSFUL = True

except ImportError as e:
    print(f"❌ Schema import failed: {e}")
    print("This might be due to missing dependencies in the Phase 2 implementation.")
    IMPORTS_SUCCESSFUL = False


class TestDomainSchemas:
    """Test domain-related schemas and functionality."""

    def test_coaching_domain_enum(self):
        """Test coaching domain enumeration."""
        assert CoachingDomain.STRENGTH.value == "strength"
        assert CoachingDomain.NUTRITION.value == "nutrition"
        assert CoachingDomain.CARDIO.value == "cardio"
        assert CoachingDomain.GENERAL.value == "general"

    def test_domain_classification(self):
        """Test domain classification data structure."""
        classification = DomainClassification(
            primary_domain=CoachingDomain.STRENGTH,
            secondary_domains=[CoachingDomain.CARDIO],
            confidence_scores={
                CoachingDomain.STRENGTH: 0.8,
                CoachingDomain.CARDIO: 0.3,
            },
        )

        assert classification.primary_domain == CoachingDomain.STRENGTH
        assert len(classification.secondary_domains) == 1
        assert classification.confidence_scores[CoachingDomain.STRENGTH] == 0.8

        # Test all_domains property
        all_domains = classification.all_domains
        assert CoachingDomain.STRENGTH in all_domains
        assert CoachingDomain.CARDIO in all_domains
        assert len(all_domains) == 2

    def test_memory_domain_metadata_serialization(self):
        """Test domain metadata serialization and deserialization."""
        classification = DomainClassification(
            primary_domain=CoachingDomain.NUTRITION,
            secondary_domains=[CoachingDomain.RECOVERY],
            confidence_scores={CoachingDomain.NUTRITION: 0.9},
        )

        metadata = MemoryDomainMetadata(
            domain_classification=classification,
            domain_specific_tags=["protein", "calories"],
            cross_domain_references=["mem_123", "mem_456"],
            domain_relevance_score=0.85,
        )

        # Test serialization
        data_dict = metadata.to_dict()
        assert data_dict["primary_domain"] == "nutrition"
        assert "recovery" in data_dict["secondary_domains"]
        assert data_dict["domain_specific_tags"] == ["protein", "calories"]
        assert data_dict["domain_relevance_score"] == 0.85

        # Test deserialization
        reconstructed = MemoryDomainMetadata.from_dict(data_dict)
        assert (
            reconstructed.domain_classification.primary_domain
            == CoachingDomain.NUTRITION
        )
        assert (
            CoachingDomain.RECOVERY
            in reconstructed.domain_classification.secondary_domains
        )
        assert reconstructed.domain_specific_tags == ["protein", "calories"]

    def test_domain_keywords_coverage(self):
        """Test that domain keywords are comprehensive."""
        # All domains should have keywords
        for domain in CoachingDomain:
            assert domain in DOMAIN_KEYWORDS, f"Domain {domain} missing keywords"
            assert len(DOMAIN_KEYWORDS[domain]) > 0, f"Domain {domain} has no keywords"

        # Test specific keyword presence
        assert "deadlift" in DOMAIN_KEYWORDS[CoachingDomain.STRENGTH]
        assert "nutrition" in DOMAIN_KEYWORDS[CoachingDomain.NUTRITION]
        assert "cardio" in DOMAIN_KEYWORDS[CoachingDomain.CARDIO]


class TestAnalyticsSchemas:
    """Test analytics and monitoring schemas."""

    def test_memory_analytics_event(self):
        """Test analytics event structure and serialization."""
        event = MemoryAnalyticsEvent(
            event_id="evt_123",
            user_id="user_456",
            operation_type=MemoryOperationType.SEARCH,
            timestamp=datetime.now(),
            duration_ms=250.5,
            success=True,
            metadata={"query": "workout plan", "results": 5},
        )

        assert event.operation_type == MemoryOperationType.SEARCH
        assert event.success is True
        assert event.duration_ms == 250.5

        # Test serialization
        data = event.to_dict()
        assert data["operation_type"] == "search"
        assert data["success"] is True
        assert data["metadata"]["query"] == "workout plan"

    def test_retrieval_metrics(self):
        """Test retrieval metrics structure."""
        metrics = RetrievalMetrics(
            total_queries=100,
            average_latency_ms=150.0,
            hit_rate=0.85,
            average_relevance_score=0.75,
            unique_users=25,
            domain_distribution={"strength": 40, "nutrition": 30, "cardio": 30},
        )

        assert metrics.total_queries == 100
        assert metrics.hit_rate == 0.85

        # Test serialization
        data = metrics.to_dict()
        assert data["total_queries"] == 100
        assert data["domain_distribution"]["strength"] == 40

    def test_analytics_report(self):
        """Test comprehensive analytics report."""
        start_time = datetime.now() - timedelta(hours=24)
        end_time = datetime.now()

        retrieval_metrics = RetrievalMetrics(
            total_queries=50,
            average_latency_ms=200.0,
            hit_rate=0.8,
            average_relevance_score=0.7,
            unique_users=10,
        )

        storage_metrics = StorageMetrics(
            total_memories=1000,
            memories_by_type={"conversation": 500, "goal": 100},
            memories_by_domain={"strength": 300, "nutrition": 200},
        )

        performance_metrics = PerformanceMetrics(
            average_operation_latency={MemoryOperationType.SEARCH: 150.0},
            operations_per_second=2.5,
            error_rate=0.02,
        )

        report = AnalyticsReport(
            start_time=start_time,
            end_time=end_time,
            retrieval_metrics=retrieval_metrics,
            storage_metrics=storage_metrics,
            performance_metrics=performance_metrics,
            recommendations=["Optimize search queries", "Archive old memories"],
        )

        assert report.duration.total_seconds() == pytest.approx(
            86400, rel=1e-2
        )  # ~24 hours
        assert len(report.recommendations) == 2

        # Test serialization
        data = report.to_dict()
        assert "retrieval_metrics" in data
        assert "duration_hours" in data
        assert data["recommendations"] == [
            "Optimize search queries",
            "Archive old memories",
        ]

    def test_health_status(self):
        """Test system health status."""
        health = HealthStatus(
            status_level=HealthStatusLevel.HEALTHY,
            timestamp=datetime.now(),
            component_status={"memory_adapter": HealthStatusLevel.HEALTHY},
            active_alerts=[],
        )

        assert health.status_level == HealthStatusLevel.HEALTHY
        assert len(health.active_alerts) == 0

        # Test alert addition
        health.add_alert("Warning: High latency detected")
        assert len(health.active_alerts) == 1
        assert health.status_level == HealthStatusLevel.WARNING

        health.add_alert("Critical error occurred")
        assert health.status_level == HealthStatusLevel.ERROR

    def test_date_range(self):
        """Test date range validation."""
        start = datetime.now() - timedelta(days=7)
        end = datetime.now()

        date_range = DateRange(start, end)
        assert date_range.duration_days == 7

        # Test invalid range
        with pytest.raises(ValueError):
            DateRange(end, start)  # End before start


class TestMemoryMetadata:
    """Test memory metadata and importance scoring."""

    def test_importance_score_decay(self):
        """Test importance score decay calculation."""
        base_time = datetime.now()

        # Linear decay
        score = ImportanceScore(
            base_score=1.0,
            current_score=1.0,
            decay_mode=ImportanceDecayMode.LINEAR,
            decay_rate=0.1,  # 10% per day
            last_accessed=base_time - timedelta(days=5),
        )

        # Calculate decay after 5 days
        decayed_score = score.calculate_time_decay(base_time)
        assert decayed_score == 0.5  # 1.0 - (0.1 * 5)

        # Exponential decay
        score.decay_mode = ImportanceDecayMode.EXPONENTIAL
        exp_decayed = score.calculate_time_decay(base_time)
        expected = 1.0 * ((1 - 0.1) ** 5)  # 0.9^5
        assert exp_decayed == pytest.approx(expected, rel=1e-3)

        # No decay
        score.decay_mode = ImportanceDecayMode.NONE
        no_decay = score.calculate_time_decay(base_time)
        assert no_decay == 1.0

    def test_importance_score_boost(self):
        """Test importance score access boost."""
        score = ImportanceScore(base_score=0.5, current_score=0.5, access_count=0)

        initial_count = score.access_count
        initial_score = score.current_score

        # Apply boost
        score.access_boost(boost_factor=0.2)

        assert score.access_count == initial_count + 1
        assert score.current_score == min(1.0, initial_score + 0.2)
        assert score.last_accessed is not None
        assert len(score.boost_events) == 1

    def test_importance_score_serialization(self):
        """Test importance score serialization."""
        score = ImportanceScore(
            base_score=0.8,
            current_score=0.6,
            access_count=5,
            last_accessed=datetime.now(),
            decay_mode=ImportanceDecayMode.LINEAR,
            decay_rate=0.05,
            boost_events=["access_boost_1", "access_boost_2"],
        )

        # Serialize
        data = score.to_dict()
        assert data["base_score"] == 0.8
        assert data["decay_mode"] == "linear"
        assert len(data["boost_events"]) == 2

        # Deserialize
        reconstructed = ImportanceScore.from_dict(data)
        assert reconstructed.base_score == 0.8
        assert reconstructed.decay_mode == ImportanceDecayMode.LINEAR
        assert reconstructed.access_count == 5

    def test_memory_reference(self):
        """Test memory reference structure."""
        ref = MemoryReference(
            memory_id="mem_123", relationship_type="similar", confidence=0.85
        )

        assert ref.memory_id == "mem_123"
        assert ref.relationship_type == "similar"
        assert ref.confidence == 0.85

        # Test serialization
        data = ref.to_dict()
        assert data["memory_id"] == "mem_123"
        assert data["relationship_type"] == "similar"

        # Test deserialization
        reconstructed = MemoryReference.from_dict(data)
        assert reconstructed.memory_id == "mem_123"
        assert reconstructed.confidence == 0.85

    def test_summary_metadata(self):
        """Test summary metadata structure."""
        summary_meta = SummaryMetadata(
            original_content_length=1000,
            summary_content_length=300,
            summarization_method="llm",
            summarization_timestamp=datetime.now(),
            summary_quality_score=0.85,
            key_points=["Goal setting", "Progress tracking", "Form improvement"],
        )

        # Test compression ratio calculation
        assert summary_meta.compression_ratio == 0.3

        # Test serialization
        data = summary_meta.to_dict()
        assert data["compression_ratio"] == 0.3
        assert len(data["key_points"]) == 3

        # Test deserialization
        reconstructed = SummaryMetadata.from_dict(data)
        assert reconstructed.original_content_length == 1000
        assert len(reconstructed.key_points) == 3

    def test_advanced_memory_metadata(self):
        """Test advanced memory metadata integration."""
        importance = ImportanceScore(base_score=0.8, current_score=0.7)

        metadata = AdvancedMemoryMetadata(
            importance_score=importance,
            summary_level=SummaryLevel.FULL,
            tags=["strength", "form", "progression"],
        )

        # Test reference addition
        metadata.add_reference("mem_456", "similar", 0.9)
        assert len(metadata.cross_references) == 1
        assert metadata.cross_references[0].memory_id == "mem_456"

        # Test reference ID retrieval
        similar_refs = metadata.get_reference_ids("similar")
        assert "mem_456" in similar_refs

        # Test summary level update
        summary_meta = SummaryMetadata(
            original_content_length=500,
            summary_content_length=150,
            summarization_method="llm",
            summarization_timestamp=datetime.now(),
        )

        metadata.update_summary_level(SummaryLevel.SUMMARIZED, summary_meta)
        assert metadata.summary_level == SummaryLevel.SUMMARIZED
        assert metadata.summary_metadata is not None

        # Test serialization
        data = metadata.to_dict()
        assert data["summary_level"] == "summarized"
        assert len(data["tags"]) == 3
        assert len(data["cross_references"]) == 1

        # Test deserialization
        reconstructed = AdvancedMemoryMetadata.from_dict(data)
        assert reconstructed.summary_level == SummaryLevel.SUMMARIZED
        assert reconstructed.importance_score.base_score == 0.8
        assert len(reconstructed.tags) == 3


def test_schema_integration():
    """Test that schemas work together correctly."""
    if not IMPORTS_SUCCESSFUL:
        pytest.skip("Schema imports failed, skipping integration test")

    # Create a domain classification
    domain_classification = DomainClassification(
        primary_domain=CoachingDomain.STRENGTH,
        confidence_scores={CoachingDomain.STRENGTH: 0.9},
    )

    # Create importance score
    importance = ImportanceScore(base_score=0.8, current_score=0.75)

    # Create advanced metadata
    advanced_metadata = AdvancedMemoryMetadata(
        importance_score=importance,
        summary_level=SummaryLevel.FULL,
        tags=["deadlift", "form"],
    )

    # Create analytics event
    event = MemoryAnalyticsEvent(
        event_id="test_123",
        user_id="user_456",
        operation_type=MemoryOperationType.ADD,
        timestamp=datetime.now(),
        duration_ms=100.0,
        success=True,
        metadata={"memory_type": "workout_log"},
    )

    # Verify all components work together
    assert domain_classification.primary_domain == CoachingDomain.STRENGTH
    assert importance.base_score == 0.8
    assert advanced_metadata.summary_level == SummaryLevel.FULL
    assert event.operation_type == MemoryOperationType.ADD


if __name__ == "__main__":
    # Run with pytest or standalone
    import sys

    print("🧪 Testing Phase 2 Memory Schemas")
    print("=" * 40)

    if not IMPORTS_SUCCESSFUL:
        print("❌ Cannot run tests - schema imports failed")
        print("Please ensure Phase 2 schema files are properly implemented:")
        print("  - athlea_langgraph/memory/schemas/domain_schemas.py")
        print("  - athlea_langgraph/memory/schemas/analytics_schemas.py")
        print("  - athlea_langgraph/memory/schemas/memory_metadata.py")
        sys.exit(1)

    try:
        # Test domain schemas
        print("Testing domain schemas...")
        domain_test = TestDomainSchemas()
        domain_test.test_coaching_domain_enum()
        domain_test.test_domain_classification()
        domain_test.test_memory_domain_metadata_serialization()
        domain_test.test_domain_keywords_coverage()
        print("✅ Domain schemas tests passed")

        # Test analytics schemas
        print("Testing analytics schemas...")
        analytics_test = TestAnalyticsSchemas()
        analytics_test.test_memory_analytics_event()
        analytics_test.test_retrieval_metrics()
        analytics_test.test_analytics_report()
        analytics_test.test_health_status()
        analytics_test.test_date_range()
        print("✅ Analytics schemas tests passed")

        # Test memory metadata
        print("Testing memory metadata...")
        metadata_test = TestMemoryMetadata()
        metadata_test.test_importance_score_decay()
        metadata_test.test_importance_score_boost()
        metadata_test.test_importance_score_serialization()
        metadata_test.test_memory_reference()
        metadata_test.test_summary_metadata()
        metadata_test.test_advanced_memory_metadata()
        print("✅ Memory metadata tests passed")

        # Test integration
        print("Testing schema integration...")
        test_schema_integration()
        print("✅ Schema integration tests passed")

        print("\n🎉 All Phase 2 memory schema tests passed!")
        print("✅ Domain classification and metadata")
        print("✅ Analytics events and reporting")
        print("✅ Memory importance and decay")
        print("✅ Summary metadata and references")
        print("✅ Cross-component integration")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
