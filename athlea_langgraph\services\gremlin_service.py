"""
Gremlin Graph Service for Athlea LangGraph Integration

Service for querying the existing Cosmos DB Gremlin Graph that contains
structured knowledge relationships extracted from research papers.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
import json
import time
import uuid
import concurrent.futures
import threading

from pydantic import BaseModel, Field

from ..config.graphrag_config import get_graphrag_config
from ..tools.circuit_breaker import CircuitBreaker

logger = logging.getLogger(__name__)

try:
    from gremlin_python.driver import client
    from gremlin_python.driver.protocol import GremlinServerError

    GREMLIN_AVAILABLE = True
except ImportError:
    logger.warning("gremlin_python not available. Gremlin service will be disabled.")
    client = None
    GremlinServerError = Exception
    GREMLIN_AVAILABLE = False


class GremlinQueryInput(BaseModel):
    """Input schema for Gremlin queries."""

    query: str = Field(description="Gremlin query string")
    parameters: Optional[Dict[str, Any]] = Field(
        default=None, description="Query parameters"
    )
    timeout_ms: Optional[int] = Field(
        default=None, description="Query timeout in milliseconds"
    )


class GremlinQueryOutput(BaseModel):
    """Output schema for Gremlin query results."""

    success: bool = Field(description="Whether the query was successful")
    results: List[Any] = Field(
        default=[], description="Query results (flexible schema)"
    )
    result_count: int = Field(default=0, description="Number of results returned")
    query: str = Field(description="Original query executed")
    execution_time_ms: int = Field(description="Query execution time in milliseconds")
    request_id: str = Field(description="Unique request identifier")
    error_type: Optional[str] = Field(default=None, description="Error type if failed")
    message: str = Field(description="Result message or error description")


class ThreadSafeGremlinClient:
    """Thread-safe wrapper for Gremlin client to avoid event loop conflicts."""

    def __init__(self, connection_string: str, username: str, password: str):
        self.connection_string = connection_string
        self.username = username
        self.password = password
        self._lock = threading.Lock()
        self._clients = {}  # Thread-local clients

    def _get_thread_client(self):
        """Get or create a client for the current thread."""
        if not GREMLIN_AVAILABLE:
            raise RuntimeError("gremlin_python not available")

        thread_id = threading.get_ident()

        if thread_id not in self._clients:
            try:
                logger.info(f"Creating new Gremlin client for thread {thread_id}")
                self._clients[thread_id] = client.Client(
                    self.connection_string,
                    "g",
                    username=self.username,
                    password=self.password,
                    message_serializer=client.serializer.GraphSONSerializersV2d0(),
                )
                logger.info(
                    f"Successfully created Gremlin client for thread {thread_id}"
                )
            except Exception as e:
                logger.error(
                    f"Failed to create Gremlin client for thread {thread_id}: {e}"
                )
                raise

        return self._clients[thread_id]

    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None):
        """Execute a Gremlin query in a thread-safe manner."""
        try:
            gremlin_client = self._get_thread_client()

            if parameters:
                result_set = gremlin_client.submit(query, parameters)
            else:
                result_set = gremlin_client.submit(query)

            # Convert results to list of dictionaries
            results = []
            for result in result_set:
                if isinstance(result, dict):
                    results.append(result)
                elif isinstance(result, list):
                    # Handle list results - convert each item to dict
                    for item in result:
                        if isinstance(item, dict):
                            results.append(item)
                        else:
                            # Convert complex objects to dict representation
                            try:
                                converted = json.loads(json.dumps(item, default=str))
                                if isinstance(converted, dict):
                                    results.append(converted)
                                else:
                                    results.append(
                                        {"value": str(item), "type": "converted"}
                                    )
                            except:
                                results.append({"value": str(item), "type": "fallback"})
                else:
                    # Convert complex objects to dict representation
                    try:
                        converted = json.loads(json.dumps(result, default=str))
                        if isinstance(converted, dict):
                            results.append(converted)
                        else:
                            results.append({"value": str(result), "type": "converted"})
                    except:
                        results.append({"value": str(result), "type": "fallback"})

            return results

        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise

    def close_all(self):
        """Close all thread-local clients without event loop conflicts."""
        with self._lock:
            for thread_id, client_instance in list(self._clients.items()):
                try:
                    # Close synchronously to avoid event loop conflicts
                    if (
                        hasattr(client_instance, "_transport")
                        and client_instance._transport
                    ):
                        # Force close the transport without waiting for async cleanup
                        client_instance._transport._session = None

                    # Use the basic close method without async operations
                    if hasattr(client_instance, "_client_connection"):
                        client_instance._client_connection = None

                    logger.info(f"Closed Gremlin client for thread {thread_id}")
                except Exception as e:
                    logger.warning(f"Error closing client for thread {thread_id}: {e}")
                    # Continue cleanup even if one client fails

            # Clear all clients
            self._clients.clear()
            logger.info("All Gremlin clients cleared from registry")


class GremlinService:
    """
    Service for querying Cosmos DB Gremlin Graph.

    Provides methods for executing Gremlin queries against the knowledge graph
    containing structured relationships between research entities.
    """

    def __init__(self):
        self.config = get_graphrag_config()
        self.circuit_breaker = CircuitBreaker("gremlin_service")
        self._connection_string = None
        self._username = None
        self._thread_client = None
        self._executor = None

        if not GREMLIN_AVAILABLE:
            logger.warning(
                "Gremlin service initialized but gremlin_python is not available"
            )
            return

        self._init_connection_parameters()

    def _init_connection_parameters(self):
        """Initialize the Gremlin connection parameters."""
        try:
            # Use Microsoft's standard Cosmos DB Gremlin endpoint format
            self._connection_string = self.config.cosmos_endpoint
            self._username = self.config.cosmos_username

            logger.info(f"Initialized Gremlin connection parameters:")
            logger.info(f"  Account name: {self.config.cosmos_account_name}")
            logger.info(f"  Connection string: {self._connection_string}")
            logger.info(f"  Username: {self._username}")
            logger.info(f"  Database: {self.config.cosmos_database}")
            logger.info(f"  Graph: {self.config.cosmos_graph}")

            # Initialize thread-safe client and executor
            self._thread_client = ThreadSafeGremlinClient(
                self._connection_string, self._username, self.config.cosmos_key
            )

            # Create a dedicated thread pool for Gremlin operations
            self._executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=5, thread_name_prefix="gremlin_worker"
            )

        except Exception as e:
            logger.error(f"Failed to initialize Gremlin connection parameters: {e}")
            raise ValueError(f"Invalid Cosmos DB Gremlin configuration: {e}")

    async def execute_query(
        self, query_input: Union[Dict[str, Any], GremlinQueryInput]
    ) -> GremlinQueryOutput:
        """
        Execute a Gremlin query against the knowledge graph.

        Args:
            query_input: Query parameters including Gremlin query string

        Returns:
            GremlinQueryOutput: Query results and metadata
        """
        start_time = time.time()
        request_id = str(uuid.uuid4())[:8]

        # Check if Gremlin is available
        if not GREMLIN_AVAILABLE:
            exec_time = int((time.time() - start_time) * 1000)
            logger.warning(
                f"Gremlin query {request_id} failed: gremlin_python not available"
            )
            return GremlinQueryOutput(
                success=False,
                query=(
                    query_input.get("query", "")
                    if isinstance(query_input, dict)
                    else query_input.query
                ),
                result_count=0,
                error_type="gremlin_unavailable",
                message="Gremlin service not available: gremlin_python not installed",
                request_id=request_id,
                execution_time_ms=exec_time,
            )

        # Validate input
        if isinstance(query_input, dict):
            try:
                validated_input = GremlinQueryInput(**query_input)
            except Exception as e:
                exec_time = int((time.time() - start_time) * 1000)
                logger.error(f"Gremlin query validation failed: {e}")
                return GremlinQueryOutput(
                    success=False,
                    query=query_input.get("query", ""),
                    result_count=0,
                    error_type="validation_error",
                    message=f"Input validation failed: {str(e)}",
                    request_id=request_id,
                    execution_time_ms=exec_time,
                )
        else:
            validated_input = query_input

        logger.info(
            f"Gremlin query request {request_id}: {validated_input.query[:100]}..."
        )

        # Check circuit breaker
        if not self.circuit_breaker.can_execute():
            exec_time = int((time.time() - start_time) * 1000)
            logger.warning(f"Circuit breaker OPEN for Gremlin request {request_id}")
            return GremlinQueryOutput(
                success=False,
                query=validated_input.query,
                result_count=0,
                error_type="circuit_breaker_open",
                message="Gremlin service temporarily unavailable",
                request_id=request_id,
                execution_time_ms=exec_time,
            )

        # Execute query with retries
        for attempt in range(self.config.max_retries):
            try:
                # Execute Gremlin query in dedicated thread pool
                loop = asyncio.get_event_loop()
                results = await loop.run_in_executor(
                    self._executor,
                    self._execute_sync_query,
                    validated_input.query,
                    validated_input.parameters,
                )

                self.circuit_breaker.record_success()
                exec_time = int((time.time() - start_time) * 1000)

                logger.info(
                    f"Gremlin query {request_id} successful: {len(results)} results in {exec_time}ms"
                )

                # Debug: Log the structure of results
                if results:
                    logger.debug(
                        f"Gremlin results structure: {type(results[0])} - {str(results[0])[:100]}..."
                    )

                try:
                    return GremlinQueryOutput(
                        success=True,
                        results=results,
                        result_count=len(results),
                        query=validated_input.query,
                        execution_time_ms=exec_time,
                        request_id=request_id,
                        message="Query executed successfully",
                    )
                except Exception as e:
                    logger.error(f"Failed to create GremlinQueryOutput: {e}")
                    logger.error(
                        f"Results type: {type(results)}, First result: {results[0] if results else 'None'}"
                    )
                    # Return a simplified version
                    return GremlinQueryOutput(
                        success=True,
                        results=[{"data": str(r)} for r in results],
                        result_count=len(results),
                        query=validated_input.query,
                        execution_time_ms=exec_time,
                        request_id=request_id,
                        message="Query executed successfully (simplified results)",
                    )

            except GremlinServerError as e:
                logger.error(f"Gremlin server error on attempt {attempt + 1}: {e}")
                if attempt >= self.config.max_retries - 1:
                    self.circuit_breaker.record_failure()
                    exec_time = int((time.time() - start_time) * 1000)
                    return GremlinQueryOutput(
                        success=False,
                        query=validated_input.query,
                        result_count=0,
                        error_type="gremlin_server_error",
                        message=f"Gremlin server error: {str(e)}",
                        request_id=request_id,
                        execution_time_ms=exec_time,
                    )
                await asyncio.sleep(2**attempt)  # Exponential backoff

            except Exception as e:
                logger.error(f"Gremlin query error on attempt {attempt + 1}: {e}")
                if attempt >= self.config.max_retries - 1:
                    self.circuit_breaker.record_failure()
                    exec_time = int((time.time() - start_time) * 1000)
                    return GremlinQueryOutput(
                        success=False,
                        query=validated_input.query,
                        result_count=0,
                        error_type="query_execution_error",
                        message=f"Query execution failed: {str(e)}",
                        request_id=request_id,
                        execution_time_ms=exec_time,
                    )
                await asyncio.sleep(2**attempt)

    def _execute_sync_query(
        self, query: str, parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Synchronous function to execute Gremlin query in thread pool.
        This runs in a separate thread to avoid asyncio event loop conflicts.
        """
        try:
            return self._thread_client.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Synchronous query execution failed: {e}")
            raise

    async def find_document_by_doi(self, doi: str) -> GremlinQueryOutput:
        """Find a paper node by DOI."""
        query = f"g.V().hasLabel('Paper').has('DOI', '{doi}').valueMap(true)"
        return await self.execute_query(GremlinQueryInput(query=query))

    async def find_document_by_id(self, document_id: str) -> GremlinQueryOutput:
        """Find a paper node by DocumentID."""
        query = (
            f"g.V().hasLabel('Paper').has('DocumentID', '{document_id}').valueMap(true)"
        )
        return await self.execute_query(GremlinQueryInput(query=query))

    async def get_document_relationships(self, document_id: str) -> GremlinQueryOutput:
        """Get all relationships for a specific document."""
        query = f"""
        g.V().hasLabel('Paper').has('DocumentID', '{document_id}').as('paper')
         .project('paper_details', 'relationships')
         .by(valueMap(true))
         .by(
             bothE().as('edge')
             .otherV().as('connected_node')
             .select('edge', 'connected_node')
             .by(valueMap(true))
             .by(valueMap(true))
             .fold()
         )
        """
        return await self.execute_query(GremlinQueryInput(query=query))

    async def find_entity_relationships(
        self, entity_name: str, entity_type: Optional[str] = None
    ) -> GremlinQueryOutput:
        """Find relationships for a specific entity (e.g., 'FIFA 11+')."""
        if entity_type:
            query = f"""
            g.V().hasLabel('{entity_type}').has('name', '{entity_name}').as('entity')
             .project('entity_details', 'relationships')
             .by(valueMap(true))
             .by(
                 bothE().as('edge')
                 .otherV().as('connected_node')
                 .select('edge', 'connected_node')
                 .by(valueMap(true))
                 .by(valueMap(true))
                 .fold()
             )
            """
        else:
            query = f"""
            g.V().has('name', '{entity_name}').as('entity')
             .project('entity_details', 'relationships')
             .by(valueMap(true))
             .by(
                 bothE().as('edge')
                 .otherV().as('connected_node')
                 .select('edge', 'connected_node')
                 .by(valueMap(true))
                 .by(valueMap(true))
                 .fold()
             )
            """
        return await self.execute_query(GremlinQueryInput(query=query))

    async def close(self):
        """Close the Gremlin client connections and executor gracefully."""
        try:
            if self._thread_client:
                # Close all thread clients without waiting for async operations
                self._thread_client.close_all()
                logger.info("Closed all Gremlin thread clients")

            if self._executor:
                # Shutdown executor gracefully with a timeout
                self._executor.shutdown(wait=False, cancel_futures=True)
                logger.info("Shutdown Gremlin executor")

        except Exception as e:
            logger.warning(f"Error during Gremlin service cleanup: {e}")
            # Don't raise exceptions during cleanup to avoid disrupting other services


# Global service instance
_gremlin_service: Optional[GremlinService] = None


def get_gremlin_service() -> GremlinService:
    """Get the global Gremlin service instance."""
    global _gremlin_service
    if _gremlin_service is None:
        _gremlin_service = GremlinService()
    return _gremlin_service
