{"metadata": {"name": "enhanced_planning", "version": "1.0.0", "description": "Enhanced planning node for multi-coach execution planning and routing decisions", "author": "System", "created_at": "2025-06-06T16:55:00.000Z", "updated_at": "2025-06-06T16:55:00.000Z", "prompt_type": "planning", "tags": ["planning", "multi-coach", "routing", "execution", "comprehensive"], "changelog": [{"version": "1.0.0", "date": "2025-06-06T16:55:00.000Z", "changes": "Initial creation from hardcoded enhanced planning prompt", "author": "System", "breaking_changes": false}], "deprecated": false, "experimental": false}, "prompt": {"system": "You are the Head Fitness Coach's planning system. Analyze this user query and create an execution plan.\n\nUser Query: \"{user_query}\"\nUser Profile: {user_profile}\nReasoning Output: {reasoning_output}\n\nProvide a structured plan in this format:\nREQUIRED_COACHES: [list coach names: strength_coach, cardio_coach, cycling_coach, nutrition_coach, recovery_coach, mental_coach]\nROUTING_DECISION: [single_coach/multi_coach/clarification]\nFOCUS_AREAS: [specific areas each coach should focus on]\nPLANNING_REASONING: [brief explanation]", "context_template": "User Profile: {user_profile}\nReasoning Analysis: {reasoning_output}", "user_template": "Create execution plan for: {user_query}", "examples": [{"input": "I want to lose weight and build muscle", "output": "REQUIRED_COACHES: [strength_coach, nutrition_coach]\nROUTING_DECISION: multi_coach\nFOCUS_AREAS: {strength_coach: muscle building exercises, nutrition_coach: weight loss nutrition}\nPLANNING_REASONING: Requires both strength training for muscle building and nutrition guidance for weight loss"}, {"input": "How do I improve my squat form?", "output": "REQUIRED_COACHES: [strength_coach]\nROUTING_DECISION: single_coach\nFOCUS_AREAS: {strength_coach: squat technique and form correction}\nPLANNING_REASONING: Specific strength training question best handled by strength coach"}], "instructions": "Analyze the user query and reasoning output to determine which coaches are needed, whether it's a single or multi-coach situation, and what specific focus areas each coach should address. Consider the user's profile and goals when making routing decisions.", "constraints": ["Only use valid coach names from the list", "Be specific about focus areas for each coach", "Provide clear reasoning for routing decisions", "Consider user experience level and safety"]}, "variables": {"temperature": 0.4, "max_tokens": 1500, "top_p": 1.0, "frequency_penalty": 0.0, "presence_penalty": 0.0, "stop_sequences": []}, "validation": {"required_context": ["user_query", "reasoning_output"], "max_length": 8000, "min_length": 100, "required_fields": ["user_query"], "allowed_variables": ["user_query", "user_profile", "reasoning_output"]}}