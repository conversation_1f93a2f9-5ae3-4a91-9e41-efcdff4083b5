# Archived Integration Tests

This directory contains integration tests for experimental and deprecated functionality that is no longer actively used in production.

## Archived Test Files

### Memory Integration Tests
- `test_mem0_integration.py` - Tests for mem0 memory integration (deprecated)
- `test_memory_coaching_with_specialists.py` - Memory coaching with specialist agents
- `test_advanced_memory_integration.py` - Advanced memory integration patterns
- `test_phase2_memory_integration.py` - Phase 2 memory integration testing
- `test_pinecone_integration.py` - Pinecone vector database integration

### Debug and Quick Tests
- `quick_react_test.py` - Quick debugging test for ReAct pattern
- `debug_tool_input.py` - Debug utility for tool input validation
- `test_simple_graph.py` - Simple graph testing for debugging
- `test_specific_strength.py` - Specific strength routing debugging

## Note

These tests are preserved for historical reference and may be useful if the corresponding functionality is reintroduced. The memory functionality has been integrated into the main production graphs, making these standalone tests obsolete.

For current integration tests, see the parent directory which contains tests for:
- Production coaching graphs
- Streaming API functionality
- Tool integrations
- MCP workflow testing 