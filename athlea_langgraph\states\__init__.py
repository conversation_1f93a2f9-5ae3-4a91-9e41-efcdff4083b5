"""
State management module for the Athlea LangGraph coaching system.

Exports state classes, reducers, and utility functions for managing
conversation state, memory, onboarding, reflection, and ReWOO workflows.
"""

# Onboarding state exports
from .onboarding_state import (
    COMMON_SPORT_SUGGESTIONS,
    VALID_SPORT_VALUES,
    ExampleSession,
    OnboardingField,
    OnboardingStage,
    OnboardingState,
    PlanDetails,
    PlanPhase,
    SidebarStateData,
    SportSuggestion,
    SummaryItem,
    UserGoals,
    create_initial_onboarding_state,
)

# Reflection state exports - Phase 1
from .reflection_state import (
    ReflectionAgentState,
    ReflectionMetadata,
    SafetyValidation,
    create_initial_reflection_metadata,
    create_initial_safety_validation,
    create_reflection_config,
    enhance_state_with_reflection,
    reflection_config_reducer,
    reflection_metadata_reducer,
    safety_validation_reducer,
)

# ReWOO state exports - Phase 2
from .rewoo_state import (
    ExecutionPlan,
    ReWOOConfig,
    ReWOOState,
    Task,
    WorkerResult,
    coordination_metadata_reducer,
    create_default_rewoo_config,
    create_initial_rewoo_state,
    enhance_state_with_rewoo,
    parallel_execution_status_reducer,
    worker_results_reducer,
)

# Core state exports
from .state import (
    AgentState,
    GraphState,
    aggregated_plan_reducer,
    completed_domains_reducer,
    current_plan_reducer,
    current_step_reducer,
    domain_contributions_reducer,
    is_onboarding_reducer,
    messages_reducer,
    pending_agents_reducer,
    plan_reducer,
    proceed_to_generation_reducer,
    required_domains_reducer,
    routing_decision_reducer,
    user_profile_reducer,
)

# Memory-enhanced state exports
from .state_with_memory import (
    MemoryEnhancedAgentState,
    interaction_history_reducer,
    memory_context_reducer,
    personalization_data_reducer,
    relevant_memories_reducer,
    user_preferences_reducer,
)


# Utility functions
def get_state_schema(state_type: str = "agent"):
    """
    Get the appropriate state schema based on type.

    Args:
        state_type: Type of state schema to return
                   Options: "agent", "memory", "onboarding", "reflection", "rewoo"

    Returns:
        State class for the specified type
    """
    state_schemas = {
        "agent": AgentState,
        "memory": MemoryEnhancedAgentState,
        "onboarding": OnboardingState,
        "reflection": ReflectionAgentState,
        "rewoo": ReWOOState,
    }

    return state_schemas.get(state_type, AgentState)


def create_default_state(state_type: str = "agent", **kwargs):
    """
    Create a default state instance of the specified type.

    Args:
        state_type: Type of state to create
        **kwargs: Additional arguments for state initialization

    Returns:
        Initialized state instance
    """

    if state_type == "agent":
        return {
            "messages": [],
            "user_query": None,
            "user_profile": None,
            "routing_decision": None,
            "pending_agents": None,
            "plan": None,
            "current_step": None,
            "domain_contributions": {},
            "required_domains": [],
            "completed_domains": [],
            "aggregated_plan": None,
            "proceed_to_generation": False,
            "current_plan": None,
            "is_onboarding": False,
            "strength_response": None,
            "running_response": None,
            "cardio_response": None,
            "cycling_response": None,
            "nutrition_response": None,
            "recovery_response": None,
            "mental_response": None,
            "reasoning_output": None,
            "clarification_output": None,
            "aggregated_response": None,
        }

    elif state_type == "memory":
        base_state = create_default_state("agent")
        # Create memory-enhanced state manually since create_memory_enhanced_state doesn't exist
        memory_state = MemoryEnhancedAgentState(
            {
                **base_state,
                "memory_context": None,
                "relevant_memories": [],
                "user_preferences": {},
                "interaction_history": [],
                "personalization_data": {},
            }
        )
        return memory_state

    elif state_type == "onboarding":
        return create_initial_onboarding_state(**kwargs)

    elif state_type == "reflection":
        base_state = create_default_state("agent")
        return enhance_state_with_reflection(base_state, **kwargs)

    elif state_type == "rewoo":
        user_query = kwargs.get("user_query", "")
        user_profile = kwargs.get("user_profile")
        config = kwargs.get("config")
        return create_initial_rewoo_state(user_query, user_profile, config)

    else:
        raise ValueError(f"Unknown state type: {state_type}")


# Export all state-related components
__all__ = [
    # Core state
    "AgentState",
    "GraphState",
    "messages_reducer",
    "user_profile_reducer",
    "routing_decision_reducer",
    "pending_agents_reducer",
    "plan_reducer",
    "current_step_reducer",
    "domain_contributions_reducer",
    "required_domains_reducer",
    "completed_domains_reducer",
    "aggregated_plan_reducer",
    "proceed_to_generation_reducer",
    "current_plan_reducer",
    "is_onboarding_reducer",
    # Memory-enhanced state
    "MemoryEnhancedAgentState",
    "memory_context_reducer",
    "relevant_memories_reducer",
    "user_preferences_reducer",
    "interaction_history_reducer",
    "personalization_data_reducer",
    # Onboarding state
    "OnboardingState",
    "OnboardingStage",
    "OnboardingField",
    "UserGoals",
    "SummaryItem",
    "SportSuggestion",
    "PlanPhase",
    "ExampleSession",
    "PlanDetails",
    "SidebarStateData",
    "create_initial_onboarding_state",
    "COMMON_SPORT_SUGGESTIONS",
    "VALID_SPORT_VALUES",
    # Reflection state - Phase 1
    "ReflectionAgentState",
    "ReflectionMetadata",
    "SafetyValidation",
    "create_initial_reflection_metadata",
    "create_initial_safety_validation",
    "create_reflection_config",
    "enhance_state_with_reflection",
    "reflection_metadata_reducer",
    "safety_validation_reducer",
    "reflection_config_reducer",
    # ReWOO state - Phase 2
    "ReWOOState",
    "Task",
    "WorkerResult",
    "ExecutionPlan",
    "ReWOOConfig",
    "create_initial_rewoo_state",
    "create_default_rewoo_config",
    "enhance_state_with_rewoo",
    "worker_results_reducer",
    "parallel_execution_status_reducer",
    "coordination_metadata_reducer",
    # Utility functions
    "get_state_schema",
    "create_default_state",
]
