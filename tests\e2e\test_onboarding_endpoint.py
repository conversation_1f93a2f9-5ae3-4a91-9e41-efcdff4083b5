#!/usr/bin/env python3
"""
Test script for the onboarding endpoint

This script tests the onboarding API endpoint to verify it's working correctly.
"""

import asyncio
import json
from typing import Any, Dict

import httpx


async def test_onboarding_endpoint():
    """Test the onboarding streaming endpoint."""
    print("🧪 Testing Onboarding Endpoint")
    print("=" * 50)

    # Test data
    test_request = {
        "message": "Hi, I want to start getting fit!",
        "user_id": "test_user_123",
        "thread_id": "test_thread_123",
        "user_profile": {
            "name": "Test User",
            "age": 25,
        },
    }

    try:
        # Start the FastAPI server would need to be running
        # For this test, we'll import the app directly
        from fastapi.testclient import TestClient

        from athlea_langgraph.api.api_streaming import app

        client = TestClient(app)

        print(f"📤 Sending request:")
        print(f"   Message: {test_request['message']}")
        print(f"   User ID: {test_request['user_id']}")
        print(f"   Thread ID: {test_request['thread_id']}")

        # Test the onboarding endpoint (no stream parameter for TestClient)
        response = client.post("/api/onboarding", json=test_request)

        if response.status_code != 200:
            print(f"❌ Error: Status code {response.status_code}")
            print(f"   Response: {response.text}")
            return False

        print(f"✅ Connected to streaming endpoint")
        print(f"📡 Streaming response:")
        print("-" * 30)

        # For streaming responses with TestClient, we can read the content
        content = response.text
        if content:
            # Split into lines and show first few chunks
            lines = content.split("\n")
            chunk_count = 0
            for line in lines:
                if line.strip():
                    chunk_count += 1
                    print(f"[{chunk_count:02d}] {line}")

                    # Stop after reasonable number of chunks for testing
                    if chunk_count >= 10:
                        print("... (truncated for testing)")
                        break

            print("-" * 30)
            print(f"✅ Received {chunk_count} chunks successfully")
        else:
            print(
                "✅ Endpoint responded (streaming may not show content in TestClient)"
            )

        # Test status endpoint
        print(f"\n🔍 Testing status endpoint...")
        status_response = client.get(
            f"/api/onboarding/status/{test_request['user_id']}"
        )

        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"✅ Status endpoint works")
            print(f"   Stage: {status_data.get('stage', 'unknown')}")
            print(f"   Status: {status_data.get('status', 'unknown')}")
            print(f"   Needs input: {status_data.get('needs_input', 'unknown')}")
        else:
            print(f"❌ Status endpoint error: {status_response.status_code}")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure athlea_langgraph package is properly installed")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def test_with_actual_server():
    """Test with actual running server (if available)."""
    print("\n🌐 Testing with actual server...")

    base_url = "http://localhost:8000"  # Adjust as needed

    test_request = {
        "message": "Hello, I'm interested in fitness coaching!",
        "user_id": "live_test_user",
        "thread_id": "live_test_thread",
    }

    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint first
            try:
                health_response = await client.get(f"{base_url}/api/health")
                if health_response.status_code == 200:
                    print("✅ Server is running and healthy")
                else:
                    print(
                        f"⚠️  Server health check failed: {health_response.status_code}"
                    )
            except httpx.ConnectError:
                print(
                    "❌ Cannot connect to server - make sure it's running on localhost:8000"
                )
                return False

            # Test onboarding endpoint
            print("📤 Testing live onboarding endpoint...")

            async with client.stream(
                "POST",
                f"{base_url}/api/onboarding",
                json=test_request,
                headers={"Accept": "text/event-stream"},
            ) as response:

                if response.status_code != 200:
                    print(f"❌ Error: Status code {response.status_code}")
                    return False

                print("✅ Live streaming successful")
                chunk_count = 0

                async for chunk in response.aiter_lines():
                    if chunk:
                        chunk_count += 1
                        print(f"[{chunk_count:02d}] {chunk}")

                        if chunk_count >= 5:  # Limit for testing
                            print("... (truncated)")
                            break

                print(f"✅ Received {chunk_count} chunks from live server")
                return True

    except Exception as e:
        print(f"❌ Live test failed: {e}")
        return False


if __name__ == "__main__":

    async def main():
        print("🚀 ONBOARDING ENDPOINT TESTS")
        print("=" * 60)

        # Test 1: Direct app testing
        success1 = await test_onboarding_endpoint()

        # Test 2: Live server testing (optional)
        success2 = await test_with_actual_server()

        print("\n📋 TEST RESULTS:")
        print(f"   Direct app test: {'✅ PASS' if success1 else '❌ FAIL'}")
        print(f"   Live server test: {'✅ PASS' if success2 else '❌ FAIL'}")

        if success1 or success2:
            print("\n🎉 Onboarding endpoint is working!")
        else:
            print("\n💥 Tests failed - check implementation")

    asyncio.run(main())
