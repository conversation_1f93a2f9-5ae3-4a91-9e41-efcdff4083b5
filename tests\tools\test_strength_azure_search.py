#!/usr/bin/env python3
"""
Test script to debug why strength agent isn't calling azure_search_retriever directly
"""

import asyncio
import logging
from athlea_langgraph.agents.strength_agent import strength_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_azure_search_direct():
    """Test if azure_search_retriever can be called directly"""
    print("🔧 Testing Azure Search Direct Call")

    # Load tools
    await strength_agent.get_domain_tools()

    # Check available tools
    print(f"Available tools: {[tool.name for tool in strength_agent.tools]}")

    # Find azure_search_retriever tool
    azure_tool = None
    for tool in strength_agent.tools:
        if tool.name == "azure_search_retriever":
            azure_tool = tool
            break

    if not azure_tool:
        print("❌ azure_search_retriever tool not found!")
        return

    print(f"✅ Found azure_search_retriever tool: {azure_tool.description}")

    # Test the tool directly
    try:
        result = await azure_tool.arun(
            "progressive overload strength training research"
        )
        print(f"✅ Direct tool call successful: {len(result)} chars")
        print(f"Preview: {result[:200]}...")
    except Exception as e:
        print(f"❌ Direct tool call failed: {e}")


async def test_agent_tool_selection():
    """Test what tool the agent selects for research queries"""
    print("\n🤖 Testing Agent Tool Selection")

    test_state = {
        "user_query": "Find scientific studies on progressive overload for strength training",
        "messages": [],
        "user_profile": {},
    }

    try:
        result = await strength_agent.process(test_state)
        response = result.get("response", "")

        print(f"Agent response length: {len(response)} chars")

        # Check which tool was mentioned
        if "azure_search_retriever" in response:
            print("✅ Agent used azure_search_retriever")
        elif "graphrag_search" in response:
            print("⚠️ Agent used graphrag_search instead")
        else:
            print("❓ Tool usage unclear")

        print(f"Response preview: {response[:300]}...")

    except Exception as e:
        print(f"❌ Agent processing failed: {e}")


async def main():
    """Main test function"""
    print("🚀 Starting Azure Search Debug Test")

    await test_azure_search_direct()
    await test_agent_tool_selection()


if __name__ == "__main__":
    asyncio.run(main())
