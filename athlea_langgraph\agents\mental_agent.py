"""
Mental Agent - Modular ReAct Implementation

Specialized agent for mental training, sports psychology, motivation, and mindset coaching.
Follows multi-agent best practices with single responsibility and ReAct pattern.
"""

import logging
from typing import Any, Dict, List

from langchain_core.tools import BaseTool

from athlea_langgraph.states import AgentState

# Direct tool imports for self-contained agent
from ..tools.mental import (
    mental_state_assessment,
    stress_level_tracker,
    mood_pattern_analyzer,
    goal_tracker,
)
from ..utils.prompt_loader import get_prompt_loader
from .base_agent import BaseReActAgent

logger = logging.getLogger(__name__)


class MentalAgent(BaseReActAgent):
    """Specialized agent for mental training coaching using ReAct pattern."""

    def __init__(self):
        """Initialize the mental agent with domain-specific tools."""
        # Fallback prompt with strong tool calling instructions
        fallback_prompt = """You are a world-class Mental Performance Coach. Your primary function is to provide supportive, evidence-based advice on mental wellness, sports psychology, and mindset by leveraging your specialized tools.

**CRITICAL INSTRUCTIONS:**
1.  **Assess the User's Query:** Analyze the user's question to determine if one of your tools can provide a structured assessment or data-driven insight.
2.  **Prioritize Tool Usage:** For any request related to assessing mental state, tracking stress, analyzing mood patterns, or tracking goals, you MUST use the appropriate tool.
3.  **Use Tools Correctly:**
    - To perform a comprehensive assessment, use `mental_state_assessment`.
    - To track stress levels over time, use `stress_level_tracker`.
    - To analyze mood patterns, use `mood_pattern_analyzer`.
    - For goal management, use `goal_tracker`.
    - For research questions, use `azure_search_retriever` or `web_search`.
4.  **Handle Sensitive Information:** When using `mental_state_assessment`, if the user doesn't provide specific 1-10 ratings, you can estimate them based on their qualitative descriptions (e.g., "feeling terrible" -> mood_rating: 2-3; "really stressed" -> stress_level: 7-8).
5.  **Engage with Empathy:** If a tool is not required, respond with empathy and engage in a supportive conversation. If you need more information to use a tool effectively, gently ask the user clarifying questions.

Your goal is to be a helpful and supportive mental coach, using your tools to provide the best possible guidance while maintaining a conversational and empathetic tone."""

        # Tools will be loaded asynchronously in get_domain_tools()
        super().__init__(
            name="mental_agent",
            domain="mental_training",
            system_prompt=fallback_prompt,  # Will be updated lazily
            tools=[],  # Will be populated by get_domain_tools()
            permissions=["mental_assessment", "goal_setting", "motivation_coaching"],
            max_iterations=10,
            temperature=0.2,
        )

        self._prompt_loaded = False
        self.fallback_prompt = fallback_prompt
        self._tools_loaded = False

    async def _load_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if not self._prompt_loaded:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt("mental_coach")
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info("Successfully loaded mental coach prompt from file")
            except Exception as e:
                logger.error(f"Failed to load mental coach prompt: {e}")
                self.system_prompt = self.fallback_prompt
            self._prompt_loaded = True

        return self.system_prompt

    async def get_domain_tools(self) -> List[BaseTool]:
        """
        Instantiates and returns the specific tools for the mental agent.
        This makes the agent self-contained and independent of a central tool manager.
        """
        if not self._tools_loaded:
            # Domain-specific mental health tools - use LangChain-compatible tools
            mental_tools = [
                mental_state_assessment,
                stress_level_tracker,
                mood_pattern_analyzer,
                goal_tracker,
            ]

            # Add shared tools that all agents should have

            # 1. Web Search tool for current information (prioritize for recent/current info)
            try:
                from langchain_community.tools import DuckDuckGoSearchRun

                web_search_tool = DuckDuckGoSearchRun(
                    name="web_search",
                    description="Search the web for current mental health research, app recommendations, tech reviews, and latest psychology developments. Use for recent studies, new mental training methods, app comparisons, or current wellness trends from 2024.",
                )
                mental_tools.append(web_search_tool)
            except Exception as e:
                logger.warning(f"Failed to load web search tool: {e}")

            # 2. GraphRAG tool for research (for general/older research)
            try:
                from ..tools.graphrag_tool import create_graphrag_tool

                graphrag_tool = create_graphrag_tool()
                # Update description to be more specific
                graphrag_tool.description = "Search the internal knowledge base for established mental training research, sports psychology principles, and evidence-based strategies. Use for fundamental research, not current app reviews or tech site recommendations."
                mental_tools.append(graphrag_tool)
            except Exception as e:
                logger.warning(f"Failed to load GraphRAG tool: {e}")

            # 3. Session Generation tool
            try:
                from ..tools.external.session_generation import SessionGenerationTool
                from langchain_core.tools import BaseTool

                class SessionGenerationLangChainTool(BaseTool):
                    name: str = "session_generation"
                    description: str = """Generate structured mental training sessions and mindfulness protocols.
                    Use this tool to create detailed mental training programs, meditation sessions, and goal-setting protocols.
                    Input should be a JSON string with session parameters including goals, duration, and focus areas."""

                    def _run(self, session_params: str) -> str:
                        try:
                            import json

                            session_tool = SessionGenerationTool()
                            params = json.loads(session_params)
                            result = session_tool.generate_session(params)
                            return json.dumps(result, indent=2)
                        except Exception as e:
                            return f"Session generation failed: {str(e)}"

                    async def _arun(self, session_params: str) -> str:
                        return self._run(session_params)

                mental_tools.append(SessionGenerationLangChainTool())
            except Exception as e:
                logger.warning(f"Failed to load session generation tool: {e}")

            # 4. Azure Search tool for research and knowledge retrieval
            try:
                from ..tools.external.azure_search_retriever import (
                    AzureSearchRetrieverTool,
                )

                azure_search_tool = AzureSearchRetrieverTool()
                azure_search_langchain_tool = azure_search_tool.to_langchain_tool()
                mental_tools.append(azure_search_langchain_tool)
            except Exception as e:
                logger.warning(f"Failed to load Azure Search tool: {e}")

            # Update the agent's tools
            self.tools = mental_tools

            # Tools loaded successfully for direct tool calling
            logger.info(
                f"✅ Loaded {len(mental_tools)} tools for direct calling: {[t.name for t in mental_tools]}"
            )

            logger.info(
                f"Loaded {len(mental_tools)} tools for mental agent: {[t.name for t in mental_tools]}"
            )
            self._tools_loaded = True

        return self.tools

    def get_domain_prompt(self) -> str:
        """Get the domain-specific prompt for mental coaching."""
        return self.system_prompt

    async def process(
        self, state: AgentState, config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process mental training requests with tool loading."""
        # Ensure tools are loaded before processing
        if not self.tools:
            await self.get_domain_tools()

        # Ensure prompt is loaded before processing
        await self._load_system_prompt()

        return await super().process(state, config)


# Create the mental agent instance
mental_agent = MentalAgent()


async def mental_agent_node(
    state: AgentState, config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    LangGraph node function for mental training coaching.

    Args:
        state: Current agent state
        config: Optional configuration

    Returns:
        Updated state with mental agent response
    """
    logger.info("--- Running Mental Agent Node ---")

    try:
        # Ensure agent has tools loaded
        if not mental_agent.tools:
            await mental_agent.get_domain_tools()

        # Process the request
        result = await mental_agent.process(state, config)

        # Update state with results
        updated_state = {
            "messages": state.get("messages", [])
            + [{"role": "assistant", "content": result.get("response", "")}],
            "specialist_completed": result.get("specialist_completed", True),
            "current_agent": "mental_agent",
            "agent_metadata": result.get("metadata", {}),
        }

        if result.get("error"):
            updated_state["error"] = result["error"]

        logger.info(
            f"Mental agent completed with {len(mental_agent.tools)} tools available"
        )
        return updated_state

    except Exception as e:
        logger.error(f"Error in mental agent node: {e}")
        return {
            "messages": state.get("messages", [])
            + [
                {
                    "role": "assistant",
                    "content": f"I encountered an error while processing your mental training request: {str(e)}",
                }
            ],
            "specialist_completed": False,
            "current_agent": "mental_agent",
            "error": str(e),
        }
