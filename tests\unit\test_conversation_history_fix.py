"""
Simple test for conversation history conversion function.

This test verifies that the convert_to_base_messages function works correctly
without importing the full module hierarchy.
"""

import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", ".."))

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage


def convert_to_base_messages(conversation_history):
    """
    Convert conversation history from mixed format to proper BaseMessage objects.

    Args:
        conversation_history: List of messages in dict or BaseMessage format

    Returns:
        List of BaseMessage objects
    """
    messages = []
    for msg in conversation_history:
        if isinstance(msg, BaseMessage):
            # Already a BaseMessage, keep as is
            messages.append(msg)
        elif isinstance(msg, dict):
            # Convert dict to BaseMessage
            content = msg.get("content", "")
            role = msg.get("role", "user")

            if role == "user" or role == "human":
                messages.append(HumanMessage(content=content))
            elif role == "assistant" or role == "ai":
                messages.append(AIMessage(content=content))
            elif role == "system":
                messages.append(SystemMessage(content=content))
            else:
                # Default to HumanMessage for unknown roles
                print(f"Unknown role '{role}', treating as user message")
                messages.append(HumanMessage(content=content))
        else:
            print(f"Unknown message format: {type(msg)}, skipping")

    return messages


def test_original_error_case():
    """Test that reproduces the original error scenario from the issue."""
    # This simulates the error case from the issue
    conversation_history = [
        {
            "content": "I am an experienced athlete, what training should I focus on?",
            "role": "assistant",
        }
    ]

    # This should work fine after the fix
    result = convert_to_base_messages(conversation_history)

    assert len(result) == 1
    assert isinstance(result[0], AIMessage)
    assert (
        result[0].content
        == "I am an experienced athlete, what training should I focus on?"
    )
    print("✅ Original error case test passed")


def test_mixed_format():
    """Test converting mixed dictionary and BaseMessage objects."""
    conversation_history = [
        {"role": "user", "content": "Hello"},
        AIMessage(content="Hi there!"),
        {"role": "assistant", "content": "How can I help?"},
    ]

    result = convert_to_base_messages(conversation_history)

    assert len(result) == 3
    assert isinstance(result[0], HumanMessage)
    assert isinstance(result[1], AIMessage)
    assert isinstance(result[2], AIMessage)
    print("✅ Mixed format test passed")


def test_role_variations():
    """Test different role variations."""
    conversation_history = [
        {"role": "user", "content": "User message"},
        {"role": "human", "content": "Human message"},
        {"role": "assistant", "content": "Assistant message"},
        {"role": "ai", "content": "AI message"},
        {"role": "system", "content": "System message"},
    ]

    result = convert_to_base_messages(conversation_history)

    assert len(result) == 5
    assert isinstance(result[0], HumanMessage)
    assert isinstance(result[1], HumanMessage)
    assert isinstance(result[2], AIMessage)
    assert isinstance(result[3], AIMessage)
    assert isinstance(result[4], SystemMessage)
    print("✅ Role variations test passed")


if __name__ == "__main__":
    print("Running conversation history conversion tests...")
    test_original_error_case()
    test_mixed_format()
    test_role_variations()
    print("🎉 All tests passed!")
