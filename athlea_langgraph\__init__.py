"""
Athlea LangGraph Package

This package provides the core components for the Athlea LangGraph coaching system.
"""

from .agents.head_coach import clarification_node, head_coach_node
from .agents.planning_node import planning_node

# Import agent functions for direct use
from .agents.reasoning_node import reasoning_node
from .agents.specialized_coaches import (
    cardio_coach_node,
    cycling_coach_node,
    mental_coach_node,
    nutrition_coach_node,
    recovery_coach_node,
    running_coach_node,
    strength_coach_node,
)

# Import GraphRAG components
from .agents.graphrag_node import (
    graphrag_agent_node,
    query_injury_prevention,
    query_training_protocol,
)

# Import graph factory
from .graph_factory import get_graph_factory

# Import graph components
from .graphs.archived.comprehensive_coaching_graph import create_comprehensive_coaching_graph
from .graphs.onboarding_graph import (
    create_initial_state,
    create_onboarding_graph,
    get_compiled_onboarding_graph,
)
from .services.azure_openai_service import (
    create_azure_chat_openai,
    validate_azure_openai_config,
)

# Import GraphRAG services
from .services.graphrag_service import get_graphrag_service
from .services.gremlin_service import get_gremlin_service
from .config.graphrag_config import get_graphrag_config, validate_graphrag_config

# Import GraphRAG tools
from .tools.external.graphrag_tool import (
    create_graphrag_tool,
    create_graphrag_athlea_tool,
)

# Import from states module
from .states import (
    AgentState,
    MemoryEnhancedAgentState,
    OnboardingState,
    ReWOOState,
    create_initial_onboarding_state,
    create_initial_rewoo_state,
    messages_reducer,
)

__version__ = "0.1.0"
__all__ = [
    # Core State and Reducer
    "AgentState",
    "messages_reducer",
    "MemoryEnhancedAgentState",
    "OnboardingState",
    "ReWOOState",
    # Services
    "create_azure_chat_openai",
    "validate_azure_openai_config",
    # GraphRAG Services
    "get_graphrag_service",
    "get_gremlin_service",
    "get_graphrag_config",
    "validate_graphrag_config",
    # GraphRAG Tools
    "create_graphrag_tool",
    "create_graphrag_athlea_tool",
    # Coaching Graph
    "create_comprehensive_coaching_graph",
    # Individual agent nodes
    "reasoning_node",
    "planning_node",
    "head_coach_node",
    "clarification_node",
    "strength_coach_node",
    "cardio_coach_node",
    "running_coach_node",
    "cycling_coach_node",
    "nutrition_coach_node",
    "recovery_coach_node",
    "mental_coach_node",
    # GraphRAG agent nodes
    "graphrag_agent_node",
    "query_injury_prevention",
    "query_training_protocol",
    # Onboarding components
    "create_initial_onboarding_state",
    "create_onboarding_graph",
    "get_compiled_onboarding_graph",
    "create_initial_state",
    "create_initial_rewoo_state",
    # Graph Factory
    "get_graph_factory",
]
