"""
Strength Training Domain Schemas

Pydantic schemas for strict input/output validation of strength training tools.
These schemas enforce tool-agent contracts and automatic validation.
"""

from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, field_validator, ConfigDict


class DifficultyLevel(str, Enum):
    """Exercise difficulty levels."""

    beginner = "beginner"
    intermediate = "intermediate"
    advanced = "advanced"


class MovementPattern(str, Enum):
    """Primary movement patterns in strength training."""

    squat = "squat"
    deadlift = "deadlift"
    press = "press"
    pull = "pull"
    lunge = "lunge"
    carry = "carry"
    rotation = "rotation"
    combination = "combination"


class MuscleGroup(str, Enum):
    """Primary muscle groups."""

    quadriceps = "quadriceps"
    hamstrings = "hamstrings"
    glutes = "glutes"
    calves = "calves"
    chest = "chest"
    shoulders = "shoulders"
    triceps = "triceps"
    biceps = "biceps"
    lats = "lats"
    rhomboids = "rhomboids"
    traps = "traps"
    erector_spinae = "erector_spinae"
    core = "core"
    forearms = "forearms"


class Equipment(str, Enum):
    """Available training equipment."""

    bodyweight = "bodyweight"
    barbell = "barbell"
    dumbbells = "dumbbells"
    kettlebells = "kettlebells"
    resistance_bands = "resistance_bands"
    pull_up_bar = "pull_up_bar"
    squat_rack = "squat_rack"
    bench = "bench"
    cables = "cables"
    machines = "machines"
    suspension_trainer = "suspension_trainer"
    medicine_ball = "medicine_ball"


# Input Schemas
class ExerciseSearchInput(BaseModel):
    """Input schema for exercise database search."""

    muscle_groups: Optional[List[MuscleGroup]] = Field(
        None,
        description="Target muscle groups to search for",
        example=["chest", "shoulders", "triceps"],
    )

    equipment: Optional[List[Equipment]] = Field(
        None,
        description="Available equipment for exercises",
        example=["dumbbells", "bench"],
    )

    movement_pattern: Optional[MovementPattern] = Field(
        None, description="Primary movement pattern", example="press"
    )

    difficulty_level: Optional[DifficultyLevel] = Field(
        "intermediate", description="Maximum difficulty level to include"
    )

    exercise_type: Optional[str] = Field(
        None, description="Specific exercise type or category", example="compound"
    )

    max_results: Optional[int] = Field(
        10, ge=1, le=50, description="Maximum number of exercises to return"
    )

    @field_validator("muscle_groups")
    @classmethod
    def validate_muscle_groups(cls, v):
        if v and len(v) > 5:
            raise ValueError("Cannot search for more than 5 muscle groups at once")
        return v

    @field_validator("equipment")
    @classmethod
    def validate_equipment(cls, v):
        if v and len(v) > 8:
            raise ValueError("Cannot filter by more than 8 equipment types at once")
        return v


class ExerciseProgressionInput(BaseModel):
    """Input schema for exercise progression planning."""

    exercise_name: str = Field(
        ...,
        description="Name of the exercise to get progression for",
        example="push-ups",
    )

    current_level: DifficultyLevel = Field(
        ..., description="User's current skill level with this exercise"
    )

    user_goals: Optional[List[str]] = Field(
        None,
        description="Specific goals for progression",
        example=["increase reps", "build strength", "add difficulty"],
    )

    current_performance: Optional[Dict[str, Any]] = Field(
        None,
        description="Current performance metrics",
        example={"max_reps": 10, "sets": 3, "frequency_per_week": 3},
    )

    time_availability: Optional[str] = Field(
        None,
        description="Available time commitment",
        example="3 sessions per week, 30 minutes each",
    )

    @field_validator("exercise_name")
    @classmethod
    def validate_exercise_name(cls, v):
        if not v.strip():
            raise ValueError("Exercise name cannot be empty")
        return v.strip().lower()

    @field_validator("user_goals")
    @classmethod
    def validate_user_goals(cls, v):
        if v and len(v) > 5:
            raise ValueError("Cannot specify more than 5 goals")
        return v


class StrengthAssessmentInput(BaseModel):
    """Input schema for strength level assessment."""

    age: int = Field(..., ge=13, le=100, description="User's age")
    gender: Literal["male", "female", "other"] = Field(..., description="User's gender")
    weight_kg: float = Field(..., ge=30, le=300, description="User's weight in kg")
    experience_years: Optional[float] = Field(
        None, ge=0, le=50, description="Years of training experience"
    )
    current_lifts: Optional[Dict[str, float]] = Field(
        None,
        description="Current 1RM lifts",
        example={
            "squat_1rm": 100,
            "bench_1rm": 80,
            "deadlift_1rm": 120,
            "overhead_press_1rm": 50,
        },
    )
    goals: List[str] = Field(..., description="Primary training goals")
    training_frequency: Optional[int] = Field(
        None, ge=1, le=7, description="Training days per week"
    )


class ProgramGenerationInput(BaseModel):
    """Input schema for strength program generation."""

    user_profile: Dict[str, Any] = Field(
        ..., description="User's fitness profile and preferences"
    )

    program_duration: int = Field(
        ..., ge=1, le=52, description="Program duration in weeks"
    )

    sessions_per_week: int = Field(
        ..., ge=1, le=7, description="Training sessions per week"
    )

    available_equipment: List[Equipment] = Field(
        ..., description="Equipment available to user"
    )

    primary_goals: List[str] = Field(
        ...,
        description="Primary training goals",
        example=["strength", "muscle building", "power"],
    )

    program_style: Optional[str] = Field(
        "balanced", description="Program style or methodology", example="powerlifting"
    )

    @field_validator("primary_goals")
    @classmethod
    def validate_primary_goals(cls, v):
        if len(v) < 1:
            raise ValueError("Must specify at least one primary goal")
        if len(v) > 3:
            raise ValueError("Cannot specify more than 3 primary goals")
        return v

    @field_validator("available_equipment")
    @classmethod
    def validate_available_equipment(cls, v):
        if len(v) < 1:
            raise ValueError("Must specify at least one available equipment type")
        return v


# Output Schemas
class ExerciseDetail(BaseModel):
    """Detailed exercise information."""

    name: str = Field(..., description="Exercise name")
    muscle_groups: List[str] = Field(..., description="Targeted muscle groups")
    equipment: List[str] = Field(..., description="Required equipment")
    movement_pattern: str = Field(..., description="Primary movement pattern")
    difficulty: str = Field(..., description="Difficulty level")
    benefits: List[str] = Field(..., description="Primary benefits")
    variations: List[str] = Field(..., description="Exercise variations")
    technique_cues: Optional[List[str]] = Field(
        None, description="Key technique points"
    )
    safety_notes: Optional[List[str]] = Field(None, description="Safety considerations")


class ExerciseSearchOutput(BaseModel):
    """Output schema for exercise search results."""

    exercises: List[ExerciseDetail] = Field(
        ..., description="List of matching exercises"
    )

    total_count: int = Field(..., description="Total number of exercises found")

    search_criteria: Dict[str, Any] = Field(..., description="Applied search criteria")

    recommendations: Optional[List[str]] = Field(
        None, description="Additional recommendations"
    )

    error: Optional[str] = Field(None, description="Error message if search failed")


class ProgressionStep(BaseModel):
    """Individual progression step."""

    step_number: int = Field(..., description="Step order in progression")
    description: str = Field(..., description="Step description")
    target_reps: Optional[str] = Field(None, description="Target repetitions")
    target_sets: Optional[str] = Field(None, description="Target sets")
    duration: Optional[str] = Field(None, description="Time to master this step")
    key_focus: Optional[str] = Field(None, description="Primary focus for this step")


class ExerciseProgressionOutput(BaseModel):
    """Output schema for exercise progression planning."""

    exercise_name: str = Field(..., description="Exercise being progressed")
    current_level: str = Field(..., description="Starting skill level")

    progression_steps: List[str] = Field(
        ..., description="Ordered list of progression steps"
    )

    estimated_timeline: str = Field(
        ..., description="Estimated time to complete progression"
    )

    key_milestones: List[str] = Field(..., description="Important milestones to track")

    prerequisite_skills: Optional[List[str]] = Field(
        None, description="Skills needed before starting"
    )

    next_progression: Optional[str] = Field(
        None, description="Next exercise in progression chain"
    )

    error: Optional[str] = Field(
        None, description="Error message if progression failed"
    )


class WorkoutSession(BaseModel):
    """Individual workout session."""

    session_name: str = Field(..., description="Name of the workout session")
    exercises: List[Dict[str, Any]] = Field(..., description="Exercises in session")
    estimated_duration: str = Field(..., description="Estimated session duration")
    focus_areas: List[str] = Field(..., description="Primary focus of session")
    warm_up: Optional[List[str]] = Field(None, description="Warm-up routine")
    cool_down: Optional[List[str]] = Field(None, description="Cool-down routine")


class ProgramGenerationOutput(BaseModel):
    """Output schema for strength program generation."""

    program_name: str = Field(..., description="Generated program name")
    duration_weeks: int = Field(..., description="Program duration in weeks")
    sessions_per_week: int = Field(..., description="Training frequency")

    weekly_schedule: List[WorkoutSession] = Field(
        ..., description="Weekly workout schedule"
    )

    progression_plan: Dict[str, Any] = Field(
        ..., description="How the program progresses over time"
    )

    equipment_required: List[str] = Field(
        ..., description="Equipment needed for program"
    )

    key_principles: List[str] = Field(..., description="Core training principles used")

    success_metrics: List[str] = Field(
        ..., description="How to measure program success"
    )

    error: Optional[str] = Field(None, description="Error message if generation failed")


# Tool Contract Enforcement
class StrengthToolContract(BaseModel):
    """Enforces tool contracts for strength domain."""

    tool_name: str = Field(..., description="Name of the tool")
    input_schema: type = Field(..., description="Required input schema")
    output_schema: type = Field(..., description="Expected output schema")
    required_permissions: List[str] = Field(
        ..., description="Required agent permissions"
    )
    domain: str = Field("strength_training", description="Tool domain")

    model_config = ConfigDict(arbitrary_types_allowed=True)


# Available tool contracts for strength domain
STRENGTH_TOOL_CONTRACTS = {
    "exercise_database": StrengthToolContract(
        tool_name="exercise_database",
        input_schema=ExerciseSearchInput,
        output_schema=ExerciseSearchOutput,
        required_permissions=["read_exercise_database", "search_exercises"],
    ),
    "exercise_progression": StrengthToolContract(
        tool_name="exercise_progression",
        input_schema=ExerciseProgressionInput,
        output_schema=ExerciseProgressionOutput,
        required_permissions=["read_exercise_database", "generate_progressions"],
    ),
    "program_generator": StrengthToolContract(
        tool_name="program_generator",
        input_schema=ProgramGenerationInput,
        output_schema=ProgramGenerationOutput,
        required_permissions=[
            "read_exercise_database",
            "generate_programs",
            "access_user_profile",
        ],
    ),
}


class GenerateStrengthProgramSchema(BaseModel):
    """Schema for strength program generation tool."""

    athlete_type: str = Field(
        ...,
        description="Type of athlete (beginner, intermediate, advanced, powerlifter, bodybuilder)",
    )
    program_type: str = Field(
        ...,
        description="Type of program (hypertrophy, strength, power, endurance, general_fitness)",
    )
    program_duration_weeks: int = Field(
        ..., ge=1, le=52, description="Program duration in weeks"
    )
    sessions_per_week: int = Field(
        ..., ge=1, le=7, description="Number of training sessions per week"
    )
    available_equipment: List[str] = Field(
        ...,
        description="Available equipment (barbell, dumbbells, machines, bodyweight, etc.)",
    )
    focus_areas: List[str] = Field(
        ...,
        description="Body areas to focus on (upper_body, lower_body, core, full_body)",
    )
    training_experience_years: Optional[int] = Field(
        None, ge=0, le=50, description="Years of training experience"
    )
    limitations: Optional[List[str]] = Field(
        None, description="Physical limitations or injuries to consider"
    )


class SearchStrengthExercisesSchema(BaseModel):
    """Schema for searching strength exercises."""

    query: str = Field(..., description="Search query for exercises")
    muscle_group: Optional[str] = Field(
        None, description="Target muscle group to filter by"
    )
    equipment: Optional[str] = Field(None, description="Equipment type to filter by")
    difficulty: Optional[str] = Field(None, description="Difficulty level to filter by")
    max_results: Optional[int] = Field(
        10, ge=1, le=50, description="Maximum number of results to return"
    )
