"""
Integration tests for all MCP servers.
Tests the complete MCP workflow including initialization, tool listing, and tool calling.
"""

import asyncio
from typing import Any, Dict
from unittest.mock import AsyncMock, patch

import pytest

# Import MCP types
from mcp.types import (
    CallToolRequest,
    CallToolRequestParams,
    ListPromptsRequest,
    ListToolsRequest,
    TextContent,
    Tool,
)

from mcp_servers.cardio_mcp.server import app as cardio_app
from mcp_servers.external_mcp.server import app as external_app
from mcp_servers.nutrition_mcp.server import app as nutrition_app

# Import all MCP servers
from mcp_servers.strength_mcp.server import app as strength_app


@pytest.mark.integration
class TestMCPServersIntegration:
    """Integration tests for all MCP servers."""

    @pytest.fixture(autouse=True)
    def setup_servers(self):
        """Set up all MCP servers for testing."""
        self.servers = {
            "strength": strength_app,
            "nutrition": nutrition_app,
            "cardio": cardio_app,
            "external": external_app,
        }

    @pytest.mark.asyncio
    async def test_all_servers_initialization(self):
        """Test that all MCP servers can be initialized properly."""
        for server_name, server in self.servers.items():
            assert server is not None, f"{server_name} server failed to initialize"
            assert hasattr(
                server, "list_tools"
            ), f"{server_name} missing list_tools handler"
            assert hasattr(
                server, "call_tool"
            ), f"{server_name} missing call_tool handler"

    @pytest.mark.asyncio
    async def test_all_servers_list_tools(self, sample_tool_arguments, mcp_validators):
        """Test that all servers can list their tools correctly."""
        tools_by_server = {}

        for server_name, server in self.servers.items():
            # Use the proper MCP request handler
            request = ListToolsRequest(method="tools/list")
            result = await server.request_handlers[ListToolsRequest](request)
            tools = result.root.tools

            assert len(tools) > 0, f"{server_name} server has no tools"
            tools_by_server[server_name] = tools

            # Validate each tool
            for tool in tools:
                mcp_validators["tool"](tool)

        # Verify expected tools are present
        expected_tools = {
            "strength": ["search_strength_exercises", "generate_strength_program"],
            "nutrition": [
                "calculate_daily_calories",
                "generate_comprehensive_meal_plan",
                "track_hydration",
            ],
            "cardio": [
                "generate_running_route",
                "calculate_pace_zones",
                "calculate_vo2max_estimate",
                "plan_interval_workout",
            ],
            "external": [
                "search_locations",
                "search_web",
                "query_airtable",
                "get_weather_data",
                "search_wikipedia",
                "find_nearby_facilities",
                "calculate_travel_carbon",
            ],
        }

        for server_name, expected in expected_tools.items():
            actual_tool_names = [tool.name for tool in tools_by_server[server_name]]
            for expected_tool in expected:
                assert (
                    expected_tool in actual_tool_names
                ), f"{expected_tool} not found in {server_name} server"

    @pytest.mark.asyncio
    async def test_cross_server_tool_calling(
        self, sample_tool_arguments, sample_tool_responses, mcp_validators
    ):
        """Test calling tools across different servers."""

        # Test tools from each server without complex mocking
        test_cases = [
            (
                "strength",
                "search_strength_exercises",
                sample_tool_arguments["strength"]["search_strength_exercises"],
            ),
            (
                "nutrition",
                "calculate_daily_calories",
                sample_tool_arguments["nutrition"]["calculate_daily_calories"],
            ),
            (
                "cardio",
                "generate_running_route",
                sample_tool_arguments["cardio"]["generate_running_route"],
            ),
            (
                "external",
                "search_locations",
                sample_tool_arguments["external"]["search_locations"],
            ),
        ]

        for server_name, tool_name, arguments in test_cases:
            server = self.servers[server_name]

            # Use the proper MCP request handler
            params = CallToolRequestParams(name=tool_name, arguments=arguments)
            request = CallToolRequest(method="tools/call", params=params)

            try:
                result = await server.request_handlers[CallToolRequest](request)

                # Validate MCP response format
                mcp_validators["response"](result)

                # Verify content
                assert len(result.root.content) == 1
                assert isinstance(result.root.content[0], TextContent)
                response_text = result.root.content[0].text
                assert len(response_text) > 0
            except Exception as e:
                # Some tools may fail due to missing dependencies or API keys
                # That's expected in test environment, just check it fails gracefully
                assert "Error:" in str(e) or isinstance(e, (ValueError, AttributeError))

    @pytest.mark.asyncio
    async def test_server_performance_under_load(
        self, sample_tool_arguments, performance_timer
    ):
        """Test server performance under concurrent load."""

        async def call_tool_concurrent(server, tool_name, arguments):
            """Helper to call a tool concurrently."""
            from mcp.types import CallToolRequest, CallToolRequestParams

            params = CallToolRequestParams(name=tool_name, arguments=arguments)
            request = CallToolRequest(method="tools/call", params=params)
            return await server.request_handlers[CallToolRequest](request)

        # Create multiple concurrent requests
        tasks = []
        for i in range(5):  # Reduced to 5 concurrent requests for stability
            task = call_tool_concurrent(
                self.servers["strength"],
                "search_strength_exercises",
                sample_tool_arguments["strength"]["search_strength_exercises"],
            )
            tasks.append(task)

        # Execute all requests concurrently and measure time
        with performance_timer() as get_time:
            results = await asyncio.gather(*tasks, return_exceptions=True)

        execution_time = get_time()

        # Verify all requests completed (successfully or with expected errors)
        for result in results:
            if isinstance(result, Exception):
                # Expected for some tools that need external dependencies
                assert "Error:" in str(result) or isinstance(
                    result, (ValueError, AttributeError)
                )
            else:
                assert hasattr(result, "root")
                assert hasattr(result.root, "content")

        # Performance assertion (should complete within reasonable time)
        assert execution_time < 30.0  # 30 seconds max for 5 concurrent requests

    @pytest.mark.asyncio
    async def test_server_error_handling(self):
        """Test error handling across all servers."""

        # Test invalid tool names
        for server_name, server in self.servers.items():
            from mcp.types import CallToolRequest, CallToolRequestParams

            params = CallToolRequestParams(name="non_existent_tool", arguments={})
            request = CallToolRequest(method="tools/call", params=params)

            # MCP servers should return error responses instead of raising exceptions
            result = await server.request_handlers[CallToolRequest](request)
            assert hasattr(result, "root")
            assert hasattr(result.root, "isError")
            assert result.root.isError == True

    @pytest.mark.asyncio
    async def test_server_input_validation(self, sample_tool_arguments):
        """Test input validation across all servers."""

        # Test with invalid inputs (missing required fields)
        invalid_test_cases = [
            ("strength", "search_strength_exercises", {}),  # Missing required fields
            (
                "nutrition",
                "calculate_daily_calories",
                {"age": 30},
            ),  # Missing other required fields
            ("cardio", "generate_running_route", {"distance_km": -5}),  # Invalid value
            ("external", "search_locations", {"query": ""}),  # Invalid empty query
        ]

        for server_name, tool_name, invalid_arguments in invalid_test_cases:
            server = self.servers[server_name]
            from mcp.types import CallToolRequest, CallToolRequestParams

            params = CallToolRequestParams(name=tool_name, arguments=invalid_arguments)
            request = CallToolRequest(method="tools/call", params=params)

            # MCP servers should return error responses for invalid input
            result = await server.request_handlers[CallToolRequest](request)
            assert hasattr(result, "root")
            # Should either be an error or handle gracefully
            if hasattr(result.root, "isError"):
                # If error flag exists, it should be True
                assert result.root.isError == True
            else:
                # Otherwise should have content (graceful handling)
                assert hasattr(result.root, "content")

    @pytest.mark.asyncio
    async def test_all_servers_prompt_handling(self):
        """Test that all servers can handle prompt requests if they have prompts."""

        for server_name, server in self.servers.items():
            from mcp.types import ListPromptsRequest

            if ListPromptsRequest in server.request_handlers:
                request = ListPromptsRequest(method="prompts/list")
                result = await server.request_handlers[ListPromptsRequest](request)

                # Prompts should be in the result
                prompts = result.root if hasattr(result, "root") else result
                assert isinstance(prompts, list) or hasattr(prompts, "prompts")

                # If prompts exist, they should have the right structure
                if hasattr(prompts, "prompts"):
                    for prompt in prompts.prompts:
                        assert hasattr(prompt, "name")
                        assert hasattr(prompt, "description")


@pytest.mark.integration
@pytest.mark.asyncio
async def test_mcp_server_ecosystem_health():
    """Test the overall health of the MCP server ecosystem."""

    servers = {
        "strength": strength_app,
        "nutrition": nutrition_app,
        "cardio": cardio_app,
        "external": external_app,
    }

    # Verify all servers are different instances
    server_ids = set(id(server) for server in servers.values())
    assert len(server_ids) == len(servers), "Servers should be separate instances"

    # Verify each server has unique tools
    all_tool_names = set()
    for server_name, server in servers.items():
        from mcp.types import ListToolsRequest

        request = ListToolsRequest(method="tools/list")
        result = await server.request_handlers[ListToolsRequest](request)
        tools = result.root.tools
        tool_names = {tool.name for tool in tools}

        # Check for tool name conflicts across servers
        overlap = all_tool_names.intersection(tool_names)
        assert len(overlap) == 0, f"Tool name conflicts found: {overlap}"

        all_tool_names.update(tool_names)

    # Verify we have a good coverage of tools
    total_tools = len(all_tool_names)
    assert (
        total_tools >= 15
    ), f"Expected at least 15 tools across all servers, got {total_tools}"


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--tb=short"])
