"""
Reasoning Node

Python translation of app/api/coaching/lib/agents/reasoning-node.ts
Analyzes the user request and provides structured thought process.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>rom<PERSON><PERSON>emplate, MessagesPlaceholder

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states import AgentState

logger = logging.getLogger(__name__)


async def reasoning_node(
    state: AgentState, config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Reasoning node that analyzes the user request and provides structured thought process.

    Args:
        state: The current state of the graph
        config: Optional configuration including user profile and current plan

    Returns:
        Partial graph state with the reasoning message
    """

    node_name = "ReasoningAgent"
    print(f"--- Running {node_name} Node ---")

    # Extract configuration
    config = config or {}
    user_profile = config.get("userProfile") or state.get("user_profile")
    current_plan_info = config.get("currentPlan")
    messages = state.get("messages", [])

    # Handle onboarding state that might not have user_profile in expected format
    if not user_profile and hasattr(state, "user_profile"):
        user_profile = getattr(state, "user_profile", None)

    # For onboarding, user_profile might be None - provide safe fallback
    if user_profile is None:
        user_profile = {
            "note": "User profile not yet available (onboarding in progress)"
        }

    # Better user input extraction - handle both dict and Pydantic model state
    user_query = ""
    if isinstance(state, dict):
        # State is a dictionary - use dict access
        user_query = state.get("user_query", "")
        if not user_query and state.get("messages"):
            # Extract from last human message
            for msg in reversed(state["messages"]):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break
    else:
        # State is a Pydantic model - use attribute access
        user_query = (
            getattr(state, "user_query", "") if hasattr(state, "user_query") else ""
        )
        if not user_query and hasattr(state, "messages") and state.messages:
            # Extract from last human message
            for msg in reversed(state.messages):
                if isinstance(msg, HumanMessage):
                    user_query = msg.content
                    break

    # Fallback to "Hi" if still empty (common Studio default)
    if not user_query:
        user_query = "Hi"

    # Create the reasoning LLM
    reasoning_llm = create_azure_chat_openai(
        temperature=0.7, max_tokens=4000, streaming=True
    )

    # Improved system prompt with better structure
    reasoning_prompt_template = """You are an analytical assistant for a fitness coaching application. Your task is to analyze the user's current request, provide structured reasoning about the situation, and identify any physiological or mental aspects mentioned.

User Profile:
{user_profile_info}

Current Plan Context:
{current_plan_info}

Conversation History:
{message_history}

CURRENT USER INPUT TO ANALYZE:
"{latest_user_message_content}"

Analyze the current user input above within the context of the history and profile/plan. Generate a well-structured analysis using proper markdown formatting.

Include the following sections:

## Request Summary
- Summarize what the user is asking for in their current message
- If the input is unclear or minimal (like "Hi" or "N/A"), note this explicitly

## Clarity Assessment
- Is the request clear or ambiguous?
- What specific information is provided vs. what might be missing?

## Domain Analysis
- What fitness or wellness domains does this request relate to? (strength, cardio, nutrition, recovery, mental, etc.)
- *If applicable*: What specific physiological body parts or mental aspects are referenced? (e.g., knees, back, motivation, stress)

## Context Relevance
- How does this request relate to the user's profile or current plan?
- Any relevant historical context from previous messages?

## Next Steps Analysis
- What would be the most helpful response approach?
- Is clarification needed before proceeding?

Structure your response using proper markdown formatting with clear sections and bullet points. Be concise but thorough."""

    # Prepare template variables
    user_profile_info = (
        json.dumps(user_profile, indent=2) if user_profile else "Not Available"
    )
    current_plan_info_str = (
        json.dumps(current_plan_info, indent=2)
        if current_plan_info
        else "Not Available"
    )

    # Prepare history string with proper formatting
    history_string = ""
    if messages:
        history_parts = []
        for msg in messages:
            msg_type = (
                "User"
                if msg.__class__.__name__ == "HumanMessage"
                else (getattr(msg, "name", None) or "Assistant")
            )
            content = (
                str(msg.content) if hasattr(msg, "content") else "[non-string content]"
            )
            history_parts.append(f"{msg_type}: {content}")
        history_string = "\n".join(history_parts)
    else:
        history_string = "No history yet."

    # Use the properly extracted user query instead of latest message
    latest_user_content = user_query if user_query else "N/A"

    # Fill in the template
    prompt_input = reasoning_prompt_template.format(
        user_profile_info=user_profile_info,
        current_plan_info=current_plan_info_str,
        message_history=history_string,
        latest_user_message_content=latest_user_content,
    )

    llm_messages = [SystemMessage(content=prompt_input)]
    reasoning_text = "Analyzing request..."  # Default fallback

    try:
        print(f"  > Calling {node_name} LLM with streaming...")
        reasoning_text = ""
        async for chunk in reasoning_llm.astream(llm_messages):
            if hasattr(chunk, "content") and chunk.content:
                reasoning_text += chunk.content
                # Individual chunks will be automatically handled by the graph streaming

        # Safely handle the response content
        if reasoning_text.strip():
            print(f"  > {node_name} LLM Response Summary: {reasoning_text[:150]}...")
        else:
            print(f"  > {node_name} LLM returned empty content.")
            reasoning_text = "Analysis completed."

    except Exception as error:
        print(f"Error during {node_name} LLM call: {error}")
        reasoning_text = "Error during analysis."

    # Create the reasoning message with domain metadata
    reasoning_message = AIMessage(content=reasoning_text, name=node_name)

    print(f"  > {node_name} completed analysis")

    # Return updated state with the reasoning message
    return {"messages": [reasoning_message]}


def extract_markdown_sections(markdown_text: str) -> Dict[str, str]:
    """
    Extract markdown sections from the reasoning text for telemetry.

    Args:
        markdown_text: The markdown formatted text

    Returns:
        Dictionary mapping section names to their content
    """
    sections = {}
    lines = markdown_text.split("\n")
    current_section = None
    current_content = []

    for line in lines:
        if line.startswith("## "):
            # Save previous section if exists
            if current_section and current_content:
                sections[current_section] = "\n".join(current_content).strip()

            # Start new section
            current_section = line[3:].strip()
            current_content = []
        elif current_section:
            current_content.append(line)

    # Save last section
    if current_section and current_content:
        sections[current_section] = "\n".join(current_content).strip()

    return sections
