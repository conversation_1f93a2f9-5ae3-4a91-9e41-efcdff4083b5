"""
Tests for Stress Level Tracker Tool

Comprehensive test suite covering stress tracking functionality,
pattern analysis, and coping strategy evaluation.
"""

import pytest

from athlea_langgraph.tools.mental.stress_tracker import StressLevelTracker
from athlea_langgraph.tools.schemas.mental_schemas import (
    StressLevel,
    StressTrackerInput,
    StressTrackerOutput,
)


class TestStressLevelTracker:
    """Test suite for Stress Level Tracker Tool."""

    @pytest.fixture
    def stress_tracker(self):
        """Create stress tracker instance."""
        return StressLevelTracker()

    @pytest.fixture
    def moderate_stress_input(self):
        """Moderate stress input for testing."""
        return StressTrackerInput(
            user_id="user_123",
            stress_level=6,
            stress_triggers=["work deadlines", "heavy traffic"],
            stress_symptoms=["headache", "muscle tension"],
            coping_strategies_used=["deep breathing", "short walk"],
            context="Work presentation deadline approaching",
            duration_hours=4.0,
            intensity_change="stable",
        )

    @pytest.fixture
    def high_stress_input(self):
        """High stress input for testing."""
        return StressTrackerInput(
            user_id="user_456",
            stress_level=9,
            stress_triggers=["job loss", "family illness", "financial pressure"],
            stress_symptoms=["panic", "overwhelm", "sleep problems", "stomach issues"],
            coping_strategies_used=["tried meditation"],
            context="Multiple life crises happening simultaneously",
            duration_hours=48.0,
            intensity_change="increasing",
        )

    @pytest.fixture
    def low_stress_input(self):
        """Low stress input for testing."""
        return StressTrackerInput(
            user_id="user_789",
            stress_level=3,
            stress_triggers=["minor schedule change"],
            stress_symptoms=["slight tension"],
            coping_strategies_used=["positive thinking", "exercise"],
            context="Small disruption to routine",
            duration_hours=1.0,
            intensity_change="decreasing",
        )

    @pytest.mark.asyncio
    async def test_moderate_stress_tracking(
        self, stress_tracker, moderate_stress_input
    ):
        """Test moderate stress level tracking."""
        result = await stress_tracker.track_stress_level(moderate_stress_input)

        assert isinstance(result, StressTrackerOutput)
        assert result.stress_category == StressLevel.MODERATE
        assert len(result.trigger_patterns) > 0
        assert len(result.effective_coping_strategies) > 0
        assert len(result.stress_management_recommendations) > 0
        assert len(result.immediate_relief_techniques) > 0

    @pytest.mark.asyncio
    async def test_high_stress_tracking(self, stress_tracker, high_stress_input):
        """Test high stress level tracking."""
        result = await stress_tracker.track_stress_level(high_stress_input)

        assert isinstance(result, StressTrackerOutput)
        assert result.stress_category in [StressLevel.HIGH, StressLevel.SEVERE]
        assert (
            "intervention" in result.stress_trend.lower()
            or "escalating" in result.stress_trend.lower()
        )
        assert len(result.warning_signs) > 0
        assert any(
            "urgent" in technique.lower()
            for technique in result.immediate_relief_techniques
        )

    @pytest.mark.asyncio
    async def test_low_stress_tracking(self, stress_tracker, low_stress_input):
        """Test low stress level tracking."""
        result = await stress_tracker.track_stress_level(low_stress_input)

        assert isinstance(result, StressTrackerOutput)
        assert result.stress_category in [StressLevel.LOW, StressLevel.MINIMAL]
        assert "decreasing" in result.stress_trend.lower()
        assert len(result.effective_coping_strategies) > 0

    def test_categorize_stress_level(self, stress_tracker):
        """Test stress level categorization."""
        assert stress_tracker._categorize_stress_level(2) == StressLevel.MINIMAL
        assert stress_tracker._categorize_stress_level(4) == StressLevel.LOW
        assert stress_tracker._categorize_stress_level(6) == StressLevel.MODERATE
        assert stress_tracker._categorize_stress_level(8) == StressLevel.HIGH
        assert stress_tracker._categorize_stress_level(10) == StressLevel.SEVERE

    def test_analyze_stress_trend(self, stress_tracker):
        """Test stress trend analysis."""
        # Test increasing trend
        increasing_input = StressTrackerInput(
            user_id="test",
            stress_level=8,
            stress_triggers=["trigger"],
            intensity_change="increasing",
            duration_hours=6.0,
        )
        trend = stress_tracker._analyze_stress_trend(increasing_input)
        assert "increasing" in trend.lower() or "escalating" in trend.lower()

        # Test decreasing trend
        decreasing_input = StressTrackerInput(
            user_id="test",
            stress_level=4,
            stress_triggers=["trigger"],
            intensity_change="decreasing",
        )
        trend = stress_tracker._analyze_stress_trend(decreasing_input)
        assert "decreasing" in trend.lower()

    def test_identify_trigger_patterns(self, stress_tracker):
        """Test trigger pattern identification."""
        work_triggers = ["deadlines", "meetings", "workload", "pressure"]
        patterns = stress_tracker._identify_trigger_patterns(work_triggers)

        assert isinstance(patterns, list)
        assert len(patterns) > 0
        assert any("work" in pattern.lower() for pattern in patterns)

    def test_evaluate_coping_strategies(self, stress_tracker):
        """Test coping strategy evaluation."""
        # Test with effective strategies
        effective_strategies = ["breathing exercises", "meditation", "exercise"]
        evaluation = stress_tracker._evaluate_coping_strategies(effective_strategies, 5)

        assert isinstance(evaluation, list)
        assert len(evaluation) > 0
        assert any("effective" in strategy.lower() for strategy in evaluation)

        # Test with no strategies
        no_strategies = []
        evaluation = stress_tracker._evaluate_coping_strategies(no_strategies, 8)
        assert any(
            "no coping strategies" in strategy.lower() for strategy in evaluation
        )

    def test_generate_stress_recommendations(self, stress_tracker, high_stress_input):
        """Test stress management recommendation generation."""
        recommendations = stress_tracker._generate_stress_recommendations(
            StressLevel.HIGH, high_stress_input
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert len(recommendations) <= 6  # Should be limited

    def test_identify_warning_signs(self, stress_tracker, high_stress_input):
        """Test warning sign identification."""
        warning_signs = stress_tracker._identify_warning_signs(high_stress_input)

        assert isinstance(warning_signs, list)
        assert len(warning_signs) > 0

    def test_get_immediate_relief_techniques(self, stress_tracker):
        """Test immediate relief technique generation."""
        # Test for high stress
        high_techniques = stress_tracker._get_immediate_relief_techniques(
            StressLevel.HIGH
        )
        assert len(high_techniques) > 0
        assert any(
            "STOP" in technique or "breathing" in technique.lower()
            for technique in high_techniques
        )

        # Test for low stress
        low_techniques = stress_tracker._get_immediate_relief_techniques(
            StressLevel.LOW
        )
        assert len(low_techniques) > 0

    def test_get_long_term_strategies(self, stress_tracker, moderate_stress_input):
        """Test long-term strategy generation."""
        strategies = stress_tracker._get_long_term_strategies(
            StressLevel.MODERATE, moderate_stress_input
        )

        assert isinstance(strategies, list)
        assert len(strategies) > 0

    @pytest.mark.asyncio
    async def test_edge_case_minimal_input(self, stress_tracker):
        """Test with minimal required input."""
        minimal_input = StressTrackerInput(
            user_id="user_minimal", stress_level=5, stress_triggers=["general stress"]
        )

        result = await stress_tracker.track_stress_level(minimal_input)
        assert isinstance(result, StressTrackerOutput)
        assert result.stress_category == StressLevel.MODERATE

    @pytest.mark.asyncio
    async def test_edge_case_extreme_stress(self, stress_tracker):
        """Test with extreme stress level."""
        extreme_input = StressTrackerInput(
            user_id="user_extreme",
            stress_level=10,
            stress_triggers=["crisis"] * 5,
            stress_symptoms=["panic", "overwhelm", "can't cope"],
            duration_hours=72.0,
            intensity_change="increasing",
        )

        result = await stress_tracker.track_stress_level(extreme_input)
        assert isinstance(result, StressTrackerOutput)
        assert result.stress_category == StressLevel.SEVERE
        assert len(result.warning_signs) > 0

    @pytest.mark.asyncio
    async def test_no_coping_strategies(self, stress_tracker):
        """Test handling when no coping strategies are used."""
        no_coping_input = StressTrackerInput(
            user_id="user_no_coping",
            stress_level=7,
            stress_triggers=["work stress"],
            coping_strategies_used=[],
        )

        result = await stress_tracker.track_stress_level(no_coping_input)
        assert isinstance(result, StressTrackerOutput)
        assert any(
            "no coping strategies" in strategy.lower()
            for strategy in result.effective_coping_strategies
        )

    def test_multiple_trigger_categories(self, stress_tracker):
        """Test identification of multiple trigger categories."""
        mixed_triggers = [
            "work deadlines",  # work
            "family arguments",  # personal
            "traffic noise",  # environmental
            "headache",  # physical
            "anxiety",  # emotional
        ]

        patterns = stress_tracker._identify_trigger_patterns(mixed_triggers)
        assert len(patterns) > 1  # Should identify multiple patterns

    def test_context_specific_recommendations(self, stress_tracker):
        """Test context-specific recommendations."""
        work_context_input = StressTrackerInput(
            user_id="test",
            stress_level=7,
            stress_triggers=["deadlines"],
            context="Work presentation deadline approaching",
        )

        recommendations = stress_tracker._generate_stress_recommendations(
            StressLevel.HIGH, work_context_input
        )

        assert any("work" in rec.lower() for rec in recommendations)

    def test_duration_based_analysis(self, stress_tracker):
        """Test duration-based stress analysis."""
        # Test long duration
        long_duration_input = StressTrackerInput(
            user_id="test",
            stress_level=6,
            stress_triggers=["chronic issue"],
            duration_hours=48.0,
        )

        trend = stress_tracker._analyze_stress_trend(long_duration_input)
        assert "24 hours" in trend or "prolonged" in trend.lower()

    def test_validation_constraints(self):
        """Test input validation constraints."""
        # Test valid input
        valid_input = StressTrackerInput(
            user_id="test", stress_level=5, stress_triggers=["work"]
        )
        assert valid_input.stress_level == 5

        # Test invalid stress level (should raise validation error)
        with pytest.raises(ValueError):
            StressTrackerInput(
                user_id="test",
                stress_level=11,  # Invalid: > 10
                stress_triggers=["work"],
            )

    def test_tool_properties(self, stress_tracker):
        """Test tool properties and configuration."""
        assert stress_tracker.domain == "mental_training"
        assert stress_tracker.name == "stress_level_tracker"
        assert stress_tracker.description
        assert hasattr(stress_tracker, "stress_categories")
        assert hasattr(stress_tracker, "common_triggers")
        assert hasattr(stress_tracker, "coping_strategies")
        assert hasattr(stress_tracker, "management_techniques")
