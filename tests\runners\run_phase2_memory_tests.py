#!/usr/bin/env python3
"""
Phase 2 Advanced Memory System Test Runner

Simple script to run the comprehensive Phase 2 memory tests with proper setup.

Usage:
    python run_phase2_memory_tests.py

Requirements:
    - All dependencies installed (pip install -r requirements.txt)
    - Environment variables set (see .env.local)
    - mem0 configuration available
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path and change working directory
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)


async def main():
    """Run the Phase 2 advanced memory test suite."""
    print("🎯 Phase 2: Advanced Memory System Test Suite")
    print("=" * 55)
    print("Testing comprehensive advanced memory features:")
    print("• Multi-domain memory separation")
    print("• Advanced hybrid search and ranking")
    print("• LLM-based memory summarization")
    print("• Intelligent memory decay and cleanup")
    print("• Real-time analytics and monitoring")
    print("• Unified advanced memory management")
    print()

    # Environment check
    print("🔧 Environment Validation:")
    required_vars = ["OPENAI_API_KEY"]
    optional_vars = ["MONGODB_URI", "CHROMADB_PATH", "PINECONE_API_KEY"]

    env_ok = True
    for var in required_vars:
        if os.getenv(var):
            print(f"  ✅ {var} is configured")
        else:
            print(f"  ❌ {var} is missing (required)")
            env_ok = False

    for var in optional_vars:
        if os.getenv(var):
            print(f"  ✅ {var} is configured")
        else:
            print(f"  ⚠️  {var} not set (optional, may limit some tests)")

    if not env_ok:
        print("\n❌ Missing required environment variables!")
        print("Please ensure all required variables are set in .env.local")
        return False

    print()

    try:
        # Import and run the test suite
        from tests.unit.test_advanced_memory_phase2 import TestAdvancedMemoryPhase2

        print("🚀 Starting Phase 2 Advanced Memory Test Suite...")
        print("-" * 55)

        tester = TestAdvancedMemoryPhase2()
        success = await tester.run_comprehensive_tests()

        if success:
            print("\n🏆 Phase 2 Advanced Memory System - ALL TESTS PASSED!")
            print("\n🎉 Validated Features:")
            print("  ✅ Domain-aware memory classification and storage")
            print("  ✅ Advanced hybrid search with multi-factor ranking")
            print("  ✅ LLM-powered conversation summarization")
            print("  ✅ Intelligent memory lifecycle management")
            print("  ✅ Real-time analytics and health monitoring")
            print("  ✅ Unified advanced memory manager interface")

            print("\n🔗 Key Components Tested:")
            print("  - DomainMemoryManager: Multi-domain separation")
            print("  - AdvancedRetrievalPipeline: Hybrid search & ranking")
            print("  - MemorySummarizationEngine: LLM-based summarization")
            print("  - MemoryDecayManager: Lifecycle management")
            print("  - MemoryAnalyticsEngine: Real-time monitoring")
            print("  - AdvancedMemoryManager: Unified interface")

            print("\n🚀 Phase 2 Implementation Ready for Production!")
            return True
        else:
            print("\n⚠️  Some Phase 2 tests failed.")
            print("\n🔧 Common troubleshooting steps:")
            print("  1. Verify mem0 configuration in environment")
            print("  2. Check LLM model availability (OpenAI API)")
            print("  3. Ensure vector database connectivity")
            print("  4. Validate test data access permissions")
            return False

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure you're in the python-langgraph directory")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Check that Phase 2 memory files exist:")
        print("   - athlea_langgraph/memory/domain_manager.py")
        print("   - athlea_langgraph/memory/advanced_retrieval.py")
        print("   - athlea_langgraph/memory/summarization_engine.py")
        print("   - athlea_langgraph/memory/decay_manager.py")
        print("   - athlea_langgraph/memory/analytics.py")
        print("   - athlea_langgraph/memory/advanced_memory_manager.py")
        print(f"4. Current working directory: {os.getcwd()}")
        return False

    except Exception as e:
        print(f"❌ Test Execution Error: {e}")
        print("\n🔧 This might indicate:")
        print("- Configuration issues with mem0 or vector databases")
        print("- Missing API keys or database connections")
        print("- LLM model availability issues")
        print("- Dependency version conflicts")
        print("\nSee detailed logs above for specific error information.")
        return False


def run_quick_validation():
    """Run a quick validation to check if components can be imported."""
    print("🔍 Quick Phase 2 Component Import Validation:")

    try:
        from athlea_langgraph.memory import (
            AdvancedMemoryManager,
            AdvancedRetrievalPipeline,
            DomainMemoryManager,
            MemoryAnalyticsEngine,
            MemoryDecayManager,
            MemorySummarizationEngine,
        )

        print("  ✅ All Phase 2 components imported successfully")
        return True
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return False


if __name__ == "__main__":
    print("🎯 Phase 2 Advanced Memory System - Test Runner")
    print("=" * 50)

    # Quick validation first
    if not run_quick_validation():
        print("\n❌ Component import validation failed!")
        print("Please ensure Phase 2 implementation is complete.")
        sys.exit(1)

    print()

    # Run full test suite
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Test run cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
