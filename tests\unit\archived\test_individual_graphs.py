"""
Test script for individual agent graphs

This script tests that all individual agent graphs can be created successfully
and verifies they work with basic inputs.
"""

import asyncio
import logging
from typing import Any, Dict

from langchain_core.messages import HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import all individual graph creators
from athlea_langgraph.graphs.archived.individual_graphs import (
    IndividualAgentState,
    create_cardio_agent_graph,
    create_cycling_agent_graph,
    create_head_coach_graph,
    create_mental_agent_graph,
    create_nutrition_agent_graph,
    create_recovery_agent_graph,
    create_strength_agent_graph,
)


async def test_graph_creation(
    graph_name: str, graph_creator_func, config: Dict[str, Any] = None
):
    """Test that a graph can be created successfully."""

    try:
        logger.info(f"Testing creation of {graph_name}...")

        if config is None:
            config = {}

        # Create the graph
        graph = await graph_creator_func(config)

        logger.info(f"✓ {graph_name} created successfully")
        return graph

    except Exception as e:
        logger.error(f"✗ Failed to create {graph_name}: {e}")
        return None


async def test_graph_execution(graph_name: str, graph, test_query: str):
    """Test that a graph can execute with a simple query."""

    try:
        logger.info(f"Testing execution of {graph_name}...")

        # Create initial state
        initial_state = IndividualAgentState(
            messages=[HumanMessage(content=test_query)], user_query=test_query
        )

        # Run the graph
        result = await graph.ainvoke(initial_state)

        logger.info(f"✓ {graph_name} executed successfully")
        logger.info(f"  Current Agent: {result.get('current_agent', 'Unknown')}")
        logger.info(f"  Response Length: {len(result.get('agent_response', ''))}")

        return result

    except Exception as e:
        logger.error(f"✗ Failed to execute {graph_name}: {e}")
        return None


async def main():
    """Run all individual graph tests."""

    logger.info("Starting individual graph tests...")

    # Test configurations
    test_configs = [
        ("head_coach", create_head_coach_graph, "I want to improve my fitness"),
        (
            "strength_agent",
            create_strength_agent_graph,
            "I want to build muscle and get stronger",
        ),
        (
            "nutrition_agent",
            create_nutrition_agent_graph,
            "What should I eat to support my training?",
        ),
        (
            "cardio_agent",
            create_cardio_agent_graph,
            "How can I improve my running endurance?",
        ),
        (
            "recovery_agent",
            create_recovery_agent_graph,
            "How should I recover after intense workouts?",
        ),
        (
            "mental_agent",
            create_mental_agent_graph,
            "How can I stay motivated with my fitness goals?",
        ),
        (
            "cycling_agent",
            create_cycling_agent_graph,
            "I want to improve my cycling performance",
        ),
    ]

    # Test graph creation
    logger.info("\n=== TESTING GRAPH CREATION ===")
    graphs = {}

    for graph_name, creator_func, _ in test_configs:
        graph = await test_graph_creation(graph_name, creator_func)
        if graph:
            graphs[graph_name] = graph

    # Test graph execution
    logger.info("\n=== TESTING GRAPH EXECUTION ===")

    for graph_name, _, test_query in test_configs:
        if graph_name in graphs:
            await test_graph_execution(graph_name, graphs[graph_name], test_query)
        else:
            logger.warning(
                f"Skipping execution test for {graph_name} (creation failed)"
            )

    # Summary
    logger.info("\n=== TEST SUMMARY ===")
    logger.info(f"Graphs successfully created: {len(graphs)}/{len(test_configs)}")

    for graph_name, _, _ in test_configs:
        status = "✓" if graph_name in graphs else "✗"
        logger.info(f"{status} {graph_name}")

    if len(graphs) == len(test_configs):
        logger.info("\n🎉 All individual graphs are working correctly!")
    else:
        logger.warning(
            f"\n⚠️  {len(test_configs) - len(graphs)} graphs failed to create"
        )


if __name__ == "__main__":
    asyncio.run(main())
