"""
Comprehensive tests for Google Maps Elevation Tool.

Tests cover:
- Input validation
- Output validation
- API request handling
- Error scenarios
- Circuit breaker functionality
- Fallback responses
"""

import asyncio
from typing import Any, Dict
from unittest.mock import AsyncMock, Mock, patch

import aiohttp
import pytest

from athlea_langgraph.tools import GoogleMapsElevationTool
from athlea_langgraph.tools.base_tool import ToolError, ToolErrorType
from athlea_langgraph.tools.circuit_breaker import CircuitState
from athlea_langgraph.tools.schemas.elevation_schemas import (
    ElevationInput,
    ElevationOutput,
    ElevationPoint,
)


class TestGoogleMapsElevationTool:
    """Test suite for Google Maps Elevation Tool."""

    @pytest.fixture
    def tool(self):
        """Create tool instance with mock API key."""
        return GoogleMapsElevationTool(api_key="test_api_key")

    @pytest.fixture
    def mock_google_response(self):
        """Mock successful Google Maps API response."""
        return {
            "results": [
                {
                    "elevation": 100.5,
                    "location": {"lat": 40.7128, "lng": -74.0060},
                    "resolution": 10.0,
                }
            ],
            "status": "OK",
        }

    @pytest.fixture
    def mock_path_response(self):
        """Mock Google Maps API response for path elevation."""
        return {
            "results": [
                {
                    "elevation": 100.0,
                    "location": {"lat": 40.0, "lng": -74.0},
                    "resolution": 10.0,
                },
                {
                    "elevation": 150.0,
                    "location": {"lat": 40.1, "lng": -74.1},
                    "resolution": 10.0,
                },
                {
                    "elevation": 120.0,
                    "location": {"lat": 40.2, "lng": -74.2},
                    "resolution": 10.0,
                },
            ],
            "status": "OK",
        }

    # ==================== Input Validation Tests ====================

    def test_tool_initialization_with_api_key(self):
        """Test tool initializes correctly with API key."""
        tool = GoogleMapsElevationTool(api_key="test_key")
        assert tool.api_key == "test_key"
        assert tool.name == "google_maps_elevation"
        assert tool.circuit_breaker is not None

    def test_tool_initialization_without_api_key(self):
        """Test tool initialization fails without API key."""
        with patch.dict("os.environ", {}, clear=True):
            with pytest.raises(ValueError, match="Google Maps API key required"):
                GoogleMapsElevationTool()

    @pytest.mark.asyncio
    async def test_valid_point_input(self, tool):
        """Test validation of valid point input."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch.object(tool, "_execute_tool") as mock_execute:
            mock_execute.return_value = {
                "elevation": 100.5,
                "api_status": "OK",
                "total_points": 1,
            }

            result = await tool.invoke(input_data)

            # Should call _execute_tool with validated input
            mock_execute.assert_called_once()
            validated_input = mock_execute.call_args[0][0]
            assert isinstance(validated_input, ElevationInput)
            assert validated_input.mode == "point"
            assert validated_input.lat == 40.7128
            assert validated_input.lon == -74.0060

    @pytest.mark.asyncio
    async def test_invalid_coordinates(self, tool):
        """Test validation rejects invalid coordinates."""
        input_data = {"mode": "point", "lat": 91.0, "lon": -74.0060}  # Invalid latitude

        result = await tool.invoke(input_data)

        # Should return fallback response for validation error
        assert hasattr(result, "success")
        assert not result.success
        assert "validation" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_path_mode_validation(self, tool):
        """Test path mode requires points array."""
        input_data = {
            "mode": "path",
            "lat": 40.0,  # Should be ignored in path mode
            "lon": -74.0,
            "points": [{"lat": 40.0, "lon": -74.0}, {"lat": 40.1, "lon": -74.1}],
        }

        with patch.object(tool, "_execute_tool") as mock_execute:
            mock_execute.return_value = {
                "min_elevation": 100,
                "max_elevation": 150,
                "api_status": "OK",
                "total_points": 2,
            }

            result = await tool.invoke(input_data)

            mock_execute.assert_called_once()
            validated_input = mock_execute.call_args[0][0]
            assert validated_input.mode == "path"
            assert len(validated_input.points) == 2

    # ==================== API Request Tests ====================

    @pytest.mark.asyncio
    async def test_single_point_elevation_success(self, tool, mock_google_response):
        """Test successful single point elevation lookup."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_google_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.elevation == 100.5
            assert result.data.total_points == 1
            assert len(result.data.points) == 1

    @pytest.mark.asyncio
    async def test_path_elevation_with_gain_loss(self, tool, mock_path_response):
        """Test path elevation calculation with gain/loss."""
        input_data = {
            "mode": "path",
            "points": [
                {"lat": 40.0, "lon": -74.0},
                {"lat": 40.1, "lon": -74.1},
                {"lat": 40.2, "lon": -74.2},
            ],
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_path_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.min_elevation == 100.0
            assert result.data.max_elevation == 150.0
            assert result.data.elevation_gain == 50.0  # 100 -> 150
            assert result.data.elevation_loss == 30.0  # 150 -> 120
            assert result.data.total_points == 3

    @pytest.mark.asyncio
    async def test_batch_locations_handling(self, tool):
        """Test handling of multiple discrete locations."""
        # Create input with many points to test batching
        points = [{"lat": 40.0 + i * 0.1, "lon": -74.0 + i * 0.1} for i in range(3)]
        input_data = {"mode": "locations", "points": points}

        mock_response = {
            "results": [
                {
                    "elevation": 100.0 + i * 10,
                    "location": {"lat": 40.0 + i * 0.1, "lng": -74.0 + i * 0.1},
                    "resolution": 10.0,
                }
                for i in range(3)
            ],
            "status": "OK",
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_resp = AsyncMock()
            mock_resp.status = 200
            mock_resp.json = AsyncMock(return_value=mock_response)
            mock_get.return_value.__aenter__.return_value = mock_resp

            result = await tool.invoke(input_data)

            assert result.success
            assert result.data.total_points == 3
            assert len(result.data.points) == 3

    # ==================== Error Handling Tests ====================

    @pytest.mark.asyncio
    async def test_api_rate_limit_error(self, tool):
        """Test handling of API rate limit errors."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 429
            mock_response.headers = {"Retry-After": "60"}
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "rate" in result.error_type.lower()
            assert result.retry_after == 60

    @pytest.mark.asyncio
    async def test_api_authentication_error(self, tool):
        """Test handling of authentication errors."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 403
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "authentication" in result.error_type.lower()

    @pytest.mark.asyncio
    async def test_api_over_query_limit(self, tool):
        """Test handling of quota exceeded errors."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        error_response = {
            "status": "OVER_QUERY_LIMIT",
            "error_message": "You have exceeded your quota",
        }

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=error_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            assert not result.success
            assert "rate" in result.error_type.lower()
            assert result.retry_after == 3600  # 1 hour

    @pytest.mark.asyncio
    async def test_network_timeout_error(self, tool):
        """Test handling of network timeout errors."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_get.side_effect = asyncio.TimeoutError("Request timed out")

            result = await tool.invoke(input_data)

            assert not result.success
            assert "timeout" in result.error_type.lower()

    # ==================== Circuit Breaker Tests ====================

    @pytest.mark.asyncio
    async def test_circuit_breaker_opens_on_failures(self, tool):
        """Test circuit breaker opens after repeated failures."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        # Simulate repeated failures to trigger circuit breaker
        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_get.side_effect = aiohttp.ClientError("Connection failed")

            # Execute multiple times to trigger circuit breaker
            for _ in range(6):  # More than failure threshold (5)
                result = await tool.invoke(input_data)
                assert not result.success

            # Circuit breaker should now be open
            assert tool.circuit_breaker.state == CircuitState.OPEN

    @pytest.mark.asyncio
    async def test_circuit_breaker_blocks_requests_when_open(self, tool):
        """Test circuit breaker blocks requests when open."""
        # Manually open the circuit breaker
        tool.circuit_breaker.state = CircuitState.OPEN
        tool.circuit_breaker.next_attempt_time = asyncio.get_event_loop().time() + 60

        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        result = await tool.invoke(input_data)

        assert not result.success
        assert "circuit" in result.error_type.lower()

    # ==================== Fallback Response Tests ====================

    @pytest.mark.asyncio
    async def test_fallback_response_for_rate_limit(self, tool):
        """Test fallback response generation for rate limits."""
        # Create a rate limit error
        error = ToolError(
            "Rate limited", ToolErrorType.RATE_LIMIT_ERROR, retry_after=300
        )

        fallback = await tool._generate_fallback(error)

        assert fallback is not None
        assert fallback["api_status"] == "RATE_LIMITED"
        assert "retry after 300 seconds" in fallback["fallback_message"]

    @pytest.mark.asyncio
    async def test_fallback_response_for_service_error(self, tool):
        """Test fallback response for general service errors."""
        error = ToolError("Service error", ToolErrorType.EXTERNAL_SERVICE_ERROR)

        fallback = await tool._generate_fallback(error)

        assert fallback is not None
        assert fallback["api_status"] == "SERVICE_UNAVAILABLE"
        assert "temporarily unavailable" in fallback["fallback_message"]

    # ==================== Integration Test ====================

    @pytest.mark.asyncio
    async def test_full_integration_with_mock_api(self, tool, mock_google_response):
        """Test full integration flow with mocked API."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_google_response)
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await tool.invoke(input_data)

            # Verify the complete flow
            assert result.success
            assert hasattr(result, "data")
            assert hasattr(result, "execution_time_ms")
            assert hasattr(result, "tool_name")
            assert hasattr(result, "request_id")

            # Verify data structure
            assert result.data.elevation == 100.5
            assert result.data.api_status == "OK"
            assert result.data.total_points == 1

            # Verify API was called correctly
            mock_get.assert_called_once()
            call_args = mock_get.call_args
            assert "locations=40.7128%2C-74.006" in call_args[0][0]
            assert "key=test_api_key" in call_args[0][0]


# ==================== Fixtures for Integration Testing ====================


@pytest.fixture
def integration_tool():
    """Create tool for integration testing with real API key if available."""
    import os

    api_key = os.getenv("GOOGLE_MAPS_API_KEY")
    if api_key:
        return GoogleMapsElevationTool(api_key=api_key)
    else:
        pytest.skip(
            "No GOOGLE_MAPS_API_KEY environment variable set for integration tests"
        )


class TestGoogleMapsElevationIntegration:
    """Integration tests that require a real API key."""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_api_single_point(self, integration_tool):
        """Test with real Google Maps API - single point."""
        input_data = {"mode": "point", "lat": 40.7128, "lon": -74.0060}  # New York City

        result = await integration_tool.invoke(input_data)

        assert result.success
        assert result.data.elevation is not None
        assert result.data.elevation > 0  # NYC is above sea level
        assert result.data.total_points == 1

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_api_path(self, integration_tool):
        """Test with real Google Maps API - path."""
        input_data = {
            "mode": "path",
            "points": [
                {"lat": 40.7128, "lon": -74.0060},  # NYC
                {"lat": 40.7589, "lon": -73.9851},  # Central Park
            ],
        }

        result = await integration_tool.invoke(input_data)

        assert result.success
        assert result.data.min_elevation is not None
        assert result.data.max_elevation is not None
        assert result.data.elevation_gain is not None
        assert result.data.elevation_loss is not None
        assert result.data.total_points > 1


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
